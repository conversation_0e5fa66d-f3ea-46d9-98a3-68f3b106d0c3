<?php
if (!defined('LIBRARIES')) die("Error");

/* Setting */
$setting = $cache->get("select * from #_setting limit 0,1", null, 'fetch', 7200);
$optsetting = json_decode($setting['options'], true);

/* Lang */
if (empty($_SESSION['lang'])) $_SESSION['lang'] = 'vi';
$lang = $_SESSION['lang'];

/* Favicon */
$favicon = $cache->get("select photo from #_photo where type = ? and act = ? limit 0,1", array('favicon', 'photo_static'), 'fetch', 7200);

/* Logo */
$logo = $cache->get("select id, photo, options from #_photo where type = ? and act = ? limit 0,1", array('logo', 'photo_static'), 'fetch', 7200);

/* Social */
$social = $cache->get("select name$lang, photo, link from #_photo where type = ? and act = ? and find_in_set('hienthi',status) order by numb,id desc", array('social', 'photo_static'), 'result', 7200);

/* Slideshow */
$slider = $cache->get("select name$lang, photo, link from #_photo where type = ? and act = ? and find_in_set('hienthi',status) order by numb,id desc", array('slide', 'photo_static'), 'result', 7200);

/* Spbanner */
$spbanner = $cache->get("select name$lang, photo, link from #_photo where type = ? and act = ? and find_in_set('hienthi',status) order by numb,id desc", array('spbanner', 'photo_static'), 'result', 7200);

/* Video */
$video = $cache->get("select name$lang, photo, link from #_photo where type = ? and act = ? and find_in_set('hienthi',status) order by numb,id desc limit 0,2", array('video', 'photo_static'), 'result', 7200);

/* Popup */
$popup = $cache->get("select name$lang, photo, link, options from #_photo where type = ? and act = ? and find_in_set('hienthi',status) order by numb,id desc limit 0,1", array('popup', 'photo_static'), 'fetch', 7200);

/* Newshome */
$newshome = $cache->get("select name$lang, desc$lang, date_created, id, photo, slug$lang from #_news where type='tin-tuc' and find_in_set('noibat',status) and find_in_set('hienthi',status) order by numb,id desc limit 0,6", null, 'result', 7200);

/* Product */
$productshome = $cache->get("select name$lang, desc$lang, sale_price, regular_price, discount, id, photo, slug$lang from #_product where type='san-pham' and find_in_set('noibat',status) and find_in_set('hienthi',status) order by numb,id desc limit 0,8", null, 'result', 7200);

/* Tags */
$tagsProduct = $cache->get("select name$lang, slug$lang, id from #_tags where type = ? and find_in_set('hienthi',status) order by numb,id desc", array('san-pham'), 'result', 7200);

/* Policy */
$policy = $cache->get("select name$lang, desc$lang, slug$lang, id from #_news where type = ? and find_in_set('hienthi',status) order by numb,id desc", array('chinh-sach'), 'result', 7200);

/* Footer */
$footer = $cache->get("select name$lang, content$lang from #_static where type = ? limit 0,1", array('footer'), 'fetch', 7200);

/* About */
$about = $cache->get("select name$lang, desc$lang, content$lang, photo from #_static where type = ? limit 0,1", array('gioi-thieu'), 'fetch', 7200);

/* Contact */
$contact = $cache->get("select name$lang, address$lang, hotline, phone, email, website, fanpage, coords, content$lang from #_static where type = ? limit 0,1", array('lien-he'), 'fetch', 7200);

/* Seo page */
$seopage = $cache->get("select * from #_seopage where type = ? limit 0,1", array($com), 'fetch', 7200);
if (!empty($seopage['title' . $lang])) $seo->set('title', $seopage['title' . $lang]);
if (!empty($seopage['keywords' . $lang])) $seo->set('keywords', $seopage['keywords' . $lang]);
if (!empty($seopage['description' . $lang])) $seo->set('description', $seopage['description' . $lang]);

/* Breadcrumbs */
if (!empty($titleMain)) $breadcr->set($com, $titleMain);
?>