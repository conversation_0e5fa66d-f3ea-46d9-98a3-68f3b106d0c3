<?php
define("trangchu","Trang chủ");
define("g<PERSON><PERSON><PERSON>","Giới thiệu");
define("tintuc","Tin tức");
define("lienhe","<PERSON><PERSON><PERSON> hệ");
define("motasanpham","<PERSON><PERSON> tả");
define("san<PERSON>m","Sản phẩm");
define("luotxem","Lượt xem");
define("masp","Mã sản phẩm");
define("ngaydang","Ngày đăng");
define("tintuckhac","Tin tức khác");
define("hotrotructuyen","<PERSON><PERSON> trợ trực tuyến");
define("dangonline","Đang online");
define("tongtruycap","Tổng truy cập");
define("diachi","Địa chỉ");
define("thongtinlienhe","Thông tin liên hệ");
define("sodienthoai","Số điện thoại");
define("hovaten","<PERSON><PERSON> và tên");
define("chude","Ch<PERSON> đề");
define("noidung","Nội dung");
define("mat<PERSON><PERSON><PERSON>","Mật khẩu mới");
define("nhaplaimatkhaumoi","Nhập lại mật khẩu mới");
define("gui","Gửi");
define("nhaplai","Nhập lại");
define("dangky","Đăng ký");
define("khongtimthayketqua","Không tìm thấy kết quả");
define("timduongdi","Tìm đường đi");
define("dinhkemtaptin","Đính kèm file");
define("mabaomat","Mã bảo mật");
define("dichvu","Dịch vụ");
define("timkiem","Tìm kiếm");
define("capnhat","Cập nhật");
define("boqua","Bỏ qua");
define("phuongxa","Phường/xã");
define("truycapngay","Truy cập ngày");
define("dangkynhantin","Đăng ký nhận tin");
define("quanhuyen","Quận/huyện");
define("vuilongchonquanhuyen","Vui lòng chọn quận huyện");
define("soluongqualon","Số lượng không được lớn hơn 999");
define("hoten","Họ tên");
define("chitietsanpham","Chi tiết sản phẩm");
define("xemthem","Xem thêm");
define("tuyendung","Tuyển dụng");
define("gia","Giá");
define("thongtincanhan","Thông tin cá nhân");
define("thuvienanh","Thư viện ảnh");
define("sanphamnoibat","Sản phẩm nổi bật");
define("ketquatimkiem","Kết quả tìm kiếm");
define("dathang","Đặt hàng");
define("matkhaucu","Mật khẩu cũ");
define("giohang","Giỏ hàng");
define("hinh","Hình");
define("soluong","Số lượng");
define("thanhtien","Thành tiền");
define("tinhthanh","Tỉnh/thành phố");
define("nhaptukhoatimkiem","Nhập từ khóa cần tìm...");
define("tinhtrang","Tình trạng");
define("hethang","Hết hàng");
define("dangcapnhat","Đang cập nhật");
define("danhmucsanpham","Danh mục sản phẩm");
define("trongtuan","Tuần");
define("thaydoimatkhau","Thay đổi mật khẩu");
define("thongketruycap","Thống kê truy cập");
define("thongtinsanpham","Thông tin sản phẩm");
define("vuilongchonphuongxa","Vui lòng chọn phường xã");
define("traloi","Trả lời");
define("huy","Hủy");
define("binhluanve","bình luận về");
define("moibanthaoluan","Mời bạn thảo luận. Vui lòng nhập Tiếng Việt có dấu, tối thiểu 10 ký tự.");
define("co","Có");
define("danhgia","Đánh giá");
define("xacnhan","Xác nhận");
define("sanphamcungloai","Sản phẩm cùng loại");
define("conhang","Còn hàng");
define("thanhtoan","Thanh toán");
define("chonvideo","Chọn video");
define("slogandangkynhantin","Để lại thông tin để nhận tin tức mới nhất từ chúng tôi");
define("giamoi","Giá mới");
define("giacu","Giá cũ");
define("binhluan","Bình luận");
define("baivietkhac","Bài viết khác");
define("banmuonxoasanphamnay","Bạn muốn xóa sản phẩm này khỏi giỏ hàng ? ");
define("soluongkhongnhohonkhong","Số lượng đặt mua không được nhỏ hơn 0");
define("muatiep","Mua tiếp");
define("xoatatca","Xóa tất cả");
define("kichthuoc","Kích thước");
define("dangnhap","Đăng nhập");
define("taikhoan","Tài khoản");
define("matkhau","Mật khẩu");
define("nhomatkhau","Nhớ mật khẩu");
define("quenmatkhau","Quên mật khẩu");
define("nhaplaimatkhau","Nhập lại mật khẩu");
define("dienthoai","Điện thoại");
define("banchuacotaikhoan","Bạn chưa có tài khoản ");
define("laymatkhau","Lấy mật khẩu");
define("apdung","Áp dụng");
define("phivanchuyen","Phí vận chuyển");
define("tamtinh","Tạm tính");
define("tongtien","Tổng tiền");
define("map","Bản đồ");
define("donhang","Đơn hàng");
define("lichsudonhang","Lịch sử đơn hàng");
define("trangdau","Trang đầu");
define("trangcuoi","Trang cuối");
define("trangbantruycapkhongtontai","Trang bạn truy cập không tồn tại.");
define("vetrangchu","Về trang chủ");
define("chiase","Chia sẻ");
define("xoa","Xóa");
define("mausac","Màu sắc");
define("thongtingiaohang","Thông tin giao hàng");
define("vuilongchontinhthanh","Vui lòng chọn tỉnh thành");
define("yeucaukhac","Yêu cầu khác (không bắt buộc)");
define("khongtontaisanphamtronggiohang","Không tồn tại sản phẩm nào trong giỏ hàng !");
define("giohangcuaban","Giỏ hàng của bạn");
define("vuilongchonsizevamau","Vui lòng chọn size và màu !");
define("dangkytaiday","Đăng ký");
define("nhaphoten","Nhập họ tên của bạn");
define("nhaptaikhoan","Nhập tài khoản của bạn");
define("nhapmatkhau","Nhập mật khẩu (5-15 ký tự)");
define("nhapemail","Nhập địa chỉ email của bạn");
define("nhapdienthoai","Nhập số điện thoại");
define("nhapdiachi","Nhập địa chỉ của bạn");
define("nhapngaysinh","Nhập ngày sinh");
define("gioitinh","Giới tính");
define("nam","Nam");
define("nu","Nữ");
define("kichhoat","Kích hoạt");
define("nhapmakichhoat","Nhập mã kích hoạt");
define("nhapmatkhaucu","Nhập mật khẩu cũ");
define("nhapmatkhaumoi","Nhập mật khẩu mới");
define("thich","Thích");
define("dathich","Đã thích");
define("taithembinhluan","Tải thêm bình luận");
define("trangke","Trang kế");
define("trangtruoc","Trang trước");
define("hinhanh","Hình ảnh");
define("tensanpham","Tên sản phẩm");
define("duong","Đường phố");
define("hinhthucthanhtoan","Hình thức thanh toán");
define("vuilongnhaphoten","Vui lòng nhập họ và tên");
define("vuilongnhapsodienthoai","Vui lòng nhập số điện thoại");
define("vuilongnhapdiachi","Vui lòng nhập địa chỉ");
define("vuilongnhapdiachiemail","Vui lòng nhập địa chỉ email");
define("vuilongnhapchude","Vui lòng nhập chủ đề");
define("vuilongnhapnoidung","Vui lòng nhập nội dung");
define("chon","Chọn");
define("tieptucmuahang","Tiếp tục mua hàng");
define("trongthang","Tháng");
define("chinhsach","Chính sách hỗ trợ");
define("thuonghieu","Thương hiệu");
define("vuilongnhaptaikhoan","Vui lòng nhập tài khoản");
define("vuilongnhapmatkhau","Vui lòng nhập mật khẩu");
define("vuilongnhaplaimatkhau","Vui lòng nhập lại mật khẩu");
define("vuilongnhapngaysinh","Vui lòng nhập ngày sinh");
define("ngaysinh","Ngày sinh");
define("vuilongnhapmakichhoat","Vui lòng nhập mã kích hoạt");
define("dangxuat","Đăng xuất");
define("homnay","Hôm nay");
define("homqua","Hôm qua");
define("thongbao","Thông báo");
define("thoat","Thoát");
define("chuanhaptukhoatimkiem","Chưa nhập từ khóa tìm kiếm");
define("capnhatthongtin","Cập nhật thông tin");
define("noidungdangcapnhat","Nội dung đang cập nhật");
define("dangcapnhatdulieu","Đang cập nhật dữ liệu");
define("giamgia","Giảm giá");
define("emailkhonghople","Email không hợp lệ");
define("sodienthoaikhonghople","Số điện thoại không hợp lệ");
define("emailkhongduoctrong","Email không được trống");
define("dangkynhantinthanhcong","Đăng ký nhận tin thành công. Chúng tôi sẽ liên hệ với bạn sớm.");
define("dangkynhantinthatbai","Đăng ký nhận tin thất bại. Vui lòng thử lại sau.");
define("guilienhethatbai","Gửi liên hệ thất bại. Vui lòng thử lại sau");
define("guilienhethanhcong","Gửi liên hệ thành công");
define("donhangkhonghople","Đơn hàng không hợp lệ. Vui lòng thử lại sau.");
define("chuachonhinhthucthanhtoan","Chưa chọn hình thức thanh toán");
define("chuachontinhthanhpho","Chưa chọn tỉnh/thành phố");
define("chuachonquanhuyen","Chưa chọn quận/huyện");
define("chuachonphongxa","Chưa chọn phường/xã");
define("thongtindonhangguithanhcong","Thông tin đơn hàng đã được gửi thành công");
define("goidien","Gọi điện");
define("nhantin","Nhắn tin");
define("danhgiasanpham","Đánh giá sản phẩm");
define("danhgiatrungbinh","Đánh Giá Trung Bình");
define("nhanxet","nhận xét");
define("chiasenhanxetvesanpham","Chia sẻ nhận xét về sản phẩm");
define("vietnhanxetcuaban","Viết nhận xét của bạn");
define("guinhanxetcuaban","Gửi nhận xét của bạn");
define("danhgiavesanphamnay","Đánh giá của bạn về sản phẩm này");
define("tieudecuanhanxet","Tiêu đề của nhận xét");
define("vietnhanxetcuabanvaobenduoi","Viết nhận xét của bạn vào bên dưới");
define("nhaptieudenhanxet","Nhập tiêu đề nhận xét");
define("nhanxetcuabanvesanphamnay","Nhận xét của bạn về sản phẩm này");
define("toida3hinh","Tối đa 3 hình");
define("videoclip","Video clip");
define("hinhdaidien","Hình ảnh đại diện");
define("dinhdang","Định dạng");
define("taptinvideo","Tập tin video");
define("toida","Tối đa");
define("guinhanxet","Gửi nhận xét");
define("cacbinhluankhac","Các bình luận khác");
?>