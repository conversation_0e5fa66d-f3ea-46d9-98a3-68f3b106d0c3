/* Login */
.login-box{width:320px;}
.login-box .card .login-card-body{padding:30px 20px;border-radius:0.25rem;}
.login-box .card .login-card-body .login-box-msg{color:#686f75;font-size:16px;padding-bottom:1rem;text-transform:capitalize;}
.login-box .card .login-card-body .input-group .form-control{border-right:1px solid #ced4da;}
.login-box .card .login-card-body .input-group .form-control:focus{border-color:#80bdff!important;}
.login-box .card .login-card-body .input-group .login-input-group-append .input-group-text{border:1px solid #ced4da!important;border-right:0px!important;border-top-left-radius:.25rem!important;border-bottom-left-radius:.25rem!important;}
.login-view-website{position:absolute;background-color:#1c1f22;width:100%;left:0px;top:0px;text-align:center;color:#fff;padding:.5rem .5rem;font-size:13px!important;}
.login-view-website a{color:#fff;opacity:0.8;}
.login-view-website a:hover{opacity:1;}
.login-view-website a i{font-size:11px;}
.login-copyright{position:absolute;width:100%;left:0px;bottom:0px;text-align:center;padding:.5rem .5rem;font-size:13px!important;}
.show-password{cursor:pointer;width:45px;border-color:#ced4da!important;padding:.375rem;justify-content:center;}
.show-password.disabled{pointer-events:none;background-color:#e9ecef!important;}

/* Loader */
.loader-wrapper{z-index:99999999;position:fixed;top:0;left:0;bottom:0;right:0;width:100%;height:100%;background:#eee;overflow:hidden;text-align:center}
.loader-wrapper .loader{position:relative;top:calc(50% - 30px)}
.loader-wrapper .loader p{font-size:13px;margin-top:10px;font-weight:700;color:#444}
.loader-wrapper .loader .preloader{display:inline-block;position:relative;width:50px;height:50px;-webkit-animation:container-rotate 1568ms linear infinite;animation:container-rotate 1568ms linear infinite}
.loader-wrapper .loader .preloader .spinner-layer{position:absolute;width:100%;height:100%;border-color:#007bff;-ms-opacity:1;opacity:1;-webkit-animation:fill-unfill-rotate 5332ms cubic-bezier(.4,0,.2,1) infinite both;animation:fill-unfill-rotate 5332ms cubic-bezier(.4,0,.2,1) infinite both}
.loader-wrapper .loader .preloader .spinner-layer .circle-clipper{display:inline-block;position:relative;width:50%;height:100%;overflow:hidden;border-color:inherit}
.loader-wrapper .loader .preloader .spinner-layer .circle-clipper .circle{width:200%;height:100%;border-width:3px;border-style:solid;border-color:inherit;border-bottom-color:transparent!important;border-radius:50%;-webkit-animation:none;animation:none;position:absolute;top:0;right:0;bottom:0}
.loader-wrapper .loader .preloader .spinner-layer .circle-clipper.float-left .circle{left:0;border-right-color:transparent!important;-webkit-transform:rotate(129deg);transform:rotate(129deg);-webkit-animation:left-spin 1333ms cubic-bezier(.4,0,.2,1) infinite both;animation:left-spin 1333ms cubic-bezier(.4,0,.2,1) infinite both}
.loader-wrapper .loader .preloader .spinner-layer .circle-clipper.float-right .circle{left:-100%;border-left-color:transparent!important;-webkit-transform:rotate(-129deg);transform:rotate(-129deg);-webkit-animation:right-spin 1333ms cubic-bezier(.4,0,.2,1) infinite both;animation:right-spin 1333ms cubic-bezier(.4,0,.2,1) infinite both}
@-webkit-keyframes container-rotate{
	to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}
}
@keyframes container-rotate{
	to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}
}
@-webkit-keyframes fill-unfill-rotate{
	12.5%{-webkit-transform:rotate(135deg);transform:rotate(135deg)}
	25%{-webkit-transform:rotate(270deg);transform:rotate(270deg)}
	37.5%{-webkit-transform:rotate(405deg);transform:rotate(405deg)}
	50%{-webkit-transform:rotate(540deg);transform:rotate(540deg)}
	62.5%{-webkit-transform:rotate(675deg);transform:rotate(675deg)}
	75%{-webkit-transform:rotate(810deg);transform:rotate(810deg)}
	87.5%{-webkit-transform:rotate(945deg);transform:rotate(945deg)}
	to{-webkit-transform:rotate(1080deg);transform:rotate(1080deg)}
}
@keyframes fill-unfill-rotate{
	12.5%{-webkit-transform:rotate(135deg);transform:rotate(135deg)}
	25%{-webkit-transform:rotate(270deg);transform:rotate(270deg)}
	37.5%{-webkit-transform:rotate(405deg);transform:rotate(405deg)}
	50%{-webkit-transform:rotate(540deg);transform:rotate(540deg)}
	62.5%{-webkit-transform:rotate(675deg);transform:rotate(675deg)}
	75%{-webkit-transform:rotate(810deg);transform:rotate(810deg)}
	87.5%{-webkit-transform:rotate(945deg);transform:rotate(945deg)}
	to{-webkit-transform:rotate(1080deg);transform:rotate(1080deg)}
}
@-webkit-keyframes left-spin{
	from{-webkit-transform:rotate(130deg);transform:rotate(130deg)}
	50%{-webkit-transform:rotate(-5deg);transform:rotate(-5deg)}
	to{-webkit-transform:rotate(130deg);transform:rotate(130deg)}
}
@keyframes left-spin{
	from{-webkit-transform:rotate(130deg);transform:rotate(130deg)}
	50%{-webkit-transform:rotate(-5deg);transform:rotate(-5deg)}
	to{-webkit-transform:rotate(130deg);transform:rotate(130deg)}
}
@-webkit-keyframes right-spin{
	from{-webkit-transform:rotate(-130deg);transform:rotate(-130deg)}
	50%{-webkit-transform:rotate(5deg);transform:rotate(5deg)}
	to{-webkit-transform:rotate(-130deg);transform:rotate(-130deg)}
}
@keyframes right-spin{
	from{-webkit-transform:rotate(-130deg);transform:rotate(-130deg)}
	50%{-webkit-transform:rotate(5deg);transform:rotate(5deg)}
	to{-webkit-transform:rotate(-130deg);transform:rotate(-130deg)}
}

/* Loading */
.sk-chase{width:20.5px;height:20.5px;margin:auto;position:relative;animation:sk-chase 2.5s infinite linear both;}
.sk-chase-dot{width:100%;height:100%;position:absolute;left:0;top:0;animation:sk-chase-dot 2.0s infinite ease-in-out both;}
.sk-chase-dot:before{content:'';display:block;width:25%;height:25%;background-color:#fff;border-radius:100%;animation:sk-chase-dot-before 2.0s infinite ease-in-out both;}
.sk-chase-dot:nth-child(1){animation-delay:-1.1s;}
.sk-chase-dot:nth-child(2){animation-delay:-1.0s;}
.sk-chase-dot:nth-child(3){animation-delay:-0.9s;}
.sk-chase-dot:nth-child(4){animation-delay:-0.8s;}
.sk-chase-dot:nth-child(5){animation-delay:-0.7s;}
.sk-chase-dot:nth-child(6){animation-delay:-0.6s;}
.sk-chase-dot:nth-child(1):before{animation-delay:-1.1s;}
.sk-chase-dot:nth-child(2):before{animation-delay:-1.0s;}
.sk-chase-dot:nth-child(3):before{animation-delay:-0.9s;}
.sk-chase-dot:nth-child(4):before{animation-delay:-0.8s;}
.sk-chase-dot:nth-child(5):before{animation-delay:-0.7s;}
.sk-chase-dot:nth-child(6):before{animation-delay:-0.6s;}
@keyframes sk-chase{
	100%{transform:rotate(360deg);}
}
@keyframes sk-chase-dot{
	80%,100%{transform:rotate(360deg);}
}
@keyframes sk-chase-dot-before{
	50%{transform:scale(0.4);}
	100%,0%{transform:scale(1.0);}
}

/* Alert */
.my-alert.alert-primary{color:#004085;background-color:#cce5ff;border-color:#b8daff;}
.my-alert.alert-secondary{color:#383d41;background-color:#e2e3e5;border-color:#d6d8db;}
.my-alert.alert-warning{color:#856404;background-color:#fff3cd;border-color:#ffeeba;}
.my-alert.alert-dark{color:#1b1e21;background-color:#d6d8d9;border-color:#c6c8ca;}
.my-alert.alert-light{color:#818182;background-color:#fefefe;border-color:#fdfdfe;}
.my-alert.alert-danger{color:#721c24;background-color:#f8d7da;border-color:#f5c6cb;}
.my-alert.alert-success{color:#155724;background-color:#d4edda;border-color:#c3e6cb;}
.my-alert.alert-info{color:#0c5460;background-color:#d1ecf1;border-color:#bee5eb;}
.my-alert .close{outline:none;}

/* Info box */
.my-info-box{border-radius:0px;box-shadow:none;border:1px solid #dee2e6;}
.my-info-box .my-info-box-icon{border-radius:0px;}

/* General */
.w-clear:after{display:block;content:"";clear:both;}

/* Alert user login */
.alert-user-login{position:absolute;top:3.3rem;right:0.3rem;z-index:100;}

/* Sidebar */
.main-sidebar{background-color:#1c1f22;}
.sidebar-mini.sidebar-collapse .text-sm .brand-link .brand-image{margin-left:1.15rem;}
.nav-compact .nav-link > p > .right{top:0.7rem;}
.brand-link{padding:.25rem .5rem 1rem .5rem;width:100%;height:95px;display:-webkit-flex;display:-moz-flex;display:-ms-flex;display:-o-flex;display:flex;-ms-flex-align:center;align-items:center;justify-content:center;border-bottom:0px!important;text-align:center;background-image:url(../images/logo-border.png);background-position:bottom center;background-repeat:no-repeat;}
.brand-link .brand-image{float:none;height:auto!important;margin:0px!important;display:inline-block;max-height:inherit;max-width:100%;}
.text-sm .brand-link .brand-image{margin-left:.75rem;transition:margin-left 0.3s linear,opacity 0.3s ease,visibility 0.3s ease;}

/* Header */
.main-header .navbar-nav .nav-item .dropdown-menu{max-width:250px;}
.main-header .navbar-nav .nav-item .dropdown-menu .dropdown-item{padding:.25rem 0.75rem;}
.main-header .navbar-nav .nav-item .dropdown-menu .dropdown-item i{margin-right:.2rem;margin-top:0.2rem;text-align:center;width:1.5rem;vertical-align:text-bottom;}
.main-header .navbar-nav .nav-item .dropdown-menu .dropdown-divider{margin:.25rem 0;}
.main-header .navbar-nav .nav-item .dropdown-menu .dropdown-divider:last-child{display:none;}
.main-header .navbar-nav .nav-item.nav-item-hello span{max-width:120px;padding-right:10px;}
.main-header .navbar-nav .nav-item.nav-item-hello span.text-split{-webkit-line-clamp:1;}
.main-header .navbar-nav .nav-item .nav-link .badge{vertical-align:top;margin-top:-5px;margin-left:-4px;}

/* Table */
.table-responsive .table{min-width:1000px;}

/* Badge */
.badge{line-height:1.1;}

/* Watermark */
.watermark-position{background-color:#eeeeee;max-width:355px;height:370px;position:relative;}
.watermark-position label{margin-bottom:0px;position:absolute;width:15px;height:15px;cursor:pointer;}
.watermark-position label input{display:none;}
.watermark-position label img{position:absolute;z-index:1;max-width:50px;opacity:0.6;-webkit-transition:0.3s all;-moz-transition:0.3s all;-o-transition:0.3s all;transition:0.3s all;}
.watermark-position label img:hover,.watermark-position label img[src^="http"]{opacity:1;}
.watermark-position label:nth-child(1){top:15px;left:15px;}
.watermark-position label:nth-child(1) img{top:-10px;left:-10px;}
.watermark-position label:nth-child(2){top:15px;left:calc(50% - 15px/2);}
.watermark-position label:nth-child(2) img{top:-10px;left:50%;-moz-transform:translateX(-50%);-webkit-transform:translateX(-50%);-o-transform:translateX(-50%);-ms-transform:translateX(-50%);transform:translateX(-50%);}
.watermark-position label:nth-child(3){top:15px;right:15px;}
.watermark-position label:nth-child(3) img{top:-10px;right:-10px;}
.watermark-position label:nth-child(4){top:calc(50% - 15px/2);right:15px;}
.watermark-position label:nth-child(4) img{top:50%;right:-10px;-moz-transform:translateY(-50%);-webkit-transform:translateY(-50%);-o-transform:translateY(-50%);-ms-transform:translateY(-50%);transform:translateY(-50%);}
.watermark-position label:nth-child(5){bottom:15px;right:15px;}
.watermark-position label:nth-child(5) img{bottom:-10px;right:-10px;}
.watermark-position label:nth-child(6){bottom:15px;left:calc(50% - 15px/2);}
.watermark-position label:nth-child(6) img{bottom:-10px;left:50%;-moz-transform:translateX(-50%);-webkit-transform:translateX(-50%);-o-transform:translateX(-50%);-ms-transform:translateX(-50%);transform:translateX(-50%);}
.watermark-position label:nth-child(7){bottom:15px;left:15px;}
.watermark-position label:nth-child(7) img{bottom:-10px;left:-10px;}
.watermark-position label:nth-child(8){top:calc(50% - 15px/2);left:15px;}
.watermark-position label:nth-child(8) img{top:50%;left:-10px;-moz-transform:translateY(-50%);-webkit-transform:translateY(-50%);-o-transform:translateY(-50%);-ms-transform:translateY(-50%);transform:translateY(-50%);}
.watermark-position label:nth-child(9){top:calc(50% - 15px/2);left:calc(50% - 15px/2);}
.watermark-position label:nth-child(9) img{top:50%;left:50%;-moz-transform:translate(-50%,-50%);-webkit-transform:translate(-50%,-50%);-o-transform:translate(-50%,-50%);-ms-transform:translate(-50%,-50%);transform:translate(-50%,-50%);}

/* Apexcharts */
.form-filter-charts .select2-container{width:100%!important;}
#apexMixedChart{overflow:hidden;}
.apexcharts-menu-icon{display:none;}

/* Time statistic */
.form-time-static{display:-webkit-flex;display:-moz-flex;display:-ms-flex;display:-o-flex;display:flex;align-items:stretch;justify-content:space-between;}
.form-time-static input{width:calc(100% - 130px);margin-right:10px;}
.form-time-static button{width:130px;}

/* Confirm Js */
.jconfirm .jconfirm-box .jconfirm-buttons{float:none!important;text-align:center;}

/* Search */
.form-search .btn{margin-bottom:0px;}

/* Tool action */
.tool-action a{float:left;cursor:pointer;}

/* Card */
.card-footer.sticky-top{background-color:rgb(236,238,241);position:sticky;top:0px;z-index:11;}

/* Iamges */
.img-upload{max-width:300px;}
.img-preview{max-width:70px;max-height:55px;}

/* Photo upload zone */
.photoUpload-zone .photoUpload-detail{text-align:center;display:block;margin-bottom:1.5rem;}
.photoUpload-zone .photoUpload-detail img{max-width:100%;max-height:250px;}
.photoUpload-zone .photoUpload-file{cursor:pointer;background-color:#F5F5F5;outline:2px dashed #CDCDCD;outline-offset:0px;padding:2rem 0.75rem;margin-bottom:1rem;border-radius:0.25rem;font-weight:normal!important;display:-webkit-flex;display:-moz-flex;display:-ms-flex;display:-o-flex;display:flex;flex-direction:column;align-items:center;justify-content:center;-webkit-transition:outline-offset .15s ease-in-out,background-color .15s linear;transition:outline-offset .15s ease-in-out,background-color .15s linear;}
.photoUpload-zone .photoUpload-file.drag-over{outline-offset:-10px;background-color:#fff9f9;}
.photoUpload-zone .photoUpload-file input{display:none;}
.photoUpload-zone .photoUpload-file i:hover{opacity:0.9;}
.photoUpload-zone .photoUpload-file i{color:#6C7D8F;font-size:55px;margin-bottom:0.75rem;}
.photoUpload-zone .photoUpload-file p{margin:0;color:#767978;font-size:16px;}
.photoUpload-zone .photoUpload-file .photoUpload-drop{margin-bottom:0.5rem;}
.photoUpload-zone .photoUpload-file .photoUpload-or{font-size:14px;margin-bottom:0.75rem;}
.photoUpload-zone .photoUpload-file .photoUpload-choose{color:#fff;}
.photoUpload-zone .photoUpload-dimension{font-weight:700;}

/* Upload file */
.upload-file-label{cursor:pointer;width:100%;max-width:300px;}

/* Scrollbar */
.scroll-bar::-webkit-scrollbar{width:10px;}
.scroll-bar::-webkit-scrollbar-track{-webkit-border-radius:10px;border-radius:10px;background:#eeeeee;}
.scroll-bar::-webkit-scrollbar-thumb{-webkit-border-radius:10px;border-radius:10px;background:#999999;}
.scroll-bar::-webkit-scrollbar-thumb:window-inactive{background:#999999;}

/* Text Hide */
.text-split{overflow:hidden;text-overflow:ellipsis;white-space:normal;-webkit-box-orient:vertical;display:-webkit-box;-webkit-line-clamp:3;}

/* Color */
.color-preview{display:inline-block;width:100%;max-width:65px;height:35px;}

/* Checkbox */
.my-checkbox{width:17px;height:17px;min-height:inherit;padding:0px;margin:auto;}
.my-checkbox .custom-control-input{cursor:pointer;z-index:1;width:100%;height:100%;}
.my-checkbox .custom-control-label{position:absolute;top:0px;left:0px;width:100%;height:100%;}
.my-checkbox .custom-control-label::before,.my-checkbox .custom-control-label::after{top:0px;left:0px;width:100%;height:100%;}

/* Input */
.form-control-mini{width:50px;padding:0.25rem;text-align:center;line-height:normal;font-size:14px;}
.form-control-mini::-webkit-outer-spin-button,.form-control-mini::-webkit-inner-spin-button{-webkit-appearance:none;-moz-appearance:none;appearance:none;margin:0;}
.form-control.no-validate:valid{border-color:#ced4da;background:none;padding:.375rem .75rem;}

/* Input file */
.my-custom-file input{cursor:pointer;}
.my-custom-file label{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;padding-right:77px;padding-top:0.5rem;}
.my-custom-file label::after{padding-top:0.5rem;}

/* Order */
.price-cart-detail span{display:block}
.price-new-cart-detail{color:red;}
.price-old-cart-detail{text-decoration:line-through}
.title-money-cart-detail{text-align:right!important;padding-right:10px!important;text-transform:capitalize;font-weight:700;font-size:13px}
.cast-money-cart-detail{color:red;font-weight:700;font-size:15px;text-align:right!important;padding-right:15px!important}

/* Seo */
.label-seo{display:-webkit-flex;display:-moz-flex;display:-ms-flex;display:-o-flex;display:flex;align-items:center;justify-content:space-between;}
.label-seo-preview{text-transform:uppercase;font-size:1rem;margin-bottom:0.75rem;}
.seo-preview{max-width:700px;}
.slug-seo-preview{color:#5f6368;margin-bottom:2px;}
.slug-seo-preview strong{font-weight:normal;}
.title-seo-preview{color:#0980ff;font-size:20px;margin-bottom:2px;}
.title-seo-preview.text-split{-webkit-line-clamp:1;}
.description-seo-preview{color:#4d5156;margin-bottom:0px;}
.description-seo-preview.text-split{-webkit-line-clamp:2;}

/* Filer */
.label-filer-gallery{word-break:break-word;}
.alert-sort-filer{display:none;}
.jFiler-input-dragDrop{width:100%;}
.jFiler-items-grid.row{margin-left:-7.5px;}
.jFiler-items-grid.scroll-bar{max-height:560px;overflow-x:hidden;overflow-y:scroll;}
.jFiler-items-grid .jFiler-item .jFiler-item-container{margin:0px;margin-bottom:1rem;padding:5px;box-shadow:none;}
.jFiler-items-grid .jFiler-item .jFiler-item-container .jFiler-item-assets{margin-top:0px;padding:5px 0px;}
.jFiler-items-grid .jFiler-item .jFiler-item-container .jFiler-item-assets .pull-left{float:none;}
.jFiler-items-grid .jFiler-item .jFiler-item-container .jFiler-item-assets .pull-left li{display:block;}
.jFiler-items-grid .jFiler-item .jFiler-item-container .jFiler-item-assets .pull-left li .jFiler-jProgressBar{width:100%;margin:0px;}
.jFiler-items-grid .jFiler-item .jFiler-item-container .jFiler-item-assets .pull-left li .jFiler-jProgressBar .bar{float:none;}
.jFiler-items-grid .jFiler-item .jFiler-item-container .jFiler-item-assets .pull-right{float:none;text-align:center;}
.jFiler-items-grid .jFiler-item .jFiler-item-container .jFiler-item-thumb{width:100%;height:auto;min-height:inherit;}
.jFiler-items-grid .jFiler-item .jFiler-item-container .jFiler-item-thumb .jFiler-item-thumb-image{max-height:150px;position:relative;}
.jFiler-items-grid .jFiler-item .jFiler-item-container .jFiler-item-thumb .jFiler-item-thumb-image img{max-width:100%;width:100%;}
.jFiler-items-grid .jFiler-item .jFiler-item-container .jFiler-item-thumb .jFiler-item-thumb-image i{position:absolute;top:calc(50% - 25px/2);left:calc(50% - 25px/2);color:#ffc107;width:25px;height:25px;font-size:25px;text-align:center;line-height:25px;display:none;}
.jFiler-items-grid .jFiler-item .jFiler-item-container .jFiler-item-thumb .jFiler-item-info{padding:0px 5px;}
.jFiler-items-grid .jFiler-item .jFiler-item-container .jFiler-item-thumb .jFiler-item-info .jFiler-item-title,.jFiler-items-grid .jFiler-item .jFiler-item-container .jFiler-item-info .jFiler-item-title b{font-weight:normal;}
.jFiler-items-grid .jFiler-item.my-jFiler-item.moved{cursor:pointer;}
.jFiler-items-grid .jFiler-item.my-jFiler-item.moved.ghostclass .jFiler-item-container{background:#fddf83;opacity:0.85;}
.jFiler-items-grid .jFiler-item.my-jFiler-item.selected .jFiler-item-container{border-color:#ffc107;opacity:0.85;}
.jFiler-items-grid .jFiler-item.my-jFiler-item.selected .jFiler-item-container:before{content:"";position:absolute;width:35px;height:35px;z-index:1;background-color:#ffc107;top:calc(50% - 35px/2);left:calc(50% - 35px/2);border-radius:100%;}
.jFiler-items-grid .jFiler-item.my-jFiler-item.selected .jFiler-item-container:after{content:"";position:absolute;left:calc(50% - 9px/2);top:calc(50% - 25px/2);width:10px;height:20px;z-index:2;border:solid #fff;border-width:0 3px 3px 0;-webkit-transform:rotate(45deg);-ms-transform:rotate(45deg);transform:rotate(45deg);}
.jFiler-items-grid .jFiler-item.my-jFiler-item.selected .jFiler-item-container .jFiler-item-inner{opacity:0.7;}
.jFiler-items-grid .jFiler-item.my-jFiler-item.moved:not(.selected) .jFiler-item-container .jFiler-item-thumb .jFiler-item-thumb-image img{-ms-filter:brightness(70%);-webkit-filter:brightness(70%);-moz-filter:brightness(70%);-o-filter:brightness(70%);filter:brightness(70%);}
.jFiler-items-grid .jFiler-item.my-jFiler-item.moved:not(.selected) .jFiler-item-container .jFiler-item-thumb .jFiler-item-thumb-image i{display:block;}

/* Sumoselect */
.SumoSelect .select-all{height:auto!important;padding:8px 0px 8px 35px!important;}
.SumoSelect label{margin-bottom:0px;font-weight:normal!important;display:block;}
.SumoSelect > .CaptionCont{border:1px solid #ced4da;padding:.46875rem .75rem;border-radius:4px;}
.SumoSelect > .CaptionCont > span.placeholder{color:#444444;font-style:unset;}
.SumoSelect > .CaptionCont > label > i{background-size:10px;opacity:.6;}
.SumoSelect > .optWrapper.multiple > .options li.opt span i,.SumoSelect .select-all > span i{top:1px;}
.SumoSelect:focus > .CaptionCont,.SumoSelect:hover > .CaptionCont,.SumoSelect.open > .CaptionCont{box-shadow:0 0 2px #80bdff;border-color:#80bdff;}
.SumoSelect.open .search-txt{padding:.46875rem .75rem;}
.SumoSelect.open > .optWrapper{top:40px;}
.form-group-category .SumoSelect{width:100%;}

/* Select */
.was-validated select.form-control:invalid{background-position:right calc(.375em + 0.6rem) center;}
.was-validated select.form-control:valid{background-position:right calc(.375em + 0.6rem) center;}
select.form-control option:disabled{color:#b8babb;font-style:italic;}

/* Select2 */
.form-group-category .select2-container{width:100%!important;}
.select2-container--default .select2-selection--single{height:calc(2.25rem + 2px)!important;}
.select2-container--default .select2-selection--single .select2-selection__rendered{margin-top:-3px!important;}
.select2-container--default .select2-selection--single .select2-selection__arrow b{margin-top:3px!important;}

/* Pagination */
.pagination{flex-wrap:wrap;}

/* Responsive */
@media(max-width:1201px){
	.watermark-position{max-width:300px;height:300px;}
}
@media(max-width:641px){
	.main-header .navbar-nav .nav-item .nav-link{padding:0.35rem 0.55rem!important;}
}
@media(max-width:569px){
	.form-search{width:100%;margin-left:0px!important;margin-top:0.75rem!important;}
}
@media(max-width:469px){
	.card-body{padding:0.75rem;}
	.card-title-order{width:100%;margin-bottom:0.75rem;}
	.btn-export-excel{margin-left:0px!important;}
	.card-footer{padding:.75rem;}
	.card-footer .btn{margin-bottom:0.25rem;}
}
@media(max-width:401px){
	.main-header .navbar-nav .nav-item.nav-item-hello span,.label-filer{display:none;}
}