.comment-title{font-size:18px;}
.comment-show{display:block!important;}
.comment-point{font-size:47px;color:#fe302e;}
.comment-star{font-size:0;position:relative;display:inline-block;vertical-align:top;margin-bottom:0.5rem;}
.comment-star span{display:block;position:absolute;top:0;left:0px;bottom:0;z-index:1;overflow:hidden;text-align:left;white-space:nowrap;}
.comment-star i{display:inline-block;margin:.1rem;color:#b8b8b8;text-rendering:auto;font-size:20px;}
.comment-star i:nth-child(1){margin-left:0;}
.comment-star span i{color:#ffc120;}
.comment-progress{width:100%;margin-bottom:0.375rem;display:flex;align-items:center;justify-content:space-between;}
.comment-progress:last-child{margin-bottom:0px;}
.comment-progress .progress{height:12px;margin-right:10px;margin-left:5px;width:calc(100% - 90px);border-radius:10px;}
.comment-progress .progress-bar#has-rate{background-color:#23b445;}
.comment-progress .progress-num{font-size:15px;width:25px;text-align:center;color:#787878;}
.comment-progress .progress-total{font-weight:600;text-align:left;color:#787878;width:50px;}
.carousel-comment-media .carousel-control a{opacity:1;width:45px;height:45px;top:calc(50% - 45px/2);bottom:inherit;}
.carousel-comment-media .carousel-control a span{display:block;cursor:pointer;color:#222222;width:45px;height:45px;padding:5px;text-align:center;display:flex;align-items:center;justify-content:center;background-color:#ffffff;box-shadow:0 1px 12px 0 rgb(0 0 0 / 12%);border-radius:100%;-webkit-transition:0.3s ease-out;-moz-transition:0.3s ease-out;-o-transition:0.3s ease-out;transition:0.3s ease-out;}
.carousel-comment-media .carousel-control a.carousel-control-prev{left:20px;}
.carousel-comment-media .carousel-control a.carousel-control-prev span{padding-right:9px;}
.carousel-comment-media .carousel-control a.carousel-control-next{right:20px;}
.carousel-comment-media .carousel-control a.carousel-control-next span{padding-left:8px;}
.carousel-comment-media .carousel-indicators{position:static;margin:0px 0px 0.75rem 0px;text-align:left;display:block;}
.carousel-comment-media .carousel-indicators li{width:auto;max-width:60px;height:auto;text-indent:inherit;flex:none;margin:0px;border:2px solid transparent;opacity:1;cursor:zoom-in;}
.carousel-comment-media .carousel-indicators li.active{border-color:#ffc120;cursor:zoom-out;-moz-transform:scale(1.05);-webkit-transform:scale(1.05);-o-transform:scale(1.05);-ms-transform:scale(1.05);transform:scale(1.05);}
.carousel-comment-media .carousel-inner{max-width:550px;box-shadow:0 1px 12px 0 rgb(0 0 0 / 12%);}
.carousel-comment-media .carousel-inner .carousel-lists .carousel-comment-media-item-video #file-video{max-width:100%;width:100%;height:500px;vertical-align:top;display:inline-block;background-color:#000000;}
#form-reply{display:none;}
.comment-item{display:flex;align-items:flex-start;justify-content:flex-start;margin-bottom:3rem;}
.comment-item-poster{width:200px;margin-right:30px;text-align:center;}
.comment-item-letter{border-radius:50%;background-color:#d3d2d3;color:#919090;width:65px;height:65px;text-transform:uppercase;display:inline-block;vertical-align:top;line-height:67px;font-size:18px;font-weight:700;margin-bottom:0.5rem;}
.comment-item-name{margin-bottom:0.25rem;text-transform:capitalize;}
.comment-item-posttime{font-size:12px;color:#999999;}
.comment-item-information{width:calc(100% - 230px);}
.comment-item-star{float:left;margin-right:10px;}
.comment-item-star i{font-size:17px;}
.comment-item-title{font-size:15px;font-weight:600;}
.comment-replies{position:relative;background:#f8f9fa;border:1px solid #dfdfdf;padding:1.75rem 1.5rem;}
.comment-replies:after,.comment-replies:before{content:'';position:absolute;}
.comment-replies:before{z-index:1;top:-13px;left:10px;border-left:10px solid transparent;border-right:10px solid transparent;border-bottom:13px solid #f8f9fa;}
.comment-replies:after{top:-14px;left:9px;border-left:11px solid transparent;border-right:11px solid transparent;border-bottom:14px solid #dfdfdf;}
.comment-replies-item{margin-bottom:1.5rem;display:flex;align-items:flex-start;justify-content:flex-start;}
.comment-replies-item:last-child{margin-bottom:0px;}
.comment-replies-letter{width:45px;height:45px;margin-right:10px;line-height:45px;padding-left:1px;font-size:14px;border-radius:50%;background-color:#e5e3e5;color:#919090;text-transform:uppercase;display:inline-block;vertical-align:top;font-weight:600;text-align:center;margin-top:0.125rem;}
.comment-replies-letter.admin{position:relative;background-color:#189eff;text-indent:9999px;overflow:hidden;}
.comment-replies-letter.admin:before{content:'QTV';text-indent:0px;color:#ffffff;font-weight:600;text-transform:uppercase;font-size:13px;position:absolute;top:50%;left:50%;-moz-transform:translate(-50%,-50%);-webkit-transform:translate(-50%,-50%);-o-transform:translate(-50%,-50%);-ms-transform:translate(-50%,-50%);transform:translate(-50%,-50%);}
.comment-replies-info{width:calc(100% - 55px);}
.comment-replies-name{font-weight:600;margin-bottom:0.125rem;}
.comment-replies [class*=btn-load-more-]{border:0px;outline:none;padding:0px;background:transparent;}
.comment-media-play{z-index:1;position:absolute;top:50%;left:50%;transform:translateY(-50%) translateX(-50%);}
.comment-media-play svg{-webkit-box-shadow:1px 1px 15px rgb(0 0 0 / 30%);box-shadow:1px 1px 15px rgb(0 0 0 / 30%);border-radius:50%;background-color:rgb(255 255 255 / 15%);}
.comment-media-play svg .comment-media-play-stroke-dotted{opacity:0;stroke-dasharray:4,5;stroke-width:1px;transform-origin:50% 50%;animation:spin 4s infinite linear;transition:opacity 1s ease,stroke-width 1s ease;}
.comment-media-play svg .comment-media-play-stroke-solid{stroke-dashoffset:0;stroke-dashArray:300;stroke-width:4px;transition:stroke-dashoffset 1s ease,opacity 1s ease;}
.comment-media-play svg .comment-media-play-icon{transform-origin:50% 50%;transition:transform 200ms ease-out;}
.comment-media-play:hover svg .comment-media-play-stroke-dotted{stroke-width:4px;opacity:1;}
.comment-media-play:hover svg .comment-media-play-stroke-solid{opacity:0;stroke-dashoffset:300;}
.comment-media-play:hover svg .comment-media-play-icon{transform:scale(1.05);}
@keyframes spin{
	to{transform:rotate(360deg);}
}