!function(e){"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof exports?module.exports=e:e(jQuery)}(function(d){var c,m,e=["wheel","mousewheel","DOMMouseScroll","MozMousePixelScroll"],t="onwheel"in document||9<=document.documentMode?["wheel"]:["mousewheel","DomMouseScroll","MozMousePixelScroll"],g=Array.prototype.slice;if(d.event.fixHooks)for(var i=e.length;i;)d.event.fixHooks[e[--i]]=d.event.mouseHooks;var w=d.event.special.mousewheel={version:"3.1.12",setup:function(){if(this.addEventListener)for(var e=t.length;e;)this.addEventListener(t[--e],n,!1);else this.onmousewheel=n;d.data(this,"mousewheel-line-height",w.getLineHeight(this)),d.data(this,"mousewheel-page-height",w.getPageHeight(this))},teardown:function(){if(this.removeEventListener)for(var e=t.length;e;)this.removeEventListener(t[--e],n,!1);else this.onmousewheel=null;d.removeData(this,"mousewheel-line-height"),d.removeData(this,"mousewheel-page-height")},getLineHeight:function(e){var t=d(e),i=t["offsetParent"in d.fn?"offsetParent":"parent"]();return i.length||(i=d("body")),parseInt(i.css("fontSize"),10)||parseInt(t.css("fontSize"),10)||16},getPageHeight:function(e){return d(e).height()},settings:{adjustOldDeltas:!0,normalizeOffset:!0}};function n(e){var t,i=e||window.event,n=g.call(arguments,1),o=0,l=0,s=0,a=0,h=0;if((e=d.event.fix(i)).type="mousewheel","detail"in i&&(s=-1*i.detail),"wheelDelta"in i&&(s=i.wheelDelta),"wheelDeltaY"in i&&(s=i.wheelDeltaY),"wheelDeltaX"in i&&(l=-1*i.wheelDeltaX),"axis"in i&&i.axis===i.HORIZONTAL_AXIS&&(l=-1*s,s=0),o=0===s?l:s,"deltaY"in i&&(o=s=-1*i.deltaY),"deltaX"in i&&(l=i.deltaX,0===s&&(o=-1*l)),0!==s||0!==l){if(1===i.deltaMode){var r=d.data(this,"mousewheel-line-height");o*=r,s*=r,l*=r}else if(2===i.deltaMode){var u=d.data(this,"mousewheel-page-height");o*=u,s*=u,l*=u}if(t=Math.max(Math.abs(s),Math.abs(l)),(!m||t<m)&&p(i,m=t)&&(m/=40),p(i,t)&&(o/=40,l/=40,s/=40),o=Math[1<=o?"floor":"ceil"](o/m),l=Math[1<=l?"floor":"ceil"](l/m),s=Math[1<=s?"floor":"ceil"](s/m),w.settings.normalizeOffset&&this.getBoundingClientRect){var f=this.getBoundingClientRect();a=e.clientX-f.left,h=e.clientY-f.top}return e.deltaX=l,e.deltaY=s,e.deltaFactor=m,e.offsetX=a,e.offsetY=h,e.deltaMode=0,n.unshift(e,o,l,s),c&&clearTimeout(c),c=setTimeout(v,200),(d.event.dispatch||d.event.handle).apply(this,n)}}function v(){m=null}function p(e,t){return w.settings.adjustOldDeltas&&"mousewheel"===e.type&&t%120==0}d.fn.extend({mousewheel:function(e){return e?this.bind("mousewheel",e):this.trigger("mousewheel")},unmousewheel:function(e){return this.unbind("mousewheel",e)}})});