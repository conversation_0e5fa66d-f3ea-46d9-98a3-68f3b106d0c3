!function(c){"use strict";"undefined"==typeof HoldOn&&(c.HoldOn=function(){if("undefined"==typeof jQuery)throw new Error("HoldOn.js requires jQuery");var c={open:function(c){$("#holdon-overlay").remove();var i="sk-rect",s="",d="";switch(c&&(c.hasOwnProperty("theme")&&(i=c.theme),c.hasOwnProperty("message")&&(d=c.message)),i){case"custom":s='<div style="text-align: center;">'+c.content+"</div>";break;case"sk-dot":s='<div class="sk-dot"> <div class="sk-dot1"></div> <div class="sk-dot2"></div> </div>';break;case"sk-rect":s='<div class="sk-rect"> <div class="rect1"></div> <div class="rect2"></div> <div class="rect3"></div> <div class="rect4"></div> <div class="rect5"></div> </div>';break;case"sk-cube":s='<div class="sk-cube"> <div class="sk-cube1"></div> <div class="sk-cube2"></div> </div>';break;case"sk-bounce":s='<div class="sk-bounce"> <div class="bounce1"></div> <div class="bounce2"></div> <div class="bounce3"></div> </div>';break;case"sk-circle":s='<div class="sk-circle"> <div class="sk-circle1 sk-child"></div> <div class="sk-circle2 sk-child"></div> <div class="sk-circle3 sk-child"></div> <div class="sk-circle4 sk-child"></div> <div class="sk-circle5 sk-child"></div> <div class="sk-circle6 sk-child"></div> <div class="sk-circle7 sk-child"></div> <div class="sk-circle8 sk-child"></div> <div class="sk-circle9 sk-child"></div> <div class="sk-circle10 sk-child"></div> <div class="sk-circle11 sk-child"></div> <div class="sk-circle12 sk-child"></div> </div>';break;case"sk-cube-grid":s='<div class="sk-cube-grid"> <div class="sk-cube-child sk-cube-grid1"></div> <div class="sk-cube-child sk-cube-grid2"></div> <div class="sk-cube-child sk-cube-grid3"></div> <div class="sk-cube-child sk-cube-grid4"></div> <div class="sk-cube-child sk-cube-grid5"></div> <div class="sk-cube-child sk-cube-grid6"></div> <div class="sk-cube-child sk-cube-grid7"></div> <div class="sk-cube-child sk-cube-grid8"></div> <div class="sk-cube-child sk-cube-grid9"></div> </div>';break;case"sk-folding-cube":s='<div class="sk-folding-cube"> <div class="sk-cubechild1 sk-cube-parent"></div> <div class="sk-cubechild2 sk-cube-parent"></div> <div class="sk-cubechild4 sk-cube-parent"></div> <div class="sk-cubechild3 sk-cube-parent"></div> </div>';break;case"sk-fading-circle":s='<div class="sk-fading-circle"> <div class="sk-fading-circle1 sk-circle-child"></div> <div class="sk-fading-circle2 sk-circle-child"></div> <div class="sk-fading-circle3 sk-circle-child"></div> <div class="sk-fading-circle4 sk-circle-child"></div> <div class="sk-fading-circle5 sk-circle-child"></div> <div class="sk-fading-circle6 sk-circle-child"></div> <div class="sk-fading-circle7 sk-circle-child"></div> <div class="sk-fading-circle8 sk-circle-child"></div> <div class="sk-fading-circle9 sk-circle-child"></div> <div class="sk-fading-circle10 sk-circle-child"></div> <div class="sk-fading-circle11 sk-circle-child"></div> <div class="sk-fading-circle12 sk-circle-child"></div> </div>';break;default:s='<div class="sk-rect"> <div class="rect1"></div> <div class="rect2"></div> <div class="rect3"></div> <div class="rect4"></div> <div class="rect5"></div> </div>',console.warn(i+" doesn't exist for HoldOn.js")}$('<div id="holdon-overlay" style="display: none;">\n                                    <div id="holdon-content-container">\n                                        <div id="holdon-content">'+s+'</div>\n                                        <div id="holdon-message">'+d+"</div>\n                                    </div>\n                                </div>").appendTo("body").fadeIn(300),c&&(c.backgroundColor&&$("#holdon-overlay").css("backgroundColor",c.backgroundColor),c.backgroundColor&&$("#holdon-message").css("color",c.textColor))},close:function(){$("#holdon-overlay").fadeOut(300,function(){$(this).remove()})}};return c}())}(window);