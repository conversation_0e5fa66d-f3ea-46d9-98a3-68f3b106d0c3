<?php
// This file was auto-generated from sdk-root/src/data/iotsitewise/2019-12-02/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2019-12-02', 'endpointPrefix' => 'iotsitewise', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'AWS IoT SiteWise', 'serviceId' => 'IoTSiteWise', 'signatureVersion' => 'v4', 'signingName' => 'iotsitewise', 'uid' => 'iotsitewise-2019-12-02', ], 'operations' => [ 'AssociateAssets' => [ 'name' => 'AssociateAssets', 'http' => [ 'method' => 'POST', 'requestUri' => '/assets/{assetId}/associate', ], 'input' => [ 'shape' => 'AssociateAssetsRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'AssociateTimeSeriesToAssetProperty' => [ 'name' => 'AssociateTimeSeriesToAssetProperty', 'http' => [ 'method' => 'POST', 'requestUri' => '/timeseries/associate/', ], 'input' => [ 'shape' => 'AssociateTimeSeriesToAssetPropertyRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'BatchAssociateProjectAssets' => [ 'name' => 'BatchAssociateProjectAssets', 'http' => [ 'method' => 'POST', 'requestUri' => '/projects/{projectId}/assets/associate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchAssociateProjectAssetsRequest', ], 'output' => [ 'shape' => 'BatchAssociateProjectAssetsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'BatchDisassociateProjectAssets' => [ 'name' => 'BatchDisassociateProjectAssets', 'http' => [ 'method' => 'POST', 'requestUri' => '/projects/{projectId}/assets/disassociate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchDisassociateProjectAssetsRequest', ], 'output' => [ 'shape' => 'BatchDisassociateProjectAssetsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'BatchPutAssetPropertyValue' => [ 'name' => 'BatchPutAssetPropertyValue', 'http' => [ 'method' => 'POST', 'requestUri' => '/properties', ], 'input' => [ 'shape' => 'BatchPutAssetPropertyValueRequest', ], 'output' => [ 'shape' => 'BatchPutAssetPropertyValueResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'data.', ], ], 'CreateAccessPolicy' => [ 'name' => 'CreateAccessPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/access-policies', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateAccessPolicyRequest', ], 'output' => [ 'shape' => 'CreateAccessPolicyResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'CreateAsset' => [ 'name' => 'CreateAsset', 'http' => [ 'method' => 'POST', 'requestUri' => '/assets', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateAssetRequest', ], 'output' => [ 'shape' => 'CreateAssetResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'CreateAssetModel' => [ 'name' => 'CreateAssetModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/asset-models', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateAssetModelRequest', ], 'output' => [ 'shape' => 'CreateAssetModelResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'CreateDashboard' => [ 'name' => 'CreateDashboard', 'http' => [ 'method' => 'POST', 'requestUri' => '/dashboards', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateDashboardRequest', ], 'output' => [ 'shape' => 'CreateDashboardResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'CreateGateway' => [ 'name' => 'CreateGateway', 'http' => [ 'method' => 'POST', 'requestUri' => '/********/gateways', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateGatewayRequest', ], 'output' => [ 'shape' => 'CreateGatewayResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'CreatePortal' => [ 'name' => 'CreatePortal', 'http' => [ 'method' => 'POST', 'requestUri' => '/portals', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreatePortalRequest', ], 'output' => [ 'shape' => 'CreatePortalResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'CreateProject' => [ 'name' => 'CreateProject', 'http' => [ 'method' => 'POST', 'requestUri' => '/projects', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateProjectRequest', ], 'output' => [ 'shape' => 'CreateProjectResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'DeleteAccessPolicy' => [ 'name' => 'DeleteAccessPolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/access-policies/{accessPolicyId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAccessPolicyRequest', ], 'output' => [ 'shape' => 'DeleteAccessPolicyResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'DeleteAsset' => [ 'name' => 'DeleteAsset', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/assets/{assetId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteAssetRequest', ], 'output' => [ 'shape' => 'DeleteAssetResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DeleteAssetModel' => [ 'name' => 'DeleteAssetModel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/asset-models/{assetModelId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteAssetModelRequest', ], 'output' => [ 'shape' => 'DeleteAssetModelResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DeleteDashboard' => [ 'name' => 'DeleteDashboard', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/dashboards/{dashboardId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteDashboardRequest', ], 'output' => [ 'shape' => 'DeleteDashboardResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'DeleteGateway' => [ 'name' => 'DeleteGateway', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/********/gateways/{gatewayId}', ], 'input' => [ 'shape' => 'DeleteGatewayRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DeletePortal' => [ 'name' => 'DeletePortal', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/portals/{portalId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeletePortalRequest', ], 'output' => [ 'shape' => 'DeletePortalResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'DeleteProject' => [ 'name' => 'DeleteProject', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/projects/{projectId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteProjectRequest', ], 'output' => [ 'shape' => 'DeleteProjectResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'DeleteTimeSeries' => [ 'name' => 'DeleteTimeSeries', 'http' => [ 'method' => 'POST', 'requestUri' => '/timeseries/delete/', ], 'input' => [ 'shape' => 'DeleteTimeSeriesRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DescribeAccessPolicy' => [ 'name' => 'DescribeAccessPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/access-policies/{accessPolicyId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeAccessPolicyRequest', ], 'output' => [ 'shape' => 'DescribeAccessPolicyResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'DescribeAsset' => [ 'name' => 'DescribeAsset', 'http' => [ 'method' => 'GET', 'requestUri' => '/assets/{assetId}', ], 'input' => [ 'shape' => 'DescribeAssetRequest', ], 'output' => [ 'shape' => 'DescribeAssetResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DescribeAssetModel' => [ 'name' => 'DescribeAssetModel', 'http' => [ 'method' => 'GET', 'requestUri' => '/asset-models/{assetModelId}', ], 'input' => [ 'shape' => 'DescribeAssetModelRequest', ], 'output' => [ 'shape' => 'DescribeAssetModelResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DescribeAssetProperty' => [ 'name' => 'DescribeAssetProperty', 'http' => [ 'method' => 'GET', 'requestUri' => '/assets/{assetId}/properties/{propertyId}', ], 'input' => [ 'shape' => 'DescribeAssetPropertyRequest', ], 'output' => [ 'shape' => 'DescribeAssetPropertyResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DescribeDashboard' => [ 'name' => 'DescribeDashboard', 'http' => [ 'method' => 'GET', 'requestUri' => '/dashboards/{dashboardId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeDashboardRequest', ], 'output' => [ 'shape' => 'DescribeDashboardResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'DescribeDefaultEncryptionConfiguration' => [ 'name' => 'DescribeDefaultEncryptionConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/configuration/account/encryption', ], 'input' => [ 'shape' => 'DescribeDefaultEncryptionConfigurationRequest', ], 'output' => [ 'shape' => 'DescribeDefaultEncryptionConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DescribeGateway' => [ 'name' => 'DescribeGateway', 'http' => [ 'method' => 'GET', 'requestUri' => '/********/gateways/{gatewayId}', ], 'input' => [ 'shape' => 'DescribeGatewayRequest', ], 'output' => [ 'shape' => 'DescribeGatewayResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DescribeGatewayCapabilityConfiguration' => [ 'name' => 'DescribeGatewayCapabilityConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/********/gateways/{gatewayId}/capability/{capabilityNamespace}', ], 'input' => [ 'shape' => 'DescribeGatewayCapabilityConfigurationRequest', ], 'output' => [ 'shape' => 'DescribeGatewayCapabilityConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DescribeLoggingOptions' => [ 'name' => 'DescribeLoggingOptions', 'http' => [ 'method' => 'GET', 'requestUri' => '/logging', ], 'input' => [ 'shape' => 'DescribeLoggingOptionsRequest', ], 'output' => [ 'shape' => 'DescribeLoggingOptionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DescribePortal' => [ 'name' => 'DescribePortal', 'http' => [ 'method' => 'GET', 'requestUri' => '/portals/{portalId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribePortalRequest', ], 'output' => [ 'shape' => 'DescribePortalResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'DescribeProject' => [ 'name' => 'DescribeProject', 'http' => [ 'method' => 'GET', 'requestUri' => '/projects/{projectId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeProjectRequest', ], 'output' => [ 'shape' => 'DescribeProjectResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'DescribeStorageConfiguration' => [ 'name' => 'DescribeStorageConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/configuration/account/storage', ], 'input' => [ 'shape' => 'DescribeStorageConfigurationRequest', ], 'output' => [ 'shape' => 'DescribeStorageConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DescribeTimeSeries' => [ 'name' => 'DescribeTimeSeries', 'http' => [ 'method' => 'GET', 'requestUri' => '/timeseries/describe/', ], 'input' => [ 'shape' => 'DescribeTimeSeriesRequest', ], 'output' => [ 'shape' => 'DescribeTimeSeriesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DisassociateAssets' => [ 'name' => 'DisassociateAssets', 'http' => [ 'method' => 'POST', 'requestUri' => '/assets/{assetId}/disassociate', ], 'input' => [ 'shape' => 'DisassociateAssetsRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DisassociateTimeSeriesFromAssetProperty' => [ 'name' => 'DisassociateTimeSeriesFromAssetProperty', 'http' => [ 'method' => 'POST', 'requestUri' => '/timeseries/disassociate/', ], 'input' => [ 'shape' => 'DisassociateTimeSeriesFromAssetPropertyRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'GetAssetPropertyAggregates' => [ 'name' => 'GetAssetPropertyAggregates', 'http' => [ 'method' => 'GET', 'requestUri' => '/properties/aggregates', ], 'input' => [ 'shape' => 'GetAssetPropertyAggregatesRequest', ], 'output' => [ 'shape' => 'GetAssetPropertyAggregatesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'endpoint' => [ 'hostPrefix' => 'data.', ], ], 'GetAssetPropertyValue' => [ 'name' => 'GetAssetPropertyValue', 'http' => [ 'method' => 'GET', 'requestUri' => '/properties/latest', ], 'input' => [ 'shape' => 'GetAssetPropertyValueRequest', ], 'output' => [ 'shape' => 'GetAssetPropertyValueResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'endpoint' => [ 'hostPrefix' => 'data.', ], ], 'GetAssetPropertyValueHistory' => [ 'name' => 'GetAssetPropertyValueHistory', 'http' => [ 'method' => 'GET', 'requestUri' => '/properties/history', ], 'input' => [ 'shape' => 'GetAssetPropertyValueHistoryRequest', ], 'output' => [ 'shape' => 'GetAssetPropertyValueHistoryResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'endpoint' => [ 'hostPrefix' => 'data.', ], ], 'GetInterpolatedAssetPropertyValues' => [ 'name' => 'GetInterpolatedAssetPropertyValues', 'http' => [ 'method' => 'GET', 'requestUri' => '/properties/interpolated', ], 'input' => [ 'shape' => 'GetInterpolatedAssetPropertyValuesRequest', ], 'output' => [ 'shape' => 'GetInterpolatedAssetPropertyValuesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceUnavailableException', ], ], 'endpoint' => [ 'hostPrefix' => 'data.', ], ], 'ListAccessPolicies' => [ 'name' => 'ListAccessPolicies', 'http' => [ 'method' => 'GET', 'requestUri' => '/access-policies', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAccessPoliciesRequest', ], 'output' => [ 'shape' => 'ListAccessPoliciesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'ListAssetModels' => [ 'name' => 'ListAssetModels', 'http' => [ 'method' => 'GET', 'requestUri' => '/asset-models', ], 'input' => [ 'shape' => 'ListAssetModelsRequest', ], 'output' => [ 'shape' => 'ListAssetModelsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListAssetRelationships' => [ 'name' => 'ListAssetRelationships', 'http' => [ 'method' => 'GET', 'requestUri' => '/assets/{assetId}/assetRelationships', ], 'input' => [ 'shape' => 'ListAssetRelationshipsRequest', ], 'output' => [ 'shape' => 'ListAssetRelationshipsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListAssets' => [ 'name' => 'ListAssets', 'http' => [ 'method' => 'GET', 'requestUri' => '/assets', ], 'input' => [ 'shape' => 'ListAssetsRequest', ], 'output' => [ 'shape' => 'ListAssetsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListAssociatedAssets' => [ 'name' => 'ListAssociatedAssets', 'http' => [ 'method' => 'GET', 'requestUri' => '/assets/{assetId}/hierarchies', ], 'input' => [ 'shape' => 'ListAssociatedAssetsRequest', ], 'output' => [ 'shape' => 'ListAssociatedAssetsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListDashboards' => [ 'name' => 'ListDashboards', 'http' => [ 'method' => 'GET', 'requestUri' => '/dashboards', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDashboardsRequest', ], 'output' => [ 'shape' => 'ListDashboardsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'ListGateways' => [ 'name' => 'ListGateways', 'http' => [ 'method' => 'GET', 'requestUri' => '/********/gateways', ], 'input' => [ 'shape' => 'ListGatewaysRequest', ], 'output' => [ 'shape' => 'ListGatewaysResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListPortals' => [ 'name' => 'ListPortals', 'http' => [ 'method' => 'GET', 'requestUri' => '/portals', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPortalsRequest', ], 'output' => [ 'shape' => 'ListPortalsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'ListProjectAssets' => [ 'name' => 'ListProjectAssets', 'http' => [ 'method' => 'GET', 'requestUri' => '/projects/{projectId}/assets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListProjectAssetsRequest', ], 'output' => [ 'shape' => 'ListProjectAssetsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'ListProjects' => [ 'name' => 'ListProjects', 'http' => [ 'method' => 'GET', 'requestUri' => '/projects', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListProjectsRequest', ], 'output' => [ 'shape' => 'ListProjectsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictingOperationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'UnauthorizedException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListTimeSeries' => [ 'name' => 'ListTimeSeries', 'http' => [ 'method' => 'GET', 'requestUri' => '/timeseries/', ], 'input' => [ 'shape' => 'ListTimeSeriesRequest', ], 'output' => [ 'shape' => 'ListTimeSeriesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'PutDefaultEncryptionConfiguration' => [ 'name' => 'PutDefaultEncryptionConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/configuration/account/encryption', ], 'input' => [ 'shape' => 'PutDefaultEncryptionConfigurationRequest', ], 'output' => [ 'shape' => 'PutDefaultEncryptionConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'PutLoggingOptions' => [ 'name' => 'PutLoggingOptions', 'http' => [ 'method' => 'PUT', 'requestUri' => '/logging', ], 'input' => [ 'shape' => 'PutLoggingOptionsRequest', ], 'output' => [ 'shape' => 'PutLoggingOptionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ConflictingOperationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'PutStorageConfiguration' => [ 'name' => 'PutStorageConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/configuration/account/storage', ], 'input' => [ 'shape' => 'PutStorageConfigurationRequest', ], 'output' => [ 'shape' => 'PutStorageConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictingOperationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'TooManyTagsException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictingOperationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'UnauthorizedException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'UpdateAccessPolicy' => [ 'name' => 'UpdateAccessPolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/access-policies/{accessPolicyId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAccessPolicyRequest', ], 'output' => [ 'shape' => 'UpdateAccessPolicyResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'UpdateAsset' => [ 'name' => 'UpdateAsset', 'http' => [ 'method' => 'PUT', 'requestUri' => '/assets/{assetId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateAssetRequest', ], 'output' => [ 'shape' => 'UpdateAssetResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'UpdateAssetModel' => [ 'name' => 'UpdateAssetModel', 'http' => [ 'method' => 'PUT', 'requestUri' => '/asset-models/{assetModelId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateAssetModelRequest', ], 'output' => [ 'shape' => 'UpdateAssetModelResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'UpdateAssetProperty' => [ 'name' => 'UpdateAssetProperty', 'http' => [ 'method' => 'PUT', 'requestUri' => '/assets/{assetId}/properties/{propertyId}', ], 'input' => [ 'shape' => 'UpdateAssetPropertyRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'UpdateDashboard' => [ 'name' => 'UpdateDashboard', 'http' => [ 'method' => 'PUT', 'requestUri' => '/dashboards/{dashboardId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateDashboardRequest', ], 'output' => [ 'shape' => 'UpdateDashboardResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'UpdateGateway' => [ 'name' => 'UpdateGateway', 'http' => [ 'method' => 'PUT', 'requestUri' => '/********/gateways/{gatewayId}', ], 'input' => [ 'shape' => 'UpdateGatewayRequest', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictingOperationException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'UpdateGatewayCapabilityConfiguration' => [ 'name' => 'UpdateGatewayCapabilityConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/********/gateways/{gatewayId}/capability', 'responseCode' => 201, ], 'input' => [ 'shape' => 'UpdateGatewayCapabilityConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateGatewayCapabilityConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictingOperationException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'UpdatePortal' => [ 'name' => 'UpdatePortal', 'http' => [ 'method' => 'PUT', 'requestUri' => '/portals/{portalId}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdatePortalRequest', ], 'output' => [ 'shape' => 'UpdatePortalResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictingOperationException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], 'UpdateProject' => [ 'name' => 'UpdateProject', 'http' => [ 'method' => 'PUT', 'requestUri' => '/projects/{projectId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateProjectRequest', ], 'output' => [ 'shape' => 'UpdateProjectResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'monitor.', ], ], ], 'shapes' => [ 'ARN' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '.*', ], 'AccessPolicySummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccessPolicySummary', ], ], 'AccessPolicySummary' => [ 'type' => 'structure', 'required' => [ 'id', 'identity', 'resource', 'permission', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'identity' => [ 'shape' => 'Identity', ], 'resource' => [ 'shape' => 'Resource', ], 'permission' => [ 'shape' => 'Permission', ], 'creationDate' => [ 'shape' => 'Timestamp', ], 'lastUpdateDate' => [ 'shape' => 'Timestamp', ], ], ], 'AggregateType' => [ 'type' => 'string', 'enum' => [ 'AVERAGE', 'COUNT', 'MAXIMUM', 'MINIMUM', 'SUM', 'STANDARD_DEVIATION', ], ], 'AggregateTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'AggregateType', ], 'min' => 1, ], 'AggregatedDoubleValue' => [ 'type' => 'double', ], 'AggregatedValue' => [ 'type' => 'structure', 'required' => [ 'timestamp', 'value', ], 'members' => [ 'timestamp' => [ 'shape' => 'Timestamp', ], 'quality' => [ 'shape' => 'Quality', ], 'value' => [ 'shape' => 'Aggregates', ], ], ], 'AggregatedValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'AggregatedValue', ], ], 'Aggregates' => [ 'type' => 'structure', 'members' => [ 'average' => [ 'shape' => 'AggregatedDoubleValue', ], 'count' => [ 'shape' => 'AggregatedDoubleValue', ], 'maximum' => [ 'shape' => 'AggregatedDoubleValue', ], 'minimum' => [ 'shape' => 'AggregatedDoubleValue', ], 'sum' => [ 'shape' => 'AggregatedDoubleValue', ], 'standardDeviation' => [ 'shape' => 'AggregatedDoubleValue', ], ], ], 'Alarms' => [ 'type' => 'structure', 'required' => [ 'alarmRoleArn', ], 'members' => [ 'alarmRoleArn' => [ 'shape' => 'ARN', ], 'notificationLambdaArn' => [ 'shape' => 'ARN', ], ], ], 'AmazonResourceName' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, ], 'AssetCompositeModel' => [ 'type' => 'structure', 'required' => [ 'name', 'type', 'properties', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'type' => [ 'shape' => 'Name', ], 'properties' => [ 'shape' => 'AssetProperties', ], ], ], 'AssetCompositeModels' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetCompositeModel', ], ], 'AssetErrorCode' => [ 'type' => 'string', 'enum' => [ 'INTERNAL_FAILURE', ], ], 'AssetErrorDetails' => [ 'type' => 'structure', 'required' => [ 'assetId', 'code', 'message', ], 'members' => [ 'assetId' => [ 'shape' => 'ID', ], 'code' => [ 'shape' => 'AssetErrorCode', ], 'message' => [ 'shape' => 'AssetErrorMessage', ], ], ], 'AssetErrorMessage' => [ 'type' => 'string', ], 'AssetHierarchies' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetHierarchy', ], ], 'AssetHierarchy' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'name' => [ 'shape' => 'Name', ], ], ], 'AssetHierarchyInfo' => [ 'type' => 'structure', 'members' => [ 'parentAssetId' => [ 'shape' => 'ID', ], 'childAssetId' => [ 'shape' => 'ID', ], ], ], 'AssetIDs' => [ 'type' => 'list', 'member' => [ 'shape' => 'ID', ], ], 'AssetModelCompositeModel' => [ 'type' => 'structure', 'required' => [ 'name', 'type', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'type' => [ 'shape' => 'Name', ], 'properties' => [ 'shape' => 'AssetModelProperties', ], ], ], 'AssetModelCompositeModelDefinition' => [ 'type' => 'structure', 'required' => [ 'name', 'type', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'type' => [ 'shape' => 'Name', ], 'properties' => [ 'shape' => 'AssetModelPropertyDefinitions', ], ], ], 'AssetModelCompositeModelDefinitions' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetModelCompositeModelDefinition', ], ], 'AssetModelCompositeModels' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetModelCompositeModel', ], ], 'AssetModelHierarchies' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetModelHierarchy', ], ], 'AssetModelHierarchy' => [ 'type' => 'structure', 'required' => [ 'name', 'childAssetModelId', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'name' => [ 'shape' => 'Name', ], 'childAssetModelId' => [ 'shape' => 'ID', ], ], ], 'AssetModelHierarchyDefinition' => [ 'type' => 'structure', 'required' => [ 'name', 'childAssetModelId', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'childAssetModelId' => [ 'shape' => 'ID', ], ], ], 'AssetModelHierarchyDefinitions' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetModelHierarchyDefinition', ], ], 'AssetModelProperties' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetModelProperty', ], ], 'AssetModelProperty' => [ 'type' => 'structure', 'required' => [ 'name', 'dataType', 'type', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'name' => [ 'shape' => 'Name', ], 'dataType' => [ 'shape' => 'PropertyDataType', ], 'dataTypeSpec' => [ 'shape' => 'Name', ], 'unit' => [ 'shape' => 'PropertyUnit', ], 'type' => [ 'shape' => 'PropertyType', ], ], ], 'AssetModelPropertyDefinition' => [ 'type' => 'structure', 'required' => [ 'name', 'dataType', 'type', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'dataType' => [ 'shape' => 'PropertyDataType', ], 'dataTypeSpec' => [ 'shape' => 'Name', ], 'unit' => [ 'shape' => 'PropertyUnit', ], 'type' => [ 'shape' => 'PropertyType', ], ], ], 'AssetModelPropertyDefinitions' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetModelPropertyDefinition', ], ], 'AssetModelState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'UPDATING', 'PROPAGATING', 'DELETING', 'FAILED', ], ], 'AssetModelStatus' => [ 'type' => 'structure', 'required' => [ 'state', ], 'members' => [ 'state' => [ 'shape' => 'AssetModelState', ], 'error' => [ 'shape' => 'ErrorDetails', ], ], ], 'AssetModelSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetModelSummary', ], ], 'AssetModelSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'name', 'description', 'creationDate', 'lastUpdateDate', 'status', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'arn' => [ 'shape' => 'ARN', ], 'name' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'creationDate' => [ 'shape' => 'Timestamp', ], 'lastUpdateDate' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'AssetModelStatus', ], ], ], 'AssetProperties' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetProperty', ], ], 'AssetProperty' => [ 'type' => 'structure', 'required' => [ 'id', 'name', 'dataType', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'name' => [ 'shape' => 'Name', ], 'alias' => [ 'shape' => 'PropertyAlias', ], 'notification' => [ 'shape' => 'PropertyNotification', ], 'dataType' => [ 'shape' => 'PropertyDataType', ], 'dataTypeSpec' => [ 'shape' => 'Name', ], 'unit' => [ 'shape' => 'PropertyUnit', ], ], ], 'AssetPropertyAlias' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '[^\\u0000-\\u001F\\u007F]+', ], 'AssetPropertyValue' => [ 'type' => 'structure', 'required' => [ 'value', 'timestamp', ], 'members' => [ 'value' => [ 'shape' => 'Variant', ], 'timestamp' => [ 'shape' => 'TimeInNanos', ], 'quality' => [ 'shape' => 'Quality', ], ], ], 'AssetPropertyValueHistory' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetPropertyValue', ], ], 'AssetPropertyValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetPropertyValue', ], ], 'AssetRelationshipSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetRelationshipSummary', ], ], 'AssetRelationshipSummary' => [ 'type' => 'structure', 'required' => [ 'relationshipType', ], 'members' => [ 'hierarchyInfo' => [ 'shape' => 'AssetHierarchyInfo', ], 'relationshipType' => [ 'shape' => 'AssetRelationshipType', ], ], ], 'AssetRelationshipType' => [ 'type' => 'string', 'enum' => [ 'HIERARCHY', ], ], 'AssetState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'UPDATING', 'DELETING', 'FAILED', ], ], 'AssetStatus' => [ 'type' => 'structure', 'required' => [ 'state', ], 'members' => [ 'state' => [ 'shape' => 'AssetState', ], 'error' => [ 'shape' => 'ErrorDetails', ], ], ], 'AssetSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetSummary', ], ], 'AssetSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'name', 'assetModelId', 'creationDate', 'lastUpdateDate', 'status', 'hierarchies', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'arn' => [ 'shape' => 'ARN', ], 'name' => [ 'shape' => 'Name', ], 'assetModelId' => [ 'shape' => 'ID', ], 'creationDate' => [ 'shape' => 'Timestamp', ], 'lastUpdateDate' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'AssetStatus', ], 'hierarchies' => [ 'shape' => 'AssetHierarchies', ], ], ], 'AssociateAssetsRequest' => [ 'type' => 'structure', 'required' => [ 'assetId', 'hierarchyId', 'childAssetId', ], 'members' => [ 'assetId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'assetId', ], 'hierarchyId' => [ 'shape' => 'ID', ], 'childAssetId' => [ 'shape' => 'ID', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'AssociateTimeSeriesToAssetPropertyRequest' => [ 'type' => 'structure', 'required' => [ 'alias', 'assetId', 'propertyId', ], 'members' => [ 'alias' => [ 'shape' => 'PropertyAlias', 'location' => 'querystring', 'locationName' => 'alias', ], 'assetId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'assetId', ], 'propertyId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'propertyId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'AssociatedAssetsSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssociatedAssetsSummary', ], ], 'AssociatedAssetsSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'name', 'assetModelId', 'creationDate', 'lastUpdateDate', 'status', 'hierarchies', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'arn' => [ 'shape' => 'ARN', ], 'name' => [ 'shape' => 'Name', ], 'assetModelId' => [ 'shape' => 'ID', ], 'creationDate' => [ 'shape' => 'Timestamp', ], 'lastUpdateDate' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'AssetStatus', ], 'hierarchies' => [ 'shape' => 'AssetHierarchies', ], ], ], 'Attribute' => [ 'type' => 'structure', 'members' => [ 'defaultValue' => [ 'shape' => 'DefaultValue', ], ], ], 'AuthMode' => [ 'type' => 'string', 'enum' => [ 'IAM', 'SSO', ], ], 'BatchAssociateProjectAssetsErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetErrorDetails', ], ], 'BatchAssociateProjectAssetsRequest' => [ 'type' => 'structure', 'required' => [ 'projectId', 'assetIds', ], 'members' => [ 'projectId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'projectId', ], 'assetIds' => [ 'shape' => 'IDs', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'BatchAssociateProjectAssetsResponse' => [ 'type' => 'structure', 'members' => [ 'errors' => [ 'shape' => 'BatchAssociateProjectAssetsErrors', ], ], ], 'BatchDisassociateProjectAssetsErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetErrorDetails', ], ], 'BatchDisassociateProjectAssetsRequest' => [ 'type' => 'structure', 'required' => [ 'projectId', 'assetIds', ], 'members' => [ 'projectId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'projectId', ], 'assetIds' => [ 'shape' => 'IDs', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'BatchDisassociateProjectAssetsResponse' => [ 'type' => 'structure', 'members' => [ 'errors' => [ 'shape' => 'BatchDisassociateProjectAssetsErrors', ], ], ], 'BatchPutAssetPropertyError' => [ 'type' => 'structure', 'required' => [ 'errorCode', 'errorMessage', 'timestamps', ], 'members' => [ 'errorCode' => [ 'shape' => 'BatchPutAssetPropertyValueErrorCode', ], 'errorMessage' => [ 'shape' => 'ErrorMessage', ], 'timestamps' => [ 'shape' => 'Timestamps', ], ], ], 'BatchPutAssetPropertyErrorEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchPutAssetPropertyErrorEntry', ], ], 'BatchPutAssetPropertyErrorEntry' => [ 'type' => 'structure', 'required' => [ 'entryId', 'errors', ], 'members' => [ 'entryId' => [ 'shape' => 'EntryId', ], 'errors' => [ 'shape' => 'BatchPutAssetPropertyErrors', ], ], ], 'BatchPutAssetPropertyErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchPutAssetPropertyError', ], ], 'BatchPutAssetPropertyValueErrorCode' => [ 'type' => 'string', 'enum' => [ 'ResourceNotFoundException', 'InvalidRequestException', 'InternalFailureException', 'ServiceUnavailableException', 'ThrottlingException', 'LimitExceededException', 'ConflictingOperationException', 'TimestampOutOfRangeException', 'AccessDeniedException', ], ], 'BatchPutAssetPropertyValueRequest' => [ 'type' => 'structure', 'required' => [ 'entries', ], 'members' => [ 'entries' => [ 'shape' => 'PutAssetPropertyValueEntries', ], ], ], 'BatchPutAssetPropertyValueResponse' => [ 'type' => 'structure', 'required' => [ 'errorEntries', ], 'members' => [ 'errorEntries' => [ 'shape' => 'BatchPutAssetPropertyErrorEntries', ], ], ], 'CapabilityConfiguration' => [ 'type' => 'string', 'max' => 104857600, 'min' => 1, ], 'CapabilityNamespace' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '^[a-zA-Z]+:[a-zA-Z]+:[0-9]+$', ], 'CapabilitySyncStatus' => [ 'type' => 'string', 'enum' => [ 'IN_SYNC', 'OUT_OF_SYNC', 'SYNC_FAILED', 'UNKNOWN', ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 64, 'min' => 36, 'pattern' => '\\S{36,64}', ], 'CompositeModelProperty' => [ 'type' => 'structure', 'required' => [ 'name', 'type', 'assetProperty', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'type' => [ 'shape' => 'Name', ], 'assetProperty' => [ 'shape' => 'Property', ], ], ], 'ComputeLocation' => [ 'type' => 'string', 'enum' => [ 'EDGE', 'CLOUD', ], ], 'ConfigurationErrorDetails' => [ 'type' => 'structure', 'required' => [ 'code', 'message', ], 'members' => [ 'code' => [ 'shape' => 'ErrorCode', ], 'message' => [ 'shape' => 'ErrorMessage', ], ], ], 'ConfigurationState' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'UPDATE_IN_PROGRESS', 'UPDATE_FAILED', ], ], 'ConfigurationStatus' => [ 'type' => 'structure', 'required' => [ 'state', ], 'members' => [ 'state' => [ 'shape' => 'ConfigurationState', ], 'error' => [ 'shape' => 'ConfigurationErrorDetails', ], ], ], 'ConflictingOperationException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceArn', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceArn' => [ 'shape' => 'ResourceArn', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'CoreDeviceThingName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'CreateAccessPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'accessPolicyIdentity', 'accessPolicyResource', 'accessPolicyPermission', ], 'members' => [ 'accessPolicyIdentity' => [ 'shape' => 'Identity', ], 'accessPolicyResource' => [ 'shape' => 'Resource', ], 'accessPolicyPermission' => [ 'shape' => 'Permission', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateAccessPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'accessPolicyId', 'accessPolicyArn', ], 'members' => [ 'accessPolicyId' => [ 'shape' => 'ID', ], 'accessPolicyArn' => [ 'shape' => 'ARN', ], ], ], 'CreateAssetModelRequest' => [ 'type' => 'structure', 'required' => [ 'assetModelName', ], 'members' => [ 'assetModelName' => [ 'shape' => 'Name', ], 'assetModelDescription' => [ 'shape' => 'Description', ], 'assetModelProperties' => [ 'shape' => 'AssetModelPropertyDefinitions', ], 'assetModelHierarchies' => [ 'shape' => 'AssetModelHierarchyDefinitions', ], 'assetModelCompositeModels' => [ 'shape' => 'AssetModelCompositeModelDefinitions', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateAssetModelResponse' => [ 'type' => 'structure', 'required' => [ 'assetModelId', 'assetModelArn', 'assetModelStatus', ], 'members' => [ 'assetModelId' => [ 'shape' => 'ID', ], 'assetModelArn' => [ 'shape' => 'ARN', ], 'assetModelStatus' => [ 'shape' => 'AssetModelStatus', ], ], ], 'CreateAssetRequest' => [ 'type' => 'structure', 'required' => [ 'assetName', 'assetModelId', ], 'members' => [ 'assetName' => [ 'shape' => 'Name', ], 'assetModelId' => [ 'shape' => 'ID', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateAssetResponse' => [ 'type' => 'structure', 'required' => [ 'assetId', 'assetArn', 'assetStatus', ], 'members' => [ 'assetId' => [ 'shape' => 'ID', ], 'assetArn' => [ 'shape' => 'ARN', ], 'assetStatus' => [ 'shape' => 'AssetStatus', ], ], ], 'CreateDashboardRequest' => [ 'type' => 'structure', 'required' => [ 'projectId', 'dashboardName', 'dashboardDefinition', ], 'members' => [ 'projectId' => [ 'shape' => 'ID', ], 'dashboardName' => [ 'shape' => 'Name', ], 'dashboardDescription' => [ 'shape' => 'Description', ], 'dashboardDefinition' => [ 'shape' => 'DashboardDefinition', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateDashboardResponse' => [ 'type' => 'structure', 'required' => [ 'dashboardId', 'dashboardArn', ], 'members' => [ 'dashboardId' => [ 'shape' => 'ID', ], 'dashboardArn' => [ 'shape' => 'ARN', ], ], ], 'CreateGatewayRequest' => [ 'type' => 'structure', 'required' => [ 'gatewayName', 'gatewayPlatform', ], 'members' => [ 'gatewayName' => [ 'shape' => 'Name', ], 'gatewayPlatform' => [ 'shape' => 'GatewayPlatform', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateGatewayResponse' => [ 'type' => 'structure', 'required' => [ 'gatewayId', 'gatewayArn', ], 'members' => [ 'gatewayId' => [ 'shape' => 'ID', ], 'gatewayArn' => [ 'shape' => 'ARN', ], ], ], 'CreatePortalRequest' => [ 'type' => 'structure', 'required' => [ 'portalName', 'portalContactEmail', 'roleArn', ], 'members' => [ 'portalName' => [ 'shape' => 'Name', ], 'portalDescription' => [ 'shape' => 'Description', ], 'portalContactEmail' => [ 'shape' => 'Email', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'portalLogoImageFile' => [ 'shape' => 'ImageFile', ], 'roleArn' => [ 'shape' => 'ARN', ], 'tags' => [ 'shape' => 'TagMap', ], 'portalAuthMode' => [ 'shape' => 'AuthMode', ], 'notificationSenderEmail' => [ 'shape' => 'Email', ], 'alarms' => [ 'shape' => 'Alarms', ], ], ], 'CreatePortalResponse' => [ 'type' => 'structure', 'required' => [ 'portalId', 'portalArn', 'portalStartUrl', 'portalStatus', 'ssoApplicationId', ], 'members' => [ 'portalId' => [ 'shape' => 'ID', ], 'portalArn' => [ 'shape' => 'ARN', ], 'portalStartUrl' => [ 'shape' => 'Url', ], 'portalStatus' => [ 'shape' => 'PortalStatus', ], 'ssoApplicationId' => [ 'shape' => 'SSOApplicationId', ], ], ], 'CreateProjectRequest' => [ 'type' => 'structure', 'required' => [ 'portalId', 'projectName', ], 'members' => [ 'portalId' => [ 'shape' => 'ID', ], 'projectName' => [ 'shape' => 'Name', ], 'projectDescription' => [ 'shape' => 'Description', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateProjectResponse' => [ 'type' => 'structure', 'required' => [ 'projectId', 'projectArn', ], 'members' => [ 'projectId' => [ 'shape' => 'ID', ], 'projectArn' => [ 'shape' => 'ARN', ], ], ], 'CustomerManagedS3Storage' => [ 'type' => 'structure', 'required' => [ 's3ResourceArn', 'roleArn', ], 'members' => [ 's3ResourceArn' => [ 'shape' => 'ARN', ], 'roleArn' => [ 'shape' => 'ARN', ], ], ], 'DashboardDefinition' => [ 'type' => 'string', 'max' => 204800, 'min' => 0, 'pattern' => '.+', ], 'DashboardSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'DashboardSummary', ], ], 'DashboardSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'name', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'name' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'creationDate' => [ 'shape' => 'Timestamp', ], 'lastUpdateDate' => [ 'shape' => 'Timestamp', ], ], ], 'DefaultValue' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[^\\u0000-\\u001F\\u007F]+', ], 'DeleteAccessPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'accessPolicyId', ], 'members' => [ 'accessPolicyId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'accessPolicyId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DeleteAccessPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAssetModelRequest' => [ 'type' => 'structure', 'required' => [ 'assetModelId', ], 'members' => [ 'assetModelId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'assetModelId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DeleteAssetModelResponse' => [ 'type' => 'structure', 'required' => [ 'assetModelStatus', ], 'members' => [ 'assetModelStatus' => [ 'shape' => 'AssetModelStatus', ], ], ], 'DeleteAssetRequest' => [ 'type' => 'structure', 'required' => [ 'assetId', ], 'members' => [ 'assetId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'assetId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DeleteAssetResponse' => [ 'type' => 'structure', 'required' => [ 'assetStatus', ], 'members' => [ 'assetStatus' => [ 'shape' => 'AssetStatus', ], ], ], 'DeleteDashboardRequest' => [ 'type' => 'structure', 'required' => [ 'dashboardId', ], 'members' => [ 'dashboardId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'dashboardId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DeleteDashboardResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteGatewayRequest' => [ 'type' => 'structure', 'required' => [ 'gatewayId', ], 'members' => [ 'gatewayId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'gatewayId', ], ], ], 'DeletePortalRequest' => [ 'type' => 'structure', 'required' => [ 'portalId', ], 'members' => [ 'portalId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'portalId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DeletePortalResponse' => [ 'type' => 'structure', 'required' => [ 'portalStatus', ], 'members' => [ 'portalStatus' => [ 'shape' => 'PortalStatus', ], ], ], 'DeleteProjectRequest' => [ 'type' => 'structure', 'required' => [ 'projectId', ], 'members' => [ 'projectId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'projectId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], ], ], 'DeleteProjectResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTimeSeriesRequest' => [ 'type' => 'structure', 'members' => [ 'alias' => [ 'shape' => 'PropertyAlias', 'location' => 'querystring', 'locationName' => 'alias', ], 'assetId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'assetId', ], 'propertyId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'propertyId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'DescribeAccessPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'accessPolicyId', ], 'members' => [ 'accessPolicyId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'accessPolicyId', ], ], ], 'DescribeAccessPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'accessPolicyId', 'accessPolicyArn', 'accessPolicyIdentity', 'accessPolicyResource', 'accessPolicyPermission', 'accessPolicyCreationDate', 'accessPolicyLastUpdateDate', ], 'members' => [ 'accessPolicyId' => [ 'shape' => 'ID', ], 'accessPolicyArn' => [ 'shape' => 'ARN', ], 'accessPolicyIdentity' => [ 'shape' => 'Identity', ], 'accessPolicyResource' => [ 'shape' => 'Resource', ], 'accessPolicyPermission' => [ 'shape' => 'Permission', ], 'accessPolicyCreationDate' => [ 'shape' => 'Timestamp', ], 'accessPolicyLastUpdateDate' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeAssetModelRequest' => [ 'type' => 'structure', 'required' => [ 'assetModelId', ], 'members' => [ 'assetModelId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'assetModelId', ], ], ], 'DescribeAssetModelResponse' => [ 'type' => 'structure', 'required' => [ 'assetModelId', 'assetModelArn', 'assetModelName', 'assetModelDescription', 'assetModelProperties', 'assetModelHierarchies', 'assetModelCreationDate', 'assetModelLastUpdateDate', 'assetModelStatus', ], 'members' => [ 'assetModelId' => [ 'shape' => 'ID', ], 'assetModelArn' => [ 'shape' => 'ARN', ], 'assetModelName' => [ 'shape' => 'Name', ], 'assetModelDescription' => [ 'shape' => 'Description', ], 'assetModelProperties' => [ 'shape' => 'AssetModelProperties', ], 'assetModelHierarchies' => [ 'shape' => 'AssetModelHierarchies', ], 'assetModelCompositeModels' => [ 'shape' => 'AssetModelCompositeModels', ], 'assetModelCreationDate' => [ 'shape' => 'Timestamp', ], 'assetModelLastUpdateDate' => [ 'shape' => 'Timestamp', ], 'assetModelStatus' => [ 'shape' => 'AssetModelStatus', ], ], ], 'DescribeAssetPropertyRequest' => [ 'type' => 'structure', 'required' => [ 'assetId', 'propertyId', ], 'members' => [ 'assetId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'assetId', ], 'propertyId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'propertyId', ], ], ], 'DescribeAssetPropertyResponse' => [ 'type' => 'structure', 'required' => [ 'assetId', 'assetName', 'assetModelId', ], 'members' => [ 'assetId' => [ 'shape' => 'ID', ], 'assetName' => [ 'shape' => 'Name', ], 'assetModelId' => [ 'shape' => 'ID', ], 'assetProperty' => [ 'shape' => 'Property', ], 'compositeModel' => [ 'shape' => 'CompositeModelProperty', ], ], ], 'DescribeAssetRequest' => [ 'type' => 'structure', 'required' => [ 'assetId', ], 'members' => [ 'assetId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'assetId', ], ], ], 'DescribeAssetResponse' => [ 'type' => 'structure', 'required' => [ 'assetId', 'assetArn', 'assetName', 'assetModelId', 'assetProperties', 'assetHierarchies', 'assetCreationDate', 'assetLastUpdateDate', 'assetStatus', ], 'members' => [ 'assetId' => [ 'shape' => 'ID', ], 'assetArn' => [ 'shape' => 'ARN', ], 'assetName' => [ 'shape' => 'Name', ], 'assetModelId' => [ 'shape' => 'ID', ], 'assetProperties' => [ 'shape' => 'AssetProperties', ], 'assetHierarchies' => [ 'shape' => 'AssetHierarchies', ], 'assetCompositeModels' => [ 'shape' => 'AssetCompositeModels', ], 'assetCreationDate' => [ 'shape' => 'Timestamp', ], 'assetLastUpdateDate' => [ 'shape' => 'Timestamp', ], 'assetStatus' => [ 'shape' => 'AssetStatus', ], ], ], 'DescribeDashboardRequest' => [ 'type' => 'structure', 'required' => [ 'dashboardId', ], 'members' => [ 'dashboardId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'dashboardId', ], ], ], 'DescribeDashboardResponse' => [ 'type' => 'structure', 'required' => [ 'dashboardId', 'dashboardArn', 'dashboardName', 'projectId', 'dashboardDefinition', 'dashboardCreationDate', 'dashboardLastUpdateDate', ], 'members' => [ 'dashboardId' => [ 'shape' => 'ID', ], 'dashboardArn' => [ 'shape' => 'ARN', ], 'dashboardName' => [ 'shape' => 'Name', ], 'projectId' => [ 'shape' => 'ID', ], 'dashboardDescription' => [ 'shape' => 'Description', ], 'dashboardDefinition' => [ 'shape' => 'DashboardDefinition', ], 'dashboardCreationDate' => [ 'shape' => 'Timestamp', ], 'dashboardLastUpdateDate' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeDefaultEncryptionConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'DescribeDefaultEncryptionConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'encryptionType', 'configurationStatus', ], 'members' => [ 'encryptionType' => [ 'shape' => 'EncryptionType', ], 'kmsKeyArn' => [ 'shape' => 'ARN', ], 'configurationStatus' => [ 'shape' => 'ConfigurationStatus', ], ], ], 'DescribeGatewayCapabilityConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'gatewayId', 'capabilityNamespace', ], 'members' => [ 'gatewayId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'gatewayId', ], 'capabilityNamespace' => [ 'shape' => 'CapabilityNamespace', 'location' => 'uri', 'locationName' => 'capabilityNamespace', ], ], ], 'DescribeGatewayCapabilityConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'gatewayId', 'capabilityNamespace', 'capabilityConfiguration', 'capabilitySyncStatus', ], 'members' => [ 'gatewayId' => [ 'shape' => 'ID', ], 'capabilityNamespace' => [ 'shape' => 'CapabilityNamespace', ], 'capabilityConfiguration' => [ 'shape' => 'CapabilityConfiguration', ], 'capabilitySyncStatus' => [ 'shape' => 'CapabilitySyncStatus', ], ], ], 'DescribeGatewayRequest' => [ 'type' => 'structure', 'required' => [ 'gatewayId', ], 'members' => [ 'gatewayId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'gatewayId', ], ], ], 'DescribeGatewayResponse' => [ 'type' => 'structure', 'required' => [ 'gatewayId', 'gatewayName', 'gatewayArn', 'gatewayCapabilitySummaries', 'creationDate', 'lastUpdateDate', ], 'members' => [ 'gatewayId' => [ 'shape' => 'ID', ], 'gatewayName' => [ 'shape' => 'Name', ], 'gatewayArn' => [ 'shape' => 'ARN', ], 'gatewayPlatform' => [ 'shape' => 'GatewayPlatform', ], 'gatewayCapabilitySummaries' => [ 'shape' => 'GatewayCapabilitySummaries', ], 'creationDate' => [ 'shape' => 'Timestamp', ], 'lastUpdateDate' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeLoggingOptionsRequest' => [ 'type' => 'structure', 'members' => [], ], 'DescribeLoggingOptionsResponse' => [ 'type' => 'structure', 'required' => [ 'loggingOptions', ], 'members' => [ 'loggingOptions' => [ 'shape' => 'LoggingOptions', ], ], ], 'DescribePortalRequest' => [ 'type' => 'structure', 'required' => [ 'portalId', ], 'members' => [ 'portalId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'portalId', ], ], ], 'DescribePortalResponse' => [ 'type' => 'structure', 'required' => [ 'portalId', 'portalArn', 'portalName', 'portalClientId', 'portalStartUrl', 'portalContactEmail', 'portalStatus', 'portalCreationDate', 'portalLastUpdateDate', ], 'members' => [ 'portalId' => [ 'shape' => 'ID', ], 'portalArn' => [ 'shape' => 'ARN', ], 'portalName' => [ 'shape' => 'Name', ], 'portalDescription' => [ 'shape' => 'Description', ], 'portalClientId' => [ 'shape' => 'PortalClientId', ], 'portalStartUrl' => [ 'shape' => 'Url', ], 'portalContactEmail' => [ 'shape' => 'Email', ], 'portalStatus' => [ 'shape' => 'PortalStatus', ], 'portalCreationDate' => [ 'shape' => 'Timestamp', ], 'portalLastUpdateDate' => [ 'shape' => 'Timestamp', ], 'portalLogoImageLocation' => [ 'shape' => 'ImageLocation', ], 'roleArn' => [ 'shape' => 'ARN', ], 'portalAuthMode' => [ 'shape' => 'AuthMode', ], 'notificationSenderEmail' => [ 'shape' => 'Email', ], 'alarms' => [ 'shape' => 'Alarms', ], ], ], 'DescribeProjectRequest' => [ 'type' => 'structure', 'required' => [ 'projectId', ], 'members' => [ 'projectId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'projectId', ], ], ], 'DescribeProjectResponse' => [ 'type' => 'structure', 'required' => [ 'projectId', 'projectArn', 'projectName', 'portalId', 'projectCreationDate', 'projectLastUpdateDate', ], 'members' => [ 'projectId' => [ 'shape' => 'ID', ], 'projectArn' => [ 'shape' => 'ARN', ], 'projectName' => [ 'shape' => 'Name', ], 'portalId' => [ 'shape' => 'ID', ], 'projectDescription' => [ 'shape' => 'Description', ], 'projectCreationDate' => [ 'shape' => 'Timestamp', ], 'projectLastUpdateDate' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeStorageConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'DescribeStorageConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'storageType', 'configurationStatus', ], 'members' => [ 'storageType' => [ 'shape' => 'StorageType', ], 'multiLayerStorage' => [ 'shape' => 'MultiLayerStorage', ], 'disassociatedDataStorage' => [ 'shape' => 'DisassociatedDataStorageState', ], 'retentionPeriod' => [ 'shape' => 'RetentionPeriod', ], 'configurationStatus' => [ 'shape' => 'ConfigurationStatus', ], 'lastUpdateDate' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeTimeSeriesRequest' => [ 'type' => 'structure', 'members' => [ 'alias' => [ 'shape' => 'PropertyAlias', 'location' => 'querystring', 'locationName' => 'alias', ], 'assetId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'assetId', ], 'propertyId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'propertyId', ], ], ], 'DescribeTimeSeriesResponse' => [ 'type' => 'structure', 'required' => [ 'timeSeriesId', 'dataType', 'timeSeriesCreationDate', 'timeSeriesLastUpdateDate', ], 'members' => [ 'assetId' => [ 'shape' => 'ID', ], 'propertyId' => [ 'shape' => 'ID', ], 'alias' => [ 'shape' => 'PropertyAlias', ], 'timeSeriesId' => [ 'shape' => 'TimeSeriesId', ], 'dataType' => [ 'shape' => 'PropertyDataType', ], 'dataTypeSpec' => [ 'shape' => 'Name', ], 'timeSeriesCreationDate' => [ 'shape' => 'Timestamp', ], 'timeSeriesLastUpdateDate' => [ 'shape' => 'Timestamp', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '[^\\u0000-\\u001F\\u007F]+', ], 'DetailedError' => [ 'type' => 'structure', 'required' => [ 'code', 'message', ], 'members' => [ 'code' => [ 'shape' => 'DetailedErrorCode', ], 'message' => [ 'shape' => 'DetailedErrorMessage', ], ], ], 'DetailedErrorCode' => [ 'type' => 'string', 'enum' => [ 'INCOMPATIBLE_COMPUTE_LOCATION', 'INCOMPATIBLE_FORWARDING_CONFIGURATION', ], ], 'DetailedErrorMessage' => [ 'type' => 'string', ], 'DetailedErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'DetailedError', ], ], 'DisassociateAssetsRequest' => [ 'type' => 'structure', 'required' => [ 'assetId', 'hierarchyId', 'childAssetId', ], 'members' => [ 'assetId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'assetId', ], 'hierarchyId' => [ 'shape' => 'ID', ], 'childAssetId' => [ 'shape' => 'ID', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'DisassociateTimeSeriesFromAssetPropertyRequest' => [ 'type' => 'structure', 'required' => [ 'alias', 'assetId', 'propertyId', ], 'members' => [ 'alias' => [ 'shape' => 'PropertyAlias', 'location' => 'querystring', 'locationName' => 'alias', ], 'assetId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'assetId', ], 'propertyId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'propertyId', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'DisassociatedDataStorageState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'Email' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[^@]+@[^@]+', ], 'EncryptionType' => [ 'type' => 'string', 'enum' => [ 'SITEWISE_DEFAULT_ENCRYPTION', 'KMS_BASED_ENCRYPTION', ], ], 'EntryId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_-]+$', ], 'ErrorCode' => [ 'type' => 'string', 'enum' => [ 'VALIDATION_ERROR', 'INTERNAL_FAILURE', ], ], 'ErrorDetails' => [ 'type' => 'structure', 'required' => [ 'code', 'message', ], 'members' => [ 'code' => [ 'shape' => 'ErrorCode', ], 'message' => [ 'shape' => 'ErrorMessage', ], 'details' => [ 'shape' => 'DetailedErrors', ], ], ], 'ErrorMessage' => [ 'type' => 'string', ], 'ExceptionMessage' => [ 'type' => 'string', ], 'Expression' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ExpressionVariable' => [ 'type' => 'structure', 'required' => [ 'name', 'value', ], 'members' => [ 'name' => [ 'shape' => 'VariableName', ], 'value' => [ 'shape' => 'VariableValue', ], ], ], 'ExpressionVariables' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExpressionVariable', ], ], 'ForwardingConfig' => [ 'type' => 'structure', 'required' => [ 'state', ], 'members' => [ 'state' => [ 'shape' => 'ForwardingConfigState', ], ], ], 'ForwardingConfigState' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'GatewayCapabilitySummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'GatewayCapabilitySummary', ], ], 'GatewayCapabilitySummary' => [ 'type' => 'structure', 'required' => [ 'capabilityNamespace', 'capabilitySyncStatus', ], 'members' => [ 'capabilityNamespace' => [ 'shape' => 'CapabilityNamespace', ], 'capabilitySyncStatus' => [ 'shape' => 'CapabilitySyncStatus', ], ], ], 'GatewayPlatform' => [ 'type' => 'structure', 'members' => [ 'greengrass' => [ 'shape' => 'Greengrass', ], 'greengrassV2' => [ 'shape' => 'GreengrassV2', ], ], ], 'GatewaySummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'GatewaySummary', ], ], 'GatewaySummary' => [ 'type' => 'structure', 'required' => [ 'gatewayId', 'gatewayName', 'creationDate', 'lastUpdateDate', ], 'members' => [ 'gatewayId' => [ 'shape' => 'ID', ], 'gatewayName' => [ 'shape' => 'Name', ], 'gatewayPlatform' => [ 'shape' => 'GatewayPlatform', ], 'gatewayCapabilitySummaries' => [ 'shape' => 'GatewayCapabilitySummaries', ], 'creationDate' => [ 'shape' => 'Timestamp', ], 'lastUpdateDate' => [ 'shape' => 'Timestamp', ], ], ], 'GetAssetPropertyAggregatesRequest' => [ 'type' => 'structure', 'required' => [ 'aggregateTypes', 'resolution', 'startDate', 'endDate', ], 'members' => [ 'assetId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'assetId', ], 'propertyId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'propertyId', ], 'propertyAlias' => [ 'shape' => 'AssetPropertyAlias', 'location' => 'querystring', 'locationName' => 'propertyAlias', ], 'aggregateTypes' => [ 'shape' => 'AggregateTypes', 'location' => 'querystring', 'locationName' => 'aggregateTypes', ], 'resolution' => [ 'shape' => 'Resolution', 'location' => 'querystring', 'locationName' => 'resolution', ], 'qualities' => [ 'shape' => 'Qualities', 'location' => 'querystring', 'locationName' => 'qualities', ], 'startDate' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'startDate', ], 'endDate' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'endDate', ], 'timeOrdering' => [ 'shape' => 'TimeOrdering', 'location' => 'querystring', 'locationName' => 'timeOrdering', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'GetAssetPropertyAggregatesResponse' => [ 'type' => 'structure', 'required' => [ 'aggregatedValues', ], 'members' => [ 'aggregatedValues' => [ 'shape' => 'AggregatedValues', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetAssetPropertyValueHistoryRequest' => [ 'type' => 'structure', 'members' => [ 'assetId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'assetId', ], 'propertyId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'propertyId', ], 'propertyAlias' => [ 'shape' => 'AssetPropertyAlias', 'location' => 'querystring', 'locationName' => 'propertyAlias', ], 'startDate' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'startDate', ], 'endDate' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'endDate', ], 'qualities' => [ 'shape' => 'Qualities', 'location' => 'querystring', 'locationName' => 'qualities', ], 'timeOrdering' => [ 'shape' => 'TimeOrdering', 'location' => 'querystring', 'locationName' => 'timeOrdering', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'GetAssetPropertyValueHistoryResponse' => [ 'type' => 'structure', 'required' => [ 'assetPropertyValueHistory', ], 'members' => [ 'assetPropertyValueHistory' => [ 'shape' => 'AssetPropertyValueHistory', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetAssetPropertyValueRequest' => [ 'type' => 'structure', 'members' => [ 'assetId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'assetId', ], 'propertyId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'propertyId', ], 'propertyAlias' => [ 'shape' => 'AssetPropertyAlias', 'location' => 'querystring', 'locationName' => 'propertyAlias', ], ], ], 'GetAssetPropertyValueResponse' => [ 'type' => 'structure', 'members' => [ 'propertyValue' => [ 'shape' => 'AssetPropertyValue', ], ], ], 'GetInterpolatedAssetPropertyValuesRequest' => [ 'type' => 'structure', 'required' => [ 'startTimeInSeconds', 'endTimeInSeconds', 'quality', 'intervalInSeconds', 'type', ], 'members' => [ 'assetId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'assetId', ], 'propertyId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'propertyId', ], 'propertyAlias' => [ 'shape' => 'AssetPropertyAlias', 'location' => 'querystring', 'locationName' => 'propertyAlias', ], 'startTimeInSeconds' => [ 'shape' => 'TimeInSeconds', 'location' => 'querystring', 'locationName' => 'startTimeInSeconds', ], 'startTimeOffsetInNanos' => [ 'shape' => 'OffsetInNanos', 'location' => 'querystring', 'locationName' => 'startTimeOffsetInNanos', ], 'endTimeInSeconds' => [ 'shape' => 'TimeInSeconds', 'location' => 'querystring', 'locationName' => 'endTimeInSeconds', ], 'endTimeOffsetInNanos' => [ 'shape' => 'OffsetInNanos', 'location' => 'querystring', 'locationName' => 'endTimeOffsetInNanos', ], 'quality' => [ 'shape' => 'Quality', 'location' => 'querystring', 'locationName' => 'quality', ], 'intervalInSeconds' => [ 'shape' => 'IntervalInSeconds', 'location' => 'querystring', 'locationName' => 'intervalInSeconds', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxInterpolatedResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'type' => [ 'shape' => 'InterpolationType', 'location' => 'querystring', 'locationName' => 'type', ], 'intervalWindowInSeconds' => [ 'shape' => 'IntervalWindowInSeconds', 'location' => 'querystring', 'locationName' => 'intervalWindowInSeconds', ], ], ], 'GetInterpolatedAssetPropertyValuesResponse' => [ 'type' => 'structure', 'required' => [ 'interpolatedAssetPropertyValues', ], 'members' => [ 'interpolatedAssetPropertyValues' => [ 'shape' => 'InterpolatedAssetPropertyValues', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'Greengrass' => [ 'type' => 'structure', 'required' => [ 'groupArn', ], 'members' => [ 'groupArn' => [ 'shape' => 'ARN', ], ], ], 'GreengrassV2' => [ 'type' => 'structure', 'required' => [ 'coreDeviceThingName', ], 'members' => [ 'coreDeviceThingName' => [ 'shape' => 'CoreDeviceThingName', ], ], ], 'GroupIdentity' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'IdentityId', ], ], ], 'IAMRoleIdentity' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'ARN', ], ], ], 'IAMUserIdentity' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'ARN', ], ], ], 'ID' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$', ], 'IDs' => [ 'type' => 'list', 'member' => [ 'shape' => 'ID', ], 'max' => 100, 'min' => 1, ], 'Identity' => [ 'type' => 'structure', 'members' => [ 'user' => [ 'shape' => 'UserIdentity', ], 'group' => [ 'shape' => 'GroupIdentity', ], 'iamUser' => [ 'shape' => 'IAMUserIdentity', ], 'iamRole' => [ 'shape' => 'IAMRoleIdentity', ], ], ], 'IdentityId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '\\S+', ], 'IdentityType' => [ 'type' => 'string', 'enum' => [ 'USER', 'GROUP', 'IAM', ], ], 'Image' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ID', ], 'file' => [ 'shape' => 'ImageFile', ], ], ], 'ImageFile' => [ 'type' => 'structure', 'required' => [ 'data', 'type', ], 'members' => [ 'data' => [ 'shape' => 'ImageFileData', ], 'type' => [ 'shape' => 'ImageFileType', ], ], ], 'ImageFileData' => [ 'type' => 'blob', 'max' => 1500000, 'min' => 1, ], 'ImageFileType' => [ 'type' => 'string', 'enum' => [ 'PNG', ], ], 'ImageLocation' => [ 'type' => 'structure', 'required' => [ 'id', 'url', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'url' => [ 'shape' => 'Url', ], ], ], 'InternalFailureException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InterpolatedAssetPropertyValue' => [ 'type' => 'structure', 'required' => [ 'timestamp', 'value', ], 'members' => [ 'timestamp' => [ 'shape' => 'TimeInNanos', ], 'value' => [ 'shape' => 'Variant', ], ], ], 'InterpolatedAssetPropertyValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'InterpolatedAssetPropertyValue', ], ], 'InterpolationType' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'Interval' => [ 'type' => 'string', 'max' => 23, 'min' => 2, ], 'IntervalInSeconds' => [ 'type' => 'long', 'max' => 320000000, 'min' => 1, ], 'IntervalWindowInSeconds' => [ 'type' => 'long', 'max' => 320000000, 'min' => 1, ], 'InvalidRequestException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'KmsKeyId' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'LimitExceededException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 410, ], 'exception' => true, ], 'ListAccessPoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'identityType' => [ 'shape' => 'IdentityType', 'location' => 'querystring', 'locationName' => 'identityType', ], 'identityId' => [ 'shape' => 'IdentityId', 'location' => 'querystring', 'locationName' => 'identityId', ], 'resourceType' => [ 'shape' => 'ResourceType', 'location' => 'querystring', 'locationName' => 'resourceType', ], 'resourceId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'resourceId', ], 'iamArn' => [ 'shape' => 'ARN', 'location' => 'querystring', 'locationName' => 'iamArn', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAccessPoliciesResponse' => [ 'type' => 'structure', 'required' => [ 'accessPolicySummaries', ], 'members' => [ 'accessPolicySummaries' => [ 'shape' => 'AccessPolicySummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAssetModelsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAssetModelsResponse' => [ 'type' => 'structure', 'required' => [ 'assetModelSummaries', ], 'members' => [ 'assetModelSummaries' => [ 'shape' => 'AssetModelSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAssetRelationshipsRequest' => [ 'type' => 'structure', 'required' => [ 'assetId', 'traversalType', ], 'members' => [ 'assetId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'assetId', ], 'traversalType' => [ 'shape' => 'TraversalType', 'location' => 'querystring', 'locationName' => 'traversalType', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAssetRelationshipsResponse' => [ 'type' => 'structure', 'required' => [ 'assetRelationshipSummaries', ], 'members' => [ 'assetRelationshipSummaries' => [ 'shape' => 'AssetRelationshipSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAssetsFilter' => [ 'type' => 'string', 'enum' => [ 'ALL', 'TOP_LEVEL', ], ], 'ListAssetsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'assetModelId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'assetModelId', ], 'filter' => [ 'shape' => 'ListAssetsFilter', 'location' => 'querystring', 'locationName' => 'filter', ], ], ], 'ListAssetsResponse' => [ 'type' => 'structure', 'required' => [ 'assetSummaries', ], 'members' => [ 'assetSummaries' => [ 'shape' => 'AssetSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAssociatedAssetsRequest' => [ 'type' => 'structure', 'required' => [ 'assetId', ], 'members' => [ 'assetId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'assetId', ], 'hierarchyId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'hierarchyId', ], 'traversalDirection' => [ 'shape' => 'TraversalDirection', 'location' => 'querystring', 'locationName' => 'traversalDirection', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAssociatedAssetsResponse' => [ 'type' => 'structure', 'required' => [ 'assetSummaries', ], 'members' => [ 'assetSummaries' => [ 'shape' => 'AssociatedAssetsSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDashboardsRequest' => [ 'type' => 'structure', 'required' => [ 'projectId', ], 'members' => [ 'projectId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'projectId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListDashboardsResponse' => [ 'type' => 'structure', 'required' => [ 'dashboardSummaries', ], 'members' => [ 'dashboardSummaries' => [ 'shape' => 'DashboardSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListGatewaysRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListGatewaysResponse' => [ 'type' => 'structure', 'required' => [ 'gatewaySummaries', ], 'members' => [ 'gatewaySummaries' => [ 'shape' => 'GatewaySummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPortalsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListPortalsResponse' => [ 'type' => 'structure', 'members' => [ 'portalSummaries' => [ 'shape' => 'PortalSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListProjectAssetsRequest' => [ 'type' => 'structure', 'required' => [ 'projectId', ], 'members' => [ 'projectId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'projectId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListProjectAssetsResponse' => [ 'type' => 'structure', 'required' => [ 'assetIds', ], 'members' => [ 'assetIds' => [ 'shape' => 'AssetIDs', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListProjectsRequest' => [ 'type' => 'structure', 'required' => [ 'portalId', ], 'members' => [ 'portalId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'portalId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListProjectsResponse' => [ 'type' => 'structure', 'required' => [ 'projectSummaries', ], 'members' => [ 'projectSummaries' => [ 'shape' => 'ProjectSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'querystring', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ListTimeSeriesRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'assetId' => [ 'shape' => 'ID', 'location' => 'querystring', 'locationName' => 'assetId', ], 'aliasPrefix' => [ 'shape' => 'PropertyAlias', 'location' => 'querystring', 'locationName' => 'aliasPrefix', ], 'timeSeriesType' => [ 'shape' => 'ListTimeSeriesType', 'location' => 'querystring', 'locationName' => 'timeSeriesType', ], ], ], 'ListTimeSeriesResponse' => [ 'type' => 'structure', 'required' => [ 'TimeSeriesSummaries', ], 'members' => [ 'TimeSeriesSummaries' => [ 'shape' => 'TimeSeriesSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTimeSeriesType' => [ 'type' => 'string', 'enum' => [ 'ASSOCIATED', 'DISASSOCIATED', ], ], 'LoggingLevel' => [ 'type' => 'string', 'enum' => [ 'ERROR', 'INFO', 'OFF', ], ], 'LoggingOptions' => [ 'type' => 'structure', 'required' => [ 'level', ], 'members' => [ 'level' => [ 'shape' => 'LoggingLevel', ], ], ], 'Macro' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[^\\u0000-\\u001F\\u007F]+', ], 'MaxInterpolatedResults' => [ 'type' => 'integer', 'min' => 1, ], 'MaxResults' => [ 'type' => 'integer', 'max' => 250, 'min' => 1, ], 'Measurement' => [ 'type' => 'structure', 'members' => [ 'processingConfig' => [ 'shape' => 'MeasurementProcessingConfig', ], ], ], 'MeasurementProcessingConfig' => [ 'type' => 'structure', 'required' => [ 'forwardingConfig', ], 'members' => [ 'forwardingConfig' => [ 'shape' => 'ForwardingConfig', ], ], ], 'Metric' => [ 'type' => 'structure', 'required' => [ 'expression', 'variables', 'window', ], 'members' => [ 'expression' => [ 'shape' => 'Expression', ], 'variables' => [ 'shape' => 'ExpressionVariables', ], 'window' => [ 'shape' => 'MetricWindow', ], 'processingConfig' => [ 'shape' => 'MetricProcessingConfig', ], ], ], 'MetricProcessingConfig' => [ 'type' => 'structure', 'required' => [ 'computeLocation', ], 'members' => [ 'computeLocation' => [ 'shape' => 'ComputeLocation', ], ], ], 'MetricWindow' => [ 'type' => 'structure', 'members' => [ 'tumbling' => [ 'shape' => 'TumblingWindow', ], ], ], 'MonitorErrorCode' => [ 'type' => 'string', 'enum' => [ 'INTERNAL_FAILURE', 'VALIDATION_ERROR', 'LIMIT_EXCEEDED', ], ], 'MonitorErrorDetails' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'MonitorErrorCode', ], 'message' => [ 'shape' => 'MonitorErrorMessage', ], ], ], 'MonitorErrorMessage' => [ 'type' => 'string', ], 'MultiLayerStorage' => [ 'type' => 'structure', 'required' => [ 'customerManagedS3Storage', ], 'members' => [ 'customerManagedS3Storage' => [ 'shape' => 'CustomerManagedS3Storage', ], ], ], 'Name' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[^\\u0000-\\u001F\\u007F]+', ], 'NextToken' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '[A-Za-z0-9+/=]+', ], 'NumberOfDays' => [ 'type' => 'integer', 'min' => 30, ], 'Offset' => [ 'type' => 'string', 'max' => 25, 'min' => 2, ], 'OffsetInNanos' => [ 'type' => 'integer', 'max' => 999999999, 'min' => 0, ], 'Permission' => [ 'type' => 'string', 'enum' => [ 'ADMINISTRATOR', 'VIEWER', ], ], 'PortalClientId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[!-~]*', ], 'PortalResource' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], ], ], 'PortalState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'UPDATING', 'DELETING', 'ACTIVE', 'FAILED', ], ], 'PortalStatus' => [ 'type' => 'structure', 'required' => [ 'state', ], 'members' => [ 'state' => [ 'shape' => 'PortalState', ], 'error' => [ 'shape' => 'MonitorErrorDetails', ], ], ], 'PortalSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'PortalSummary', ], ], 'PortalSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'name', 'startUrl', 'status', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'name' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'startUrl' => [ 'shape' => 'Url', ], 'creationDate' => [ 'shape' => 'Timestamp', ], 'lastUpdateDate' => [ 'shape' => 'Timestamp', ], 'roleArn' => [ 'shape' => 'ARN', ], 'status' => [ 'shape' => 'PortalStatus', ], ], ], 'ProjectResource' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], ], ], 'ProjectSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProjectSummary', ], ], 'ProjectSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'name', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'name' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'creationDate' => [ 'shape' => 'Timestamp', ], 'lastUpdateDate' => [ 'shape' => 'Timestamp', ], ], ], 'Property' => [ 'type' => 'structure', 'required' => [ 'id', 'name', 'dataType', ], 'members' => [ 'id' => [ 'shape' => 'ID', ], 'name' => [ 'shape' => 'Name', ], 'alias' => [ 'shape' => 'PropertyAlias', ], 'notification' => [ 'shape' => 'PropertyNotification', ], 'dataType' => [ 'shape' => 'PropertyDataType', ], 'unit' => [ 'shape' => 'PropertyUnit', ], 'type' => [ 'shape' => 'PropertyType', ], ], ], 'PropertyAlias' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[^\\u0000-\\u001F\\u007F]+', ], 'PropertyDataType' => [ 'type' => 'string', 'enum' => [ 'STRING', 'INTEGER', 'DOUBLE', 'BOOLEAN', 'STRUCT', ], ], 'PropertyNotification' => [ 'type' => 'structure', 'required' => [ 'topic', 'state', ], 'members' => [ 'topic' => [ 'shape' => 'PropertyNotificationTopic', ], 'state' => [ 'shape' => 'PropertyNotificationState', ], ], ], 'PropertyNotificationState' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'PropertyNotificationTopic' => [ 'type' => 'string', ], 'PropertyType' => [ 'type' => 'structure', 'members' => [ 'attribute' => [ 'shape' => 'Attribute', ], 'measurement' => [ 'shape' => 'Measurement', ], 'transform' => [ 'shape' => 'Transform', ], 'metric' => [ 'shape' => 'Metric', ], ], ], 'PropertyUnit' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[^\\u0000-\\u001F\\u007F]+', ], 'PropertyValueBooleanValue' => [ 'type' => 'boolean', ], 'PropertyValueDoubleValue' => [ 'type' => 'double', ], 'PropertyValueIntegerValue' => [ 'type' => 'integer', ], 'PropertyValueStringValue' => [ 'type' => 'string', 'pattern' => '[^\\u0000-\\u001F\\u007F]+', ], 'PutAssetPropertyValueEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'PutAssetPropertyValueEntry', ], ], 'PutAssetPropertyValueEntry' => [ 'type' => 'structure', 'required' => [ 'entryId', 'propertyValues', ], 'members' => [ 'entryId' => [ 'shape' => 'EntryId', ], 'assetId' => [ 'shape' => 'ID', ], 'propertyId' => [ 'shape' => 'ID', ], 'propertyAlias' => [ 'shape' => 'AssetPropertyAlias', ], 'propertyValues' => [ 'shape' => 'AssetPropertyValues', ], ], ], 'PutDefaultEncryptionConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'encryptionType', ], 'members' => [ 'encryptionType' => [ 'shape' => 'EncryptionType', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], ], ], 'PutDefaultEncryptionConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'encryptionType', 'configurationStatus', ], 'members' => [ 'encryptionType' => [ 'shape' => 'EncryptionType', ], 'kmsKeyArn' => [ 'shape' => 'ARN', ], 'configurationStatus' => [ 'shape' => 'ConfigurationStatus', ], ], ], 'PutLoggingOptionsRequest' => [ 'type' => 'structure', 'required' => [ 'loggingOptions', ], 'members' => [ 'loggingOptions' => [ 'shape' => 'LoggingOptions', ], ], ], 'PutLoggingOptionsResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutStorageConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'storageType', ], 'members' => [ 'storageType' => [ 'shape' => 'StorageType', ], 'multiLayerStorage' => [ 'shape' => 'MultiLayerStorage', ], 'disassociatedDataStorage' => [ 'shape' => 'DisassociatedDataStorageState', ], 'retentionPeriod' => [ 'shape' => 'RetentionPeriod', ], ], ], 'PutStorageConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'storageType', 'configurationStatus', ], 'members' => [ 'storageType' => [ 'shape' => 'StorageType', ], 'multiLayerStorage' => [ 'shape' => 'MultiLayerStorage', ], 'disassociatedDataStorage' => [ 'shape' => 'DisassociatedDataStorageState', ], 'retentionPeriod' => [ 'shape' => 'RetentionPeriod', ], 'configurationStatus' => [ 'shape' => 'ConfigurationStatus', ], ], ], 'Qualities' => [ 'type' => 'list', 'member' => [ 'shape' => 'Quality', ], 'max' => 1, 'min' => 1, ], 'Quality' => [ 'type' => 'string', 'enum' => [ 'GOOD', 'BAD', 'UNCERTAIN', ], ], 'Resolution' => [ 'type' => 'string', 'max' => 2, 'min' => 2, 'pattern' => '1m|1h|1d', ], 'Resource' => [ 'type' => 'structure', 'members' => [ 'portal' => [ 'shape' => 'PortalResource', ], 'project' => [ 'shape' => 'ProjectResource', ], ], ], 'ResourceAlreadyExistsException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceArn', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceArn' => [ 'shape' => 'ResourceArn', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ResourceArn' => [ 'type' => 'string', ], 'ResourceId' => [ 'type' => 'string', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'PORTAL', 'PROJECT', ], ], 'RetentionPeriod' => [ 'type' => 'structure', 'members' => [ 'numberOfDays' => [ 'shape' => 'NumberOfDays', ], 'unlimited' => [ 'shape' => 'Unlimited', ], ], ], 'SSOApplicationId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[!-~]*', ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], 'StorageType' => [ 'type' => 'string', 'enum' => [ 'SITEWISE_DEFAULT_STORAGE', 'MULTI_LAYER_STORAGE', ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'querystring', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'TimeInNanos' => [ 'type' => 'structure', 'required' => [ 'timeInSeconds', ], 'members' => [ 'timeInSeconds' => [ 'shape' => 'TimeInSeconds', ], 'offsetInNanos' => [ 'shape' => 'OffsetInNanos', ], ], ], 'TimeInSeconds' => [ 'type' => 'long', 'max' => 31556889864403199, 'min' => 1, ], 'TimeOrdering' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'TimeSeriesId' => [ 'type' => 'string', 'max' => 73, 'min' => 36, ], 'TimeSeriesSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'TimeSeriesSummary', ], ], 'TimeSeriesSummary' => [ 'type' => 'structure', 'required' => [ 'timeSeriesId', 'dataType', 'timeSeriesCreationDate', 'timeSeriesLastUpdateDate', ], 'members' => [ 'assetId' => [ 'shape' => 'ID', ], 'propertyId' => [ 'shape' => 'ID', ], 'alias' => [ 'shape' => 'PropertyAlias', ], 'timeSeriesId' => [ 'shape' => 'TimeSeriesId', ], 'dataType' => [ 'shape' => 'PropertyDataType', ], 'dataTypeSpec' => [ 'shape' => 'Name', ], 'timeSeriesCreationDate' => [ 'shape' => 'Timestamp', ], 'timeSeriesLastUpdateDate' => [ 'shape' => 'Timestamp', ], ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'Timestamps' => [ 'type' => 'list', 'member' => [ 'shape' => 'TimeInNanos', ], ], 'TooManyTagsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], 'resourceName' => [ 'shape' => 'AmazonResourceName', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'Transform' => [ 'type' => 'structure', 'required' => [ 'expression', 'variables', ], 'members' => [ 'expression' => [ 'shape' => 'Expression', ], 'variables' => [ 'shape' => 'ExpressionVariables', ], 'processingConfig' => [ 'shape' => 'TransformProcessingConfig', ], ], ], 'TransformProcessingConfig' => [ 'type' => 'structure', 'required' => [ 'computeLocation', ], 'members' => [ 'computeLocation' => [ 'shape' => 'ComputeLocation', ], 'forwardingConfig' => [ 'shape' => 'ForwardingConfig', ], ], ], 'TraversalDirection' => [ 'type' => 'string', 'enum' => [ 'PARENT', 'CHILD', ], ], 'TraversalType' => [ 'type' => 'string', 'enum' => [ 'PATH_TO_ROOT', ], ], 'TumblingWindow' => [ 'type' => 'structure', 'required' => [ 'interval', ], 'members' => [ 'interval' => [ 'shape' => 'Interval', ], 'offset' => [ 'shape' => 'Offset', ], ], ], 'UnauthorizedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], 'Unlimited' => [ 'type' => 'boolean', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'querystring', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAccessPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'accessPolicyId', 'accessPolicyIdentity', 'accessPolicyResource', 'accessPolicyPermission', ], 'members' => [ 'accessPolicyId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'accessPolicyId', ], 'accessPolicyIdentity' => [ 'shape' => 'Identity', ], 'accessPolicyResource' => [ 'shape' => 'Resource', ], 'accessPolicyPermission' => [ 'shape' => 'Permission', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'UpdateAccessPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAssetModelRequest' => [ 'type' => 'structure', 'required' => [ 'assetModelId', 'assetModelName', ], 'members' => [ 'assetModelId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'assetModelId', ], 'assetModelName' => [ 'shape' => 'Name', ], 'assetModelDescription' => [ 'shape' => 'Description', ], 'assetModelProperties' => [ 'shape' => 'AssetModelProperties', ], 'assetModelHierarchies' => [ 'shape' => 'AssetModelHierarchies', ], 'assetModelCompositeModels' => [ 'shape' => 'AssetModelCompositeModels', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'UpdateAssetModelResponse' => [ 'type' => 'structure', 'required' => [ 'assetModelStatus', ], 'members' => [ 'assetModelStatus' => [ 'shape' => 'AssetModelStatus', ], ], ], 'UpdateAssetPropertyRequest' => [ 'type' => 'structure', 'required' => [ 'assetId', 'propertyId', ], 'members' => [ 'assetId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'assetId', ], 'propertyId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'propertyId', ], 'propertyAlias' => [ 'shape' => 'PropertyAlias', ], 'propertyNotificationState' => [ 'shape' => 'PropertyNotificationState', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'UpdateAssetRequest' => [ 'type' => 'structure', 'required' => [ 'assetId', 'assetName', ], 'members' => [ 'assetId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'assetId', ], 'assetName' => [ 'shape' => 'Name', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'UpdateAssetResponse' => [ 'type' => 'structure', 'required' => [ 'assetStatus', ], 'members' => [ 'assetStatus' => [ 'shape' => 'AssetStatus', ], ], ], 'UpdateDashboardRequest' => [ 'type' => 'structure', 'required' => [ 'dashboardId', 'dashboardName', 'dashboardDefinition', ], 'members' => [ 'dashboardId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'dashboardId', ], 'dashboardName' => [ 'shape' => 'Name', ], 'dashboardDescription' => [ 'shape' => 'Description', ], 'dashboardDefinition' => [ 'shape' => 'DashboardDefinition', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'UpdateDashboardResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateGatewayCapabilityConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'gatewayId', 'capabilityNamespace', 'capabilityConfiguration', ], 'members' => [ 'gatewayId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'gatewayId', ], 'capabilityNamespace' => [ 'shape' => 'CapabilityNamespace', ], 'capabilityConfiguration' => [ 'shape' => 'CapabilityConfiguration', ], ], ], 'UpdateGatewayCapabilityConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'capabilityNamespace', 'capabilitySyncStatus', ], 'members' => [ 'capabilityNamespace' => [ 'shape' => 'CapabilityNamespace', ], 'capabilitySyncStatus' => [ 'shape' => 'CapabilitySyncStatus', ], ], ], 'UpdateGatewayRequest' => [ 'type' => 'structure', 'required' => [ 'gatewayId', 'gatewayName', ], 'members' => [ 'gatewayId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'gatewayId', ], 'gatewayName' => [ 'shape' => 'Name', ], ], ], 'UpdatePortalRequest' => [ 'type' => 'structure', 'required' => [ 'portalId', 'portalName', 'portalContactEmail', 'roleArn', ], 'members' => [ 'portalId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'portalId', ], 'portalName' => [ 'shape' => 'Name', ], 'portalDescription' => [ 'shape' => 'Description', ], 'portalContactEmail' => [ 'shape' => 'Email', ], 'portalLogoImage' => [ 'shape' => 'Image', ], 'roleArn' => [ 'shape' => 'ARN', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'notificationSenderEmail' => [ 'shape' => 'Email', ], 'alarms' => [ 'shape' => 'Alarms', ], ], ], 'UpdatePortalResponse' => [ 'type' => 'structure', 'required' => [ 'portalStatus', ], 'members' => [ 'portalStatus' => [ 'shape' => 'PortalStatus', ], ], ], 'UpdateProjectRequest' => [ 'type' => 'structure', 'required' => [ 'projectId', 'projectName', ], 'members' => [ 'projectId' => [ 'shape' => 'ID', 'location' => 'uri', 'locationName' => 'projectId', ], 'projectName' => [ 'shape' => 'Name', ], 'projectDescription' => [ 'shape' => 'Description', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'UpdateProjectResponse' => [ 'type' => 'structure', 'members' => [], ], 'Url' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^(http|https)\\://\\S+', ], 'UserIdentity' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'IdentityId', ], ], ], 'VariableName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-z][a-z0-9_]*$', ], 'VariableValue' => [ 'type' => 'structure', 'required' => [ 'propertyId', ], 'members' => [ 'propertyId' => [ 'shape' => 'Macro', ], 'hierarchyId' => [ 'shape' => 'Macro', ], ], ], 'Variant' => [ 'type' => 'structure', 'members' => [ 'stringValue' => [ 'shape' => 'PropertyValueStringValue', ], 'integerValue' => [ 'shape' => 'PropertyValueIntegerValue', ], 'doubleValue' => [ 'shape' => 'PropertyValueDoubleValue', ], 'booleanValue' => [ 'shape' => 'PropertyValueBooleanValue', ], ], ], ],];
