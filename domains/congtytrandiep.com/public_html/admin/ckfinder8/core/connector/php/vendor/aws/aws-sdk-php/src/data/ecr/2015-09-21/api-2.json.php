<?php
// This file was auto-generated from sdk-root/src/data/ecr/2015-09-21/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2015-09-21', 'endpointPrefix' => 'api.ecr', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceAbbreviation' => 'Amazon ECR', 'serviceFullName' => 'Amazon EC2 Container Registry', 'serviceId' => 'ECR', 'signatureVersion' => 'v4', 'signingName' => 'ecr', 'targetPrefix' => 'AmazonEC2ContainerRegistry_V20150921', 'uid' => 'ecr-2015-09-21', ], 'operations' => [ 'BatchCheckLayerAvailability' => [ 'name' => 'BatchCheckLayerAvailability', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchCheckLayerAvailabilityRequest', ], 'output' => [ 'shape' => 'BatchCheckLayerAvailabilityResponse', ], 'errors' => [ [ 'shape' => 'RepositoryNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ServerException', ], ], ], 'BatchDeleteImage' => [ 'name' => 'BatchDeleteImage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDeleteImageRequest', ], 'output' => [ 'shape' => 'BatchDeleteImageResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'RepositoryNotFoundException', ], ], ], 'BatchGetImage' => [ 'name' => 'BatchGetImage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetImageRequest', ], 'output' => [ 'shape' => 'BatchGetImageResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'RepositoryNotFoundException', ], ], ], 'BatchGetRepositoryScanningConfiguration' => [ 'name' => 'BatchGetRepositoryScanningConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetRepositoryScanningConfigurationRequest', ], 'output' => [ 'shape' => 'BatchGetRepositoryScanningConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'RepositoryNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'CompleteLayerUpload' => [ 'name' => 'CompleteLayerUpload', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CompleteLayerUploadRequest', ], 'output' => [ 'shape' => 'CompleteLayerUploadResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'RepositoryNotFoundException', ], [ 'shape' => 'UploadNotFoundException', ], [ 'shape' => 'InvalidLayerException', ], [ 'shape' => 'LayerPartTooSmallException', ], [ 'shape' => 'LayerAlreadyExistsException', ], [ 'shape' => 'EmptyUploadException', ], [ 'shape' => 'KmsException', ], ], ], 'CreatePullThroughCacheRule' => [ 'name' => 'CreatePullThroughCacheRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePullThroughCacheRuleRequest', ], 'output' => [ 'shape' => 'CreatePullThroughCacheRuleResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PullThroughCacheRuleAlreadyExistsException', ], [ 'shape' => 'UnsupportedUpstreamRegistryException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateRepository' => [ 'name' => 'CreateRepository', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRepositoryRequest', ], 'output' => [ 'shape' => 'CreateRepositoryResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidTagParameterException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'RepositoryAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'KmsException', ], ], ], 'DeleteLifecyclePolicy' => [ 'name' => 'DeleteLifecyclePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteLifecyclePolicyRequest', ], 'output' => [ 'shape' => 'DeleteLifecyclePolicyResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'RepositoryNotFoundException', ], [ 'shape' => 'LifecyclePolicyNotFoundException', ], ], ], 'DeletePullThroughCacheRule' => [ 'name' => 'DeletePullThroughCacheRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePullThroughCacheRuleRequest', ], 'output' => [ 'shape' => 'DeletePullThroughCacheRuleResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PullThroughCacheRuleNotFoundException', ], ], ], 'DeleteRegistryPolicy' => [ 'name' => 'DeleteRegistryPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRegistryPolicyRequest', ], 'output' => [ 'shape' => 'DeleteRegistryPolicyResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'RegistryPolicyNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteRepository' => [ 'name' => 'DeleteRepository', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRepositoryRequest', ], 'output' => [ 'shape' => 'DeleteRepositoryResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'RepositoryNotFoundException', ], [ 'shape' => 'RepositoryNotEmptyException', ], [ 'shape' => 'KmsException', ], ], ], 'DeleteRepositoryPolicy' => [ 'name' => 'DeleteRepositoryPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRepositoryPolicyRequest', ], 'output' => [ 'shape' => 'DeleteRepositoryPolicyResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'RepositoryNotFoundException', ], [ 'shape' => 'RepositoryPolicyNotFoundException', ], ], ], 'DescribeImageReplicationStatus' => [ 'name' => 'DescribeImageReplicationStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeImageReplicationStatusRequest', ], 'output' => [ 'shape' => 'DescribeImageReplicationStatusResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ImageNotFoundException', ], [ 'shape' => 'RepositoryNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeImageScanFindings' => [ 'name' => 'DescribeImageScanFindings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeImageScanFindingsRequest', ], 'output' => [ 'shape' => 'DescribeImageScanFindingsResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'RepositoryNotFoundException', ], [ 'shape' => 'ImageNotFoundException', ], [ 'shape' => 'ScanNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeImages' => [ 'name' => 'DescribeImages', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeImagesRequest', ], 'output' => [ 'shape' => 'DescribeImagesResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'RepositoryNotFoundException', ], [ 'shape' => 'ImageNotFoundException', ], ], ], 'DescribePullThroughCacheRules' => [ 'name' => 'DescribePullThroughCacheRules', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePullThroughCacheRulesRequest', ], 'output' => [ 'shape' => 'DescribePullThroughCacheRulesResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PullThroughCacheRuleNotFoundException', ], ], ], 'DescribeRegistry' => [ 'name' => 'DescribeRegistry', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRegistryRequest', ], 'output' => [ 'shape' => 'DescribeRegistryResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeRepositories' => [ 'name' => 'DescribeRepositories', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRepositoriesRequest', ], 'output' => [ 'shape' => 'DescribeRepositoriesResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'RepositoryNotFoundException', ], ], ], 'GetAuthorizationToken' => [ 'name' => 'GetAuthorizationToken', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAuthorizationTokenRequest', ], 'output' => [ 'shape' => 'GetAuthorizationTokenResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], ], ], 'GetDownloadUrlForLayer' => [ 'name' => 'GetDownloadUrlForLayer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDownloadUrlForLayerRequest', ], 'output' => [ 'shape' => 'GetDownloadUrlForLayerResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'LayersNotFoundException', ], [ 'shape' => 'LayerInaccessibleException', ], [ 'shape' => 'RepositoryNotFoundException', ], ], ], 'GetLifecyclePolicy' => [ 'name' => 'GetLifecyclePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetLifecyclePolicyRequest', ], 'output' => [ 'shape' => 'GetLifecyclePolicyResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'RepositoryNotFoundException', ], [ 'shape' => 'LifecyclePolicyNotFoundException', ], ], ], 'GetLifecyclePolicyPreview' => [ 'name' => 'GetLifecyclePolicyPreview', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetLifecyclePolicyPreviewRequest', ], 'output' => [ 'shape' => 'GetLifecyclePolicyPreviewResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'RepositoryNotFoundException', ], [ 'shape' => 'LifecyclePolicyPreviewNotFoundException', ], ], ], 'GetRegistryPolicy' => [ 'name' => 'GetRegistryPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRegistryPolicyRequest', ], 'output' => [ 'shape' => 'GetRegistryPolicyResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'RegistryPolicyNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetRegistryScanningConfiguration' => [ 'name' => 'GetRegistryScanningConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRegistryScanningConfigurationRequest', ], 'output' => [ 'shape' => 'GetRegistryScanningConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetRepositoryPolicy' => [ 'name' => 'GetRepositoryPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRepositoryPolicyRequest', ], 'output' => [ 'shape' => 'GetRepositoryPolicyResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'RepositoryNotFoundException', ], [ 'shape' => 'RepositoryPolicyNotFoundException', ], ], ], 'InitiateLayerUpload' => [ 'name' => 'InitiateLayerUpload', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'InitiateLayerUploadRequest', ], 'output' => [ 'shape' => 'InitiateLayerUploadResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'RepositoryNotFoundException', ], [ 'shape' => 'KmsException', ], ], ], 'ListImages' => [ 'name' => 'ListImages', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListImagesRequest', ], 'output' => [ 'shape' => 'ListImagesResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'RepositoryNotFoundException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'RepositoryNotFoundException', ], [ 'shape' => 'ServerException', ], ], ], 'PutImage' => [ 'name' => 'PutImage', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutImageRequest', ], 'output' => [ 'shape' => 'PutImageResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'RepositoryNotFoundException', ], [ 'shape' => 'ImageAlreadyExistsException', ], [ 'shape' => 'LayersNotFoundException', ], [ 'shape' => 'ReferencedImagesNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ImageTagAlreadyExistsException', ], [ 'shape' => 'ImageDigestDoesNotMatchException', ], [ 'shape' => 'KmsException', ], ], ], 'PutImageScanningConfiguration' => [ 'name' => 'PutImageScanningConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutImageScanningConfigurationRequest', ], 'output' => [ 'shape' => 'PutImageScanningConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'RepositoryNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'PutImageTagMutability' => [ 'name' => 'PutImageTagMutability', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutImageTagMutabilityRequest', ], 'output' => [ 'shape' => 'PutImageTagMutabilityResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'RepositoryNotFoundException', ], ], ], 'PutLifecyclePolicy' => [ 'name' => 'PutLifecyclePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutLifecyclePolicyRequest', ], 'output' => [ 'shape' => 'PutLifecyclePolicyResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'RepositoryNotFoundException', ], ], ], 'PutRegistryPolicy' => [ 'name' => 'PutRegistryPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutRegistryPolicyRequest', ], 'output' => [ 'shape' => 'PutRegistryPolicyResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ValidationException', ], ], ], 'PutRegistryScanningConfiguration' => [ 'name' => 'PutRegistryScanningConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutRegistryScanningConfigurationRequest', ], 'output' => [ 'shape' => 'PutRegistryScanningConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ValidationException', ], ], ], 'PutReplicationConfiguration' => [ 'name' => 'PutReplicationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutReplicationConfigurationRequest', ], 'output' => [ 'shape' => 'PutReplicationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ValidationException', ], ], ], 'SetRepositoryPolicy' => [ 'name' => 'SetRepositoryPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetRepositoryPolicyRequest', ], 'output' => [ 'shape' => 'SetRepositoryPolicyResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'RepositoryNotFoundException', ], ], ], 'StartImageScan' => [ 'name' => 'StartImageScan', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartImageScanRequest', ], 'output' => [ 'shape' => 'StartImageScanResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnsupportedImageTypeException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'RepositoryNotFoundException', ], [ 'shape' => 'ImageNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'StartLifecyclePolicyPreview' => [ 'name' => 'StartLifecyclePolicyPreview', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartLifecyclePolicyPreviewRequest', ], 'output' => [ 'shape' => 'StartLifecyclePolicyPreviewResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'RepositoryNotFoundException', ], [ 'shape' => 'LifecyclePolicyNotFoundException', ], [ 'shape' => 'LifecyclePolicyPreviewInProgressException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidTagParameterException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'RepositoryNotFoundException', ], [ 'shape' => 'ServerException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidTagParameterException', ], [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'RepositoryNotFoundException', ], [ 'shape' => 'ServerException', ], ], ], 'UploadLayerPart' => [ 'name' => 'UploadLayerPart', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UploadLayerPartRequest', ], 'output' => [ 'shape' => 'UploadLayerPartResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidLayerPartException', ], [ 'shape' => 'RepositoryNotFoundException', ], [ 'shape' => 'UploadNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'KmsException', ], ], ], ], 'shapes' => [ 'Arch' => [ 'type' => 'string', ], 'Arn' => [ 'type' => 'string', ], 'Attribute' => [ 'type' => 'structure', 'required' => [ 'key', ], 'members' => [ 'key' => [ 'shape' => 'AttributeKey', ], 'value' => [ 'shape' => 'AttributeValue', ], ], ], 'AttributeKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'AttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Attribute', ], 'max' => 50, 'min' => 0, ], 'AttributeValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'Author' => [ 'type' => 'string', ], 'AuthorizationData' => [ 'type' => 'structure', 'members' => [ 'authorizationToken' => [ 'shape' => 'Base64', ], 'expiresAt' => [ 'shape' => 'ExpirationTimestamp', ], 'proxyEndpoint' => [ 'shape' => 'ProxyEndpoint', ], ], ], 'AuthorizationDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AuthorizationData', ], ], 'AwsEcrContainerImageDetails' => [ 'type' => 'structure', 'members' => [ 'architecture' => [ 'shape' => 'Arch', ], 'author' => [ 'shape' => 'Author', ], 'imageHash' => [ 'shape' => 'ImageDigest', ], 'imageTags' => [ 'shape' => 'ImageTagsList', ], 'platform' => [ 'shape' => 'Platform', ], 'pushedAt' => [ 'shape' => 'Date', ], 'registry' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], ], ], 'Base64' => [ 'type' => 'string', 'pattern' => '^\\S+$', ], 'BaseScore' => [ 'type' => 'double', ], 'BatchCheckLayerAvailabilityRequest' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'layerDigests', ], 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'layerDigests' => [ 'shape' => 'BatchedOperationLayerDigestList', ], ], ], 'BatchCheckLayerAvailabilityResponse' => [ 'type' => 'structure', 'members' => [ 'layers' => [ 'shape' => 'LayerList', ], 'failures' => [ 'shape' => 'LayerFailureList', ], ], ], 'BatchDeleteImageRequest' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'imageIds', ], 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'imageIds' => [ 'shape' => 'ImageIdentifierList', ], ], ], 'BatchDeleteImageResponse' => [ 'type' => 'structure', 'members' => [ 'imageIds' => [ 'shape' => 'ImageIdentifierList', ], 'failures' => [ 'shape' => 'ImageFailureList', ], ], ], 'BatchGetImageRequest' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'imageIds', ], 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'imageIds' => [ 'shape' => 'ImageIdentifierList', ], 'acceptedMediaTypes' => [ 'shape' => 'MediaTypeList', ], ], ], 'BatchGetImageResponse' => [ 'type' => 'structure', 'members' => [ 'images' => [ 'shape' => 'ImageList', ], 'failures' => [ 'shape' => 'ImageFailureList', ], ], ], 'BatchGetRepositoryScanningConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'repositoryNames', ], 'members' => [ 'repositoryNames' => [ 'shape' => 'ScanningConfigurationRepositoryNameList', ], ], ], 'BatchGetRepositoryScanningConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'scanningConfigurations' => [ 'shape' => 'RepositoryScanningConfigurationList', ], 'failures' => [ 'shape' => 'RepositoryScanningConfigurationFailureList', ], ], ], 'BatchedOperationLayerDigest' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, ], 'BatchedOperationLayerDigestList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchedOperationLayerDigest', ], 'max' => 100, 'min' => 1, ], 'CompleteLayerUploadRequest' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'uploadId', 'layerDigests', ], 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'uploadId' => [ 'shape' => 'UploadId', ], 'layerDigests' => [ 'shape' => 'LayerDigestList', ], ], ], 'CompleteLayerUploadResponse' => [ 'type' => 'structure', 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'uploadId' => [ 'shape' => 'UploadId', ], 'layerDigest' => [ 'shape' => 'LayerDigest', ], ], ], 'CreatePullThroughCacheRuleRequest' => [ 'type' => 'structure', 'required' => [ 'ecrRepositoryPrefix', 'upstreamRegistryUrl', ], 'members' => [ 'ecrRepositoryPrefix' => [ 'shape' => 'PullThroughCacheRuleRepositoryPrefix', ], 'upstreamRegistryUrl' => [ 'shape' => 'Url', ], 'registryId' => [ 'shape' => 'RegistryId', ], ], ], 'CreatePullThroughCacheRuleResponse' => [ 'type' => 'structure', 'members' => [ 'ecrRepositoryPrefix' => [ 'shape' => 'PullThroughCacheRuleRepositoryPrefix', ], 'upstreamRegistryUrl' => [ 'shape' => 'Url', ], 'createdAt' => [ 'shape' => 'CreationTimestamp', ], 'registryId' => [ 'shape' => 'RegistryId', ], ], ], 'CreateRepositoryRequest' => [ 'type' => 'structure', 'required' => [ 'repositoryName', ], 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'tags' => [ 'shape' => 'TagList', ], 'imageTagMutability' => [ 'shape' => 'ImageTagMutability', ], 'imageScanningConfiguration' => [ 'shape' => 'ImageScanningConfiguration', ], 'encryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], ], ], 'CreateRepositoryResponse' => [ 'type' => 'structure', 'members' => [ 'repository' => [ 'shape' => 'Repository', ], ], ], 'CreationTimestamp' => [ 'type' => 'timestamp', ], 'CvssScore' => [ 'type' => 'structure', 'members' => [ 'baseScore' => [ 'shape' => 'BaseScore', ], 'scoringVector' => [ 'shape' => 'ScoringVector', ], 'source' => [ 'shape' => 'Source', ], 'version' => [ 'shape' => 'Version', ], ], ], 'CvssScoreAdjustment' => [ 'type' => 'structure', 'members' => [ 'metric' => [ 'shape' => 'Metric', ], 'reason' => [ 'shape' => 'Reason', ], ], ], 'CvssScoreAdjustmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CvssScoreAdjustment', ], ], 'CvssScoreDetails' => [ 'type' => 'structure', 'members' => [ 'adjustments' => [ 'shape' => 'CvssScoreAdjustmentList', ], 'score' => [ 'shape' => 'Score', ], 'scoreSource' => [ 'shape' => 'Source', ], 'scoringVector' => [ 'shape' => 'ScoringVector', ], 'version' => [ 'shape' => 'Version', ], ], ], 'CvssScoreList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CvssScore', ], ], 'Date' => [ 'type' => 'timestamp', ], 'DeleteLifecyclePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'repositoryName', ], 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], ], ], 'DeleteLifecyclePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'lifecyclePolicyText' => [ 'shape' => 'LifecyclePolicyText', ], 'lastEvaluatedAt' => [ 'shape' => 'EvaluationTimestamp', ], ], ], 'DeletePullThroughCacheRuleRequest' => [ 'type' => 'structure', 'required' => [ 'ecrRepositoryPrefix', ], 'members' => [ 'ecrRepositoryPrefix' => [ 'shape' => 'PullThroughCacheRuleRepositoryPrefix', ], 'registryId' => [ 'shape' => 'RegistryId', ], ], ], 'DeletePullThroughCacheRuleResponse' => [ 'type' => 'structure', 'members' => [ 'ecrRepositoryPrefix' => [ 'shape' => 'PullThroughCacheRuleRepositoryPrefix', ], 'upstreamRegistryUrl' => [ 'shape' => 'Url', ], 'createdAt' => [ 'shape' => 'CreationTimestamp', ], 'registryId' => [ 'shape' => 'RegistryId', ], ], ], 'DeleteRegistryPolicyRequest' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRegistryPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'policyText' => [ 'shape' => 'RegistryPolicyText', ], ], ], 'DeleteRepositoryPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'repositoryName', ], 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], ], ], 'DeleteRepositoryPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'policyText' => [ 'shape' => 'RepositoryPolicyText', ], ], ], 'DeleteRepositoryRequest' => [ 'type' => 'structure', 'required' => [ 'repositoryName', ], 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'force' => [ 'shape' => 'ForceFlag', ], ], ], 'DeleteRepositoryResponse' => [ 'type' => 'structure', 'members' => [ 'repository' => [ 'shape' => 'Repository', ], ], ], 'DescribeImageReplicationStatusRequest' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'imageId', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'imageId' => [ 'shape' => 'ImageIdentifier', ], 'registryId' => [ 'shape' => 'RegistryId', ], ], ], 'DescribeImageReplicationStatusResponse' => [ 'type' => 'structure', 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'imageId' => [ 'shape' => 'ImageIdentifier', ], 'replicationStatuses' => [ 'shape' => 'ImageReplicationStatusList', ], ], ], 'DescribeImageScanFindingsRequest' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'imageId', ], 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'imageId' => [ 'shape' => 'ImageIdentifier', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'DescribeImageScanFindingsResponse' => [ 'type' => 'structure', 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'imageId' => [ 'shape' => 'ImageIdentifier', ], 'imageScanStatus' => [ 'shape' => 'ImageScanStatus', ], 'imageScanFindings' => [ 'shape' => 'ImageScanFindings', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeImagesFilter' => [ 'type' => 'structure', 'members' => [ 'tagStatus' => [ 'shape' => 'TagStatus', ], ], ], 'DescribeImagesRequest' => [ 'type' => 'structure', 'required' => [ 'repositoryName', ], 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'imageIds' => [ 'shape' => 'ImageIdentifierList', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'filter' => [ 'shape' => 'DescribeImagesFilter', ], ], ], 'DescribeImagesResponse' => [ 'type' => 'structure', 'members' => [ 'imageDetails' => [ 'shape' => 'ImageDetailList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribePullThroughCacheRulesRequest' => [ 'type' => 'structure', 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'ecrRepositoryPrefixes' => [ 'shape' => 'PullThroughCacheRuleRepositoryPrefixList', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'DescribePullThroughCacheRulesResponse' => [ 'type' => 'structure', 'members' => [ 'pullThroughCacheRules' => [ 'shape' => 'PullThroughCacheRuleList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeRegistryRequest' => [ 'type' => 'structure', 'members' => [], ], 'DescribeRegistryResponse' => [ 'type' => 'structure', 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'replicationConfiguration' => [ 'shape' => 'ReplicationConfiguration', ], ], ], 'DescribeRepositoriesRequest' => [ 'type' => 'structure', 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryNames' => [ 'shape' => 'RepositoryNameList', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'DescribeRepositoriesResponse' => [ 'type' => 'structure', 'members' => [ 'repositories' => [ 'shape' => 'RepositoryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'EmptyUploadException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'EncryptionConfiguration' => [ 'type' => 'structure', 'required' => [ 'encryptionType', ], 'members' => [ 'encryptionType' => [ 'shape' => 'EncryptionType', ], 'kmsKey' => [ 'shape' => 'KmsKey', ], ], ], 'EncryptionType' => [ 'type' => 'string', 'enum' => [ 'AES256', 'KMS', ], ], 'EnhancedImageScanFinding' => [ 'type' => 'structure', 'members' => [ 'awsAccountId' => [ 'shape' => 'RegistryId', ], 'description' => [ 'shape' => 'FindingDescription', ], 'findingArn' => [ 'shape' => 'FindingArn', ], 'firstObservedAt' => [ 'shape' => 'Date', ], 'lastObservedAt' => [ 'shape' => 'Date', ], 'packageVulnerabilityDetails' => [ 'shape' => 'PackageVulnerabilityDetails', ], 'remediation' => [ 'shape' => 'Remediation', ], 'resources' => [ 'shape' => 'ResourceList', ], 'score' => [ 'shape' => 'Score', ], 'scoreDetails' => [ 'shape' => 'ScoreDetails', ], 'severity' => [ 'shape' => 'Severity', ], 'status' => [ 'shape' => 'Status', ], 'title' => [ 'shape' => 'Title', ], 'type' => [ 'shape' => 'Type', ], 'updatedAt' => [ 'shape' => 'Date', ], ], ], 'EnhancedImageScanFindingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnhancedImageScanFinding', ], ], 'Epoch' => [ 'type' => 'integer', ], 'EvaluationTimestamp' => [ 'type' => 'timestamp', ], 'ExceptionMessage' => [ 'type' => 'string', ], 'ExpirationTimestamp' => [ 'type' => 'timestamp', ], 'FilePath' => [ 'type' => 'string', ], 'FindingArn' => [ 'type' => 'string', ], 'FindingDescription' => [ 'type' => 'string', ], 'FindingName' => [ 'type' => 'string', ], 'FindingSeverity' => [ 'type' => 'string', 'enum' => [ 'INFORMATIONAL', 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL', 'UNDEFINED', ], ], 'FindingSeverityCounts' => [ 'type' => 'map', 'key' => [ 'shape' => 'FindingSeverity', ], 'value' => [ 'shape' => 'SeverityCount', ], ], 'ForceFlag' => [ 'type' => 'boolean', ], 'GetAuthorizationTokenRegistryIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegistryId', ], 'max' => 10, 'min' => 1, ], 'GetAuthorizationTokenRequest' => [ 'type' => 'structure', 'members' => [ 'registryIds' => [ 'shape' => 'GetAuthorizationTokenRegistryIdList', 'deprecated' => true, 'deprecatedMessage' => 'This field is deprecated. The returned authorization token can be used to access any Amazon ECR registry that the IAM principal has access to, specifying a registry ID doesn\'t change the permissions scope of the authorization token.', ], ], ], 'GetAuthorizationTokenResponse' => [ 'type' => 'structure', 'members' => [ 'authorizationData' => [ 'shape' => 'AuthorizationDataList', ], ], ], 'GetDownloadUrlForLayerRequest' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'layerDigest', ], 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'layerDigest' => [ 'shape' => 'LayerDigest', ], ], ], 'GetDownloadUrlForLayerResponse' => [ 'type' => 'structure', 'members' => [ 'downloadUrl' => [ 'shape' => 'Url', ], 'layerDigest' => [ 'shape' => 'LayerDigest', ], ], ], 'GetLifecyclePolicyPreviewRequest' => [ 'type' => 'structure', 'required' => [ 'repositoryName', ], 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'imageIds' => [ 'shape' => 'ImageIdentifierList', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'LifecyclePreviewMaxResults', ], 'filter' => [ 'shape' => 'LifecyclePolicyPreviewFilter', ], ], ], 'GetLifecyclePolicyPreviewResponse' => [ 'type' => 'structure', 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'lifecyclePolicyText' => [ 'shape' => 'LifecyclePolicyText', ], 'status' => [ 'shape' => 'LifecyclePolicyPreviewStatus', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'previewResults' => [ 'shape' => 'LifecyclePolicyPreviewResultList', ], 'summary' => [ 'shape' => 'LifecyclePolicyPreviewSummary', ], ], ], 'GetLifecyclePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'repositoryName', ], 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], ], ], 'GetLifecyclePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'lifecyclePolicyText' => [ 'shape' => 'LifecyclePolicyText', ], 'lastEvaluatedAt' => [ 'shape' => 'EvaluationTimestamp', ], ], ], 'GetRegistryPolicyRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetRegistryPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'policyText' => [ 'shape' => 'RegistryPolicyText', ], ], ], 'GetRegistryScanningConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetRegistryScanningConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'scanningConfiguration' => [ 'shape' => 'RegistryScanningConfiguration', ], ], ], 'GetRepositoryPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'repositoryName', ], 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], ], ], 'GetRepositoryPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'policyText' => [ 'shape' => 'RepositoryPolicyText', ], ], ], 'Image' => [ 'type' => 'structure', 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'imageId' => [ 'shape' => 'ImageIdentifier', ], 'imageManifest' => [ 'shape' => 'ImageManifest', ], 'imageManifestMediaType' => [ 'shape' => 'MediaType', ], ], ], 'ImageActionType' => [ 'type' => 'string', 'enum' => [ 'EXPIRE', ], ], 'ImageAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'ImageCount' => [ 'type' => 'integer', 'min' => 0, ], 'ImageDetail' => [ 'type' => 'structure', 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'imageDigest' => [ 'shape' => 'ImageDigest', ], 'imageTags' => [ 'shape' => 'ImageTagList', ], 'imageSizeInBytes' => [ 'shape' => 'ImageSizeInBytes', ], 'imagePushedAt' => [ 'shape' => 'PushTimestamp', ], 'imageScanStatus' => [ 'shape' => 'ImageScanStatus', ], 'imageScanFindingsSummary' => [ 'shape' => 'ImageScanFindingsSummary', ], 'imageManifestMediaType' => [ 'shape' => 'MediaType', ], 'artifactMediaType' => [ 'shape' => 'MediaType', ], ], ], 'ImageDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImageDetail', ], ], 'ImageDigest' => [ 'type' => 'string', ], 'ImageDigestDoesNotMatchException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'ImageFailure' => [ 'type' => 'structure', 'members' => [ 'imageId' => [ 'shape' => 'ImageIdentifier', ], 'failureCode' => [ 'shape' => 'ImageFailureCode', ], 'failureReason' => [ 'shape' => 'ImageFailureReason', ], ], ], 'ImageFailureCode' => [ 'type' => 'string', 'enum' => [ 'InvalidImageDigest', 'InvalidImageTag', 'ImageTagDoesNotMatchDigest', 'ImageNotFound', 'MissingDigestAndTag', 'ImageReferencedByManifestList', 'KmsError', ], ], 'ImageFailureList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImageFailure', ], ], 'ImageFailureReason' => [ 'type' => 'string', ], 'ImageIdentifier' => [ 'type' => 'structure', 'members' => [ 'imageDigest' => [ 'shape' => 'ImageDigest', ], 'imageTag' => [ 'shape' => 'ImageTag', ], ], ], 'ImageIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImageIdentifier', ], 'max' => 100, 'min' => 1, ], 'ImageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Image', ], ], 'ImageManifest' => [ 'type' => 'string', 'max' => 4194304, 'min' => 1, ], 'ImageNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'ImageReplicationStatus' => [ 'type' => 'structure', 'members' => [ 'region' => [ 'shape' => 'Region', ], 'registryId' => [ 'shape' => 'RegistryId', ], 'status' => [ 'shape' => 'ReplicationStatus', ], 'failureCode' => [ 'shape' => 'ReplicationError', ], ], ], 'ImageReplicationStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImageReplicationStatus', ], ], 'ImageScanFinding' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'FindingName', ], 'description' => [ 'shape' => 'FindingDescription', ], 'uri' => [ 'shape' => 'Url', ], 'severity' => [ 'shape' => 'FindingSeverity', ], 'attributes' => [ 'shape' => 'AttributeList', ], ], ], 'ImageScanFindingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImageScanFinding', ], ], 'ImageScanFindings' => [ 'type' => 'structure', 'members' => [ 'imageScanCompletedAt' => [ 'shape' => 'ScanTimestamp', ], 'vulnerabilitySourceUpdatedAt' => [ 'shape' => 'VulnerabilitySourceUpdateTimestamp', ], 'findingSeverityCounts' => [ 'shape' => 'FindingSeverityCounts', ], 'findings' => [ 'shape' => 'ImageScanFindingList', ], 'enhancedFindings' => [ 'shape' => 'EnhancedImageScanFindingList', ], ], ], 'ImageScanFindingsSummary' => [ 'type' => 'structure', 'members' => [ 'imageScanCompletedAt' => [ 'shape' => 'ScanTimestamp', ], 'vulnerabilitySourceUpdatedAt' => [ 'shape' => 'VulnerabilitySourceUpdateTimestamp', ], 'findingSeverityCounts' => [ 'shape' => 'FindingSeverityCounts', ], ], ], 'ImageScanStatus' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'ScanStatus', ], 'description' => [ 'shape' => 'ScanStatusDescription', ], ], ], 'ImageScanningConfiguration' => [ 'type' => 'structure', 'members' => [ 'scanOnPush' => [ 'shape' => 'ScanOnPushFlag', ], ], ], 'ImageSizeInBytes' => [ 'type' => 'long', ], 'ImageTag' => [ 'type' => 'string', 'max' => 300, 'min' => 1, ], 'ImageTagAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'ImageTagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImageTag', ], ], 'ImageTagMutability' => [ 'type' => 'string', 'enum' => [ 'MUTABLE', 'IMMUTABLE', ], ], 'ImageTagsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImageTag', ], ], 'InitiateLayerUploadRequest' => [ 'type' => 'structure', 'required' => [ 'repositoryName', ], 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], ], ], 'InitiateLayerUploadResponse' => [ 'type' => 'structure', 'members' => [ 'uploadId' => [ 'shape' => 'UploadId', ], 'partSize' => [ 'shape' => 'PartSize', ], ], ], 'InvalidLayerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'InvalidLayerPartException' => [ 'type' => 'structure', 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'uploadId' => [ 'shape' => 'UploadId', ], 'lastValidByteReceived' => [ 'shape' => 'PartSize', ], 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'InvalidParameterException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'InvalidTagParameterException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'KmsError' => [ 'type' => 'string', ], 'KmsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], 'kmsError' => [ 'shape' => 'KmsError', ], ], 'exception' => true, ], 'KmsKey' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'Layer' => [ 'type' => 'structure', 'members' => [ 'layerDigest' => [ 'shape' => 'LayerDigest', ], 'layerAvailability' => [ 'shape' => 'LayerAvailability', ], 'layerSize' => [ 'shape' => 'LayerSizeInBytes', ], 'mediaType' => [ 'shape' => 'MediaType', ], ], ], 'LayerAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'LayerAvailability' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'UNAVAILABLE', ], ], 'LayerDigest' => [ 'type' => 'string', 'pattern' => '[a-zA-Z0-9-_+.]+:[a-fA-F0-9]+', ], 'LayerDigestList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LayerDigest', ], 'max' => 100, 'min' => 1, ], 'LayerFailure' => [ 'type' => 'structure', 'members' => [ 'layerDigest' => [ 'shape' => 'BatchedOperationLayerDigest', ], 'failureCode' => [ 'shape' => 'LayerFailureCode', ], 'failureReason' => [ 'shape' => 'LayerFailureReason', ], ], ], 'LayerFailureCode' => [ 'type' => 'string', 'enum' => [ 'InvalidLayerDigest', 'MissingLayerDigest', ], ], 'LayerFailureList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LayerFailure', ], ], 'LayerFailureReason' => [ 'type' => 'string', ], 'LayerInaccessibleException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'LayerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Layer', ], ], 'LayerPartBlob' => [ 'type' => 'blob', 'max' => 20971520, 'min' => 0, ], 'LayerPartTooSmallException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'LayerSizeInBytes' => [ 'type' => 'long', ], 'LayersNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'LifecyclePolicyNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'LifecyclePolicyPreviewFilter' => [ 'type' => 'structure', 'members' => [ 'tagStatus' => [ 'shape' => 'TagStatus', ], ], ], 'LifecyclePolicyPreviewInProgressException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'LifecyclePolicyPreviewNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'LifecyclePolicyPreviewResult' => [ 'type' => 'structure', 'members' => [ 'imageTags' => [ 'shape' => 'ImageTagList', ], 'imageDigest' => [ 'shape' => 'ImageDigest', ], 'imagePushedAt' => [ 'shape' => 'PushTimestamp', ], 'action' => [ 'shape' => 'LifecyclePolicyRuleAction', ], 'appliedRulePriority' => [ 'shape' => 'LifecyclePolicyRulePriority', ], ], ], 'LifecyclePolicyPreviewResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LifecyclePolicyPreviewResult', ], ], 'LifecyclePolicyPreviewStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'COMPLETE', 'EXPIRED', 'FAILED', ], ], 'LifecyclePolicyPreviewSummary' => [ 'type' => 'structure', 'members' => [ 'expiringImageTotalCount' => [ 'shape' => 'ImageCount', ], ], ], 'LifecyclePolicyRuleAction' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'ImageActionType', ], ], ], 'LifecyclePolicyRulePriority' => [ 'type' => 'integer', 'min' => 1, ], 'LifecyclePolicyText' => [ 'type' => 'string', 'max' => 30720, 'min' => 100, ], 'LifecyclePreviewMaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'ListImagesFilter' => [ 'type' => 'structure', 'members' => [ 'tagStatus' => [ 'shape' => 'TagStatus', ], ], ], 'ListImagesRequest' => [ 'type' => 'structure', 'required' => [ 'repositoryName', ], 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'filter' => [ 'shape' => 'ListImagesFilter', ], ], ], 'ListImagesResponse' => [ 'type' => 'structure', 'members' => [ 'imageIds' => [ 'shape' => 'ImageIdentifierList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagList', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'max' => 1000, 'min' => 1, ], 'MediaType' => [ 'type' => 'string', ], 'MediaTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MediaType', ], 'max' => 100, 'min' => 1, ], 'Metric' => [ 'type' => 'string', ], 'NextToken' => [ 'type' => 'string', ], 'PackageManager' => [ 'type' => 'string', ], 'PackageVulnerabilityDetails' => [ 'type' => 'structure', 'members' => [ 'cvss' => [ 'shape' => 'CvssScoreList', ], 'referenceUrls' => [ 'shape' => 'ReferenceUrlsList', ], 'relatedVulnerabilities' => [ 'shape' => 'RelatedVulnerabilitiesList', ], 'source' => [ 'shape' => 'Source', ], 'sourceUrl' => [ 'shape' => 'Url', ], 'vendorCreatedAt' => [ 'shape' => 'Date', ], 'vendorSeverity' => [ 'shape' => 'Severity', ], 'vendorUpdatedAt' => [ 'shape' => 'Date', ], 'vulnerabilityId' => [ 'shape' => 'VulnerabilityId', ], 'vulnerablePackages' => [ 'shape' => 'VulnerablePackagesList', ], ], ], 'PartSize' => [ 'type' => 'long', 'min' => 0, ], 'Platform' => [ 'type' => 'string', ], 'ProxyEndpoint' => [ 'type' => 'string', ], 'PullThroughCacheRule' => [ 'type' => 'structure', 'members' => [ 'ecrRepositoryPrefix' => [ 'shape' => 'PullThroughCacheRuleRepositoryPrefix', ], 'upstreamRegistryUrl' => [ 'shape' => 'Url', ], 'createdAt' => [ 'shape' => 'CreationTimestamp', ], 'registryId' => [ 'shape' => 'RegistryId', ], ], ], 'PullThroughCacheRuleAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'PullThroughCacheRuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PullThroughCacheRule', ], ], 'PullThroughCacheRuleNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'PullThroughCacheRuleRepositoryPrefix' => [ 'type' => 'string', 'max' => 20, 'min' => 2, 'pattern' => '[a-z0-9]+(?:[._-][a-z0-9]+)*', ], 'PullThroughCacheRuleRepositoryPrefixList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PullThroughCacheRuleRepositoryPrefix', ], 'max' => 100, 'min' => 1, ], 'PushTimestamp' => [ 'type' => 'timestamp', ], 'PutImageRequest' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'imageManifest', ], 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'imageManifest' => [ 'shape' => 'ImageManifest', ], 'imageManifestMediaType' => [ 'shape' => 'MediaType', ], 'imageTag' => [ 'shape' => 'ImageTag', ], 'imageDigest' => [ 'shape' => 'ImageDigest', ], ], ], 'PutImageResponse' => [ 'type' => 'structure', 'members' => [ 'image' => [ 'shape' => 'Image', ], ], ], 'PutImageScanningConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'imageScanningConfiguration', ], 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'imageScanningConfiguration' => [ 'shape' => 'ImageScanningConfiguration', ], ], ], 'PutImageScanningConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'imageScanningConfiguration' => [ 'shape' => 'ImageScanningConfiguration', ], ], ], 'PutImageTagMutabilityRequest' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'imageTagMutability', ], 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'imageTagMutability' => [ 'shape' => 'ImageTagMutability', ], ], ], 'PutImageTagMutabilityResponse' => [ 'type' => 'structure', 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'imageTagMutability' => [ 'shape' => 'ImageTagMutability', ], ], ], 'PutLifecyclePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'lifecyclePolicyText', ], 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'lifecyclePolicyText' => [ 'shape' => 'LifecyclePolicyText', ], ], ], 'PutLifecyclePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'lifecyclePolicyText' => [ 'shape' => 'LifecyclePolicyText', ], ], ], 'PutRegistryPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'policyText', ], 'members' => [ 'policyText' => [ 'shape' => 'RegistryPolicyText', ], ], ], 'PutRegistryPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'policyText' => [ 'shape' => 'RegistryPolicyText', ], ], ], 'PutRegistryScanningConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'scanType' => [ 'shape' => 'ScanType', ], 'rules' => [ 'shape' => 'RegistryScanningRuleList', ], ], ], 'PutRegistryScanningConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'registryScanningConfiguration' => [ 'shape' => 'RegistryScanningConfiguration', ], ], ], 'PutReplicationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'replicationConfiguration', ], 'members' => [ 'replicationConfiguration' => [ 'shape' => 'ReplicationConfiguration', ], ], ], 'PutReplicationConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'replicationConfiguration' => [ 'shape' => 'ReplicationConfiguration', ], ], ], 'Reason' => [ 'type' => 'string', ], 'Recommendation' => [ 'type' => 'structure', 'members' => [ 'url' => [ 'shape' => 'Url', ], 'text' => [ 'shape' => 'RecommendationText', ], ], ], 'RecommendationText' => [ 'type' => 'string', ], 'ReferenceUrlsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Url', ], ], 'ReferencedImagesNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'Region' => [ 'type' => 'string', 'max' => 25, 'min' => 2, 'pattern' => '[0-9a-z-]{2,25}', ], 'RegistryId' => [ 'type' => 'string', 'pattern' => '[0-9]{12}', ], 'RegistryPolicyNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'RegistryPolicyText' => [ 'type' => 'string', 'max' => 10240, 'min' => 0, ], 'RegistryScanningConfiguration' => [ 'type' => 'structure', 'members' => [ 'scanType' => [ 'shape' => 'ScanType', ], 'rules' => [ 'shape' => 'RegistryScanningRuleList', ], ], ], 'RegistryScanningRule' => [ 'type' => 'structure', 'required' => [ 'scanFrequency', 'repositoryFilters', ], 'members' => [ 'scanFrequency' => [ 'shape' => 'ScanFrequency', ], 'repositoryFilters' => [ 'shape' => 'ScanningRepositoryFilterList', ], ], ], 'RegistryScanningRuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegistryScanningRule', ], 'max' => 2, 'min' => 0, ], 'RelatedVulnerabilitiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RelatedVulnerability', ], ], 'RelatedVulnerability' => [ 'type' => 'string', ], 'Release' => [ 'type' => 'string', ], 'Remediation' => [ 'type' => 'structure', 'members' => [ 'recommendation' => [ 'shape' => 'Recommendation', ], ], ], 'ReplicationConfiguration' => [ 'type' => 'structure', 'required' => [ 'rules', ], 'members' => [ 'rules' => [ 'shape' => 'ReplicationRuleList', ], ], ], 'ReplicationDestination' => [ 'type' => 'structure', 'required' => [ 'region', 'registryId', ], 'members' => [ 'region' => [ 'shape' => 'Region', ], 'registryId' => [ 'shape' => 'RegistryId', ], ], ], 'ReplicationDestinationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationDestination', ], 'max' => 25, 'min' => 0, ], 'ReplicationError' => [ 'type' => 'string', ], 'ReplicationRule' => [ 'type' => 'structure', 'required' => [ 'destinations', ], 'members' => [ 'destinations' => [ 'shape' => 'ReplicationDestinationList', ], 'repositoryFilters' => [ 'shape' => 'RepositoryFilterList', ], ], ], 'ReplicationRuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationRule', ], 'max' => 10, 'min' => 0, ], 'ReplicationStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'COMPLETE', 'FAILED', ], ], 'Repository' => [ 'type' => 'structure', 'members' => [ 'repositoryArn' => [ 'shape' => 'Arn', ], 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'repositoryUri' => [ 'shape' => 'Url', ], 'createdAt' => [ 'shape' => 'CreationTimestamp', ], 'imageTagMutability' => [ 'shape' => 'ImageTagMutability', ], 'imageScanningConfiguration' => [ 'shape' => 'ImageScanningConfiguration', ], 'encryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], ], ], 'RepositoryAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'RepositoryFilter' => [ 'type' => 'structure', 'required' => [ 'filter', 'filterType', ], 'members' => [ 'filter' => [ 'shape' => 'RepositoryFilterValue', ], 'filterType' => [ 'shape' => 'RepositoryFilterType', ], ], ], 'RepositoryFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RepositoryFilter', ], 'max' => 100, 'min' => 1, ], 'RepositoryFilterType' => [ 'type' => 'string', 'enum' => [ 'PREFIX_MATCH', ], ], 'RepositoryFilterValue' => [ 'type' => 'string', 'max' => 256, 'min' => 2, 'pattern' => '^(?:[a-z0-9]+(?:[._-][a-z0-9]*)*/)*[a-z0-9]*(?:[._-][a-z0-9]*)*$', ], 'RepositoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Repository', ], ], 'RepositoryName' => [ 'type' => 'string', 'max' => 256, 'min' => 2, 'pattern' => '(?:[a-z0-9]+(?:[._-][a-z0-9]+)*/)*[a-z0-9]+(?:[._-][a-z0-9]+)*', ], 'RepositoryNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RepositoryName', ], 'max' => 100, 'min' => 1, ], 'RepositoryNotEmptyException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'RepositoryNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'RepositoryPolicyNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'RepositoryPolicyText' => [ 'type' => 'string', 'max' => 10240, 'min' => 0, ], 'RepositoryScanningConfiguration' => [ 'type' => 'structure', 'members' => [ 'repositoryArn' => [ 'shape' => 'Arn', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'scanOnPush' => [ 'shape' => 'ScanOnPushFlag', ], 'scanFrequency' => [ 'shape' => 'ScanFrequency', ], 'appliedScanFilters' => [ 'shape' => 'ScanningRepositoryFilterList', ], ], ], 'RepositoryScanningConfigurationFailure' => [ 'type' => 'structure', 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'failureCode' => [ 'shape' => 'ScanningConfigurationFailureCode', ], 'failureReason' => [ 'shape' => 'ScanningConfigurationFailureReason', ], ], ], 'RepositoryScanningConfigurationFailureList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RepositoryScanningConfigurationFailure', ], ], 'RepositoryScanningConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RepositoryScanningConfiguration', ], ], 'Resource' => [ 'type' => 'structure', 'members' => [ 'details' => [ 'shape' => 'ResourceDetails', ], 'id' => [ 'shape' => 'ResourceId', ], 'tags' => [ 'shape' => 'Tags', ], 'type' => [ 'shape' => 'Type', ], ], ], 'ResourceDetails' => [ 'type' => 'structure', 'members' => [ 'awsEcrContainerImage' => [ 'shape' => 'AwsEcrContainerImageDetails', ], ], ], 'ResourceId' => [ 'type' => 'string', ], 'ResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Resource', ], ], 'ScanFrequency' => [ 'type' => 'string', 'enum' => [ 'SCAN_ON_PUSH', 'CONTINUOUS_SCAN', 'MANUAL', ], ], 'ScanNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'ScanOnPushFlag' => [ 'type' => 'boolean', ], 'ScanStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'COMPLETE', 'FAILED', 'UNSUPPORTED_IMAGE', 'ACTIVE', 'PENDING', 'SCAN_ELIGIBILITY_EXPIRED', 'FINDINGS_UNAVAILABLE', ], ], 'ScanStatusDescription' => [ 'type' => 'string', ], 'ScanTimestamp' => [ 'type' => 'timestamp', ], 'ScanType' => [ 'type' => 'string', 'enum' => [ 'BASIC', 'ENHANCED', ], ], 'ScanningConfigurationFailureCode' => [ 'type' => 'string', 'enum' => [ 'REPOSITORY_NOT_FOUND', ], ], 'ScanningConfigurationFailureReason' => [ 'type' => 'string', ], 'ScanningConfigurationRepositoryNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RepositoryName', ], 'max' => 25, 'min' => 1, ], 'ScanningRepositoryFilter' => [ 'type' => 'structure', 'required' => [ 'filter', 'filterType', ], 'members' => [ 'filter' => [ 'shape' => 'ScanningRepositoryFilterValue', ], 'filterType' => [ 'shape' => 'ScanningRepositoryFilterType', ], ], ], 'ScanningRepositoryFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScanningRepositoryFilter', ], 'max' => 100, 'min' => 0, ], 'ScanningRepositoryFilterType' => [ 'type' => 'string', 'enum' => [ 'WILDCARD', ], ], 'ScanningRepositoryFilterValue' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-z0-9*](?:[._\\-/a-z0-9*]?[a-z0-9*]+)*$', ], 'Score' => [ 'type' => 'double', ], 'ScoreDetails' => [ 'type' => 'structure', 'members' => [ 'cvss' => [ 'shape' => 'CvssScoreDetails', ], ], ], 'ScoringVector' => [ 'type' => 'string', ], 'ServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, 'fault' => true, ], 'SetRepositoryPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'policyText', ], 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'policyText' => [ 'shape' => 'RepositoryPolicyText', ], 'force' => [ 'shape' => 'ForceFlag', ], ], ], 'SetRepositoryPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'policyText' => [ 'shape' => 'RepositoryPolicyText', ], ], ], 'Severity' => [ 'type' => 'string', ], 'SeverityCount' => [ 'type' => 'integer', 'min' => 0, ], 'Source' => [ 'type' => 'string', ], 'SourceLayerHash' => [ 'type' => 'string', ], 'StartImageScanRequest' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'imageId', ], 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'imageId' => [ 'shape' => 'ImageIdentifier', ], ], ], 'StartImageScanResponse' => [ 'type' => 'structure', 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'imageId' => [ 'shape' => 'ImageIdentifier', ], 'imageScanStatus' => [ 'shape' => 'ImageScanStatus', ], ], ], 'StartLifecyclePolicyPreviewRequest' => [ 'type' => 'structure', 'required' => [ 'repositoryName', ], 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'lifecyclePolicyText' => [ 'shape' => 'LifecyclePolicyText', ], ], ], 'StartLifecyclePolicyPreviewResponse' => [ 'type' => 'structure', 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'lifecyclePolicyText' => [ 'shape' => 'LifecyclePolicyText', ], 'status' => [ 'shape' => 'LifecyclePolicyPreviewStatus', ], ], ], 'Status' => [ 'type' => 'string', ], 'Tag' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagStatus' => [ 'type' => 'string', 'enum' => [ 'TAGGED', 'UNTAGGED', 'ANY', ], ], 'TagValue' => [ 'type' => 'string', ], 'Tags' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], 'Title' => [ 'type' => 'string', ], 'TooManyTagsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'Type' => [ 'type' => 'string', ], 'UnsupportedImageTypeException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'UnsupportedUpstreamRegistryException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UploadId' => [ 'type' => 'string', 'pattern' => '[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}', ], 'UploadLayerPartRequest' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'uploadId', 'partFirstByte', 'partLastByte', 'layerPartBlob', ], 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'uploadId' => [ 'shape' => 'UploadId', ], 'partFirstByte' => [ 'shape' => 'PartSize', ], 'partLastByte' => [ 'shape' => 'PartSize', ], 'layerPartBlob' => [ 'shape' => 'LayerPartBlob', ], ], ], 'UploadLayerPartResponse' => [ 'type' => 'structure', 'members' => [ 'registryId' => [ 'shape' => 'RegistryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'uploadId' => [ 'shape' => 'UploadId', ], 'lastByteReceived' => [ 'shape' => 'PartSize', ], ], ], 'UploadNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'Url' => [ 'type' => 'string', ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'Version' => [ 'type' => 'string', ], 'VulnerabilityId' => [ 'type' => 'string', ], 'VulnerabilitySourceUpdateTimestamp' => [ 'type' => 'timestamp', ], 'VulnerablePackage' => [ 'type' => 'structure', 'members' => [ 'arch' => [ 'shape' => 'Arch', ], 'epoch' => [ 'shape' => 'Epoch', ], 'filePath' => [ 'shape' => 'FilePath', ], 'name' => [ 'shape' => 'VulnerablePackageName', ], 'packageManager' => [ 'shape' => 'PackageManager', ], 'release' => [ 'shape' => 'Release', ], 'sourceLayerHash' => [ 'shape' => 'SourceLayerHash', ], 'version' => [ 'shape' => 'Version', ], ], ], 'VulnerablePackageName' => [ 'type' => 'string', ], 'VulnerablePackagesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VulnerablePackage', ], ], ],];
