<?php
// This file was auto-generated from sdk-root/src/data/sagemaker-edge/2020-09-23/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-09-23', 'endpointPrefix' => 'edge.sagemaker', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'Amazon Sagemaker Edge Manager', 'serviceId' => 'Sagemaker Edge', 'signatureVersion' => 'v4', 'signingName' => 'sagemaker', 'uid' => 'sagemaker-edge-2020-09-23', ], 'operations' => [ 'GetDeviceRegistration' => [ 'name' => 'GetDeviceRegistration', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetDeviceRegistration', ], 'input' => [ 'shape' => 'GetDeviceRegistrationRequest', ], 'output' => [ 'shape' => 'GetDeviceRegistrationResult', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], ], ], 'SendHeartbeat' => [ 'name' => 'SendHeartbeat', 'http' => [ 'method' => 'POST', 'requestUri' => '/SendHeartbeat', ], 'input' => [ 'shape' => 'SendHeartbeatRequest', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], ], ], ], 'shapes' => [ 'CacheTTLSeconds' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'DeviceFleetName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*_*[a-zA-Z0-9])*$', ], 'DeviceName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*_*[a-zA-Z0-9])*$', ], 'DeviceRegistration' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'Dimension' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9\\/])*$', ], 'EdgeMetric' => [ 'type' => 'structure', 'members' => [ 'Dimension' => [ 'shape' => 'Dimension', ], 'MetricName' => [ 'shape' => 'Metric', ], 'Value' => [ 'shape' => 'Value', ], 'Timestamp' => [ 'shape' => 'Timestamp', ], ], ], 'EdgeMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'EdgeMetric', ], ], 'ErrorMessage' => [ 'type' => 'string', ], 'GetDeviceRegistrationRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceName', 'DeviceFleetName', ], 'members' => [ 'DeviceName' => [ 'shape' => 'DeviceName', ], 'DeviceFleetName' => [ 'shape' => 'DeviceFleetName', ], ], ], 'GetDeviceRegistrationResult' => [ 'type' => 'structure', 'members' => [ 'DeviceRegistration' => [ 'shape' => 'DeviceRegistration', ], 'CacheTTL' => [ 'shape' => 'CacheTTLSeconds', ], ], ], 'InternalServiceException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'Metric' => [ 'type' => 'string', 'max' => 100, 'min' => 4, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9])*$', ], 'Model' => [ 'type' => 'structure', 'members' => [ 'ModelName' => [ 'shape' => 'ModelName', ], 'ModelVersion' => [ 'shape' => 'Version', ], 'LatestSampleTime' => [ 'shape' => 'Timestamp', ], 'LatestInference' => [ 'shape' => 'Timestamp', ], 'ModelMetrics' => [ 'shape' => 'EdgeMetrics', ], ], ], 'ModelName' => [ 'type' => 'string', 'max' => 255, 'min' => 4, 'pattern' => '^[a-zA-Z0-9](-*[a-zA-Z0-9])*$', ], 'Models' => [ 'type' => 'list', 'member' => [ 'shape' => 'Model', ], ], 'SendHeartbeatRequest' => [ 'type' => 'structure', 'required' => [ 'AgentVersion', 'DeviceName', 'DeviceFleetName', ], 'members' => [ 'AgentMetrics' => [ 'shape' => 'EdgeMetrics', ], 'Models' => [ 'shape' => 'Models', ], 'AgentVersion' => [ 'shape' => 'Version', ], 'DeviceName' => [ 'shape' => 'DeviceName', ], 'DeviceFleetName' => [ 'shape' => 'DeviceFleetName', ], ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'Value' => [ 'type' => 'double', ], 'Version' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9\\ \\_\\.]+', ], ],];
