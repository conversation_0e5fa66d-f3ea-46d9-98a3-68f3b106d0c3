<!-- HTML footer for doxygen *******-->
<!-- start footer part -->
<!--BEGIN GENERATE_TREEVIEW-->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
	<ul>
		<li class="footer">
			Copyright &copy; 2007-2019, <a href="https://cksource.com">CKSource</a> - <PERSON><PERSON>. All rights reserved. | Generated with <a href="http://www.doxygen.org">Doxygen</a>.
		</li>
	</ul>
</div>
<!--END GENERATE_TREEVIEW-->

<script src="//cdn.rawgit.com/google/code-prettify/master/loader/run_prettify.js" type="text/javascript" defer="defer"></script>
<link href="//cdn.rawgit.com/google/code-prettify/master/loader/prettify.css" type="text/css">
<link rel="stylesheet" type="text/css" href="//fonts.googleapis.com/css?family=Exo">
<script type="text/javascript">
	function htmlEscape(str) {
		return String(str)
				.replace(/&/g, '&amp;')
				.replace(/"/g, '&quot;')
				.replace(/'/g, '&#39;')
				.replace(/</g, '&lt;')
				.replace(/>/g, '&gt;');
	}

	$(function() {
		$(".fragment").each(function(i,node) {
			var $node = $(node);
			$node.find('.lineno').remove();
			$node.html("<pre><code class=\"prettyprint\">" +
					htmlEscape( $node.text() ) +"</code></pre>");
		});
	});
</script>
</body>
</html>

