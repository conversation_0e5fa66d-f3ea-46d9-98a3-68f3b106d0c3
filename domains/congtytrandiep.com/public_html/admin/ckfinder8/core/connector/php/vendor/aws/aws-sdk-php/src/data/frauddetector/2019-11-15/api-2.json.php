<?php
// This file was auto-generated from sdk-root/src/data/frauddetector/2019-11-15/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2019-11-15', 'endpointPrefix' => 'frauddetector', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceFullName' => 'Amazon Fraud Detector', 'serviceId' => 'FraudDetector', 'signatureVersion' => 'v4', 'targetPrefix' => 'AWSHawksNestServiceFacade', 'uid' => 'frauddetector-2019-11-15', ], 'operations' => [ 'BatchCreateVariable' => [ 'name' => 'BatchCreateVariable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchCreateVariableRequest', ], 'output' => [ 'shape' => 'BatchCreateVariableResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'BatchGetVariable' => [ 'name' => 'BatchGetVariable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetVariableRequest', ], 'output' => [ 'shape' => 'BatchGetVariableResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CancelBatchImportJob' => [ 'name' => 'CancelBatchImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelBatchImportJobRequest', ], 'output' => [ 'shape' => 'CancelBatchImportJobResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CancelBatchPredictionJob' => [ 'name' => 'CancelBatchPredictionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelBatchPredictionJobRequest', ], 'output' => [ 'shape' => 'CancelBatchPredictionJobResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateBatchImportJob' => [ 'name' => 'CreateBatchImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateBatchImportJobRequest', ], 'output' => [ 'shape' => 'CreateBatchImportJobResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateBatchPredictionJob' => [ 'name' => 'CreateBatchPredictionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateBatchPredictionJobRequest', ], 'output' => [ 'shape' => 'CreateBatchPredictionJobResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'CreateDetectorVersion' => [ 'name' => 'CreateDetectorVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDetectorVersionRequest', ], 'output' => [ 'shape' => 'CreateDetectorVersionResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateModel' => [ 'name' => 'CreateModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateModelRequest', ], 'output' => [ 'shape' => 'CreateModelResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateModelVersion' => [ 'name' => 'CreateModelVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateModelVersionRequest', ], 'output' => [ 'shape' => 'CreateModelVersionResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateRule' => [ 'name' => 'CreateRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRuleRequest', ], 'output' => [ 'shape' => 'CreateRuleResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateVariable' => [ 'name' => 'CreateVariable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateVariableRequest', ], 'output' => [ 'shape' => 'CreateVariableResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteBatchImportJob' => [ 'name' => 'DeleteBatchImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteBatchImportJobRequest', ], 'output' => [ 'shape' => 'DeleteBatchImportJobResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteBatchPredictionJob' => [ 'name' => 'DeleteBatchPredictionJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteBatchPredictionJobRequest', ], 'output' => [ 'shape' => 'DeleteBatchPredictionJobResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteDetector' => [ 'name' => 'DeleteDetector', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDetectorRequest', ], 'output' => [ 'shape' => 'DeleteDetectorResult', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteDetectorVersion' => [ 'name' => 'DeleteDetectorVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDetectorVersionRequest', ], 'output' => [ 'shape' => 'DeleteDetectorVersionResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteEntityType' => [ 'name' => 'DeleteEntityType', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEntityTypeRequest', ], 'output' => [ 'shape' => 'DeleteEntityTypeResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteEvent' => [ 'name' => 'DeleteEvent', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEventRequest', ], 'output' => [ 'shape' => 'DeleteEventResult', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteEventType' => [ 'name' => 'DeleteEventType', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEventTypeRequest', ], 'output' => [ 'shape' => 'DeleteEventTypeResult', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteEventsByEventType' => [ 'name' => 'DeleteEventsByEventType', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEventsByEventTypeRequest', ], 'output' => [ 'shape' => 'DeleteEventsByEventTypeResult', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteExternalModel' => [ 'name' => 'DeleteExternalModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteExternalModelRequest', ], 'output' => [ 'shape' => 'DeleteExternalModelResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteLabel' => [ 'name' => 'DeleteLabel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteLabelRequest', ], 'output' => [ 'shape' => 'DeleteLabelResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteModel' => [ 'name' => 'DeleteModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteModelRequest', ], 'output' => [ 'shape' => 'DeleteModelResult', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteModelVersion' => [ 'name' => 'DeleteModelVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteModelVersionRequest', ], 'output' => [ 'shape' => 'DeleteModelVersionResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteOutcome' => [ 'name' => 'DeleteOutcome', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteOutcomeRequest', ], 'output' => [ 'shape' => 'DeleteOutcomeResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteRule' => [ 'name' => 'DeleteRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRuleRequest', ], 'output' => [ 'shape' => 'DeleteRuleResult', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteVariable' => [ 'name' => 'DeleteVariable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteVariableRequest', ], 'output' => [ 'shape' => 'DeleteVariableResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeDetector' => [ 'name' => 'DescribeDetector', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDetectorRequest', ], 'output' => [ 'shape' => 'DescribeDetectorResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeModelVersions' => [ 'name' => 'DescribeModelVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeModelVersionsRequest', ], 'output' => [ 'shape' => 'DescribeModelVersionsResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetBatchImportJobs' => [ 'name' => 'GetBatchImportJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetBatchImportJobsRequest', ], 'output' => [ 'shape' => 'GetBatchImportJobsResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetBatchPredictionJobs' => [ 'name' => 'GetBatchPredictionJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetBatchPredictionJobsRequest', ], 'output' => [ 'shape' => 'GetBatchPredictionJobsResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetDeleteEventsByEventTypeStatus' => [ 'name' => 'GetDeleteEventsByEventTypeStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDeleteEventsByEventTypeStatusRequest', ], 'output' => [ 'shape' => 'GetDeleteEventsByEventTypeStatusResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetDetectorVersion' => [ 'name' => 'GetDetectorVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDetectorVersionRequest', ], 'output' => [ 'shape' => 'GetDetectorVersionResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetDetectors' => [ 'name' => 'GetDetectors', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDetectorsRequest', ], 'output' => [ 'shape' => 'GetDetectorsResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetEntityTypes' => [ 'name' => 'GetEntityTypes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetEntityTypesRequest', ], 'output' => [ 'shape' => 'GetEntityTypesResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetEvent' => [ 'name' => 'GetEvent', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetEventRequest', ], 'output' => [ 'shape' => 'GetEventResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetEventPrediction' => [ 'name' => 'GetEventPrediction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetEventPredictionRequest', ], 'output' => [ 'shape' => 'GetEventPredictionResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceUnavailableException', ], ], ], 'GetEventTypes' => [ 'name' => 'GetEventTypes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetEventTypesRequest', ], 'output' => [ 'shape' => 'GetEventTypesResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetExternalModels' => [ 'name' => 'GetExternalModels', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetExternalModelsRequest', ], 'output' => [ 'shape' => 'GetExternalModelsResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetKMSEncryptionKey' => [ 'name' => 'GetKMSEncryptionKey', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'output' => [ 'shape' => 'GetKMSEncryptionKeyResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetLabels' => [ 'name' => 'GetLabels', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetLabelsRequest', ], 'output' => [ 'shape' => 'GetLabelsResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetModelVersion' => [ 'name' => 'GetModelVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetModelVersionRequest', ], 'output' => [ 'shape' => 'GetModelVersionResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetModels' => [ 'name' => 'GetModels', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetModelsRequest', ], 'output' => [ 'shape' => 'GetModelsResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetOutcomes' => [ 'name' => 'GetOutcomes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetOutcomesRequest', ], 'output' => [ 'shape' => 'GetOutcomesResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetRules' => [ 'name' => 'GetRules', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRulesRequest', ], 'output' => [ 'shape' => 'GetRulesResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetVariables' => [ 'name' => 'GetVariables', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetVariablesRequest', ], 'output' => [ 'shape' => 'GetVariablesResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'PutDetector' => [ 'name' => 'PutDetector', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutDetectorRequest', ], 'output' => [ 'shape' => 'PutDetectorResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'PutEntityType' => [ 'name' => 'PutEntityType', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutEntityTypeRequest', ], 'output' => [ 'shape' => 'PutEntityTypeResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'PutEventType' => [ 'name' => 'PutEventType', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutEventTypeRequest', ], 'output' => [ 'shape' => 'PutEventTypeResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'PutExternalModel' => [ 'name' => 'PutExternalModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutExternalModelRequest', ], 'output' => [ 'shape' => 'PutExternalModelResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'PutKMSEncryptionKey' => [ 'name' => 'PutKMSEncryptionKey', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutKMSEncryptionKeyRequest', ], 'output' => [ 'shape' => 'PutKMSEncryptionKeyResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'PutLabel' => [ 'name' => 'PutLabel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutLabelRequest', ], 'output' => [ 'shape' => 'PutLabelResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'PutOutcome' => [ 'name' => 'PutOutcome', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutOutcomeRequest', ], 'output' => [ 'shape' => 'PutOutcomeResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'SendEvent' => [ 'name' => 'SendEvent', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SendEventRequest', ], 'output' => [ 'shape' => 'SendEventResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateDetectorVersion' => [ 'name' => 'UpdateDetectorVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDetectorVersionRequest', ], 'output' => [ 'shape' => 'UpdateDetectorVersionResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateDetectorVersionMetadata' => [ 'name' => 'UpdateDetectorVersionMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDetectorVersionMetadataRequest', ], 'output' => [ 'shape' => 'UpdateDetectorVersionMetadataResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateDetectorVersionStatus' => [ 'name' => 'UpdateDetectorVersionStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDetectorVersionStatusRequest', ], 'output' => [ 'shape' => 'UpdateDetectorVersionStatusResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateEventLabel' => [ 'name' => 'UpdateEventLabel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateEventLabelRequest', ], 'output' => [ 'shape' => 'UpdateEventLabelResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateModel' => [ 'name' => 'UpdateModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateModelRequest', ], 'output' => [ 'shape' => 'UpdateModelResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateModelVersion' => [ 'name' => 'UpdateModelVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateModelVersionRequest', ], 'output' => [ 'shape' => 'UpdateModelVersionResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateModelVersionStatus' => [ 'name' => 'UpdateModelVersionStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateModelVersionStatusRequest', ], 'output' => [ 'shape' => 'UpdateModelVersionStatusResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateRuleMetadata' => [ 'name' => 'UpdateRuleMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRuleMetadataRequest', ], 'output' => [ 'shape' => 'UpdateRuleMetadataResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateRuleVersion' => [ 'name' => 'UpdateRuleVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRuleVersionRequest', ], 'output' => [ 'shape' => 'UpdateRuleVersionResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateVariable' => [ 'name' => 'UpdateVariable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateVariableRequest', ], 'output' => [ 'shape' => 'UpdateVariableResult', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'AsyncJobStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS_INITIALIZING', 'IN_PROGRESS', 'CANCEL_IN_PROGRESS', 'CANCELED', 'COMPLETE', 'FAILED', ], ], 'BatchCreateVariableError' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'string', ], 'code' => [ 'shape' => 'integer', ], 'message' => [ 'shape' => 'string', ], ], ], 'BatchCreateVariableErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchCreateVariableError', ], ], 'BatchCreateVariableRequest' => [ 'type' => 'structure', 'required' => [ 'variableEntries', ], 'members' => [ 'variableEntries' => [ 'shape' => 'VariableEntryList', ], 'tags' => [ 'shape' => 'tagList', ], ], ], 'BatchCreateVariableResult' => [ 'type' => 'structure', 'members' => [ 'errors' => [ 'shape' => 'BatchCreateVariableErrorList', ], ], ], 'BatchGetVariableError' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'string', ], 'code' => [ 'shape' => 'integer', ], 'message' => [ 'shape' => 'string', ], ], ], 'BatchGetVariableErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetVariableError', ], ], 'BatchGetVariableRequest' => [ 'type' => 'structure', 'required' => [ 'names', ], 'members' => [ 'names' => [ 'shape' => 'NameList', ], ], ], 'BatchGetVariableResult' => [ 'type' => 'structure', 'members' => [ 'variables' => [ 'shape' => 'VariableList', ], 'errors' => [ 'shape' => 'BatchGetVariableErrorList', ], ], ], 'BatchImport' => [ 'type' => 'structure', 'members' => [ 'jobId' => [ 'shape' => 'identifier', ], 'status' => [ 'shape' => 'AsyncJobStatus', ], 'failureReason' => [ 'shape' => 'string', ], 'startTime' => [ 'shape' => 'time', ], 'completionTime' => [ 'shape' => 'time', ], 'inputPath' => [ 'shape' => 's3BucketLocation', ], 'outputPath' => [ 'shape' => 's3BucketLocation', ], 'eventTypeName' => [ 'shape' => 'identifier', ], 'iamRoleArn' => [ 'shape' => 'iamRoleArn', ], 'arn' => [ 'shape' => 'fraudDetectorArn', ], 'processedRecordsCount' => [ 'shape' => 'Integer', ], 'failedRecordsCount' => [ 'shape' => 'Integer', ], 'totalRecordsCount' => [ 'shape' => 'Integer', ], ], ], 'BatchImportList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchImport', ], ], 'BatchPrediction' => [ 'type' => 'structure', 'members' => [ 'jobId' => [ 'shape' => 'identifier', ], 'status' => [ 'shape' => 'AsyncJobStatus', ], 'failureReason' => [ 'shape' => 'string', ], 'startTime' => [ 'shape' => 'time', ], 'completionTime' => [ 'shape' => 'time', ], 'lastHeartbeatTime' => [ 'shape' => 'time', ], 'inputPath' => [ 'shape' => 's3BucketLocation', ], 'outputPath' => [ 'shape' => 's3BucketLocation', ], 'eventTypeName' => [ 'shape' => 'identifier', ], 'detectorName' => [ 'shape' => 'identifier', ], 'detectorVersion' => [ 'shape' => 'floatVersionString', ], 'iamRoleArn' => [ 'shape' => 'iamRoleArn', ], 'arn' => [ 'shape' => 'fraudDetectorArn', ], 'processedRecordsCount' => [ 'shape' => 'Integer', ], 'totalRecordsCount' => [ 'shape' => 'Integer', ], ], ], 'BatchPredictionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchPrediction', ], ], 'CancelBatchImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'identifier', ], ], ], 'CancelBatchImportJobResult' => [ 'type' => 'structure', 'members' => [], ], 'CancelBatchPredictionJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'identifier', ], ], ], 'CancelBatchPredictionJobResult' => [ 'type' => 'structure', 'members' => [], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'CreateBatchImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobId', 'inputPath', 'outputPath', 'eventTypeName', 'iamRoleArn', ], 'members' => [ 'jobId' => [ 'shape' => 'identifier', ], 'inputPath' => [ 'shape' => 's3BucketLocation', ], 'outputPath' => [ 'shape' => 's3BucketLocation', ], 'eventTypeName' => [ 'shape' => 'identifier', ], 'iamRoleArn' => [ 'shape' => 'iamRoleArn', ], 'tags' => [ 'shape' => 'tagList', ], ], ], 'CreateBatchImportJobResult' => [ 'type' => 'structure', 'members' => [], ], 'CreateBatchPredictionJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobId', 'inputPath', 'outputPath', 'eventTypeName', 'detectorName', 'iamRoleArn', ], 'members' => [ 'jobId' => [ 'shape' => 'identifier', ], 'inputPath' => [ 'shape' => 's3BucketLocation', ], 'outputPath' => [ 'shape' => 's3BucketLocation', ], 'eventTypeName' => [ 'shape' => 'identifier', ], 'detectorName' => [ 'shape' => 'identifier', ], 'detectorVersion' => [ 'shape' => 'wholeNumberVersionString', ], 'iamRoleArn' => [ 'shape' => 'iamRoleArn', ], 'tags' => [ 'shape' => 'tagList', ], ], ], 'CreateBatchPredictionJobResult' => [ 'type' => 'structure', 'members' => [], ], 'CreateDetectorVersionRequest' => [ 'type' => 'structure', 'required' => [ 'detectorId', 'rules', ], 'members' => [ 'detectorId' => [ 'shape' => 'identifier', ], 'description' => [ 'shape' => 'description', ], 'externalModelEndpoints' => [ 'shape' => 'ListOfStrings', ], 'rules' => [ 'shape' => 'RuleList', ], 'modelVersions' => [ 'shape' => 'ListOfModelVersions', ], 'ruleExecutionMode' => [ 'shape' => 'RuleExecutionMode', ], 'tags' => [ 'shape' => 'tagList', ], ], ], 'CreateDetectorVersionResult' => [ 'type' => 'structure', 'members' => [ 'detectorId' => [ 'shape' => 'identifier', ], 'detectorVersionId' => [ 'shape' => 'wholeNumberVersionString', ], 'status' => [ 'shape' => 'DetectorVersionStatus', ], ], ], 'CreateModelRequest' => [ 'type' => 'structure', 'required' => [ 'modelId', 'modelType', 'eventTypeName', ], 'members' => [ 'modelId' => [ 'shape' => 'modelIdentifier', ], 'modelType' => [ 'shape' => 'ModelTypeEnum', ], 'description' => [ 'shape' => 'description', ], 'eventTypeName' => [ 'shape' => 'string', ], 'tags' => [ 'shape' => 'tagList', ], ], ], 'CreateModelResult' => [ 'type' => 'structure', 'members' => [], ], 'CreateModelVersionRequest' => [ 'type' => 'structure', 'required' => [ 'modelId', 'modelType', 'trainingDataSource', 'trainingDataSchema', ], 'members' => [ 'modelId' => [ 'shape' => 'modelIdentifier', ], 'modelType' => [ 'shape' => 'ModelTypeEnum', ], 'trainingDataSource' => [ 'shape' => 'TrainingDataSourceEnum', ], 'trainingDataSchema' => [ 'shape' => 'TrainingDataSchema', ], 'externalEventsDetail' => [ 'shape' => 'ExternalEventsDetail', ], 'ingestedEventsDetail' => [ 'shape' => 'IngestedEventsDetail', ], 'tags' => [ 'shape' => 'tagList', ], ], ], 'CreateModelVersionResult' => [ 'type' => 'structure', 'members' => [ 'modelId' => [ 'shape' => 'modelIdentifier', ], 'modelType' => [ 'shape' => 'ModelTypeEnum', ], 'modelVersionNumber' => [ 'shape' => 'floatVersionString', ], 'status' => [ 'shape' => 'string', ], ], ], 'CreateRuleRequest' => [ 'type' => 'structure', 'required' => [ 'ruleId', 'detectorId', 'expression', 'language', 'outcomes', ], 'members' => [ 'ruleId' => [ 'shape' => 'identifier', ], 'detectorId' => [ 'shape' => 'identifier', ], 'description' => [ 'shape' => 'description', ], 'expression' => [ 'shape' => 'ruleExpression', ], 'language' => [ 'shape' => 'Language', ], 'outcomes' => [ 'shape' => 'NonEmptyListOfStrings', ], 'tags' => [ 'shape' => 'tagList', ], ], ], 'CreateRuleResult' => [ 'type' => 'structure', 'members' => [ 'rule' => [ 'shape' => 'Rule', ], ], ], 'CreateVariableRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'dataType', 'dataSource', 'defaultValue', ], 'members' => [ 'name' => [ 'shape' => 'string', ], 'dataType' => [ 'shape' => 'DataType', ], 'dataSource' => [ 'shape' => 'DataSource', ], 'defaultValue' => [ 'shape' => 'string', ], 'description' => [ 'shape' => 'string', ], 'variableType' => [ 'shape' => 'string', ], 'tags' => [ 'shape' => 'tagList', ], ], ], 'CreateVariableResult' => [ 'type' => 'structure', 'members' => [], ], 'CsvIndexToVariableMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'string', ], 'value' => [ 'shape' => 'string', ], ], 'DataSource' => [ 'type' => 'string', 'enum' => [ 'EVENT', 'MODEL_SCORE', 'EXTERNAL_MODEL_SCORE', ], ], 'DataType' => [ 'type' => 'string', 'enum' => [ 'STRING', 'INTEGER', 'FLOAT', 'BOOLEAN', ], ], 'DataValidationMetrics' => [ 'type' => 'structure', 'members' => [ 'fileLevelMessages' => [ 'shape' => 'fileValidationMessageList', ], 'fieldLevelMessages' => [ 'shape' => 'fieldValidationMessageList', ], ], ], 'DeleteAuditHistory' => [ 'type' => 'boolean', ], 'DeleteBatchImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'identifier', ], ], ], 'DeleteBatchImportJobResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteBatchPredictionJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'identifier', ], ], ], 'DeleteBatchPredictionJobResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDetectorRequest' => [ 'type' => 'structure', 'required' => [ 'detectorId', ], 'members' => [ 'detectorId' => [ 'shape' => 'identifier', ], ], ], 'DeleteDetectorResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDetectorVersionRequest' => [ 'type' => 'structure', 'required' => [ 'detectorId', 'detectorVersionId', ], 'members' => [ 'detectorId' => [ 'shape' => 'identifier', ], 'detectorVersionId' => [ 'shape' => 'wholeNumberVersionString', ], ], ], 'DeleteDetectorVersionResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteEntityTypeRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'identifier', ], ], ], 'DeleteEntityTypeResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteEventRequest' => [ 'type' => 'structure', 'required' => [ 'eventId', 'eventTypeName', ], 'members' => [ 'eventId' => [ 'shape' => 'identifier', ], 'eventTypeName' => [ 'shape' => 'identifier', ], 'deleteAuditHistory' => [ 'shape' => 'DeleteAuditHistory', ], ], ], 'DeleteEventResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteEventTypeRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'identifier', ], ], ], 'DeleteEventTypeResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteEventsByEventTypeRequest' => [ 'type' => 'structure', 'required' => [ 'eventTypeName', ], 'members' => [ 'eventTypeName' => [ 'shape' => 'identifier', ], ], ], 'DeleteEventsByEventTypeResult' => [ 'type' => 'structure', 'members' => [ 'eventTypeName' => [ 'shape' => 'identifier', ], 'eventsDeletionStatus' => [ 'shape' => 'string', ], ], ], 'DeleteExternalModelRequest' => [ 'type' => 'structure', 'required' => [ 'modelEndpoint', ], 'members' => [ 'modelEndpoint' => [ 'shape' => 'sageMakerEndpointIdentifier', ], ], ], 'DeleteExternalModelResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteLabelRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'identifier', ], ], ], 'DeleteLabelResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteModelRequest' => [ 'type' => 'structure', 'required' => [ 'modelId', 'modelType', ], 'members' => [ 'modelId' => [ 'shape' => 'modelIdentifier', ], 'modelType' => [ 'shape' => 'ModelTypeEnum', ], ], ], 'DeleteModelResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteModelVersionRequest' => [ 'type' => 'structure', 'required' => [ 'modelId', 'modelType', 'modelVersionNumber', ], 'members' => [ 'modelId' => [ 'shape' => 'modelIdentifier', ], 'modelType' => [ 'shape' => 'ModelTypeEnum', ], 'modelVersionNumber' => [ 'shape' => 'floatVersionString', ], ], ], 'DeleteModelVersionResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteOutcomeRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'identifier', ], ], ], 'DeleteOutcomeResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRuleRequest' => [ 'type' => 'structure', 'required' => [ 'rule', ], 'members' => [ 'rule' => [ 'shape' => 'Rule', ], ], ], 'DeleteRuleResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteVariableRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'string', ], ], ], 'DeleteVariableResult' => [ 'type' => 'structure', 'members' => [], ], 'DescribeDetectorRequest' => [ 'type' => 'structure', 'required' => [ 'detectorId', ], 'members' => [ 'detectorId' => [ 'shape' => 'identifier', ], 'nextToken' => [ 'shape' => 'string', ], 'maxResults' => [ 'shape' => 'DetectorVersionMaxResults', ], ], ], 'DescribeDetectorResult' => [ 'type' => 'structure', 'members' => [ 'detectorId' => [ 'shape' => 'identifier', ], 'detectorVersionSummaries' => [ 'shape' => 'DetectorVersionSummaryList', ], 'nextToken' => [ 'shape' => 'string', ], 'arn' => [ 'shape' => 'fraudDetectorArn', ], ], ], 'DescribeModelVersionsRequest' => [ 'type' => 'structure', 'members' => [ 'modelId' => [ 'shape' => 'modelIdentifier', ], 'modelVersionNumber' => [ 'shape' => 'floatVersionString', ], 'modelType' => [ 'shape' => 'ModelTypeEnum', ], 'nextToken' => [ 'shape' => 'string', ], 'maxResults' => [ 'shape' => 'modelsMaxPageSize', ], ], ], 'DescribeModelVersionsResult' => [ 'type' => 'structure', 'members' => [ 'modelVersionDetails' => [ 'shape' => 'modelVersionDetailList', ], 'nextToken' => [ 'shape' => 'string', ], ], ], 'Detector' => [ 'type' => 'structure', 'members' => [ 'detectorId' => [ 'shape' => 'identifier', ], 'description' => [ 'shape' => 'description', ], 'eventTypeName' => [ 'shape' => 'identifier', ], 'lastUpdatedTime' => [ 'shape' => 'time', ], 'createdTime' => [ 'shape' => 'time', ], 'arn' => [ 'shape' => 'fraudDetectorArn', ], ], ], 'DetectorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Detector', ], ], 'DetectorVersionMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 2500, 'min' => 1000, ], 'DetectorVersionStatus' => [ 'type' => 'string', 'enum' => [ 'DRAFT', 'ACTIVE', 'INACTIVE', ], ], 'DetectorVersionSummary' => [ 'type' => 'structure', 'members' => [ 'detectorVersionId' => [ 'shape' => 'wholeNumberVersionString', ], 'status' => [ 'shape' => 'DetectorVersionStatus', ], 'description' => [ 'shape' => 'description', ], 'lastUpdatedTime' => [ 'shape' => 'time', ], ], ], 'DetectorVersionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DetectorVersionSummary', ], ], 'DetectorsMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 5, ], 'Entity' => [ 'type' => 'structure', 'required' => [ 'entityType', 'entityId', ], 'members' => [ 'entityType' => [ 'shape' => 'string', ], 'entityId' => [ 'shape' => 'entityRestrictedString', ], ], 'sensitive' => true, ], 'EntityType' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'string', ], 'description' => [ 'shape' => 'description', ], 'lastUpdatedTime' => [ 'shape' => 'time', ], 'createdTime' => [ 'shape' => 'time', ], 'arn' => [ 'shape' => 'fraudDetectorArn', ], ], ], 'Event' => [ 'type' => 'structure', 'members' => [ 'eventId' => [ 'shape' => 'string', ], 'eventTypeName' => [ 'shape' => 'string', ], 'eventTimestamp' => [ 'shape' => 'string', ], 'eventVariables' => [ 'shape' => 'EventAttributeMap', ], 'currentLabel' => [ 'shape' => 'string', ], 'labelTimestamp' => [ 'shape' => 'string', ], 'entities' => [ 'shape' => 'listOfEntities', ], ], ], 'EventAttributeMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'attributeKey', ], 'value' => [ 'shape' => 'attributeValue', ], ], 'EventIngestion' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'EventType' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'string', ], 'description' => [ 'shape' => 'description', ], 'eventVariables' => [ 'shape' => 'ListOfStrings', ], 'labels' => [ 'shape' => 'ListOfStrings', ], 'entityTypes' => [ 'shape' => 'NonEmptyListOfStrings', ], 'eventIngestion' => [ 'shape' => 'EventIngestion', ], 'ingestedEventStatistics' => [ 'shape' => 'IngestedEventStatistics', ], 'lastUpdatedTime' => [ 'shape' => 'time', ], 'createdTime' => [ 'shape' => 'time', ], 'arn' => [ 'shape' => 'fraudDetectorArn', ], ], 'sensitive' => true, ], 'EventVariableMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'variableName', ], 'value' => [ 'shape' => 'variableValue', ], 'min' => 1, ], 'ExternalEventsDetail' => [ 'type' => 'structure', 'required' => [ 'dataLocation', 'dataAccessRoleArn', ], 'members' => [ 'dataLocation' => [ 'shape' => 's3BucketLocation', ], 'dataAccessRoleArn' => [ 'shape' => 'iamRoleArn', ], ], ], 'ExternalModel' => [ 'type' => 'structure', 'members' => [ 'modelEndpoint' => [ 'shape' => 'string', ], 'modelSource' => [ 'shape' => 'ModelSource', ], 'invokeModelEndpointRoleArn' => [ 'shape' => 'string', ], 'inputConfiguration' => [ 'shape' => 'ModelInputConfiguration', ], 'outputConfiguration' => [ 'shape' => 'ModelOutputConfiguration', ], 'modelEndpointStatus' => [ 'shape' => 'ModelEndpointStatus', ], 'lastUpdatedTime' => [ 'shape' => 'time', ], 'createdTime' => [ 'shape' => 'time', ], 'arn' => [ 'shape' => 'fraudDetectorArn', ], ], ], 'ExternalModelEndpointDataBlobMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'sageMakerEndpointIdentifier', ], 'value' => [ 'shape' => 'ModelEndpointDataBlob', ], 'sensitive' => true, ], 'ExternalModelList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExternalModel', ], ], 'ExternalModelOutputs' => [ 'type' => 'structure', 'members' => [ 'externalModel' => [ 'shape' => 'ExternalModelSummary', ], 'outputs' => [ 'shape' => 'ExternalModelPredictionMap', ], ], ], 'ExternalModelPredictionMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'string', ], 'value' => [ 'shape' => 'string', ], ], 'ExternalModelSummary' => [ 'type' => 'structure', 'members' => [ 'modelEndpoint' => [ 'shape' => 'string', ], 'modelSource' => [ 'shape' => 'ModelSource', ], ], ], 'ExternalModelsMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 5, ], 'FieldValidationMessage' => [ 'type' => 'structure', 'members' => [ 'fieldName' => [ 'shape' => 'string', ], 'identifier' => [ 'shape' => 'string', ], 'title' => [ 'shape' => 'string', ], 'content' => [ 'shape' => 'string', ], 'type' => [ 'shape' => 'string', ], ], ], 'FileValidationMessage' => [ 'type' => 'structure', 'members' => [ 'title' => [ 'shape' => 'string', ], 'content' => [ 'shape' => 'string', ], 'type' => [ 'shape' => 'string', ], ], ], 'GetBatchImportJobsRequest' => [ 'type' => 'structure', 'members' => [ 'jobId' => [ 'shape' => 'identifier', ], 'maxResults' => [ 'shape' => 'batchImportsMaxPageSize', ], 'nextToken' => [ 'shape' => 'string', ], ], ], 'GetBatchImportJobsResult' => [ 'type' => 'structure', 'members' => [ 'batchImports' => [ 'shape' => 'BatchImportList', ], 'nextToken' => [ 'shape' => 'string', ], ], ], 'GetBatchPredictionJobsRequest' => [ 'type' => 'structure', 'members' => [ 'jobId' => [ 'shape' => 'identifier', ], 'maxResults' => [ 'shape' => 'batchPredictionsMaxPageSize', ], 'nextToken' => [ 'shape' => 'string', ], ], ], 'GetBatchPredictionJobsResult' => [ 'type' => 'structure', 'members' => [ 'batchPredictions' => [ 'shape' => 'BatchPredictionList', ], 'nextToken' => [ 'shape' => 'string', ], ], ], 'GetDeleteEventsByEventTypeStatusRequest' => [ 'type' => 'structure', 'required' => [ 'eventTypeName', ], 'members' => [ 'eventTypeName' => [ 'shape' => 'identifier', ], ], ], 'GetDeleteEventsByEventTypeStatusResult' => [ 'type' => 'structure', 'members' => [ 'eventTypeName' => [ 'shape' => 'identifier', ], 'eventsDeletionStatus' => [ 'shape' => 'AsyncJobStatus', ], ], ], 'GetDetectorVersionRequest' => [ 'type' => 'structure', 'required' => [ 'detectorId', 'detectorVersionId', ], 'members' => [ 'detectorId' => [ 'shape' => 'identifier', ], 'detectorVersionId' => [ 'shape' => 'wholeNumberVersionString', ], ], ], 'GetDetectorVersionResult' => [ 'type' => 'structure', 'members' => [ 'detectorId' => [ 'shape' => 'identifier', ], 'detectorVersionId' => [ 'shape' => 'wholeNumberVersionString', ], 'description' => [ 'shape' => 'description', ], 'externalModelEndpoints' => [ 'shape' => 'ListOfStrings', ], 'modelVersions' => [ 'shape' => 'ListOfModelVersions', ], 'rules' => [ 'shape' => 'RuleList', ], 'status' => [ 'shape' => 'DetectorVersionStatus', ], 'lastUpdatedTime' => [ 'shape' => 'time', ], 'createdTime' => [ 'shape' => 'time', ], 'ruleExecutionMode' => [ 'shape' => 'RuleExecutionMode', ], 'arn' => [ 'shape' => 'fraudDetectorArn', ], ], ], 'GetDetectorsRequest' => [ 'type' => 'structure', 'members' => [ 'detectorId' => [ 'shape' => 'identifier', ], 'nextToken' => [ 'shape' => 'string', ], 'maxResults' => [ 'shape' => 'DetectorsMaxResults', ], ], ], 'GetDetectorsResult' => [ 'type' => 'structure', 'members' => [ 'detectors' => [ 'shape' => 'DetectorList', ], 'nextToken' => [ 'shape' => 'string', ], ], ], 'GetEntityTypesRequest' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'identifier', ], 'nextToken' => [ 'shape' => 'string', ], 'maxResults' => [ 'shape' => 'entityTypesMaxResults', ], ], ], 'GetEntityTypesResult' => [ 'type' => 'structure', 'members' => [ 'entityTypes' => [ 'shape' => 'entityTypeList', ], 'nextToken' => [ 'shape' => 'string', ], ], ], 'GetEventPredictionRequest' => [ 'type' => 'structure', 'required' => [ 'detectorId', 'eventId', 'eventTypeName', 'entities', 'eventTimestamp', 'eventVariables', ], 'members' => [ 'detectorId' => [ 'shape' => 'string', ], 'detectorVersionId' => [ 'shape' => 'wholeNumberVersionString', ], 'eventId' => [ 'shape' => 'string', ], 'eventTypeName' => [ 'shape' => 'string', ], 'entities' => [ 'shape' => 'listOfEntities', ], 'eventTimestamp' => [ 'shape' => 'utcTimestampISO8601', ], 'eventVariables' => [ 'shape' => 'EventVariableMap', ], 'externalModelEndpointDataBlobs' => [ 'shape' => 'ExternalModelEndpointDataBlobMap', ], ], ], 'GetEventPredictionResult' => [ 'type' => 'structure', 'members' => [ 'modelScores' => [ 'shape' => 'ListOfModelScores', ], 'ruleResults' => [ 'shape' => 'ListOfRuleResults', ], 'externalModelOutputs' => [ 'shape' => 'ListOfExternalModelOutputs', ], ], ], 'GetEventRequest' => [ 'type' => 'structure', 'required' => [ 'eventId', 'eventTypeName', ], 'members' => [ 'eventId' => [ 'shape' => 'string', ], 'eventTypeName' => [ 'shape' => 'string', ], ], ], 'GetEventResult' => [ 'type' => 'structure', 'members' => [ 'event' => [ 'shape' => 'Event', ], ], ], 'GetEventTypesRequest' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'identifier', ], 'nextToken' => [ 'shape' => 'string', ], 'maxResults' => [ 'shape' => 'eventTypesMaxResults', ], ], ], 'GetEventTypesResult' => [ 'type' => 'structure', 'members' => [ 'eventTypes' => [ 'shape' => 'eventTypeList', ], 'nextToken' => [ 'shape' => 'string', ], ], ], 'GetExternalModelsRequest' => [ 'type' => 'structure', 'members' => [ 'modelEndpoint' => [ 'shape' => 'string', ], 'nextToken' => [ 'shape' => 'string', ], 'maxResults' => [ 'shape' => 'ExternalModelsMaxResults', ], ], ], 'GetExternalModelsResult' => [ 'type' => 'structure', 'members' => [ 'externalModels' => [ 'shape' => 'ExternalModelList', ], 'nextToken' => [ 'shape' => 'string', ], ], ], 'GetKMSEncryptionKeyResult' => [ 'type' => 'structure', 'members' => [ 'kmsKey' => [ 'shape' => 'KMSKey', ], ], ], 'GetLabelsRequest' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'identifier', ], 'nextToken' => [ 'shape' => 'string', ], 'maxResults' => [ 'shape' => 'labelsMaxResults', ], ], ], 'GetLabelsResult' => [ 'type' => 'structure', 'members' => [ 'labels' => [ 'shape' => 'labelList', ], 'nextToken' => [ 'shape' => 'string', ], ], ], 'GetModelVersionRequest' => [ 'type' => 'structure', 'required' => [ 'modelId', 'modelType', 'modelVersionNumber', ], 'members' => [ 'modelId' => [ 'shape' => 'modelIdentifier', ], 'modelType' => [ 'shape' => 'ModelTypeEnum', ], 'modelVersionNumber' => [ 'shape' => 'floatVersionString', ], ], ], 'GetModelVersionResult' => [ 'type' => 'structure', 'members' => [ 'modelId' => [ 'shape' => 'modelIdentifier', ], 'modelType' => [ 'shape' => 'ModelTypeEnum', ], 'modelVersionNumber' => [ 'shape' => 'floatVersionString', ], 'trainingDataSource' => [ 'shape' => 'TrainingDataSourceEnum', ], 'trainingDataSchema' => [ 'shape' => 'TrainingDataSchema', ], 'externalEventsDetail' => [ 'shape' => 'ExternalEventsDetail', ], 'ingestedEventsDetail' => [ 'shape' => 'IngestedEventsDetail', ], 'status' => [ 'shape' => 'string', ], 'arn' => [ 'shape' => 'fraudDetectorArn', ], ], ], 'GetModelsRequest' => [ 'type' => 'structure', 'members' => [ 'modelId' => [ 'shape' => 'modelIdentifier', ], 'modelType' => [ 'shape' => 'ModelTypeEnum', ], 'nextToken' => [ 'shape' => 'string', ], 'maxResults' => [ 'shape' => 'modelsMaxPageSize', ], ], ], 'GetModelsResult' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'string', ], 'models' => [ 'shape' => 'modelList', ], ], ], 'GetOutcomesRequest' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'identifier', ], 'nextToken' => [ 'shape' => 'string', ], 'maxResults' => [ 'shape' => 'OutcomesMaxResults', ], ], ], 'GetOutcomesResult' => [ 'type' => 'structure', 'members' => [ 'outcomes' => [ 'shape' => 'OutcomeList', ], 'nextToken' => [ 'shape' => 'string', ], ], ], 'GetRulesRequest' => [ 'type' => 'structure', 'required' => [ 'detectorId', ], 'members' => [ 'ruleId' => [ 'shape' => 'identifier', ], 'detectorId' => [ 'shape' => 'identifier', ], 'ruleVersion' => [ 'shape' => 'wholeNumberVersionString', ], 'nextToken' => [ 'shape' => 'string', ], 'maxResults' => [ 'shape' => 'RulesMaxResults', ], ], ], 'GetRulesResult' => [ 'type' => 'structure', 'members' => [ 'ruleDetails' => [ 'shape' => 'RuleDetailList', ], 'nextToken' => [ 'shape' => 'string', ], ], ], 'GetVariablesRequest' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'string', ], 'nextToken' => [ 'shape' => 'string', ], 'maxResults' => [ 'shape' => 'VariablesMaxResults', ], ], ], 'GetVariablesResult' => [ 'type' => 'structure', 'members' => [ 'variables' => [ 'shape' => 'VariableList', ], 'nextToken' => [ 'shape' => 'string', ], ], ], 'IngestedEventStatistics' => [ 'type' => 'structure', 'members' => [ 'numberOfEvents' => [ 'shape' => 'Long', ], 'eventDataSizeInBytes' => [ 'shape' => 'Long', ], 'leastRecentEvent' => [ 'shape' => 'time', ], 'mostRecentEvent' => [ 'shape' => 'time', ], 'lastUpdatedTime' => [ 'shape' => 'time', ], ], ], 'IngestedEventsDetail' => [ 'type' => 'structure', 'required' => [ 'ingestedEventsTimeWindow', ], 'members' => [ 'ingestedEventsTimeWindow' => [ 'shape' => 'IngestedEventsTimeWindow', ], ], ], 'IngestedEventsTimeWindow' => [ 'type' => 'structure', 'required' => [ 'startTime', 'endTime', ], 'members' => [ 'startTime' => [ 'shape' => 'time', ], 'endTime' => [ 'shape' => 'time', ], ], ], 'Integer' => [ 'type' => 'integer', ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'exception' => true, 'fault' => true, ], 'JsonKeyToVariableMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'string', ], 'value' => [ 'shape' => 'string', ], ], 'KMSKey' => [ 'type' => 'structure', 'members' => [ 'kmsEncryptionKeyArn' => [ 'shape' => 'KmsEncryptionKeyArn', ], ], ], 'KmsEncryptionKeyArn' => [ 'type' => 'string', 'max' => 90, 'min' => 7, 'pattern' => '^DEFAULT|arn:[a-zA-Z0-9-]+:kms:[a-zA-Z0-9-]+:\\d{12}:key\\/\\w{8}-\\w{4}-\\w{4}-\\w{4}-\\w{12}$', ], 'Label' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'string', ], 'description' => [ 'shape' => 'description', ], 'lastUpdatedTime' => [ 'shape' => 'time', ], 'createdTime' => [ 'shape' => 'time', ], 'arn' => [ 'shape' => 'fraudDetectorArn', ], ], ], 'LabelSchema' => [ 'type' => 'structure', 'required' => [ 'labelMapper', ], 'members' => [ 'labelMapper' => [ 'shape' => 'labelMapper', ], 'unlabeledEventsTreatment' => [ 'shape' => 'UnlabeledEventsTreatment', ], ], ], 'Language' => [ 'type' => 'string', 'enum' => [ 'DETECTORPL', ], ], 'ListOfExternalModelOutputs' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExternalModelOutputs', ], ], 'ListOfLogOddsMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogOddsMetric', ], ], 'ListOfModelScores' => [ 'type' => 'list', 'member' => [ 'shape' => 'ModelScores', ], ], 'ListOfModelVersions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ModelVersion', ], ], 'ListOfRuleResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleResult', ], ], 'ListOfStrings' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', ], 'members' => [ 'resourceARN' => [ 'shape' => 'fraudDetectorArn', ], 'nextToken' => [ 'shape' => 'string', ], 'maxResults' => [ 'shape' => 'TagsMaxResults', ], ], ], 'ListTagsForResourceResult' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'tagList', ], 'nextToken' => [ 'shape' => 'string', ], ], ], 'LogOddsMetric' => [ 'type' => 'structure', 'required' => [ 'variableName', 'variableType', 'variableImportance', ], 'members' => [ 'variableName' => [ 'shape' => 'string', ], 'variableType' => [ 'shape' => 'string', ], 'variableImportance' => [ 'shape' => 'float', ], ], ], 'Long' => [ 'type' => 'long', ], 'MetricDataPoint' => [ 'type' => 'structure', 'members' => [ 'fpr' => [ 'shape' => 'float', ], 'precision' => [ 'shape' => 'float', ], 'tpr' => [ 'shape' => 'float', ], 'threshold' => [ 'shape' => 'float', ], ], ], 'Model' => [ 'type' => 'structure', 'members' => [ 'modelId' => [ 'shape' => 'modelIdentifier', ], 'modelType' => [ 'shape' => 'ModelTypeEnum', ], 'description' => [ 'shape' => 'description', ], 'eventTypeName' => [ 'shape' => 'string', ], 'createdTime' => [ 'shape' => 'time', ], 'lastUpdatedTime' => [ 'shape' => 'time', ], 'arn' => [ 'shape' => 'fraudDetectorArn', ], ], ], 'ModelEndpointDataBlob' => [ 'type' => 'structure', 'members' => [ 'byteBuffer' => [ 'shape' => 'blob', ], 'contentType' => [ 'shape' => 'contentType', ], ], ], 'ModelEndpointStatus' => [ 'type' => 'string', 'enum' => [ 'ASSOCIATED', 'DISSOCIATED', ], ], 'ModelInputConfiguration' => [ 'type' => 'structure', 'required' => [ 'useEventVariables', ], 'members' => [ 'eventTypeName' => [ 'shape' => 'identifier', ], 'format' => [ 'shape' => 'ModelInputDataFormat', ], 'useEventVariables' => [ 'shape' => 'UseEventVariables', ], 'jsonInputTemplate' => [ 'shape' => 'modelInputTemplate', ], 'csvInputTemplate' => [ 'shape' => 'modelInputTemplate', ], ], ], 'ModelInputDataFormat' => [ 'type' => 'string', 'enum' => [ 'TEXT_CSV', 'APPLICATION_JSON', ], ], 'ModelOutputConfiguration' => [ 'type' => 'structure', 'required' => [ 'format', ], 'members' => [ 'format' => [ 'shape' => 'ModelOutputDataFormat', ], 'jsonKeyToVariableMap' => [ 'shape' => 'JsonKeyToVariableMap', ], 'csvIndexToVariableMap' => [ 'shape' => 'CsvIndexToVariableMap', ], ], ], 'ModelOutputDataFormat' => [ 'type' => 'string', 'enum' => [ 'TEXT_CSV', 'APPLICATION_JSONLINES', ], ], 'ModelPredictionMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'string', ], 'value' => [ 'shape' => 'float', ], ], 'ModelScores' => [ 'type' => 'structure', 'members' => [ 'modelVersion' => [ 'shape' => 'ModelVersion', ], 'scores' => [ 'shape' => 'ModelPredictionMap', ], ], ], 'ModelSource' => [ 'type' => 'string', 'enum' => [ 'SAGEMAKER', ], ], 'ModelTypeEnum' => [ 'type' => 'string', 'enum' => [ 'ONLINE_FRAUD_INSIGHTS', 'TRANSACTION_FRAUD_INSIGHTS', ], ], 'ModelVersion' => [ 'type' => 'structure', 'required' => [ 'modelId', 'modelType', 'modelVersionNumber', ], 'members' => [ 'modelId' => [ 'shape' => 'modelIdentifier', ], 'modelType' => [ 'shape' => 'ModelTypeEnum', ], 'modelVersionNumber' => [ 'shape' => 'floatVersionString', ], 'arn' => [ 'shape' => 'fraudDetectorArn', ], ], ], 'ModelVersionDetail' => [ 'type' => 'structure', 'members' => [ 'modelId' => [ 'shape' => 'modelIdentifier', ], 'modelType' => [ 'shape' => 'ModelTypeEnum', ], 'modelVersionNumber' => [ 'shape' => 'floatVersionString', ], 'status' => [ 'shape' => 'string', ], 'trainingDataSource' => [ 'shape' => 'TrainingDataSourceEnum', ], 'trainingDataSchema' => [ 'shape' => 'TrainingDataSchema', ], 'externalEventsDetail' => [ 'shape' => 'ExternalEventsDetail', ], 'ingestedEventsDetail' => [ 'shape' => 'IngestedEventsDetail', ], 'trainingResult' => [ 'shape' => 'TrainingResult', ], 'lastUpdatedTime' => [ 'shape' => 'time', ], 'createdTime' => [ 'shape' => 'time', ], 'arn' => [ 'shape' => 'fraudDetectorArn', ], ], ], 'ModelVersionStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', 'TRAINING_CANCELLED', ], ], 'NameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', ], 'max' => 100, 'min' => 1, ], 'NonEmptyListOfStrings' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', ], 'min' => 1, ], 'Outcome' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'identifier', ], 'description' => [ 'shape' => 'description', ], 'lastUpdatedTime' => [ 'shape' => 'time', ], 'createdTime' => [ 'shape' => 'time', ], 'arn' => [ 'shape' => 'fraudDetectorArn', ], ], ], 'OutcomeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Outcome', ], ], 'OutcomesMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 50, ], 'PutDetectorRequest' => [ 'type' => 'structure', 'required' => [ 'detectorId', 'eventTypeName', ], 'members' => [ 'detectorId' => [ 'shape' => 'identifier', ], 'description' => [ 'shape' => 'description', ], 'eventTypeName' => [ 'shape' => 'identifier', ], 'tags' => [ 'shape' => 'tagList', ], ], ], 'PutDetectorResult' => [ 'type' => 'structure', 'members' => [], ], 'PutEntityTypeRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'identifier', ], 'description' => [ 'shape' => 'description', ], 'tags' => [ 'shape' => 'tagList', ], ], ], 'PutEntityTypeResult' => [ 'type' => 'structure', 'members' => [], ], 'PutEventTypeRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'eventVariables', 'entityTypes', ], 'members' => [ 'name' => [ 'shape' => 'identifier', ], 'description' => [ 'shape' => 'description', ], 'eventVariables' => [ 'shape' => 'NonEmptyListOfStrings', ], 'labels' => [ 'shape' => 'ListOfStrings', ], 'entityTypes' => [ 'shape' => 'NonEmptyListOfStrings', ], 'eventIngestion' => [ 'shape' => 'EventIngestion', ], 'tags' => [ 'shape' => 'tagList', ], ], ], 'PutEventTypeResult' => [ 'type' => 'structure', 'members' => [], ], 'PutExternalModelRequest' => [ 'type' => 'structure', 'required' => [ 'modelEndpoint', 'modelSource', 'invokeModelEndpointRoleArn', 'inputConfiguration', 'outputConfiguration', 'modelEndpointStatus', ], 'members' => [ 'modelEndpoint' => [ 'shape' => 'sageMakerEndpointIdentifier', ], 'modelSource' => [ 'shape' => 'ModelSource', ], 'invokeModelEndpointRoleArn' => [ 'shape' => 'string', ], 'inputConfiguration' => [ 'shape' => 'ModelInputConfiguration', ], 'outputConfiguration' => [ 'shape' => 'ModelOutputConfiguration', ], 'modelEndpointStatus' => [ 'shape' => 'ModelEndpointStatus', ], 'tags' => [ 'shape' => 'tagList', ], ], ], 'PutExternalModelResult' => [ 'type' => 'structure', 'members' => [], ], 'PutKMSEncryptionKeyRequest' => [ 'type' => 'structure', 'required' => [ 'kmsEncryptionKeyArn', ], 'members' => [ 'kmsEncryptionKeyArn' => [ 'shape' => 'KmsEncryptionKeyArn', ], ], ], 'PutKMSEncryptionKeyResult' => [ 'type' => 'structure', 'members' => [], ], 'PutLabelRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'identifier', ], 'description' => [ 'shape' => 'description', ], 'tags' => [ 'shape' => 'tagList', ], ], ], 'PutLabelResult' => [ 'type' => 'structure', 'members' => [], ], 'PutOutcomeRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'identifier', ], 'description' => [ 'shape' => 'description', ], 'tags' => [ 'shape' => 'tagList', ], ], ], 'PutOutcomeResult' => [ 'type' => 'structure', 'members' => [], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'ResourceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'Rule' => [ 'type' => 'structure', 'required' => [ 'detectorId', 'ruleId', 'ruleVersion', ], 'members' => [ 'detectorId' => [ 'shape' => 'identifier', ], 'ruleId' => [ 'shape' => 'identifier', ], 'ruleVersion' => [ 'shape' => 'wholeNumberVersionString', ], ], ], 'RuleDetail' => [ 'type' => 'structure', 'members' => [ 'ruleId' => [ 'shape' => 'identifier', ], 'description' => [ 'shape' => 'description', ], 'detectorId' => [ 'shape' => 'identifier', ], 'ruleVersion' => [ 'shape' => 'wholeNumberVersionString', ], 'expression' => [ 'shape' => 'ruleExpression', ], 'language' => [ 'shape' => 'Language', ], 'outcomes' => [ 'shape' => 'NonEmptyListOfStrings', ], 'lastUpdatedTime' => [ 'shape' => 'time', ], 'createdTime' => [ 'shape' => 'time', ], 'arn' => [ 'shape' => 'fraudDetectorArn', ], ], ], 'RuleDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleDetail', ], ], 'RuleExecutionMode' => [ 'type' => 'string', 'enum' => [ 'ALL_MATCHED', 'FIRST_MATCHED', ], ], 'RuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Rule', ], ], 'RuleResult' => [ 'type' => 'structure', 'members' => [ 'ruleId' => [ 'shape' => 'string', ], 'outcomes' => [ 'shape' => 'ListOfStrings', ], ], ], 'RulesMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 50, ], 'SendEventRequest' => [ 'type' => 'structure', 'required' => [ 'eventId', 'eventTypeName', 'eventTimestamp', 'eventVariables', 'entities', ], 'members' => [ 'eventId' => [ 'shape' => 'identifier', ], 'eventTypeName' => [ 'shape' => 'identifier', ], 'eventTimestamp' => [ 'shape' => 'utcTimestampISO8601', ], 'eventVariables' => [ 'shape' => 'EventVariableMap', ], 'assignedLabel' => [ 'shape' => 'identifier', ], 'labelTimestamp' => [ 'shape' => 'utcTimestampISO8601', ], 'entities' => [ 'shape' => 'listOfEntities', ], ], ], 'SendEventResult' => [ 'type' => 'structure', 'members' => [], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'tagKey', ], 'value' => [ 'shape' => 'tagValue', ], ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', 'tags', ], 'members' => [ 'resourceARN' => [ 'shape' => 'fraudDetectorArn', ], 'tags' => [ 'shape' => 'tagList', ], ], ], 'TagResourceResult' => [ 'type' => 'structure', 'members' => [], ], 'TagsMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 50, ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'TrainingDataSchema' => [ 'type' => 'structure', 'required' => [ 'modelVariables', 'labelSchema', ], 'members' => [ 'modelVariables' => [ 'shape' => 'ListOfStrings', ], 'labelSchema' => [ 'shape' => 'LabelSchema', ], ], ], 'TrainingDataSourceEnum' => [ 'type' => 'string', 'enum' => [ 'EXTERNAL_EVENTS', 'INGESTED_EVENTS', ], ], 'TrainingMetrics' => [ 'type' => 'structure', 'members' => [ 'auc' => [ 'shape' => 'float', ], 'metricDataPoints' => [ 'shape' => 'metricDataPointsList', ], ], ], 'TrainingResult' => [ 'type' => 'structure', 'members' => [ 'dataValidationMetrics' => [ 'shape' => 'DataValidationMetrics', ], 'trainingMetrics' => [ 'shape' => 'TrainingMetrics', ], 'variableImportanceMetrics' => [ 'shape' => 'VariableImportanceMetrics', ], ], ], 'UnlabeledEventsTreatment' => [ 'type' => 'string', 'enum' => [ 'IGNORE', 'FRAUD', 'LEGIT', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', 'tagKeys', ], 'members' => [ 'resourceARN' => [ 'shape' => 'fraudDetectorArn', ], 'tagKeys' => [ 'shape' => 'tagKeyList', ], ], ], 'UntagResourceResult' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDetectorVersionMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'detectorId', 'detectorVersionId', 'description', ], 'members' => [ 'detectorId' => [ 'shape' => 'identifier', ], 'detectorVersionId' => [ 'shape' => 'wholeNumberVersionString', ], 'description' => [ 'shape' => 'description', ], ], ], 'UpdateDetectorVersionMetadataResult' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDetectorVersionRequest' => [ 'type' => 'structure', 'required' => [ 'detectorId', 'detectorVersionId', 'externalModelEndpoints', 'rules', ], 'members' => [ 'detectorId' => [ 'shape' => 'identifier', ], 'detectorVersionId' => [ 'shape' => 'wholeNumberVersionString', ], 'externalModelEndpoints' => [ 'shape' => 'ListOfStrings', ], 'rules' => [ 'shape' => 'RuleList', ], 'description' => [ 'shape' => 'description', ], 'modelVersions' => [ 'shape' => 'ListOfModelVersions', ], 'ruleExecutionMode' => [ 'shape' => 'RuleExecutionMode', ], ], ], 'UpdateDetectorVersionResult' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDetectorVersionStatusRequest' => [ 'type' => 'structure', 'required' => [ 'detectorId', 'detectorVersionId', 'status', ], 'members' => [ 'detectorId' => [ 'shape' => 'identifier', ], 'detectorVersionId' => [ 'shape' => 'wholeNumberVersionString', ], 'status' => [ 'shape' => 'DetectorVersionStatus', ], ], ], 'UpdateDetectorVersionStatusResult' => [ 'type' => 'structure', 'members' => [], ], 'UpdateEventLabelRequest' => [ 'type' => 'structure', 'required' => [ 'eventId', 'eventTypeName', 'assignedLabel', 'labelTimestamp', ], 'members' => [ 'eventId' => [ 'shape' => 'identifier', ], 'eventTypeName' => [ 'shape' => 'identifier', ], 'assignedLabel' => [ 'shape' => 'identifier', ], 'labelTimestamp' => [ 'shape' => 'utcTimestampISO8601', ], ], ], 'UpdateEventLabelResult' => [ 'type' => 'structure', 'members' => [], ], 'UpdateModelRequest' => [ 'type' => 'structure', 'required' => [ 'modelId', 'modelType', ], 'members' => [ 'modelId' => [ 'shape' => 'modelIdentifier', ], 'modelType' => [ 'shape' => 'ModelTypeEnum', ], 'description' => [ 'shape' => 'description', ], ], ], 'UpdateModelResult' => [ 'type' => 'structure', 'members' => [], ], 'UpdateModelVersionRequest' => [ 'type' => 'structure', 'required' => [ 'modelId', 'modelType', 'majorVersionNumber', ], 'members' => [ 'modelId' => [ 'shape' => 'modelIdentifier', ], 'modelType' => [ 'shape' => 'ModelTypeEnum', ], 'majorVersionNumber' => [ 'shape' => 'wholeNumberVersionString', ], 'externalEventsDetail' => [ 'shape' => 'ExternalEventsDetail', ], 'ingestedEventsDetail' => [ 'shape' => 'IngestedEventsDetail', ], 'tags' => [ 'shape' => 'tagList', ], ], ], 'UpdateModelVersionResult' => [ 'type' => 'structure', 'members' => [ 'modelId' => [ 'shape' => 'modelIdentifier', ], 'modelType' => [ 'shape' => 'ModelTypeEnum', ], 'modelVersionNumber' => [ 'shape' => 'floatVersionString', ], 'status' => [ 'shape' => 'string', ], ], ], 'UpdateModelVersionStatusRequest' => [ 'type' => 'structure', 'required' => [ 'modelId', 'modelType', 'modelVersionNumber', 'status', ], 'members' => [ 'modelId' => [ 'shape' => 'modelIdentifier', ], 'modelType' => [ 'shape' => 'ModelTypeEnum', ], 'modelVersionNumber' => [ 'shape' => 'floatVersionString', ], 'status' => [ 'shape' => 'ModelVersionStatus', ], ], ], 'UpdateModelVersionStatusResult' => [ 'type' => 'structure', 'members' => [], ], 'UpdateRuleMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'rule', 'description', ], 'members' => [ 'rule' => [ 'shape' => 'Rule', ], 'description' => [ 'shape' => 'description', ], ], ], 'UpdateRuleMetadataResult' => [ 'type' => 'structure', 'members' => [], ], 'UpdateRuleVersionRequest' => [ 'type' => 'structure', 'required' => [ 'rule', 'expression', 'language', 'outcomes', ], 'members' => [ 'rule' => [ 'shape' => 'Rule', ], 'description' => [ 'shape' => 'description', ], 'expression' => [ 'shape' => 'ruleExpression', ], 'language' => [ 'shape' => 'Language', ], 'outcomes' => [ 'shape' => 'NonEmptyListOfStrings', ], 'tags' => [ 'shape' => 'tagList', ], ], ], 'UpdateRuleVersionResult' => [ 'type' => 'structure', 'members' => [ 'rule' => [ 'shape' => 'Rule', ], ], ], 'UpdateVariableRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'string', ], 'defaultValue' => [ 'shape' => 'string', ], 'description' => [ 'shape' => 'string', ], 'variableType' => [ 'shape' => 'string', ], ], ], 'UpdateVariableResult' => [ 'type' => 'structure', 'members' => [], ], 'UseEventVariables' => [ 'type' => 'boolean', ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'Variable' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'string', ], 'dataType' => [ 'shape' => 'DataType', ], 'dataSource' => [ 'shape' => 'DataSource', ], 'defaultValue' => [ 'shape' => 'string', ], 'description' => [ 'shape' => 'string', ], 'variableType' => [ 'shape' => 'string', ], 'lastUpdatedTime' => [ 'shape' => 'time', ], 'createdTime' => [ 'shape' => 'time', ], 'arn' => [ 'shape' => 'fraudDetectorArn', ], ], ], 'VariableEntry' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'string', ], 'dataType' => [ 'shape' => 'string', ], 'dataSource' => [ 'shape' => 'string', ], 'defaultValue' => [ 'shape' => 'string', ], 'description' => [ 'shape' => 'string', ], 'variableType' => [ 'shape' => 'string', ], ], ], 'VariableEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VariableEntry', ], 'max' => 25, 'min' => 1, ], 'VariableImportanceMetrics' => [ 'type' => 'structure', 'members' => [ 'logOddsMetrics' => [ 'shape' => 'ListOfLogOddsMetrics', ], ], ], 'VariableList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Variable', ], ], 'VariablesMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 50, ], 'attributeKey' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'attributeValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'sensitive' => true, ], 'batchImportsMaxPageSize' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'batchPredictionsMaxPageSize' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'blob' => [ 'type' => 'blob', ], 'contentType' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'description' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'entityRestrictedString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[0-9A-Za-z_.@+-]+$', ], 'entityTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EntityType', ], ], 'entityTypesMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 5, ], 'eventTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventType', ], ], 'eventTypesMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 5, ], 'fieldValidationMessageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldValidationMessage', ], ], 'fileValidationMessageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FileValidationMessage', ], ], 'float' => [ 'type' => 'float', ], 'floatVersionString' => [ 'type' => 'string', 'max' => 7, 'min' => 3, 'pattern' => '^[1-9][0-9]{0,3}\\.[0-9]{1,2}$', ], 'fraudDetectorArn' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^arn\\:aws[a-z-]{0,15}\\:frauddetector\\:[a-z0-9-]{3,20}\\:[0-9]{12}\\:[^\\s]{2,128}$', ], 'iamRoleArn' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^arn\\:aws[a-z-]{0,15}\\:iam\\:\\:[0-9]{12}\\:role\\/[^\\s]{2,64}$', ], 'identifier' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[0-9a-z_-]+$', ], 'integer' => [ 'type' => 'integer', ], 'labelList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Label', ], ], 'labelMapper' => [ 'type' => 'map', 'key' => [ 'shape' => 'string', ], 'value' => [ 'shape' => 'NonEmptyListOfStrings', ], ], 'labelsMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 10, ], 'listOfEntities' => [ 'type' => 'list', 'member' => [ 'shape' => 'Entity', ], ], 'metricDataPointsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricDataPoint', ], ], 'modelIdentifier' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[0-9a-z_]+$', ], 'modelInputTemplate' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, ], 'modelList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Model', ], ], 'modelVersionDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ModelVersionDetail', ], ], 'modelsMaxPageSize' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 1, ], 'ruleExpression' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'sensitive' => true, ], 's3BucketLocation' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '^s3:\\/\\/(.+)$', ], 'sageMakerEndpointIdentifier' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[0-9A-Za-z_-]+$', ], 'string' => [ 'type' => 'string', ], 'tagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'tagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'tagKey', ], 'max' => 50, 'min' => 0, ], 'tagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 0, ], 'tagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'time' => [ 'type' => 'string', 'max' => 30, 'min' => 11, ], 'utcTimestampISO8601' => [ 'type' => 'string', 'max' => 30, 'min' => 10, ], 'variableName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'variableValue' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'sensitive' => true, ], 'wholeNumberVersionString' => [ 'type' => 'string', 'max' => 5, 'min' => 1, 'pattern' => '^([1-9][0-9]*)$', ], ],];
