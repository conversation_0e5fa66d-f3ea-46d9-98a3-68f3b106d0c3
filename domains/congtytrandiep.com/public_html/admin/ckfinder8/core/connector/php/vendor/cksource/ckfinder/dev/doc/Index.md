Welcome to the **CKFinder 3 &ndash; PHP Connector Documentation**. 

This website contains documentation about the PHP connector for CKFinder 3 and includes information about:

 * Enabling the PHP connector.
 * Configuring the PHP connector.
 * Creating PHP plugins.

Use the navigation menu on the left side to browse the documentation.

## Feedback

Use the [CKFinder Issue Tracker](https://github.com/ckfinder/ckfinder/issues) to:

 * Report issues.
 * Submit feature requests.
 * Suggest improvements in the documentation.

If you are unsure what information to provide when reporting a bug, check [Reporting Issues](https://ckeditor.com/docs/ckfinder/ckfinder3/#!/guide/dev_feedback).

## Translations

In order to submit translations for CKFinder please use the
[ckfinder-translations](https://github.com/ckfinder/ckfinder-translations) repository.

## Additional Documentation

CKFinder consists of two parts: the client side and the server-side connector(s).

The client-side part is common across all distributions (PHP and ASP.NET), while
the server-side parts are different for each language. As a result, there are multiple documentation websites available.

If you are looking for information about the JavaScript part of CKFinder and things like:

 * integrating CKFinder with your website or with [CKEditor](https://ckeditor.com/),
 * client-side configuration options,
 * API documentation,
 * tutorials about creating JavaScript plugins,
 * tutorials about creating skins,

see the [CKFinder 3 Documentation](https://ckeditor.com/docs/ckfinder/ckfinder3/).

If you want to learn more about the ASP.NET server-side connector, refer to the [CKFinder 3 – ASP.NET Connector Documentation](https://ckeditor.com/docs/ckfinder/ckfinder3-net/), 
for the Java server-side connector, refer to the [CKFinder 3 – Java Connector Documentation](https://ckeditor.com/docs/ckfinder/ckfinder3-java/).

## License

CKFinder is distributed under the [CKFinder License Agreement](https://sales.ckeditor.com/license/ckfinder). 
You can purchase a license [here](https://ckeditor.com/pricing/) together with your CKEditor license.
