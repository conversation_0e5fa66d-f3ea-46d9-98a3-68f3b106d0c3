<?php
// This file was auto-generated from sdk-root/src/data/lakeformation/2017-03-31/paginators-1.json
return [ 'pagination' => [ 'GetEffectivePermissionsForPath' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', ], 'GetTableObjects' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', ], 'GetWorkUnits' => [ 'input_token' => 'NextToken', 'limit_key' => 'PageSize', 'output_token' => 'NextToken', 'result_key' => 'WorkUnitRanges', ], 'ListDataCellsFilter' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'DataCellsFilters', ], 'ListLFTags' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'LFTags', ], 'ListPermissions' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', ], 'ListResources' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', ], 'ListTableStorageOptimizers' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', ], 'ListTransactions' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', ], 'SearchDatabasesByLFTags' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'DatabaseList', ], 'SearchTablesByLFTags' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'TableList', ], ],];
