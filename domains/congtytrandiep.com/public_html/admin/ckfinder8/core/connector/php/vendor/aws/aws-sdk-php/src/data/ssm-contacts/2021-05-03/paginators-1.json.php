<?php
// This file was auto-generated from sdk-root/src/data/ssm-contacts/2021-05-03/paginators-1.json
return [ 'pagination' => [ 'ListContactChannels' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'ContactChannels', ], 'ListContacts' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'Contacts', ], 'ListEngagements' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'Engagements', ], 'ListPageReceipts' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'Receipts', ], 'ListPagesByContact' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'Pages', ], 'ListPagesByEngagement' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'Pages', ], ],];
