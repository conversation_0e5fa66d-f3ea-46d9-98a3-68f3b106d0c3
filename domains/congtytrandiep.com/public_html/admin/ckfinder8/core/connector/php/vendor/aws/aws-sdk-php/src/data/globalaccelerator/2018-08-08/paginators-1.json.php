<?php
// This file was auto-generated from sdk-root/src/data/globalaccelerator/2018-08-08/paginators-1.json
return [ 'pagination' => [ 'ListAccelerators' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'Accelerators', ], 'ListByoipCidrs' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'ByoipCidrs', ], 'ListCustomRoutingAccelerators' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'Accelerators', ], 'ListCustomRoutingEndpointGroups' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', ], 'ListCustomRoutingListeners' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'Listeners', ], 'ListCustomRoutingPortMappings' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'PortMappings', ], 'ListCustomRoutingPortMappingsByDestination' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'DestinationPortMappings', ], 'ListEndpointGroups' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'EndpointGroups', ], 'ListListeners' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'Listeners', ], ],];
