<?php
// This file was auto-generated from sdk-root/src/data/mgn/2020-02-26/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-02-26', 'endpointPrefix' => 'mgn', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'mgn', 'serviceFullName' => 'Application Migration Service', 'serviceId' => 'mgn', 'signatureVersion' => 'v4', 'signingName' => 'mgn', 'uid' => 'mgn-2020-02-26', ], 'operations' => [ 'ChangeServerLifeCycleState' => [ 'name' => 'ChangeServerLifeCycleState', 'http' => [ 'method' => 'POST', 'requestUri' => '/ChangeServerLifeCycleState', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ChangeServerLifeCycleStateRequest', ], 'output' => [ 'shape' => 'SourceServer', ], 'errors' => [ [ 'shape' => 'UninitializedAccountException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateReplicationConfigurationTemplate' => [ 'name' => 'CreateReplicationConfigurationTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateReplicationConfigurationTemplate', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateReplicationConfigurationTemplateRequest', ], 'output' => [ 'shape' => 'ReplicationConfigurationTemplate', ], 'errors' => [ [ 'shape' => 'UninitializedAccountException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteJob' => [ 'name' => 'DeleteJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteJob', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteJobRequest', ], 'output' => [ 'shape' => 'DeleteJobResponse', ], 'errors' => [ [ 'shape' => 'UninitializedAccountException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteReplicationConfigurationTemplate' => [ 'name' => 'DeleteReplicationConfigurationTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteReplicationConfigurationTemplate', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteReplicationConfigurationTemplateRequest', ], 'output' => [ 'shape' => 'DeleteReplicationConfigurationTemplateResponse', ], 'errors' => [ [ 'shape' => 'UninitializedAccountException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteSourceServer' => [ 'name' => 'DeleteSourceServer', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteSourceServer', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteSourceServerRequest', ], 'output' => [ 'shape' => 'DeleteSourceServerResponse', ], 'errors' => [ [ 'shape' => 'UninitializedAccountException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'DeleteVcenterClient' => [ 'name' => 'DeleteVcenterClient', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteVcenterClient', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteVcenterClientRequest', ], 'errors' => [ [ 'shape' => 'UninitializedAccountException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'DescribeJobLogItems' => [ 'name' => 'DescribeJobLogItems', 'http' => [ 'method' => 'POST', 'requestUri' => '/DescribeJobLogItems', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeJobLogItemsRequest', ], 'output' => [ 'shape' => 'DescribeJobLogItemsResponse', ], 'errors' => [ [ 'shape' => 'UninitializedAccountException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeJobs' => [ 'name' => 'DescribeJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/DescribeJobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeJobsRequest', ], 'output' => [ 'shape' => 'DescribeJobsResponse', ], 'errors' => [ [ 'shape' => 'UninitializedAccountException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeReplicationConfigurationTemplates' => [ 'name' => 'DescribeReplicationConfigurationTemplates', 'http' => [ 'method' => 'POST', 'requestUri' => '/DescribeReplicationConfigurationTemplates', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeReplicationConfigurationTemplatesRequest', ], 'output' => [ 'shape' => 'DescribeReplicationConfigurationTemplatesResponse', ], 'errors' => [ [ 'shape' => 'UninitializedAccountException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeSourceServers' => [ 'name' => 'DescribeSourceServers', 'http' => [ 'method' => 'POST', 'requestUri' => '/DescribeSourceServers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeSourceServersRequest', ], 'output' => [ 'shape' => 'DescribeSourceServersResponse', ], 'errors' => [ [ 'shape' => 'UninitializedAccountException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribeVcenterClients' => [ 'name' => 'DescribeVcenterClients', 'http' => [ 'method' => 'GET', 'requestUri' => '/DescribeVcenterClients', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeVcenterClientsRequest', ], 'output' => [ 'shape' => 'DescribeVcenterClientsResponse', ], 'errors' => [ [ 'shape' => 'UninitializedAccountException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'DisconnectFromService' => [ 'name' => 'DisconnectFromService', 'http' => [ 'method' => 'POST', 'requestUri' => '/DisconnectFromService', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisconnectFromServiceRequest', ], 'output' => [ 'shape' => 'SourceServer', ], 'errors' => [ [ 'shape' => 'UninitializedAccountException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'FinalizeCutover' => [ 'name' => 'FinalizeCutover', 'http' => [ 'method' => 'POST', 'requestUri' => '/FinalizeCutover', 'responseCode' => 200, ], 'input' => [ 'shape' => 'FinalizeCutoverRequest', ], 'output' => [ 'shape' => 'SourceServer', ], 'errors' => [ [ 'shape' => 'UninitializedAccountException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetLaunchConfiguration' => [ 'name' => 'GetLaunchConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetLaunchConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetLaunchConfigurationRequest', ], 'output' => [ 'shape' => 'LaunchConfiguration', ], 'errors' => [ [ 'shape' => 'UninitializedAccountException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetReplicationConfiguration' => [ 'name' => 'GetReplicationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetReplicationConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetReplicationConfigurationRequest', ], 'output' => [ 'shape' => 'ReplicationConfiguration', ], 'errors' => [ [ 'shape' => 'UninitializedAccountException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'InitializeService' => [ 'name' => 'InitializeService', 'http' => [ 'method' => 'POST', 'requestUri' => '/InitializeService', 'responseCode' => 204, ], 'input' => [ 'shape' => 'InitializeServiceRequest', ], 'output' => [ 'shape' => 'InitializeServiceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'MarkAsArchived' => [ 'name' => 'MarkAsArchived', 'http' => [ 'method' => 'POST', 'requestUri' => '/MarkAsArchived', 'responseCode' => 200, ], 'input' => [ 'shape' => 'MarkAsArchivedRequest', ], 'output' => [ 'shape' => 'SourceServer', ], 'errors' => [ [ 'shape' => 'UninitializedAccountException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'RetryDataReplication' => [ 'name' => 'RetryDataReplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/RetryDataReplication', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RetryDataReplicationRequest', ], 'output' => [ 'shape' => 'SourceServer', ], 'errors' => [ [ 'shape' => 'UninitializedAccountException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'StartCutover' => [ 'name' => 'StartCutover', 'http' => [ 'method' => 'POST', 'requestUri' => '/StartCutover', 'responseCode' => 202, ], 'input' => [ 'shape' => 'StartCutoverRequest', ], 'output' => [ 'shape' => 'StartCutoverResponse', ], 'errors' => [ [ 'shape' => 'UninitializedAccountException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'StartReplication' => [ 'name' => 'StartReplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/StartReplication', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartReplicationRequest', ], 'output' => [ 'shape' => 'SourceServer', ], 'errors' => [ [ 'shape' => 'UninitializedAccountException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], ], 'StartTest' => [ 'name' => 'StartTest', 'http' => [ 'method' => 'POST', 'requestUri' => '/StartTest', 'responseCode' => 202, ], 'input' => [ 'shape' => 'StartTestRequest', ], 'output' => [ 'shape' => 'StartTestResponse', ], 'errors' => [ [ 'shape' => 'UninitializedAccountException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'TerminateTargetInstances' => [ 'name' => 'TerminateTargetInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/TerminateTargetInstances', 'responseCode' => 202, ], 'input' => [ 'shape' => 'TerminateTargetInstancesRequest', ], 'output' => [ 'shape' => 'TerminateTargetInstancesResponse', ], 'errors' => [ [ 'shape' => 'UninitializedAccountException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdateLaunchConfiguration' => [ 'name' => 'UpdateLaunchConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateLaunchConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateLaunchConfigurationRequest', ], 'output' => [ 'shape' => 'LaunchConfiguration', ], 'errors' => [ [ 'shape' => 'UninitializedAccountException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'UpdateReplicationConfiguration' => [ 'name' => 'UpdateReplicationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateReplicationConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateReplicationConfigurationRequest', ], 'output' => [ 'shape' => 'ReplicationConfiguration', ], 'errors' => [ [ 'shape' => 'UninitializedAccountException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'UpdateReplicationConfigurationTemplate' => [ 'name' => 'UpdateReplicationConfigurationTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateReplicationConfigurationTemplate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateReplicationConfigurationTemplateRequest', ], 'output' => [ 'shape' => 'ReplicationConfigurationTemplate', ], 'errors' => [ [ 'shape' => 'UninitializedAccountException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateSourceServerReplicationType' => [ 'name' => 'UpdateSourceServerReplicationType', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateSourceServerReplicationType', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSourceServerReplicationTypeRequest', ], 'output' => [ 'shape' => 'SourceServer', ], 'errors' => [ [ 'shape' => 'UninitializedAccountException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], ], 'shapes' => [ 'ARN' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'LargeBoundedString', ], 'message' => [ 'shape' => 'LargeBoundedString', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'BoundedString' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'CPU' => [ 'type' => 'structure', 'members' => [ 'cores' => [ 'shape' => 'PositiveInteger', ], 'modelName' => [ 'shape' => 'BoundedString', ], ], ], 'ChangeServerLifeCycleStateRequest' => [ 'type' => 'structure', 'required' => [ 'lifeCycle', 'sourceServerID', ], 'members' => [ 'lifeCycle' => [ 'shape' => 'ChangeServerLifeCycleStateSourceServerLifecycle', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], ], ], 'ChangeServerLifeCycleStateSourceServerLifecycle' => [ 'type' => 'structure', 'required' => [ 'state', ], 'members' => [ 'state' => [ 'shape' => 'ChangeServerLifeCycleStateSourceServerLifecycleState', ], ], ], 'ChangeServerLifeCycleStateSourceServerLifecycleState' => [ 'type' => 'string', 'enum' => [ 'READY_FOR_TEST', 'READY_FOR_CUTOVER', 'CUTOVER', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'LargeBoundedString', ], 'message' => [ 'shape' => 'LargeBoundedString', ], 'resourceId' => [ 'shape' => 'LargeBoundedString', ], 'resourceType' => [ 'shape' => 'LargeBoundedString', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'Cpus' => [ 'type' => 'list', 'member' => [ 'shape' => 'CPU', ], 'max' => 256, 'min' => 0, ], 'CreateReplicationConfigurationTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'associateDefaultSecurityGroup', 'bandwidthThrottling', 'createPublicIP', 'dataPlaneRouting', 'defaultLargeStagingDiskType', 'ebsEncryption', 'replicationServerInstanceType', 'replicationServersSecurityGroupsIDs', 'stagingAreaSubnetId', 'stagingAreaTags', 'useDedicatedReplicationServer', ], 'members' => [ 'associateDefaultSecurityGroup' => [ 'shape' => 'Boolean', ], 'bandwidthThrottling' => [ 'shape' => 'PositiveInteger', ], 'createPublicIP' => [ 'shape' => 'Boolean', ], 'dataPlaneRouting' => [ 'shape' => 'ReplicationConfigurationDataPlaneRouting', ], 'defaultLargeStagingDiskType' => [ 'shape' => 'ReplicationConfigurationDefaultLargeStagingDiskType', ], 'ebsEncryption' => [ 'shape' => 'ReplicationConfigurationEbsEncryption', ], 'ebsEncryptionKeyArn' => [ 'shape' => 'ARN', ], 'replicationServerInstanceType' => [ 'shape' => 'EC2InstanceType', ], 'replicationServersSecurityGroupsIDs' => [ 'shape' => 'ReplicationServersSecurityGroupsIDs', ], 'stagingAreaSubnetId' => [ 'shape' => 'SubnetID', ], 'stagingAreaTags' => [ 'shape' => 'TagsMap', ], 'tags' => [ 'shape' => 'TagsMap', ], 'useDedicatedReplicationServer' => [ 'shape' => 'Boolean', ], ], ], 'DataReplicationError' => [ 'type' => 'structure', 'members' => [ 'error' => [ 'shape' => 'DataReplicationErrorString', ], 'rawError' => [ 'shape' => 'LargeBoundedString', ], ], ], 'DataReplicationErrorString' => [ 'type' => 'string', 'enum' => [ 'AGENT_NOT_SEEN', 'SNAPSHOTS_FAILURE', 'NOT_CONVERGING', 'UNSTABLE_NETWORK', 'FAILED_TO_CREATE_SECURITY_GROUP', 'FAILED_TO_LAUNCH_REPLICATION_SERVER', 'FAILED_TO_BOOT_REPLICATION_SERVER', 'FAILED_TO_AUTHENTICATE_WITH_SERVICE', 'FAILED_TO_DOWNLOAD_REPLICATION_SOFTWARE', 'FAILED_TO_CREATE_STAGING_DISKS', 'FAILED_TO_ATTACH_STAGING_DISKS', 'FAILED_TO_PAIR_REPLICATION_SERVER_WITH_AGENT', 'FAILED_TO_CONNECT_AGENT_TO_REPLICATION_SERVER', 'FAILED_TO_START_DATA_TRANSFER', 'UNSUPPORTED_VM_CONFIGURATION', 'LAST_SNAPSHOT_JOB_FAILED', ], ], 'DataReplicationInfo' => [ 'type' => 'structure', 'members' => [ 'dataReplicationError' => [ 'shape' => 'DataReplicationError', ], 'dataReplicationInitiation' => [ 'shape' => 'DataReplicationInitiation', ], 'dataReplicationState' => [ 'shape' => 'DataReplicationState', ], 'etaDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'lagDuration' => [ 'shape' => 'ISO8601DatetimeString', ], 'lastSnapshotDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'replicatedDisks' => [ 'shape' => 'DataReplicationInfoReplicatedDisks', ], ], ], 'DataReplicationInfoReplicatedDisk' => [ 'type' => 'structure', 'members' => [ 'backloggedStorageBytes' => [ 'shape' => 'PositiveInteger', ], 'deviceName' => [ 'shape' => 'BoundedString', ], 'replicatedStorageBytes' => [ 'shape' => 'PositiveInteger', ], 'rescannedStorageBytes' => [ 'shape' => 'PositiveInteger', ], 'totalStorageBytes' => [ 'shape' => 'PositiveInteger', ], ], ], 'DataReplicationInfoReplicatedDisks' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataReplicationInfoReplicatedDisk', ], 'max' => 60, 'min' => 0, ], 'DataReplicationInitiation' => [ 'type' => 'structure', 'members' => [ 'nextAttemptDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'startDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'steps' => [ 'shape' => 'DataReplicationInitiationSteps', ], ], ], 'DataReplicationInitiationStep' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'DataReplicationInitiationStepName', ], 'status' => [ 'shape' => 'DataReplicationInitiationStepStatus', ], ], ], 'DataReplicationInitiationStepName' => [ 'type' => 'string', 'enum' => [ 'WAIT', 'CREATE_SECURITY_GROUP', 'LAUNCH_REPLICATION_SERVER', 'BOOT_REPLICATION_SERVER', 'AUTHENTICATE_WITH_SERVICE', 'DOWNLOAD_REPLICATION_SOFTWARE', 'CREATE_STAGING_DISKS', 'ATTACH_STAGING_DISKS', 'PAIR_REPLICATION_SERVER_WITH_AGENT', 'CONNECT_AGENT_TO_REPLICATION_SERVER', 'START_DATA_TRANSFER', ], ], 'DataReplicationInitiationStepStatus' => [ 'type' => 'string', 'enum' => [ 'NOT_STARTED', 'IN_PROGRESS', 'SUCCEEDED', 'FAILED', 'SKIPPED', ], ], 'DataReplicationInitiationSteps' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataReplicationInitiationStep', ], ], 'DataReplicationState' => [ 'type' => 'string', 'enum' => [ 'STOPPED', 'INITIATING', 'INITIAL_SYNC', 'BACKLOG', 'CREATING_SNAPSHOT', 'CONTINUOUS', 'PAUSED', 'RESCAN', 'STALLED', 'DISCONNECTED', 'PENDING_SNAPSHOT_SHIPPING', 'SHIPPING_SNAPSHOT', ], ], 'DeleteJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobID', ], 'members' => [ 'jobID' => [ 'shape' => 'JobID', ], ], ], 'DeleteJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteReplicationConfigurationTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'replicationConfigurationTemplateID', ], 'members' => [ 'replicationConfigurationTemplateID' => [ 'shape' => 'ReplicationConfigurationTemplateID', ], ], ], 'DeleteReplicationConfigurationTemplateResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSourceServerRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'sourceServerID' => [ 'shape' => 'SourceServerID', ], ], ], 'DeleteSourceServerResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteVcenterClientRequest' => [ 'type' => 'structure', 'required' => [ 'vcenterClientID', ], 'members' => [ 'vcenterClientID' => [ 'shape' => 'VcenterClientID', ], ], ], 'DescribeJobLogItemsRequest' => [ 'type' => 'structure', 'required' => [ 'jobID', ], 'members' => [ 'jobID' => [ 'shape' => 'JobID', ], 'maxResults' => [ 'shape' => 'StrictlyPositiveInteger', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeJobLogItemsResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'JobLogs', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeJobsRequest' => [ 'type' => 'structure', 'required' => [ 'filters', ], 'members' => [ 'filters' => [ 'shape' => 'DescribeJobsRequestFilters', ], 'maxResults' => [ 'shape' => 'StrictlyPositiveInteger', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeJobsRequestFilters' => [ 'type' => 'structure', 'members' => [ 'fromDate' => [ 'shape' => 'ISO8601DatetimeString', ], 'jobIDs' => [ 'shape' => 'DescribeJobsRequestFiltersJobIDs', ], 'toDate' => [ 'shape' => 'ISO8601DatetimeString', ], ], ], 'DescribeJobsRequestFiltersJobIDs' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobID', ], 'max' => 1000, 'min' => 0, ], 'DescribeJobsResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'JobsList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeReplicationConfigurationTemplatesRequest' => [ 'type' => 'structure', 'required' => [ 'replicationConfigurationTemplateIDs', ], 'members' => [ 'maxResults' => [ 'shape' => 'StrictlyPositiveInteger', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'replicationConfigurationTemplateIDs' => [ 'shape' => 'ReplicationConfigurationTemplateIDs', ], ], ], 'DescribeReplicationConfigurationTemplatesResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'ReplicationConfigurationTemplates', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeSourceServersRequest' => [ 'type' => 'structure', 'required' => [ 'filters', ], 'members' => [ 'filters' => [ 'shape' => 'DescribeSourceServersRequestFilters', ], 'maxResults' => [ 'shape' => 'StrictlyPositiveInteger', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeSourceServersRequestFilters' => [ 'type' => 'structure', 'members' => [ 'isArchived' => [ 'shape' => 'Boolean', ], 'lifeCycleStates' => [ 'shape' => 'LifeCycleStates', ], 'replicationTypes' => [ 'shape' => 'ReplicationTypes', ], 'sourceServerIDs' => [ 'shape' => 'DescribeSourceServersRequestFiltersIDs', ], ], ], 'DescribeSourceServersRequestFiltersIDs' => [ 'type' => 'list', 'member' => [ 'shape' => 'SourceServerID', ], 'max' => 200, 'min' => 0, ], 'DescribeSourceServersResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'SourceServersList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeVcenterClientsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'StrictlyPositiveInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'DescribeVcenterClientsResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'VcenterClientList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DisconnectFromServiceRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'sourceServerID' => [ 'shape' => 'SourceServerID', ], ], ], 'Disk' => [ 'type' => 'structure', 'members' => [ 'bytes' => [ 'shape' => 'PositiveInteger', ], 'deviceName' => [ 'shape' => 'BoundedString', ], ], ], 'Disks' => [ 'type' => 'list', 'member' => [ 'shape' => 'Disk', ], 'max' => 1000, 'min' => 0, ], 'EC2InstanceID' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '^i-[0-9a-fA-F]{8,}$', ], 'EC2InstanceType' => [ 'type' => 'string', 'max' => 255, 'min' => 0, ], 'FinalizeCutoverRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'sourceServerID' => [ 'shape' => 'SourceServerID', ], ], ], 'FirstBoot' => [ 'type' => 'string', 'enum' => [ 'WAITING', 'SUCCEEDED', 'UNKNOWN', 'STOPPED', ], ], 'GetLaunchConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'sourceServerID' => [ 'shape' => 'SourceServerID', ], ], ], 'GetReplicationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'sourceServerID' => [ 'shape' => 'SourceServerID', ], ], ], 'IPsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BoundedString', ], ], 'ISO8601DatetimeString' => [ 'type' => 'string', 'max' => 32, 'min' => 19, 'pattern' => '^[1-9][0-9]*-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9](\\.[0-9]+)?Z$', ], 'IdentificationHints' => [ 'type' => 'structure', 'members' => [ 'awsInstanceID' => [ 'shape' => 'EC2InstanceID', ], 'fqdn' => [ 'shape' => 'BoundedString', ], 'hostname' => [ 'shape' => 'BoundedString', ], 'vmPath' => [ 'shape' => 'BoundedString', ], 'vmWareUuid' => [ 'shape' => 'BoundedString', ], ], ], 'InitializeServiceRequest' => [ 'type' => 'structure', 'members' => [], ], 'InitializeServiceResponse' => [ 'type' => 'structure', 'members' => [], ], 'InitiatedBy' => [ 'type' => 'string', 'enum' => [ 'START_TEST', 'START_CUTOVER', 'DIAGNOSTIC', 'TERMINATE', ], ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'LargeBoundedString', ], 'retryAfterSeconds' => [ 'shape' => 'PositiveInteger', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'Job' => [ 'type' => 'structure', 'required' => [ 'jobID', ], 'members' => [ 'arn' => [ 'shape' => 'ARN', ], 'creationDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'endDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'initiatedBy' => [ 'shape' => 'InitiatedBy', ], 'jobID' => [ 'shape' => 'JobID', ], 'participatingServers' => [ 'shape' => 'ParticipatingServers', ], 'status' => [ 'shape' => 'JobStatus', ], 'tags' => [ 'shape' => 'TagsMap', ], 'type' => [ 'shape' => 'JobType', ], ], ], 'JobID' => [ 'type' => 'string', 'max' => 24, 'min' => 24, 'pattern' => '^mgnjob-[0-9a-zA-Z]{17}$', ], 'JobLog' => [ 'type' => 'structure', 'members' => [ 'event' => [ 'shape' => 'JobLogEvent', ], 'eventData' => [ 'shape' => 'JobLogEventData', ], 'logDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], ], ], 'JobLogEvent' => [ 'type' => 'string', 'enum' => [ 'JOB_START', 'SERVER_SKIPPED', 'CLEANUP_START', 'CLEANUP_END', 'CLEANUP_FAIL', 'SNAPSHOT_START', 'SNAPSHOT_END', 'SNAPSHOT_FAIL', 'USING_PREVIOUS_SNAPSHOT', 'CONVERSION_START', 'CONVERSION_END', 'CONVERSION_FAIL', 'LAUNCH_START', 'LAUNCH_FAILED', 'JOB_CANCEL', 'JOB_END', ], ], 'JobLogEventData' => [ 'type' => 'structure', 'members' => [ 'conversionServerID' => [ 'shape' => 'EC2InstanceID', ], 'rawError' => [ 'shape' => 'LargeBoundedString', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], 'targetInstanceID' => [ 'shape' => 'EC2InstanceID', ], ], ], 'JobLogs' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobLog', ], ], 'JobStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'STARTED', 'COMPLETED', ], ], 'JobType' => [ 'type' => 'string', 'enum' => [ 'LAUNCH', 'TERMINATE', ], ], 'JobsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Job', ], ], 'LargeBoundedString' => [ 'type' => 'string', 'max' => 65536, 'min' => 0, ], 'LaunchConfiguration' => [ 'type' => 'structure', 'members' => [ 'copyPrivateIp' => [ 'shape' => 'Boolean', ], 'copyTags' => [ 'shape' => 'Boolean', ], 'ec2LaunchTemplateID' => [ 'shape' => 'BoundedString', ], 'launchDisposition' => [ 'shape' => 'LaunchDisposition', ], 'licensing' => [ 'shape' => 'Licensing', ], 'name' => [ 'shape' => 'SmallBoundedString', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], 'targetInstanceTypeRightSizingMethod' => [ 'shape' => 'TargetInstanceTypeRightSizingMethod', ], ], ], 'LaunchDisposition' => [ 'type' => 'string', 'enum' => [ 'STOPPED', 'STARTED', ], ], 'LaunchStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'IN_PROGRESS', 'LAUNCHED', 'FAILED', 'TERMINATED', ], ], 'LaunchedInstance' => [ 'type' => 'structure', 'members' => [ 'ec2InstanceID' => [ 'shape' => 'EC2InstanceID', ], 'firstBoot' => [ 'shape' => 'FirstBoot', ], 'jobID' => [ 'shape' => 'JobID', ], ], ], 'Licensing' => [ 'type' => 'structure', 'members' => [ 'osByol' => [ 'shape' => 'Boolean', ], ], ], 'LifeCycle' => [ 'type' => 'structure', 'members' => [ 'addedToServiceDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'elapsedReplicationDuration' => [ 'shape' => 'ISO8601DatetimeString', ], 'firstByteDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'lastCutover' => [ 'shape' => 'LifeCycleLastCutover', ], 'lastSeenByServiceDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'lastTest' => [ 'shape' => 'LifeCycleLastTest', ], 'state' => [ 'shape' => 'LifeCycleState', ], ], ], 'LifeCycleLastCutover' => [ 'type' => 'structure', 'members' => [ 'finalized' => [ 'shape' => 'LifeCycleLastCutoverFinalized', ], 'initiated' => [ 'shape' => 'LifeCycleLastCutoverInitiated', ], 'reverted' => [ 'shape' => 'LifeCycleLastCutoverReverted', ], ], ], 'LifeCycleLastCutoverFinalized' => [ 'type' => 'structure', 'members' => [ 'apiCallDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], ], ], 'LifeCycleLastCutoverInitiated' => [ 'type' => 'structure', 'members' => [ 'apiCallDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'jobID' => [ 'shape' => 'JobID', ], ], ], 'LifeCycleLastCutoverReverted' => [ 'type' => 'structure', 'members' => [ 'apiCallDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], ], ], 'LifeCycleLastTest' => [ 'type' => 'structure', 'members' => [ 'finalized' => [ 'shape' => 'LifeCycleLastTestFinalized', ], 'initiated' => [ 'shape' => 'LifeCycleLastTestInitiated', ], 'reverted' => [ 'shape' => 'LifeCycleLastTestReverted', ], ], ], 'LifeCycleLastTestFinalized' => [ 'type' => 'structure', 'members' => [ 'apiCallDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], ], ], 'LifeCycleLastTestInitiated' => [ 'type' => 'structure', 'members' => [ 'apiCallDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'jobID' => [ 'shape' => 'JobID', ], ], ], 'LifeCycleLastTestReverted' => [ 'type' => 'structure', 'members' => [ 'apiCallDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], ], ], 'LifeCycleState' => [ 'type' => 'string', 'enum' => [ 'STOPPED', 'NOT_READY', 'READY_FOR_TEST', 'TESTING', 'READY_FOR_CUTOVER', 'CUTTING_OVER', 'CUTOVER', 'DISCONNECTED', 'DISCOVERED', ], ], 'LifeCycleStates' => [ 'type' => 'list', 'member' => [ 'shape' => 'LifeCycleState', ], 'max' => 10, 'min' => 0, ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'MarkAsArchivedRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'sourceServerID' => [ 'shape' => 'SourceServerID', ], ], ], 'NetworkInterface' => [ 'type' => 'structure', 'members' => [ 'ips' => [ 'shape' => 'IPsList', ], 'isPrimary' => [ 'shape' => 'Boolean', ], 'macAddress' => [ 'shape' => 'BoundedString', ], ], ], 'NetworkInterfaces' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetworkInterface', ], 'max' => 32, 'min' => 0, ], 'OS' => [ 'type' => 'structure', 'members' => [ 'fullString' => [ 'shape' => 'BoundedString', ], ], ], 'PaginationToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'ParticipatingServer' => [ 'type' => 'structure', 'members' => [ 'launchStatus' => [ 'shape' => 'LaunchStatus', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], ], ], 'ParticipatingServers' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParticipatingServer', ], ], 'PositiveInteger' => [ 'type' => 'long', 'min' => 0, ], 'ReplicationConfiguration' => [ 'type' => 'structure', 'members' => [ 'associateDefaultSecurityGroup' => [ 'shape' => 'Boolean', ], 'bandwidthThrottling' => [ 'shape' => 'PositiveInteger', ], 'createPublicIP' => [ 'shape' => 'Boolean', ], 'dataPlaneRouting' => [ 'shape' => 'ReplicationConfigurationDataPlaneRouting', ], 'defaultLargeStagingDiskType' => [ 'shape' => 'ReplicationConfigurationDefaultLargeStagingDiskType', ], 'ebsEncryption' => [ 'shape' => 'ReplicationConfigurationEbsEncryption', ], 'ebsEncryptionKeyArn' => [ 'shape' => 'ARN', ], 'name' => [ 'shape' => 'SmallBoundedString', ], 'replicatedDisks' => [ 'shape' => 'ReplicationConfigurationReplicatedDisks', ], 'replicationServerInstanceType' => [ 'shape' => 'EC2InstanceType', ], 'replicationServersSecurityGroupsIDs' => [ 'shape' => 'ReplicationServersSecurityGroupsIDs', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], 'stagingAreaSubnetId' => [ 'shape' => 'SubnetID', ], 'stagingAreaTags' => [ 'shape' => 'TagsMap', ], 'useDedicatedReplicationServer' => [ 'shape' => 'Boolean', ], ], ], 'ReplicationConfigurationDataPlaneRouting' => [ 'type' => 'string', 'enum' => [ 'PRIVATE_IP', 'PUBLIC_IP', ], ], 'ReplicationConfigurationDefaultLargeStagingDiskType' => [ 'type' => 'string', 'enum' => [ 'GP2', 'ST1', ], ], 'ReplicationConfigurationEbsEncryption' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'CUSTOM', ], ], 'ReplicationConfigurationReplicatedDisk' => [ 'type' => 'structure', 'members' => [ 'deviceName' => [ 'shape' => 'BoundedString', ], 'iops' => [ 'shape' => 'PositiveInteger', ], 'isBootDisk' => [ 'shape' => 'Boolean', ], 'stagingDiskType' => [ 'shape' => 'ReplicationConfigurationReplicatedDiskStagingDiskType', ], ], ], 'ReplicationConfigurationReplicatedDiskStagingDiskType' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'GP2', 'IO1', 'SC1', 'ST1', 'STANDARD', ], ], 'ReplicationConfigurationReplicatedDisks' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationConfigurationReplicatedDisk', ], 'max' => 60, 'min' => 0, ], 'ReplicationConfigurationTemplate' => [ 'type' => 'structure', 'required' => [ 'replicationConfigurationTemplateID', ], 'members' => [ 'arn' => [ 'shape' => 'ARN', ], 'associateDefaultSecurityGroup' => [ 'shape' => 'Boolean', ], 'bandwidthThrottling' => [ 'shape' => 'PositiveInteger', ], 'createPublicIP' => [ 'shape' => 'Boolean', ], 'dataPlaneRouting' => [ 'shape' => 'ReplicationConfigurationDataPlaneRouting', ], 'defaultLargeStagingDiskType' => [ 'shape' => 'ReplicationConfigurationDefaultLargeStagingDiskType', ], 'ebsEncryption' => [ 'shape' => 'ReplicationConfigurationEbsEncryption', ], 'ebsEncryptionKeyArn' => [ 'shape' => 'ARN', ], 'replicationConfigurationTemplateID' => [ 'shape' => 'ReplicationConfigurationTemplateID', ], 'replicationServerInstanceType' => [ 'shape' => 'EC2InstanceType', ], 'replicationServersSecurityGroupsIDs' => [ 'shape' => 'ReplicationServersSecurityGroupsIDs', ], 'stagingAreaSubnetId' => [ 'shape' => 'SubnetID', ], 'stagingAreaTags' => [ 'shape' => 'TagsMap', ], 'tags' => [ 'shape' => 'TagsMap', ], 'useDedicatedReplicationServer' => [ 'shape' => 'Boolean', ], ], ], 'ReplicationConfigurationTemplateID' => [ 'type' => 'string', 'max' => 21, 'min' => 21, 'pattern' => '^rct-[0-9a-zA-Z]{17}$', ], 'ReplicationConfigurationTemplateIDs' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationConfigurationTemplateID', ], 'max' => 200, 'min' => 0, ], 'ReplicationConfigurationTemplates' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationConfigurationTemplate', ], ], 'ReplicationServersSecurityGroupsIDs' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupID', ], 'max' => 32, 'min' => 0, ], 'ReplicationType' => [ 'type' => 'string', 'enum' => [ 'AGENT_BASED', 'SNAPSHOT_SHIPPING', ], ], 'ReplicationTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationType', ], 'max' => 2, 'min' => 0, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'LargeBoundedString', ], 'message' => [ 'shape' => 'LargeBoundedString', ], 'resourceId' => [ 'shape' => 'LargeBoundedString', ], 'resourceType' => [ 'shape' => 'LargeBoundedString', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'RetryDataReplicationRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'sourceServerID' => [ 'shape' => 'SourceServerID', ], ], ], 'SecurityGroupID' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '^sg-[0-9a-fA-F]{8,}$', ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'LargeBoundedString', ], 'message' => [ 'shape' => 'LargeBoundedString', ], 'quotaCode' => [ 'shape' => 'LargeBoundedString', ], 'resourceId' => [ 'shape' => 'LargeBoundedString', ], 'resourceType' => [ 'shape' => 'LargeBoundedString', ], 'serviceCode' => [ 'shape' => 'LargeBoundedString', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SmallBoundedString' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'SourceProperties' => [ 'type' => 'structure', 'members' => [ 'cpus' => [ 'shape' => 'Cpus', ], 'disks' => [ 'shape' => 'Disks', ], 'identificationHints' => [ 'shape' => 'IdentificationHints', ], 'lastUpdatedDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'networkInterfaces' => [ 'shape' => 'NetworkInterfaces', ], 'os' => [ 'shape' => 'OS', ], 'ramBytes' => [ 'shape' => 'PositiveInteger', ], 'recommendedInstanceType' => [ 'shape' => 'EC2InstanceType', ], ], ], 'SourceServer' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ARN', ], 'dataReplicationInfo' => [ 'shape' => 'DataReplicationInfo', ], 'isArchived' => [ 'shape' => 'Boolean', ], 'launchedInstance' => [ 'shape' => 'LaunchedInstance', ], 'lifeCycle' => [ 'shape' => 'LifeCycle', ], 'replicationType' => [ 'shape' => 'ReplicationType', ], 'sourceProperties' => [ 'shape' => 'SourceProperties', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], 'tags' => [ 'shape' => 'TagsMap', ], 'vcenterClientID' => [ 'shape' => 'VcenterClientID', ], ], ], 'SourceServerID' => [ 'type' => 'string', 'max' => 19, 'min' => 19, 'pattern' => '^s-[0-9a-zA-Z]{17}$', ], 'SourceServersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SourceServer', ], ], 'StartCutoverRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerIDs', ], 'members' => [ 'sourceServerIDs' => [ 'shape' => 'StartCutoverRequestSourceServerIDs', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'StartCutoverRequestSourceServerIDs' => [ 'type' => 'list', 'member' => [ 'shape' => 'SourceServerID', ], 'max' => 200, 'min' => 1, ], 'StartCutoverResponse' => [ 'type' => 'structure', 'members' => [ 'job' => [ 'shape' => 'Job', ], ], ], 'StartReplicationRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'sourceServerID' => [ 'shape' => 'SourceServerID', ], ], ], 'StartTestRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerIDs', ], 'members' => [ 'sourceServerIDs' => [ 'shape' => 'StartTestRequestSourceServerIDs', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'StartTestRequestSourceServerIDs' => [ 'type' => 'list', 'member' => [ 'shape' => 'SourceServerID', ], 'max' => 200, 'min' => 1, ], 'StartTestResponse' => [ 'type' => 'structure', 'members' => [ 'job' => [ 'shape' => 'Job', ], ], ], 'StrictlyPositiveInteger' => [ 'type' => 'integer', 'min' => 1, ], 'SubnetID' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '^subnet-[0-9a-fA-F]{8,}$', ], 'TagKey' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'sensitive' => true, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TagsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'sensitive' => true, ], 'TargetInstanceTypeRightSizingMethod' => [ 'type' => 'string', 'enum' => [ 'NONE', 'BASIC', ], ], 'TerminateTargetInstancesRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerIDs', ], 'members' => [ 'sourceServerIDs' => [ 'shape' => 'TerminateTargetInstancesRequestSourceServerIDs', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'TerminateTargetInstancesRequestSourceServerIDs' => [ 'type' => 'list', 'member' => [ 'shape' => 'SourceServerID', ], 'max' => 200, 'min' => 1, ], 'TerminateTargetInstancesResponse' => [ 'type' => 'structure', 'members' => [ 'job' => [ 'shape' => 'Job', ], ], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'LargeBoundedString', ], 'quotaCode' => [ 'shape' => 'LargeBoundedString', ], 'retryAfterSeconds' => [ 'shape' => 'LargeBoundedString', 'location' => 'header', 'locationName' => 'Retry-After', ], 'serviceCode' => [ 'shape' => 'LargeBoundedString', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'UninitializedAccountException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'LargeBoundedString', ], 'message' => [ 'shape' => 'LargeBoundedString', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeys', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UpdateLaunchConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'copyPrivateIp' => [ 'shape' => 'Boolean', ], 'copyTags' => [ 'shape' => 'Boolean', ], 'launchDisposition' => [ 'shape' => 'LaunchDisposition', ], 'licensing' => [ 'shape' => 'Licensing', ], 'name' => [ 'shape' => 'SmallBoundedString', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], 'targetInstanceTypeRightSizingMethod' => [ 'shape' => 'TargetInstanceTypeRightSizingMethod', ], ], ], 'UpdateReplicationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'associateDefaultSecurityGroup' => [ 'shape' => 'Boolean', ], 'bandwidthThrottling' => [ 'shape' => 'PositiveInteger', ], 'createPublicIP' => [ 'shape' => 'Boolean', ], 'dataPlaneRouting' => [ 'shape' => 'ReplicationConfigurationDataPlaneRouting', ], 'defaultLargeStagingDiskType' => [ 'shape' => 'ReplicationConfigurationDefaultLargeStagingDiskType', ], 'ebsEncryption' => [ 'shape' => 'ReplicationConfigurationEbsEncryption', ], 'ebsEncryptionKeyArn' => [ 'shape' => 'ARN', ], 'name' => [ 'shape' => 'SmallBoundedString', ], 'replicatedDisks' => [ 'shape' => 'ReplicationConfigurationReplicatedDisks', ], 'replicationServerInstanceType' => [ 'shape' => 'EC2InstanceType', ], 'replicationServersSecurityGroupsIDs' => [ 'shape' => 'ReplicationServersSecurityGroupsIDs', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], 'stagingAreaSubnetId' => [ 'shape' => 'SubnetID', ], 'stagingAreaTags' => [ 'shape' => 'TagsMap', ], 'useDedicatedReplicationServer' => [ 'shape' => 'Boolean', ], ], ], 'UpdateReplicationConfigurationTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'replicationConfigurationTemplateID', ], 'members' => [ 'arn' => [ 'shape' => 'ARN', ], 'associateDefaultSecurityGroup' => [ 'shape' => 'Boolean', ], 'bandwidthThrottling' => [ 'shape' => 'PositiveInteger', ], 'createPublicIP' => [ 'shape' => 'Boolean', ], 'dataPlaneRouting' => [ 'shape' => 'ReplicationConfigurationDataPlaneRouting', ], 'defaultLargeStagingDiskType' => [ 'shape' => 'ReplicationConfigurationDefaultLargeStagingDiskType', ], 'ebsEncryption' => [ 'shape' => 'ReplicationConfigurationEbsEncryption', ], 'ebsEncryptionKeyArn' => [ 'shape' => 'ARN', ], 'replicationConfigurationTemplateID' => [ 'shape' => 'ReplicationConfigurationTemplateID', ], 'replicationServerInstanceType' => [ 'shape' => 'EC2InstanceType', ], 'replicationServersSecurityGroupsIDs' => [ 'shape' => 'ReplicationServersSecurityGroupsIDs', ], 'stagingAreaSubnetId' => [ 'shape' => 'SubnetID', ], 'stagingAreaTags' => [ 'shape' => 'TagsMap', ], 'useDedicatedReplicationServer' => [ 'shape' => 'Boolean', ], ], ], 'UpdateSourceServerReplicationTypeRequest' => [ 'type' => 'structure', 'required' => [ 'replicationType', 'sourceServerID', ], 'members' => [ 'replicationType' => [ 'shape' => 'ReplicationType', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'LargeBoundedString', ], 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], 'message' => [ 'shape' => 'LargeBoundedString', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'LargeBoundedString', ], 'name' => [ 'shape' => 'LargeBoundedString', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'unknownOperation', 'cannotParse', 'fieldValidationFailed', 'other', ], ], 'VcenterClient' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ARN', ], 'datacenterName' => [ 'shape' => 'BoundedString', ], 'hostname' => [ 'shape' => 'BoundedString', ], 'lastSeenDatetime' => [ 'shape' => 'ISO8601DatetimeString', ], 'sourceServerTags' => [ 'shape' => 'TagsMap', ], 'tags' => [ 'shape' => 'TagsMap', ], 'vcenterClientID' => [ 'shape' => 'VcenterClientID', ], 'vcenterUUID' => [ 'shape' => 'BoundedString', ], ], ], 'VcenterClientID' => [ 'type' => 'string', 'max' => 21, 'min' => 21, 'pattern' => '^vcc-[0-9a-zA-Z]{17}$', ], 'VcenterClientList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VcenterClient', ], ], ],];
