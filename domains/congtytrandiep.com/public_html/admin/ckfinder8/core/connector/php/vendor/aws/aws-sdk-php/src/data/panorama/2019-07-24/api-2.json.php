<?php
// This file was auto-generated from sdk-root/src/data/panorama/2019-07-24/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2019-07-24', 'endpointPrefix' => 'panorama', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'Panorama', 'serviceFullName' => 'AWS Panorama', 'serviceId' => 'Panorama', 'signatureVersion' => 'v4', 'signingName' => 'panorama', 'uid' => 'panorama-2019-07-24', ], 'operations' => [ 'CreateApplicationInstance' => [ 'name' => 'CreateApplicationInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/application-instances', ], 'input' => [ 'shape' => 'CreateApplicationInstanceRequest', ], 'output' => [ 'shape' => 'CreateApplicationInstanceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'CreateJobForDevices' => [ 'name' => 'CreateJobForDevices', 'http' => [ 'method' => 'POST', 'requestUri' => '/jobs', ], 'input' => [ 'shape' => 'CreateJobForDevicesRequest', ], 'output' => [ 'shape' => 'CreateJobForDevicesResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'CreateNodeFromTemplateJob' => [ 'name' => 'CreateNodeFromTemplateJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/packages/template-job', ], 'input' => [ 'shape' => 'CreateNodeFromTemplateJobRequest', ], 'output' => [ 'shape' => 'CreateNodeFromTemplateJobResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreatePackage' => [ 'name' => 'CreatePackage', 'http' => [ 'method' => 'POST', 'requestUri' => '/packages', ], 'input' => [ 'shape' => 'CreatePackageRequest', ], 'output' => [ 'shape' => 'CreatePackageResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreatePackageImportJob' => [ 'name' => 'CreatePackageImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/packages/import-jobs', ], 'input' => [ 'shape' => 'CreatePackageImportJobRequest', ], 'output' => [ 'shape' => 'CreatePackageImportJobResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteDevice' => [ 'name' => 'DeleteDevice', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/devices/{DeviceId}', ], 'input' => [ 'shape' => 'DeleteDeviceRequest', ], 'output' => [ 'shape' => 'DeleteDeviceResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeletePackage' => [ 'name' => 'DeletePackage', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/packages/{PackageId}', ], 'input' => [ 'shape' => 'DeletePackageRequest', ], 'output' => [ 'shape' => 'DeletePackageResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeregisterPackageVersion' => [ 'name' => 'DeregisterPackageVersion', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/packages/{PackageId}/versions/{PackageVersion}/patch/{PatchVersion}', ], 'input' => [ 'shape' => 'DeregisterPackageVersionRequest', ], 'output' => [ 'shape' => 'DeregisterPackageVersionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeApplicationInstance' => [ 'name' => 'DescribeApplicationInstance', 'http' => [ 'method' => 'GET', 'requestUri' => '/application-instances/{applicationInstanceId}', ], 'input' => [ 'shape' => 'DescribeApplicationInstanceRequest', ], 'output' => [ 'shape' => 'DescribeApplicationInstanceResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeApplicationInstanceDetails' => [ 'name' => 'DescribeApplicationInstanceDetails', 'http' => [ 'method' => 'GET', 'requestUri' => '/application-instances/{applicationInstanceId}/details', ], 'input' => [ 'shape' => 'DescribeApplicationInstanceDetailsRequest', ], 'output' => [ 'shape' => 'DescribeApplicationInstanceDetailsResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeDevice' => [ 'name' => 'DescribeDevice', 'http' => [ 'method' => 'GET', 'requestUri' => '/devices/{DeviceId}', ], 'input' => [ 'shape' => 'DescribeDeviceRequest', ], 'output' => [ 'shape' => 'DescribeDeviceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeDeviceJob' => [ 'name' => 'DescribeDeviceJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/jobs/{JobId}', ], 'input' => [ 'shape' => 'DescribeDeviceJobRequest', ], 'output' => [ 'shape' => 'DescribeDeviceJobResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeNode' => [ 'name' => 'DescribeNode', 'http' => [ 'method' => 'GET', 'requestUri' => '/nodes/{NodeId}', ], 'input' => [ 'shape' => 'DescribeNodeRequest', ], 'output' => [ 'shape' => 'DescribeNodeResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeNodeFromTemplateJob' => [ 'name' => 'DescribeNodeFromTemplateJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/packages/template-job/{JobId}', ], 'input' => [ 'shape' => 'DescribeNodeFromTemplateJobRequest', ], 'output' => [ 'shape' => 'DescribeNodeFromTemplateJobResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'DescribePackage' => [ 'name' => 'DescribePackage', 'http' => [ 'method' => 'GET', 'requestUri' => '/packages/metadata/{PackageId}', ], 'input' => [ 'shape' => 'DescribePackageRequest', ], 'output' => [ 'shape' => 'DescribePackageResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribePackageImportJob' => [ 'name' => 'DescribePackageImportJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/packages/import-jobs/{JobId}', ], 'input' => [ 'shape' => 'DescribePackageImportJobRequest', ], 'output' => [ 'shape' => 'DescribePackageImportJobResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'DescribePackageVersion' => [ 'name' => 'DescribePackageVersion', 'http' => [ 'method' => 'GET', 'requestUri' => '/packages/metadata/{PackageId}/versions/{PackageVersion}', ], 'input' => [ 'shape' => 'DescribePackageVersionRequest', ], 'output' => [ 'shape' => 'DescribePackageVersionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListApplicationInstanceDependencies' => [ 'name' => 'ListApplicationInstanceDependencies', 'http' => [ 'method' => 'GET', 'requestUri' => '/application-instances/{applicationInstanceId}/package-dependencies', ], 'input' => [ 'shape' => 'ListApplicationInstanceDependenciesRequest', ], 'output' => [ 'shape' => 'ListApplicationInstanceDependenciesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListApplicationInstanceNodeInstances' => [ 'name' => 'ListApplicationInstanceNodeInstances', 'http' => [ 'method' => 'GET', 'requestUri' => '/application-instances/{applicationInstanceId}/node-instances', ], 'input' => [ 'shape' => 'ListApplicationInstanceNodeInstancesRequest', ], 'output' => [ 'shape' => 'ListApplicationInstanceNodeInstancesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListApplicationInstances' => [ 'name' => 'ListApplicationInstances', 'http' => [ 'method' => 'GET', 'requestUri' => '/application-instances', ], 'input' => [ 'shape' => 'ListApplicationInstancesRequest', ], 'output' => [ 'shape' => 'ListApplicationInstancesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListDevices' => [ 'name' => 'ListDevices', 'http' => [ 'method' => 'GET', 'requestUri' => '/devices', ], 'input' => [ 'shape' => 'ListDevicesRequest', ], 'output' => [ 'shape' => 'ListDevicesResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListDevicesJobs' => [ 'name' => 'ListDevicesJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/jobs', ], 'input' => [ 'shape' => 'ListDevicesJobsRequest', ], 'output' => [ 'shape' => 'ListDevicesJobsResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListNodeFromTemplateJobs' => [ 'name' => 'ListNodeFromTemplateJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/packages/template-job', ], 'input' => [ 'shape' => 'ListNodeFromTemplateJobsRequest', ], 'output' => [ 'shape' => 'ListNodeFromTemplateJobsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListNodes' => [ 'name' => 'ListNodes', 'http' => [ 'method' => 'GET', 'requestUri' => '/nodes', ], 'input' => [ 'shape' => 'ListNodesRequest', ], 'output' => [ 'shape' => 'ListNodesResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListPackageImportJobs' => [ 'name' => 'ListPackageImportJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/packages/import-jobs', ], 'input' => [ 'shape' => 'ListPackageImportJobsRequest', ], 'output' => [ 'shape' => 'ListPackageImportJobsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListPackages' => [ 'name' => 'ListPackages', 'http' => [ 'method' => 'GET', 'requestUri' => '/packages', ], 'input' => [ 'shape' => 'ListPackagesRequest', ], 'output' => [ 'shape' => 'ListPackagesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{ResourceArn}', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ProvisionDevice' => [ 'name' => 'ProvisionDevice', 'http' => [ 'method' => 'POST', 'requestUri' => '/devices', ], 'input' => [ 'shape' => 'ProvisionDeviceRequest', ], 'output' => [ 'shape' => 'ProvisionDeviceResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'RegisterPackageVersion' => [ 'name' => 'RegisterPackageVersion', 'http' => [ 'method' => 'PUT', 'requestUri' => '/packages/{PackageId}/versions/{PackageVersion}/patch/{PatchVersion}', ], 'input' => [ 'shape' => 'RegisterPackageVersionRequest', ], 'output' => [ 'shape' => 'RegisterPackageVersionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'RemoveApplicationInstance' => [ 'name' => 'RemoveApplicationInstance', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/application-instances/{applicationInstanceId}', ], 'input' => [ 'shape' => 'RemoveApplicationInstanceRequest', ], 'output' => [ 'shape' => 'RemoveApplicationInstanceResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{ResourceArn}', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{ResourceArn}', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateDeviceMetadata' => [ 'name' => 'UpdateDeviceMetadata', 'http' => [ 'method' => 'PUT', 'requestUri' => '/devices/{DeviceId}', ], 'input' => [ 'shape' => 'UpdateDeviceMetadataRequest', ], 'output' => [ 'shape' => 'UpdateDeviceMetadataResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'ApplicationInstance' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ApplicationInstanceName', ], 'ApplicationInstanceId' => [ 'shape' => 'ApplicationInstanceId', ], 'DefaultRuntimeContextDevice' => [ 'shape' => 'DefaultRuntimeContextDevice', ], 'DefaultRuntimeContextDeviceName' => [ 'shape' => 'DeviceName', ], 'Description' => [ 'shape' => 'Description', ], 'Status' => [ 'shape' => 'ApplicationInstanceStatus', ], 'HealthStatus' => [ 'shape' => 'ApplicationInstanceHealthStatus', ], 'StatusDescription' => [ 'shape' => 'ApplicationInstanceStatusDescription', ], 'CreatedTime' => [ 'shape' => 'TimeStamp', ], 'Arn' => [ 'shape' => 'ApplicationInstanceArn', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'ApplicationInstanceArn' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ApplicationInstanceHealthStatus' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'ERROR', 'NOT_AVAILABLE', ], ], 'ApplicationInstanceId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-\\_]+$', ], 'ApplicationInstanceName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-\\_]+$', ], 'ApplicationInstanceStatus' => [ 'type' => 'string', 'enum' => [ 'DEPLOYMENT_PENDING', 'DEPLOYMENT_REQUESTED', 'DEPLOYMENT_IN_PROGRESS', 'DEPLOYMENT_ERROR', 'DEPLOYMENT_SUCCEEDED', 'REMOVAL_PENDING', 'REMOVAL_REQUESTED', 'REMOVAL_IN_PROGRESS', 'REMOVAL_FAILED', 'REMOVAL_SUCCEEDED', ], ], 'ApplicationInstanceStatusDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ApplicationInstances' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApplicationInstance', ], ], 'Boolean' => [ 'type' => 'boolean', ], 'Bucket' => [ 'type' => 'string', ], 'BucketName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^.+$', ], 'Certificates' => [ 'type' => 'blob', ], 'ClientToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-\\_]+$', ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'Message', 'ResourceId', 'ResourceType', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceId' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'String', ], 'ErrorId' => [ 'shape' => 'String', ], 'ErrorArguments' => [ 'shape' => 'ConflictExceptionErrorArgumentList', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ConflictExceptionErrorArgument' => [ 'type' => 'structure', 'required' => [ 'Name', 'Value', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], ], ], 'ConflictExceptionErrorArgumentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConflictExceptionErrorArgument', ], ], 'ConnectionType' => [ 'type' => 'string', 'enum' => [ 'STATIC_IP', 'DHCP', ], ], 'CreateApplicationInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'ManifestPayload', 'DefaultRuntimeContextDevice', ], 'members' => [ 'Name' => [ 'shape' => 'ApplicationInstanceName', ], 'Description' => [ 'shape' => 'Description', ], 'ManifestPayload' => [ 'shape' => 'ManifestPayload', ], 'ManifestOverridesPayload' => [ 'shape' => 'ManifestOverridesPayload', ], 'ApplicationInstanceIdToReplace' => [ 'shape' => 'ApplicationInstanceId', ], 'RuntimeRoleArn' => [ 'shape' => 'RuntimeRoleArn', ], 'DefaultRuntimeContextDevice' => [ 'shape' => 'DefaultRuntimeContextDevice', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateApplicationInstanceResponse' => [ 'type' => 'structure', 'required' => [ 'ApplicationInstanceId', ], 'members' => [ 'ApplicationInstanceId' => [ 'shape' => 'ApplicationInstanceId', ], ], ], 'CreateJobForDevicesRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceIds', 'DeviceJobConfig', 'JobType', ], 'members' => [ 'DeviceIds' => [ 'shape' => 'DeviceIdList', ], 'DeviceJobConfig' => [ 'shape' => 'DeviceJobConfig', ], 'JobType' => [ 'shape' => 'JobType', ], ], ], 'CreateJobForDevicesResponse' => [ 'type' => 'structure', 'required' => [ 'Jobs', ], 'members' => [ 'Jobs' => [ 'shape' => 'JobList', ], ], ], 'CreateNodeFromTemplateJobRequest' => [ 'type' => 'structure', 'required' => [ 'TemplateType', 'OutputPackageName', 'OutputPackageVersion', 'NodeName', 'TemplateParameters', ], 'members' => [ 'TemplateType' => [ 'shape' => 'TemplateType', ], 'OutputPackageName' => [ 'shape' => 'NodePackageName', ], 'OutputPackageVersion' => [ 'shape' => 'NodePackageVersion', ], 'NodeName' => [ 'shape' => 'NodeName', ], 'NodeDescription' => [ 'shape' => 'Description', ], 'TemplateParameters' => [ 'shape' => 'TemplateParametersMap', ], 'JobTags' => [ 'shape' => 'JobTagsList', ], ], ], 'CreateNodeFromTemplateJobResponse' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'CreatePackageImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobType', 'InputConfig', 'OutputConfig', 'ClientToken', ], 'members' => [ 'JobType' => [ 'shape' => 'PackageImportJobType', ], 'InputConfig' => [ 'shape' => 'PackageImportJobInputConfig', ], 'OutputConfig' => [ 'shape' => 'PackageImportJobOutputConfig', ], 'ClientToken' => [ 'shape' => 'ClientToken', ], 'JobTags' => [ 'shape' => 'JobTagsList', ], ], ], 'CreatePackageImportJobResponse' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], ], ], 'CreatePackageRequest' => [ 'type' => 'structure', 'required' => [ 'PackageName', ], 'members' => [ 'PackageName' => [ 'shape' => 'NodePackageName', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreatePackageResponse' => [ 'type' => 'structure', 'required' => [ 'StorageLocation', ], 'members' => [ 'PackageId' => [ 'shape' => 'NodePackageId', ], 'Arn' => [ 'shape' => 'NodePackageArn', ], 'StorageLocation' => [ 'shape' => 'StorageLocation', ], ], ], 'CreatedTime' => [ 'type' => 'timestamp', ], 'CurrentSoftware' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'DefaultGateway' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^.+$', ], 'DefaultRuntimeContextDevice' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-\\_]+$', ], 'DeleteDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceId', ], 'members' => [ 'DeviceId' => [ 'shape' => 'DeviceId', 'location' => 'uri', 'locationName' => 'DeviceId', ], ], ], 'DeleteDeviceResponse' => [ 'type' => 'structure', 'members' => [ 'DeviceId' => [ 'shape' => 'DeviceId', ], ], ], 'DeletePackageRequest' => [ 'type' => 'structure', 'required' => [ 'PackageId', ], 'members' => [ 'PackageId' => [ 'shape' => 'NodePackageId', 'location' => 'uri', 'locationName' => 'PackageId', ], 'ForceDelete' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'ForceDelete', ], ], ], 'DeletePackageResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeregisterPackageVersionRequest' => [ 'type' => 'structure', 'required' => [ 'PackageId', 'PackageVersion', 'PatchVersion', ], 'members' => [ 'OwnerAccount' => [ 'shape' => 'PackageOwnerAccount', 'location' => 'querystring', 'locationName' => 'OwnerAccount', ], 'PackageId' => [ 'shape' => 'NodePackageId', 'location' => 'uri', 'locationName' => 'PackageId', ], 'PackageVersion' => [ 'shape' => 'NodePackageVersion', 'location' => 'uri', 'locationName' => 'PackageVersion', ], 'PatchVersion' => [ 'shape' => 'NodePackagePatchVersion', 'location' => 'uri', 'locationName' => 'PatchVersion', ], 'UpdatedLatestPatchVersion' => [ 'shape' => 'NodePackagePatchVersion', 'location' => 'querystring', 'locationName' => 'UpdatedLatestPatchVersion', ], ], ], 'DeregisterPackageVersionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeApplicationInstanceDetailsRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationInstanceId', ], 'members' => [ 'ApplicationInstanceId' => [ 'shape' => 'ApplicationInstanceId', 'location' => 'uri', 'locationName' => 'applicationInstanceId', ], ], ], 'DescribeApplicationInstanceDetailsResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ApplicationInstanceName', ], 'Description' => [ 'shape' => 'Description', ], 'DefaultRuntimeContextDevice' => [ 'shape' => 'DefaultRuntimeContextDevice', ], 'ManifestPayload' => [ 'shape' => 'ManifestPayload', ], 'ManifestOverridesPayload' => [ 'shape' => 'ManifestOverridesPayload', ], 'ApplicationInstanceIdToReplace' => [ 'shape' => 'ApplicationInstanceId', ], 'CreatedTime' => [ 'shape' => 'TimeStamp', ], 'ApplicationInstanceId' => [ 'shape' => 'ApplicationInstanceId', ], ], ], 'DescribeApplicationInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationInstanceId', ], 'members' => [ 'ApplicationInstanceId' => [ 'shape' => 'ApplicationInstanceId', 'location' => 'uri', 'locationName' => 'applicationInstanceId', ], ], ], 'DescribeApplicationInstanceResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ApplicationInstanceName', ], 'Description' => [ 'shape' => 'Description', ], 'DefaultRuntimeContextDevice' => [ 'shape' => 'DefaultRuntimeContextDevice', ], 'DefaultRuntimeContextDeviceName' => [ 'shape' => 'DeviceName', ], 'ApplicationInstanceIdToReplace' => [ 'shape' => 'ApplicationInstanceId', ], 'RuntimeRoleArn' => [ 'shape' => 'RuntimeRoleArn', ], 'Status' => [ 'shape' => 'ApplicationInstanceStatus', ], 'HealthStatus' => [ 'shape' => 'ApplicationInstanceHealthStatus', ], 'StatusDescription' => [ 'shape' => 'ApplicationInstanceStatusDescription', ], 'CreatedTime' => [ 'shape' => 'TimeStamp', ], 'LastUpdatedTime' => [ 'shape' => 'TimeStamp', ], 'ApplicationInstanceId' => [ 'shape' => 'ApplicationInstanceId', ], 'Arn' => [ 'shape' => 'ApplicationInstanceArn', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'DescribeDeviceJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'JobId', ], ], ], 'DescribeDeviceJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'DeviceId' => [ 'shape' => 'DeviceId', ], 'DeviceArn' => [ 'shape' => 'DeviceArn', ], 'DeviceName' => [ 'shape' => 'DeviceName', ], 'DeviceType' => [ 'shape' => 'DeviceType', ], 'ImageVersion' => [ 'shape' => 'ImageVersion', ], 'Status' => [ 'shape' => 'UpdateProgress', ], 'CreatedTime' => [ 'shape' => 'UpdateCreatedTime', ], ], ], 'DescribeDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceId', ], 'members' => [ 'DeviceId' => [ 'shape' => 'DeviceId', 'location' => 'uri', 'locationName' => 'DeviceId', ], ], ], 'DescribeDeviceResponse' => [ 'type' => 'structure', 'members' => [ 'DeviceId' => [ 'shape' => 'DeviceId', ], 'Name' => [ 'shape' => 'DeviceName', ], 'Arn' => [ 'shape' => 'DeviceArn', ], 'Description' => [ 'shape' => 'Description', ], 'Type' => [ 'shape' => 'DeviceType', ], 'DeviceConnectionStatus' => [ 'shape' => 'DeviceConnectionStatus', ], 'CreatedTime' => [ 'shape' => 'CreatedTime', ], 'ProvisioningStatus' => [ 'shape' => 'DeviceStatus', ], 'LatestSoftware' => [ 'shape' => 'LatestSoftware', ], 'CurrentSoftware' => [ 'shape' => 'CurrentSoftware', ], 'SerialNumber' => [ 'shape' => 'DeviceSerialNumber', ], 'Tags' => [ 'shape' => 'TagMap', ], 'NetworkingConfiguration' => [ 'shape' => 'NetworkPayload', ], 'CurrentNetworkingStatus' => [ 'shape' => 'NetworkStatus', ], 'LeaseExpirationTime' => [ 'shape' => 'LeaseExpirationTime', ], ], ], 'DescribeNodeFromTemplateJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'JobId', ], ], ], 'DescribeNodeFromTemplateJobResponse' => [ 'type' => 'structure', 'required' => [ 'JobId', 'Status', 'StatusMessage', 'CreatedTime', 'LastUpdatedTime', 'OutputPackageName', 'OutputPackageVersion', 'NodeName', 'TemplateType', 'TemplateParameters', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'Status' => [ 'shape' => 'NodeFromTemplateJobStatus', ], 'StatusMessage' => [ 'shape' => 'NodeFromTemplateJobStatusMessage', ], 'CreatedTime' => [ 'shape' => 'CreatedTime', ], 'LastUpdatedTime' => [ 'shape' => 'LastUpdatedTime', ], 'OutputPackageName' => [ 'shape' => 'NodePackageName', ], 'OutputPackageVersion' => [ 'shape' => 'NodePackageVersion', ], 'NodeName' => [ 'shape' => 'NodeName', ], 'NodeDescription' => [ 'shape' => 'Description', ], 'TemplateType' => [ 'shape' => 'TemplateType', ], 'TemplateParameters' => [ 'shape' => 'TemplateParametersMap', ], 'JobTags' => [ 'shape' => 'JobTagsList', ], ], ], 'DescribeNodeRequest' => [ 'type' => 'structure', 'required' => [ 'NodeId', ], 'members' => [ 'NodeId' => [ 'shape' => 'NodeId', 'location' => 'uri', 'locationName' => 'NodeId', ], 'OwnerAccount' => [ 'shape' => 'PackageOwnerAccount', 'location' => 'querystring', 'locationName' => 'OwnerAccount', ], ], ], 'DescribeNodeResponse' => [ 'type' => 'structure', 'required' => [ 'NodeId', 'Name', 'Category', 'OwnerAccount', 'PackageName', 'PackageId', 'PackageVersion', 'PatchVersion', 'NodeInterface', 'Description', 'CreatedTime', 'LastUpdatedTime', ], 'members' => [ 'NodeId' => [ 'shape' => 'NodeId', ], 'Name' => [ 'shape' => 'NodeName', ], 'Category' => [ 'shape' => 'NodeCategory', ], 'OwnerAccount' => [ 'shape' => 'PackageOwnerAccount', ], 'PackageName' => [ 'shape' => 'NodePackageName', ], 'PackageId' => [ 'shape' => 'NodePackageId', ], 'PackageArn' => [ 'shape' => 'NodePackageArn', ], 'PackageVersion' => [ 'shape' => 'NodePackageVersion', ], 'PatchVersion' => [ 'shape' => 'NodePackagePatchVersion', ], 'NodeInterface' => [ 'shape' => 'NodeInterface', ], 'AssetName' => [ 'shape' => 'NodeAssetName', ], 'Description' => [ 'shape' => 'Description', ], 'CreatedTime' => [ 'shape' => 'TimeStamp', ], 'LastUpdatedTime' => [ 'shape' => 'TimeStamp', ], ], ], 'DescribePackageImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobId', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'JobId', ], ], ], 'DescribePackageImportJobResponse' => [ 'type' => 'structure', 'required' => [ 'JobId', 'JobType', 'InputConfig', 'OutputConfig', 'Output', 'CreatedTime', 'LastUpdatedTime', 'Status', 'StatusMessage', ], 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'ClientToken' => [ 'shape' => 'ClientToken', ], 'JobType' => [ 'shape' => 'PackageImportJobType', ], 'InputConfig' => [ 'shape' => 'PackageImportJobInputConfig', ], 'OutputConfig' => [ 'shape' => 'PackageImportJobOutputConfig', ], 'Output' => [ 'shape' => 'PackageImportJobOutput', ], 'CreatedTime' => [ 'shape' => 'CreatedTime', ], 'LastUpdatedTime' => [ 'shape' => 'LastUpdatedTime', ], 'Status' => [ 'shape' => 'PackageImportJobStatus', ], 'StatusMessage' => [ 'shape' => 'PackageImportJobStatusMessage', ], 'JobTags' => [ 'shape' => 'JobTagsList', ], ], ], 'DescribePackageRequest' => [ 'type' => 'structure', 'required' => [ 'PackageId', ], 'members' => [ 'PackageId' => [ 'shape' => 'NodePackageId', 'location' => 'uri', 'locationName' => 'PackageId', ], ], ], 'DescribePackageResponse' => [ 'type' => 'structure', 'required' => [ 'PackageId', 'PackageName', 'Arn', 'StorageLocation', 'CreatedTime', 'Tags', ], 'members' => [ 'PackageId' => [ 'shape' => 'NodePackageId', ], 'PackageName' => [ 'shape' => 'NodePackageName', ], 'Arn' => [ 'shape' => 'NodePackageArn', ], 'StorageLocation' => [ 'shape' => 'StorageLocation', ], 'ReadAccessPrincipalArns' => [ 'shape' => 'PrincipalArnsList', ], 'WriteAccessPrincipalArns' => [ 'shape' => 'PrincipalArnsList', ], 'CreatedTime' => [ 'shape' => 'TimeStamp', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'DescribePackageVersionRequest' => [ 'type' => 'structure', 'required' => [ 'PackageId', 'PackageVersion', ], 'members' => [ 'OwnerAccount' => [ 'shape' => 'PackageOwnerAccount', 'location' => 'querystring', 'locationName' => 'OwnerAccount', ], 'PackageId' => [ 'shape' => 'NodePackageId', 'location' => 'uri', 'locationName' => 'PackageId', ], 'PackageVersion' => [ 'shape' => 'NodePackageVersion', 'location' => 'uri', 'locationName' => 'PackageVersion', ], 'PatchVersion' => [ 'shape' => 'NodePackagePatchVersion', 'location' => 'querystring', 'locationName' => 'PatchVersion', ], ], ], 'DescribePackageVersionResponse' => [ 'type' => 'structure', 'required' => [ 'PackageId', 'PackageName', 'PackageVersion', 'PatchVersion', 'IsLatestPatch', 'Status', ], 'members' => [ 'OwnerAccount' => [ 'shape' => 'PackageOwnerAccount', ], 'PackageId' => [ 'shape' => 'NodePackageId', ], 'PackageArn' => [ 'shape' => 'NodePackageArn', ], 'PackageName' => [ 'shape' => 'NodePackageName', ], 'PackageVersion' => [ 'shape' => 'NodePackageVersion', ], 'PatchVersion' => [ 'shape' => 'NodePackagePatchVersion', ], 'IsLatestPatch' => [ 'shape' => 'Boolean', ], 'Status' => [ 'shape' => 'PackageVersionStatus', ], 'StatusDescription' => [ 'shape' => 'PackageVersionStatusDescription', ], 'RegisteredTime' => [ 'shape' => 'TimeStamp', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '^.*$', ], 'Device' => [ 'type' => 'structure', 'members' => [ 'DeviceId' => [ 'shape' => 'DeviceId', ], 'Name' => [ 'shape' => 'DeviceName', ], 'CreatedTime' => [ 'shape' => 'CreatedTime', ], 'ProvisioningStatus' => [ 'shape' => 'DeviceStatus', ], 'LastUpdatedTime' => [ 'shape' => 'LastUpdatedTime', ], 'LeaseExpirationTime' => [ 'shape' => 'LeaseExpirationTime', ], ], ], 'DeviceArn' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'DeviceConnectionStatus' => [ 'type' => 'string', 'enum' => [ 'ONLINE', 'OFFLINE', 'AWAITING_CREDENTIALS', 'NOT_AVAILABLE', 'ERROR', ], ], 'DeviceId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-\\_]+$', ], 'DeviceIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeviceId', ], 'max' => 1, 'min' => 1, ], 'DeviceJob' => [ 'type' => 'structure', 'members' => [ 'DeviceName' => [ 'shape' => 'DeviceName', ], 'DeviceId' => [ 'shape' => 'DeviceId', ], 'JobId' => [ 'shape' => 'JobId', ], 'CreatedTime' => [ 'shape' => 'CreatedTime', ], ], ], 'DeviceJobConfig' => [ 'type' => 'structure', 'members' => [ 'OTAJobConfig' => [ 'shape' => 'OTAJobConfig', ], ], ], 'DeviceJobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeviceJob', ], ], 'DeviceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Device', ], ], 'DeviceName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-\\_]+$', ], 'DeviceSerialNumber' => [ 'type' => 'string', 'pattern' => '^[0-9]{1,20}$', ], 'DeviceStatus' => [ 'type' => 'string', 'enum' => [ 'AWAITING_PROVISIONING', 'PENDING', 'SUCCEEDED', 'FAILED', 'ERROR', 'DELETING', ], ], 'DeviceType' => [ 'type' => 'string', 'enum' => [ 'PANORAMA_APPLIANCE_DEVELOPER_KIT', 'PANORAMA_APPLIANCE', ], ], 'Dns' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^.+$', ], 'DnsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Dns', ], ], 'EthernetPayload' => [ 'type' => 'structure', 'required' => [ 'ConnectionType', ], 'members' => [ 'ConnectionType' => [ 'shape' => 'ConnectionType', ], 'StaticIpConnectionInfo' => [ 'shape' => 'StaticIpConnectionInfo', ], ], ], 'EthernetStatus' => [ 'type' => 'structure', 'members' => [ 'IpAddress' => [ 'shape' => 'IpAddress', ], 'ConnectionStatus' => [ 'shape' => 'NetworkConnectionStatus', ], 'HwAddress' => [ 'shape' => 'HwAddress', ], ], ], 'HwAddress' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ImageVersion' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^.+$', ], 'InputPortList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NodeInputPort', ], ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'RetryAfterSeconds' => [ 'shape' => 'RetryAfterSeconds', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'IotThingName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'IpAddress' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d))(:(6553[0-5]|655[0-2]\\d|65[0-4]\\d{2}|6[0-4]\\d{3}|[1-5]\\d{4}|[1-9]\\d{0,3}))?$', ], 'Job' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'DeviceId' => [ 'shape' => 'DeviceId', ], ], ], 'JobId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-\\_]+$', ], 'JobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Job', ], ], 'JobResourceTags' => [ 'type' => 'structure', 'required' => [ 'ResourceType', 'Tags', ], 'members' => [ 'ResourceType' => [ 'shape' => 'JobResourceType', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'JobResourceType' => [ 'type' => 'string', 'enum' => [ 'PACKAGE', ], ], 'JobTagsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobResourceTags', ], ], 'JobType' => [ 'type' => 'string', 'enum' => [ 'OTA', ], ], 'LastUpdatedTime' => [ 'type' => 'timestamp', ], 'LatestSoftware' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'LeaseExpirationTime' => [ 'type' => 'timestamp', ], 'ListApplicationInstanceDependenciesRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationInstanceId', ], 'members' => [ 'ApplicationInstanceId' => [ 'shape' => 'ApplicationInstanceId', 'location' => 'uri', 'locationName' => 'applicationInstanceId', ], 'MaxResults' => [ 'shape' => 'MaxSize25', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListApplicationInstanceDependenciesResponse' => [ 'type' => 'structure', 'members' => [ 'PackageObjects' => [ 'shape' => 'PackageObjects', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListApplicationInstanceNodeInstancesRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationInstanceId', ], 'members' => [ 'ApplicationInstanceId' => [ 'shape' => 'ApplicationInstanceId', 'location' => 'uri', 'locationName' => 'applicationInstanceId', ], 'MaxResults' => [ 'shape' => 'MaxSize25', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListApplicationInstanceNodeInstancesResponse' => [ 'type' => 'structure', 'members' => [ 'NodeInstances' => [ 'shape' => 'NodeInstances', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListApplicationInstancesRequest' => [ 'type' => 'structure', 'members' => [ 'DeviceId' => [ 'shape' => 'DeviceId', 'location' => 'querystring', 'locationName' => 'deviceId', ], 'StatusFilter' => [ 'shape' => 'StatusFilter', 'location' => 'querystring', 'locationName' => 'statusFilter', ], 'MaxResults' => [ 'shape' => 'MaxSize25', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListApplicationInstancesResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationInstances' => [ 'shape' => 'ApplicationInstances', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDevicesJobsRequest' => [ 'type' => 'structure', 'members' => [ 'DeviceId' => [ 'shape' => 'DeviceId', 'location' => 'querystring', 'locationName' => 'DeviceId', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxSize25', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListDevicesJobsResponse' => [ 'type' => 'structure', 'members' => [ 'DeviceJobs' => [ 'shape' => 'DeviceJobList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDevicesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxSize25', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListDevicesResponse' => [ 'type' => 'structure', 'required' => [ 'Devices', ], 'members' => [ 'Devices' => [ 'shape' => 'DeviceList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListNodeFromTemplateJobsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxSize25', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListNodeFromTemplateJobsResponse' => [ 'type' => 'structure', 'required' => [ 'NodeFromTemplateJobs', ], 'members' => [ 'NodeFromTemplateJobs' => [ 'shape' => 'NodeFromTemplateJobList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListNodesRequest' => [ 'type' => 'structure', 'members' => [ 'Category' => [ 'shape' => 'NodeCategory', 'location' => 'querystring', 'locationName' => 'category', ], 'OwnerAccount' => [ 'shape' => 'PackageOwnerAccount', 'location' => 'querystring', 'locationName' => 'ownerAccount', ], 'PackageName' => [ 'shape' => 'NodePackageName', 'location' => 'querystring', 'locationName' => 'packageName', ], 'PackageVersion' => [ 'shape' => 'NodePackageVersion', 'location' => 'querystring', 'locationName' => 'packageVersion', ], 'PatchVersion' => [ 'shape' => 'NodePackagePatchVersion', 'location' => 'querystring', 'locationName' => 'patchVersion', ], 'NextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxSize25', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListNodesResponse' => [ 'type' => 'structure', 'members' => [ 'Nodes' => [ 'shape' => 'NodesList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListPackageImportJobsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxSize25', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListPackageImportJobsResponse' => [ 'type' => 'structure', 'required' => [ 'PackageImportJobs', ], 'members' => [ 'PackageImportJobs' => [ 'shape' => 'PackageImportJobList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPackagesRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxSize25', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => 'Token', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListPackagesResponse' => [ 'type' => 'structure', 'members' => [ 'Packages' => [ 'shape' => 'PackageList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'ManifestOverridesPayload' => [ 'type' => 'structure', 'members' => [ 'PayloadData' => [ 'shape' => 'ManifestOverridesPayloadData', ], ], 'union' => true, ], 'ManifestOverridesPayloadData' => [ 'type' => 'string', 'max' => 51200, 'min' => 0, 'pattern' => '^.*$', ], 'ManifestPayload' => [ 'type' => 'structure', 'members' => [ 'PayloadData' => [ 'shape' => 'ManifestPayloadData', ], ], 'union' => true, ], 'ManifestPayloadData' => [ 'type' => 'string', 'max' => 51200, 'min' => 1, 'pattern' => '^.+$', ], 'MarkLatestPatch' => [ 'type' => 'boolean', ], 'Mask' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^.+$', ], 'MaxConnections' => [ 'type' => 'integer', ], 'MaxSize25' => [ 'type' => 'integer', 'max' => 25, 'min' => 0, ], 'NetworkConnectionStatus' => [ 'type' => 'string', 'enum' => [ 'CONNECTED', 'NOT_CONNECTED', ], ], 'NetworkPayload' => [ 'type' => 'structure', 'members' => [ 'Ethernet0' => [ 'shape' => 'EthernetPayload', ], 'Ethernet1' => [ 'shape' => 'EthernetPayload', ], ], ], 'NetworkStatus' => [ 'type' => 'structure', 'members' => [ 'Ethernet0Status' => [ 'shape' => 'EthernetStatus', ], 'Ethernet1Status' => [ 'shape' => 'EthernetStatus', ], ], ], 'NextToken' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '^.+$', ], 'Node' => [ 'type' => 'structure', 'required' => [ 'NodeId', 'Name', 'Category', 'PackageName', 'PackageId', 'PackageVersion', 'PatchVersion', 'CreatedTime', ], 'members' => [ 'NodeId' => [ 'shape' => 'NodeId', ], 'Name' => [ 'shape' => 'NodeName', ], 'Category' => [ 'shape' => 'NodeCategory', ], 'OwnerAccount' => [ 'shape' => 'PackageOwnerAccount', ], 'PackageName' => [ 'shape' => 'NodePackageName', ], 'PackageId' => [ 'shape' => 'NodePackageId', ], 'PackageArn' => [ 'shape' => 'NodePackageArn', ], 'PackageVersion' => [ 'shape' => 'NodePackageVersion', ], 'PatchVersion' => [ 'shape' => 'NodePackagePatchVersion', ], 'Description' => [ 'shape' => 'Description', ], 'CreatedTime' => [ 'shape' => 'TimeStamp', ], ], ], 'NodeAssetName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-\\_]+$', ], 'NodeCategory' => [ 'type' => 'string', 'enum' => [ 'BUSINESS_LOGIC', 'ML_MODEL', 'MEDIA_SOURCE', 'MEDIA_SINK', ], ], 'NodeFromTemplateJob' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'TemplateType' => [ 'shape' => 'TemplateType', ], 'Status' => [ 'shape' => 'NodeFromTemplateJobStatus', ], 'StatusMessage' => [ 'shape' => 'NodeFromTemplateJobStatusMessage', ], 'CreatedTime' => [ 'shape' => 'CreatedTime', ], 'NodeName' => [ 'shape' => 'NodeName', ], ], ], 'NodeFromTemplateJobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NodeFromTemplateJob', ], ], 'NodeFromTemplateJobStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'SUCCEEDED', 'FAILED', ], ], 'NodeFromTemplateJobStatusMessage' => [ 'type' => 'string', ], 'NodeId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-\\_\\.]+$', ], 'NodeInputPort' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'PortName', ], 'Description' => [ 'shape' => 'Description', ], 'Type' => [ 'shape' => 'PortType', ], 'DefaultValue' => [ 'shape' => 'PortDefaultValue', ], 'MaxConnections' => [ 'shape' => 'MaxConnections', ], ], ], 'NodeInstance' => [ 'type' => 'structure', 'required' => [ 'NodeInstanceId', 'CurrentStatus', ], 'members' => [ 'NodeInstanceId' => [ 'shape' => 'NodeInstanceId', ], 'NodeId' => [ 'shape' => 'NodeId', ], 'PackageName' => [ 'shape' => 'NodePackageName', ], 'PackageVersion' => [ 'shape' => 'NodePackageVersion', ], 'PackagePatchVersion' => [ 'shape' => 'NodePackagePatchVersion', ], 'NodeName' => [ 'shape' => 'NodeName', ], 'CurrentStatus' => [ 'shape' => 'NodeInstanceStatus', ], ], ], 'NodeInstanceId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-\\_]+$', ], 'NodeInstanceStatus' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'ERROR', 'NOT_AVAILABLE', ], ], 'NodeInstances' => [ 'type' => 'list', 'member' => [ 'shape' => 'NodeInstance', ], ], 'NodeInterface' => [ 'type' => 'structure', 'required' => [ 'Inputs', 'Outputs', ], 'members' => [ 'Inputs' => [ 'shape' => 'InputPortList', ], 'Outputs' => [ 'shape' => 'OutputPortList', ], ], ], 'NodeName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-\\_]+$', ], 'NodeOutputPort' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'PortName', ], 'Description' => [ 'shape' => 'Description', ], 'Type' => [ 'shape' => 'PortType', ], ], ], 'NodePackageArn' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'NodePackageId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-\\_\\/]+$', ], 'NodePackageName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\-\\_]+$', ], 'NodePackagePatchVersion' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-z0-9]+$', ], 'NodePackageVersion' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^([0-9]+)\\.([0-9]+)$', ], 'NodesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Node', ], ], 'OTAJobConfig' => [ 'type' => 'structure', 'required' => [ 'ImageVersion', ], 'members' => [ 'ImageVersion' => [ 'shape' => 'ImageVersion', ], ], ], 'Object' => [ 'type' => 'string', ], 'ObjectKey' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^.+$', ], 'OutPutS3Location' => [ 'type' => 'structure', 'required' => [ 'BucketName', 'ObjectKey', ], 'members' => [ 'BucketName' => [ 'shape' => 'BucketName', ], 'ObjectKey' => [ 'shape' => 'ObjectKey', ], ], ], 'OutputPortList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NodeOutputPort', ], ], 'PackageImportJob' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'JobId', ], 'JobType' => [ 'shape' => 'PackageImportJobType', ], 'Status' => [ 'shape' => 'PackageImportJobStatus', ], 'StatusMessage' => [ 'shape' => 'PackageImportJobStatusMessage', ], 'CreatedTime' => [ 'shape' => 'CreatedTime', ], 'LastUpdatedTime' => [ 'shape' => 'LastUpdatedTime', ], ], ], 'PackageImportJobInputConfig' => [ 'type' => 'structure', 'members' => [ 'PackageVersionInputConfig' => [ 'shape' => 'PackageVersionInputConfig', ], ], ], 'PackageImportJobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PackageImportJob', ], ], 'PackageImportJobOutput' => [ 'type' => 'structure', 'required' => [ 'PackageId', 'PackageVersion', 'PatchVersion', 'OutputS3Location', ], 'members' => [ 'PackageId' => [ 'shape' => 'NodePackageId', ], 'PackageVersion' => [ 'shape' => 'NodePackageVersion', ], 'PatchVersion' => [ 'shape' => 'NodePackagePatchVersion', ], 'OutputS3Location' => [ 'shape' => 'OutPutS3Location', ], ], ], 'PackageImportJobOutputConfig' => [ 'type' => 'structure', 'members' => [ 'PackageVersionOutputConfig' => [ 'shape' => 'PackageVersionOutputConfig', ], ], ], 'PackageImportJobStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'SUCCEEDED', 'FAILED', ], ], 'PackageImportJobStatusMessage' => [ 'type' => 'string', ], 'PackageImportJobType' => [ 'type' => 'string', 'enum' => [ 'NODE_PACKAGE_VERSION', ], ], 'PackageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PackageListItem', ], ], 'PackageListItem' => [ 'type' => 'structure', 'members' => [ 'PackageId' => [ 'shape' => 'NodePackageId', ], 'PackageName' => [ 'shape' => 'NodePackageName', ], 'Arn' => [ 'shape' => 'NodePackageArn', ], 'CreatedTime' => [ 'shape' => 'TimeStamp', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'PackageObject' => [ 'type' => 'structure', 'required' => [ 'Name', 'PackageVersion', 'PatchVersion', ], 'members' => [ 'Name' => [ 'shape' => 'NodePackageName', ], 'PackageVersion' => [ 'shape' => 'NodePackageVersion', ], 'PatchVersion' => [ 'shape' => 'NodePackagePatchVersion', ], ], ], 'PackageObjects' => [ 'type' => 'list', 'member' => [ 'shape' => 'PackageObject', ], ], 'PackageOwnerAccount' => [ 'type' => 'string', 'max' => 12, 'min' => 1, 'pattern' => '^[0-9a-z\\_]+$', ], 'PackageVersionInputConfig' => [ 'type' => 'structure', 'required' => [ 'S3Location', ], 'members' => [ 'S3Location' => [ 'shape' => 'S3Location', ], ], ], 'PackageVersionOutputConfig' => [ 'type' => 'structure', 'required' => [ 'PackageName', 'PackageVersion', ], 'members' => [ 'PackageName' => [ 'shape' => 'NodePackageName', ], 'PackageVersion' => [ 'shape' => 'NodePackageVersion', ], 'MarkLatest' => [ 'shape' => 'MarkLatestPatch', ], ], ], 'PackageVersionStatus' => [ 'type' => 'string', 'enum' => [ 'REGISTER_PENDING', 'REGISTER_COMPLETED', 'FAILED', 'DELETING', ], ], 'PackageVersionStatusDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'PortDefaultValue' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'PortName' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\_]+$', ], 'PortType' => [ 'type' => 'string', 'enum' => [ 'BOOLEAN', 'STRING', 'INT32', 'FLOAT32', 'MEDIA', ], ], 'PrincipalArn' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^arn:[a-z0-9][-.a-z0-9]{0,62}:iam::[0-9]{12}:[a-zA-Z0-9+=,.@\\-_/]+$', ], 'PrincipalArnsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PrincipalArn', ], ], 'ProvisionDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'DeviceName', ], 'Description' => [ 'shape' => 'Description', ], 'Tags' => [ 'shape' => 'TagMap', ], 'NetworkingConfiguration' => [ 'shape' => 'NetworkPayload', ], ], ], 'ProvisionDeviceResponse' => [ 'type' => 'structure', 'required' => [ 'Arn', 'Status', ], 'members' => [ 'DeviceId' => [ 'shape' => 'DeviceId', ], 'Arn' => [ 'shape' => 'DeviceArn', ], 'Status' => [ 'shape' => 'DeviceStatus', ], 'Certificates' => [ 'shape' => 'Certificates', ], 'IotThingName' => [ 'shape' => 'IotThingName', ], ], ], 'Region' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^.+$', ], 'RegisterPackageVersionRequest' => [ 'type' => 'structure', 'required' => [ 'PackageId', 'PackageVersion', 'PatchVersion', ], 'members' => [ 'OwnerAccount' => [ 'shape' => 'PackageOwnerAccount', ], 'PackageId' => [ 'shape' => 'NodePackageId', 'location' => 'uri', 'locationName' => 'PackageId', ], 'PackageVersion' => [ 'shape' => 'NodePackageVersion', 'location' => 'uri', 'locationName' => 'PackageVersion', ], 'PatchVersion' => [ 'shape' => 'NodePackagePatchVersion', 'location' => 'uri', 'locationName' => 'PatchVersion', ], 'MarkLatest' => [ 'shape' => 'MarkLatestPatch', ], ], ], 'RegisterPackageVersionResponse' => [ 'type' => 'structure', 'members' => [], ], 'RemoveApplicationInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationInstanceId', ], 'members' => [ 'ApplicationInstanceId' => [ 'shape' => 'ApplicationInstanceId', 'location' => 'uri', 'locationName' => 'applicationInstanceId', ], ], ], 'RemoveApplicationInstanceResponse' => [ 'type' => 'structure', 'members' => [], ], 'ResourceArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^.+$', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'Message', 'ResourceId', 'ResourceType', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceId' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'RetryAfterSeconds' => [ 'type' => 'integer', ], 'RuntimeRoleArn' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^arn:[a-z0-9][-.a-z0-9]{0,62}:iam::[0-9]{12}:role/.+$', ], 'S3Location' => [ 'type' => 'structure', 'required' => [ 'BucketName', 'ObjectKey', ], 'members' => [ 'Region' => [ 'shape' => 'Region', ], 'BucketName' => [ 'shape' => 'BucketName', ], 'ObjectKey' => [ 'shape' => 'ObjectKey', ], ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'Message', 'QuotaCode', 'ServiceCode', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceId' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'String', ], 'QuotaCode' => [ 'shape' => 'String', ], 'ServiceCode' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, ], 'exception' => true, ], 'StaticIpConnectionInfo' => [ 'type' => 'structure', 'required' => [ 'IpAddress', 'Mask', 'Dns', 'DefaultGateway', ], 'members' => [ 'IpAddress' => [ 'shape' => 'IpAddress', ], 'Mask' => [ 'shape' => 'Mask', ], 'Dns' => [ 'shape' => 'DnsList', ], 'DefaultGateway' => [ 'shape' => 'DefaultGateway', ], ], ], 'StatusFilter' => [ 'type' => 'string', 'enum' => [ 'DEPLOYMENT_SUCCEEDED', 'DEPLOYMENT_ERROR', 'REMOVAL_SUCCEEDED', 'REMOVAL_FAILED', 'PROCESSING_DEPLOYMENT', 'PROCESSING_REMOVAL', ], ], 'StorageLocation' => [ 'type' => 'structure', 'required' => [ 'Bucket', 'RepoPrefixLocation', 'GeneratedPrefixLocation', 'BinaryPrefixLocation', 'ManifestPrefixLocation', ], 'members' => [ 'Bucket' => [ 'shape' => 'Bucket', ], 'RepoPrefixLocation' => [ 'shape' => 'Object', ], 'GeneratedPrefixLocation' => [ 'shape' => 'Object', ], 'BinaryPrefixLocation' => [ 'shape' => 'Object', ], 'ManifestPrefixLocation' => [ 'shape' => 'Object', ], ], ], 'String' => [ 'type' => 'string', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^.+$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^.*$', ], 'TemplateKey' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^.+$', ], 'TemplateParametersMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TemplateKey', ], 'value' => [ 'shape' => 'TemplateValue', ], ], 'TemplateType' => [ 'type' => 'string', 'enum' => [ 'RTSP_CAMERA_STREAM', ], ], 'TemplateValue' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^.+$', 'sensitive' => true, ], 'TimeStamp' => [ 'type' => 'timestamp', ], 'Token' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '^.+$', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateCreatedTime' => [ 'type' => 'timestamp', ], 'UpdateDeviceMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceId', ], 'members' => [ 'DeviceId' => [ 'shape' => 'DeviceId', 'location' => 'uri', 'locationName' => 'DeviceId', ], 'Description' => [ 'shape' => 'Description', ], ], ], 'UpdateDeviceMetadataResponse' => [ 'type' => 'structure', 'members' => [ 'DeviceId' => [ 'shape' => 'DeviceId', ], ], ], 'UpdateProgress' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'IN_PROGRESS', 'VERIFYING', 'REBOOTING', 'DOWNLOADING', 'COMPLETED', 'FAILED', ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', ], 'Reason' => [ 'shape' => 'ValidationExceptionReason', ], 'ErrorId' => [ 'shape' => 'String', ], 'ErrorArguments' => [ 'shape' => 'ValidationExceptionErrorArgumentList', ], 'Fields' => [ 'shape' => 'ValidationExceptionFieldList', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ValidationExceptionErrorArgument' => [ 'type' => 'structure', 'required' => [ 'Name', 'Value', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionErrorArgumentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionErrorArgument', ], ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'Name', 'Message', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'UNKNOWN_OPERATION', 'CANNOT_PARSE', 'FIELD_VALIDATION_FAILED', 'OTHER', ], ], ],];
