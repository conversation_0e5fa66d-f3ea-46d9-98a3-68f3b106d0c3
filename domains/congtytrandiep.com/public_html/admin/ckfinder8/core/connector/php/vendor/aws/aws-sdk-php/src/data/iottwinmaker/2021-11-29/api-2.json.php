<?php
// This file was auto-generated from sdk-root/src/data/iottwinmaker/2021-11-29/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2021-11-29', 'endpointPrefix' => 'iottwinmaker', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'AWS IoT TwinMaker', 'serviceId' => 'IoTTwinMaker', 'signatureVersion' => 'v4', 'signingName' => 'iottwinmaker', 'uid' => 'iottwinmaker-2021-11-29', ], 'operations' => [ 'BatchPutPropertyValues' => [ 'name' => 'BatchPutPropertyValues', 'http' => [ 'method' => 'POST', 'requestUri' => '/workspaces/{workspaceId}/entity-properties', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchPutPropertyValuesRequest', ], 'output' => [ 'shape' => 'BatchPutPropertyValuesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'data.', ], ], 'CreateComponentType' => [ 'name' => 'CreateComponentType', 'http' => [ 'method' => 'POST', 'requestUri' => '/workspaces/{workspaceId}/component-types/{componentTypeId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateComponentTypeRequest', ], 'output' => [ 'shape' => 'CreateComponentTypeResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'CreateEntity' => [ 'name' => 'CreateEntity', 'http' => [ 'method' => 'POST', 'requestUri' => '/workspaces/{workspaceId}/entities', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateEntityRequest', ], 'output' => [ 'shape' => 'CreateEntityResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'CreateScene' => [ 'name' => 'CreateScene', 'http' => [ 'method' => 'POST', 'requestUri' => '/workspaces/{workspaceId}/scenes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateSceneRequest', ], 'output' => [ 'shape' => 'CreateSceneResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'CreateWorkspace' => [ 'name' => 'CreateWorkspace', 'http' => [ 'method' => 'POST', 'requestUri' => '/workspaces/{workspaceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateWorkspaceRequest', ], 'output' => [ 'shape' => 'CreateWorkspaceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DeleteComponentType' => [ 'name' => 'DeleteComponentType', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/workspaces/{workspaceId}/component-types/{componentTypeId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteComponentTypeRequest', ], 'output' => [ 'shape' => 'DeleteComponentTypeResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DeleteEntity' => [ 'name' => 'DeleteEntity', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/workspaces/{workspaceId}/entities/{entityId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteEntityRequest', ], 'output' => [ 'shape' => 'DeleteEntityResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DeleteScene' => [ 'name' => 'DeleteScene', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/workspaces/{workspaceId}/scenes/{sceneId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteSceneRequest', ], 'output' => [ 'shape' => 'DeleteSceneResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'DeleteWorkspace' => [ 'name' => 'DeleteWorkspace', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/workspaces/{workspaceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteWorkspaceRequest', ], 'output' => [ 'shape' => 'DeleteWorkspaceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'GetComponentType' => [ 'name' => 'GetComponentType', 'http' => [ 'method' => 'GET', 'requestUri' => '/workspaces/{workspaceId}/component-types/{componentTypeId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetComponentTypeRequest', ], 'output' => [ 'shape' => 'GetComponentTypeResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'GetEntity' => [ 'name' => 'GetEntity', 'http' => [ 'method' => 'GET', 'requestUri' => '/workspaces/{workspaceId}/entities/{entityId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetEntityRequest', ], 'output' => [ 'shape' => 'GetEntityResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'GetPropertyValue' => [ 'name' => 'GetPropertyValue', 'http' => [ 'method' => 'POST', 'requestUri' => '/workspaces/{workspaceId}/entity-properties/value', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPropertyValueRequest', ], 'output' => [ 'shape' => 'GetPropertyValueResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConnectorFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConnectorTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'data.', ], ], 'GetPropertyValueHistory' => [ 'name' => 'GetPropertyValueHistory', 'http' => [ 'method' => 'POST', 'requestUri' => '/workspaces/{workspaceId}/entity-properties/history', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPropertyValueHistoryRequest', ], 'output' => [ 'shape' => 'GetPropertyValueHistoryResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConnectorFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConnectorTimeoutException', ], ], 'endpoint' => [ 'hostPrefix' => 'data.', ], ], 'GetScene' => [ 'name' => 'GetScene', 'http' => [ 'method' => 'GET', 'requestUri' => '/workspaces/{workspaceId}/scenes/{sceneId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSceneRequest', ], 'output' => [ 'shape' => 'GetSceneResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'GetWorkspace' => [ 'name' => 'GetWorkspace', 'http' => [ 'method' => 'GET', 'requestUri' => '/workspaces/{workspaceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetWorkspaceRequest', ], 'output' => [ 'shape' => 'GetWorkspaceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListComponentTypes' => [ 'name' => 'ListComponentTypes', 'http' => [ 'method' => 'POST', 'requestUri' => '/workspaces/{workspaceId}/component-types-list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListComponentTypesRequest', ], 'output' => [ 'shape' => 'ListComponentTypesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListEntities' => [ 'name' => 'ListEntities', 'http' => [ 'method' => 'POST', 'requestUri' => '/workspaces/{workspaceId}/entities-list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListEntitiesRequest', ], 'output' => [ 'shape' => 'ListEntitiesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListScenes' => [ 'name' => 'ListScenes', 'http' => [ 'method' => 'POST', 'requestUri' => '/workspaces/{workspaceId}/scenes-list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListScenesRequest', ], 'output' => [ 'shape' => 'ListScenesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags-list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'ListWorkspaces' => [ 'name' => 'ListWorkspaces', 'http' => [ 'method' => 'POST', 'requestUri' => '/workspaces-list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListWorkspacesRequest', ], 'output' => [ 'shape' => 'ListWorkspacesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'TooManyTagsException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'UpdateComponentType' => [ 'name' => 'UpdateComponentType', 'http' => [ 'method' => 'PUT', 'requestUri' => '/workspaces/{workspaceId}/component-types/{componentTypeId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateComponentTypeRequest', ], 'output' => [ 'shape' => 'UpdateComponentTypeResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'UpdateEntity' => [ 'name' => 'UpdateEntity', 'http' => [ 'method' => 'PUT', 'requestUri' => '/workspaces/{workspaceId}/entities/{entityId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateEntityRequest', ], 'output' => [ 'shape' => 'UpdateEntityResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'UpdateScene' => [ 'name' => 'UpdateScene', 'http' => [ 'method' => 'PUT', 'requestUri' => '/workspaces/{workspaceId}/scenes/{sceneId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSceneRequest', ], 'output' => [ 'shape' => 'UpdateSceneResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], 'UpdateWorkspace' => [ 'name' => 'UpdateWorkspace', 'http' => [ 'method' => 'PUT', 'requestUri' => '/workspaces/{workspaceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateWorkspaceRequest', ], 'output' => [ 'shape' => 'UpdateWorkspaceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'endpoint' => [ 'hostPrefix' => 'api.', ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'BatchPutPropertyError' => [ 'type' => 'structure', 'required' => [ 'entry', 'errorCode', 'errorMessage', ], 'members' => [ 'entry' => [ 'shape' => 'PropertyValueEntry', ], 'errorCode' => [ 'shape' => 'String', ], 'errorMessage' => [ 'shape' => 'String', ], ], ], 'BatchPutPropertyErrorEntry' => [ 'type' => 'structure', 'required' => [ 'errors', ], 'members' => [ 'errors' => [ 'shape' => 'Errors', ], ], ], 'BatchPutPropertyValuesRequest' => [ 'type' => 'structure', 'required' => [ 'entries', 'workspaceId', ], 'members' => [ 'entries' => [ 'shape' => 'Entries', ], 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], ], ], 'BatchPutPropertyValuesResponse' => [ 'type' => 'structure', 'required' => [ 'errorEntries', ], 'members' => [ 'errorEntries' => [ 'shape' => 'ErrorEntries', ], ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'ComponentRequest' => [ 'type' => 'structure', 'members' => [ 'componentTypeId' => [ 'shape' => 'ComponentTypeId', ], 'description' => [ 'shape' => 'Description', ], 'properties' => [ 'shape' => 'PropertyRequests', ], ], ], 'ComponentResponse' => [ 'type' => 'structure', 'members' => [ 'componentName' => [ 'shape' => 'Name', ], 'componentTypeId' => [ 'shape' => 'ComponentTypeId', ], 'definedIn' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'Description', ], 'properties' => [ 'shape' => 'PropertyResponses', ], 'status' => [ 'shape' => 'Status', ], ], ], 'ComponentTypeId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z_\\.\\-0-9:]+', ], 'ComponentTypeSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComponentTypeSummary', ], ], 'ComponentTypeSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'componentTypeId', 'creationDateTime', 'updateDateTime', ], 'members' => [ 'arn' => [ 'shape' => 'TwinMakerArn', ], 'componentTypeId' => [ 'shape' => 'ComponentTypeId', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'status' => [ 'shape' => 'Status', ], 'updateDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ComponentUpdateRequest' => [ 'type' => 'structure', 'members' => [ 'componentTypeId' => [ 'shape' => 'ComponentTypeId', ], 'description' => [ 'shape' => 'Description', ], 'propertyUpdates' => [ 'shape' => 'PropertyRequests', ], 'updateType' => [ 'shape' => 'ComponentUpdateType', ], ], ], 'ComponentUpdateType' => [ 'type' => 'string', 'enum' => [ 'CREATE', 'UPDATE', 'DELETE', ], ], 'ComponentUpdatesMapRequest' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'ComponentUpdateRequest', ], ], 'ComponentsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'ComponentResponse', ], ], 'ComponentsMapRequest' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'ComponentRequest', ], ], 'Configuration' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'Value', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ConnectorFailureException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 424, 'senderFault' => true, ], 'exception' => true, ], 'ConnectorTimeoutException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 424, 'senderFault' => true, ], 'exception' => true, ], 'CreateComponentTypeRequest' => [ 'type' => 'structure', 'required' => [ 'componentTypeId', 'workspaceId', ], 'members' => [ 'componentTypeId' => [ 'shape' => 'ComponentTypeId', 'location' => 'uri', 'locationName' => 'componentTypeId', ], 'description' => [ 'shape' => 'Description', ], 'extendsFrom' => [ 'shape' => 'ExtendsFrom', ], 'functions' => [ 'shape' => 'FunctionsRequest', ], 'isSingleton' => [ 'shape' => 'Boolean', ], 'propertyDefinitions' => [ 'shape' => 'PropertyDefinitionsRequest', ], 'tags' => [ 'shape' => 'TagMap', ], 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], ], ], 'CreateComponentTypeResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'creationDateTime', 'state', ], 'members' => [ 'arn' => [ 'shape' => 'TwinMakerArn', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'state' => [ 'shape' => 'State', ], ], ], 'CreateEntityRequest' => [ 'type' => 'structure', 'required' => [ 'entityName', 'workspaceId', ], 'members' => [ 'components' => [ 'shape' => 'ComponentsMapRequest', ], 'description' => [ 'shape' => 'Description', ], 'entityId' => [ 'shape' => 'EntityId', ], 'entityName' => [ 'shape' => 'EntityName', ], 'parentEntityId' => [ 'shape' => 'ParentEntityId', ], 'tags' => [ 'shape' => 'TagMap', ], 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], ], ], 'CreateEntityResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'creationDateTime', 'entityId', 'state', ], 'members' => [ 'arn' => [ 'shape' => 'TwinMakerArn', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'entityId' => [ 'shape' => 'EntityId', ], 'state' => [ 'shape' => 'State', ], ], ], 'CreateSceneRequest' => [ 'type' => 'structure', 'required' => [ 'contentLocation', 'sceneId', 'workspaceId', ], 'members' => [ 'capabilities' => [ 'shape' => 'SceneCapabilities', ], 'contentLocation' => [ 'shape' => 'S3Url', ], 'description' => [ 'shape' => 'Description', ], 'sceneId' => [ 'shape' => 'Id', ], 'tags' => [ 'shape' => 'TagMap', ], 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], ], ], 'CreateSceneResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'creationDateTime', ], 'members' => [ 'arn' => [ 'shape' => 'TwinMakerArn', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'CreateWorkspaceRequest' => [ 'type' => 'structure', 'required' => [ 'role', 's3Location', 'workspaceId', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'role' => [ 'shape' => 'RoleArn', ], 's3Location' => [ 'shape' => 'S3Location', ], 'tags' => [ 'shape' => 'TagMap', ], 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], ], ], 'CreateWorkspaceResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'creationDateTime', ], 'members' => [ 'arn' => [ 'shape' => 'TwinMakerArn', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'DataConnector' => [ 'type' => 'structure', 'members' => [ 'isNative' => [ 'shape' => 'Boolean', ], 'lambda' => [ 'shape' => 'LambdaFunction', ], ], ], 'DataType' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'allowedValues' => [ 'shape' => 'DataValueList', ], 'nestedType' => [ 'shape' => 'DataType', ], 'relationship' => [ 'shape' => 'Relationship', ], 'type' => [ 'shape' => 'Type', ], 'unitOfMeasure' => [ 'shape' => 'String', ], ], ], 'DataValue' => [ 'type' => 'structure', 'members' => [ 'booleanValue' => [ 'shape' => 'Boolean', ], 'doubleValue' => [ 'shape' => 'Double', ], 'expression' => [ 'shape' => 'Expression', ], 'integerValue' => [ 'shape' => 'Integer', ], 'listValue' => [ 'shape' => 'DataValueList', ], 'longValue' => [ 'shape' => 'Long', ], 'mapValue' => [ 'shape' => 'DataValueMap', ], 'relationshipValue' => [ 'shape' => 'RelationshipValue', ], 'stringValue' => [ 'shape' => 'String', ], ], ], 'DataValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataValue', ], 'max' => 50, 'min' => 0, ], 'DataValueMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'DataValue', ], 'max' => 50, 'min' => 0, ], 'DeleteComponentTypeRequest' => [ 'type' => 'structure', 'required' => [ 'componentTypeId', 'workspaceId', ], 'members' => [ 'componentTypeId' => [ 'shape' => 'ComponentTypeId', 'location' => 'uri', 'locationName' => 'componentTypeId', ], 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], ], ], 'DeleteComponentTypeResponse' => [ 'type' => 'structure', 'required' => [ 'state', ], 'members' => [ 'state' => [ 'shape' => 'State', ], ], ], 'DeleteEntityRequest' => [ 'type' => 'structure', 'required' => [ 'entityId', 'workspaceId', ], 'members' => [ 'entityId' => [ 'shape' => 'EntityId', 'location' => 'uri', 'locationName' => 'entityId', ], 'isRecursive' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'isRecursive', ], 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], ], ], 'DeleteEntityResponse' => [ 'type' => 'structure', 'required' => [ 'state', ], 'members' => [ 'state' => [ 'shape' => 'State', ], ], ], 'DeleteSceneRequest' => [ 'type' => 'structure', 'required' => [ 'sceneId', 'workspaceId', ], 'members' => [ 'sceneId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'sceneId', ], 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], ], ], 'DeleteSceneResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteWorkspaceRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', ], 'members' => [ 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], ], ], 'DeleteWorkspaceResponse' => [ 'type' => 'structure', 'members' => [], ], 'Description' => [ 'type' => 'string', 'max' => 512, 'min' => 0, 'pattern' => '.*', ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'EntityId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}|^[a-zA-Z0-9][a-zA-Z_\\-0-9.:]*[a-zA-Z0-9]+', ], 'EntityName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z_0-9-.][a-zA-Z_0-9-. ]*[a-zA-Z0-9]+', ], 'EntityPropertyReference' => [ 'type' => 'structure', 'required' => [ 'propertyName', ], 'members' => [ 'componentName' => [ 'shape' => 'Name', ], 'entityId' => [ 'shape' => 'EntityId', ], 'externalIdProperty' => [ 'shape' => 'ExternalIdProperty', ], 'propertyName' => [ 'shape' => 'Name', ], ], ], 'EntitySummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'EntitySummary', ], ], 'EntitySummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'creationDateTime', 'entityId', 'entityName', 'status', 'updateDateTime', ], 'members' => [ 'arn' => [ 'shape' => 'TwinMakerArn', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'entityId' => [ 'shape' => 'EntityId', ], 'entityName' => [ 'shape' => 'EntityName', ], 'hasChildEntities' => [ 'shape' => 'Boolean', ], 'parentEntityId' => [ 'shape' => 'ParentEntityId', ], 'status' => [ 'shape' => 'Status', ], 'updateDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'Entries' => [ 'type' => 'list', 'member' => [ 'shape' => 'PropertyValueEntry', ], 'max' => 10, 'min' => 1, ], 'ErrorCode' => [ 'type' => 'string', 'enum' => [ 'VALIDATION_ERROR', 'INTERNAL_FAILURE', ], ], 'ErrorDetails' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'ErrorCode', ], 'message' => [ 'shape' => 'ErrorMessage', ], ], ], 'ErrorEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchPutPropertyErrorEntry', ], 'max' => 10, 'min' => 1, ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'Errors' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchPutPropertyError', ], 'max' => 10, 'min' => 1, ], 'ExceptionMessage' => [ 'type' => 'string', ], 'Expression' => [ 'type' => 'string', 'max' => 316, 'min' => 1, 'pattern' => '(^\\$\\{Parameters\\.[a-zA-z]+([a-zA-z_0-9]*)}$)', ], 'ExtendsFrom' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComponentTypeId', ], ], 'ExternalIdProperty' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'FunctionRequest' => [ 'type' => 'structure', 'members' => [ 'implementedBy' => [ 'shape' => 'DataConnector', ], 'requiredProperties' => [ 'shape' => 'RequiredProperties', ], 'scope' => [ 'shape' => 'Scope', ], ], ], 'FunctionResponse' => [ 'type' => 'structure', 'members' => [ 'implementedBy' => [ 'shape' => 'DataConnector', ], 'isInherited' => [ 'shape' => 'Boolean', ], 'requiredProperties' => [ 'shape' => 'RequiredProperties', ], 'scope' => [ 'shape' => 'Scope', ], ], ], 'FunctionsRequest' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'FunctionRequest', ], ], 'FunctionsResponse' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'FunctionResponse', ], ], 'GetComponentTypeRequest' => [ 'type' => 'structure', 'required' => [ 'componentTypeId', 'workspaceId', ], 'members' => [ 'componentTypeId' => [ 'shape' => 'ComponentTypeId', 'location' => 'uri', 'locationName' => 'componentTypeId', ], 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], ], ], 'GetComponentTypeResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'componentTypeId', 'creationDateTime', 'updateDateTime', 'workspaceId', ], 'members' => [ 'arn' => [ 'shape' => 'TwinMakerArn', ], 'componentTypeId' => [ 'shape' => 'ComponentTypeId', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'extendsFrom' => [ 'shape' => 'ExtendsFrom', ], 'functions' => [ 'shape' => 'FunctionsResponse', ], 'isAbstract' => [ 'shape' => 'Boolean', ], 'isSchemaInitialized' => [ 'shape' => 'Boolean', ], 'isSingleton' => [ 'shape' => 'Boolean', ], 'propertyDefinitions' => [ 'shape' => 'PropertyDefinitionsResponse', ], 'status' => [ 'shape' => 'Status', ], 'updateDateTime' => [ 'shape' => 'Timestamp', ], 'workspaceId' => [ 'shape' => 'Id', ], ], ], 'GetEntityRequest' => [ 'type' => 'structure', 'required' => [ 'entityId', 'workspaceId', ], 'members' => [ 'entityId' => [ 'shape' => 'EntityId', 'location' => 'uri', 'locationName' => 'entityId', ], 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], ], ], 'GetEntityResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'creationDateTime', 'entityId', 'entityName', 'hasChildEntities', 'parentEntityId', 'status', 'updateDateTime', 'workspaceId', ], 'members' => [ 'arn' => [ 'shape' => 'TwinMakerArn', ], 'components' => [ 'shape' => 'ComponentsMap', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'entityId' => [ 'shape' => 'EntityId', ], 'entityName' => [ 'shape' => 'EntityName', ], 'hasChildEntities' => [ 'shape' => 'Boolean', ], 'parentEntityId' => [ 'shape' => 'ParentEntityId', ], 'status' => [ 'shape' => 'Status', ], 'updateDateTime' => [ 'shape' => 'Timestamp', ], 'workspaceId' => [ 'shape' => 'Id', ], ], ], 'GetPropertyValueHistoryRequest' => [ 'type' => 'structure', 'required' => [ 'endDateTime', 'selectedProperties', 'startDateTime', 'workspaceId', ], 'members' => [ 'componentName' => [ 'shape' => 'Name', ], 'componentTypeId' => [ 'shape' => 'ComponentTypeId', ], 'endDateTime' => [ 'shape' => 'Timestamp', ], 'entityId' => [ 'shape' => 'EntityId', ], 'interpolation' => [ 'shape' => 'InterpolationParameters', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'orderByTime' => [ 'shape' => 'OrderByTime', ], 'propertyFilters' => [ 'shape' => 'PropertyFilters', ], 'selectedProperties' => [ 'shape' => 'SelectedPropertyList', ], 'startDateTime' => [ 'shape' => 'Timestamp', ], 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], ], ], 'GetPropertyValueHistoryResponse' => [ 'type' => 'structure', 'required' => [ 'propertyValues', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'propertyValues' => [ 'shape' => 'PropertyValueList', ], ], ], 'GetPropertyValueRequest' => [ 'type' => 'structure', 'required' => [ 'selectedProperties', 'workspaceId', ], 'members' => [ 'componentName' => [ 'shape' => 'Name', ], 'componentTypeId' => [ 'shape' => 'ComponentTypeId', ], 'entityId' => [ 'shape' => 'EntityId', ], 'selectedProperties' => [ 'shape' => 'SelectedPropertyList', ], 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], ], ], 'GetPropertyValueResponse' => [ 'type' => 'structure', 'required' => [ 'propertyValues', ], 'members' => [ 'propertyValues' => [ 'shape' => 'PropertyLatestValueMap', ], ], ], 'GetSceneRequest' => [ 'type' => 'structure', 'required' => [ 'sceneId', 'workspaceId', ], 'members' => [ 'sceneId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'sceneId', ], 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], ], ], 'GetSceneResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'contentLocation', 'creationDateTime', 'sceneId', 'updateDateTime', 'workspaceId', ], 'members' => [ 'arn' => [ 'shape' => 'TwinMakerArn', ], 'capabilities' => [ 'shape' => 'SceneCapabilities', ], 'contentLocation' => [ 'shape' => 'S3Url', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'sceneId' => [ 'shape' => 'Id', ], 'updateDateTime' => [ 'shape' => 'Timestamp', ], 'workspaceId' => [ 'shape' => 'Id', ], ], ], 'GetWorkspaceRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', ], 'members' => [ 'workspaceId' => [ 'shape' => 'IdOrArn', 'location' => 'uri', 'locationName' => 'workspaceId', ], ], ], 'GetWorkspaceResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'creationDateTime', 'role', 's3Location', 'updateDateTime', 'workspaceId', ], 'members' => [ 'arn' => [ 'shape' => 'TwinMakerArn', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'role' => [ 'shape' => 'RoleArn', ], 's3Location' => [ 'shape' => 'S3Location', ], 'updateDateTime' => [ 'shape' => 'Timestamp', ], 'workspaceId' => [ 'shape' => 'Id', ], ], ], 'Id' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z_0-9][a-zA-Z_\\-0-9]*[a-zA-Z0-9]+', ], 'IdOrArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '[a-zA-Z][a-zA-Z_\\-0-9]*[a-zA-Z0-9]+$|^arn:((aws)|(aws-cn)|(aws-us-gov)):iottwinmaker:[a-z0-9-]+:[0-9]{12}:[\\/a-zA-Z0-9_-]+', ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InterpolationParameters' => [ 'type' => 'structure', 'members' => [ 'interpolationType' => [ 'shape' => 'InterpolationType', ], 'intervalInSeconds' => [ 'shape' => 'IntervalInSeconds', ], ], ], 'InterpolationType' => [ 'type' => 'string', 'enum' => [ 'LINEAR', ], ], 'IntervalInSeconds' => [ 'type' => 'long', 'box' => true, ], 'LambdaArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:((aws)|(aws-cn)|(aws-us-gov)):lambda:[a-z0-9-]+:[0-9]{12}:function:[\\/a-zA-Z0-9_-]+', ], 'LambdaFunction' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'LambdaArn', ], ], ], 'ListComponentTypesFilter' => [ 'type' => 'structure', 'members' => [ 'extendsFrom' => [ 'shape' => 'ComponentTypeId', ], 'isAbstract' => [ 'shape' => 'Boolean', ], 'namespace' => [ 'shape' => 'String', ], ], 'union' => true, ], 'ListComponentTypesFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListComponentTypesFilter', ], ], 'ListComponentTypesRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', ], 'members' => [ 'filters' => [ 'shape' => 'ListComponentTypesFilters', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], ], ], 'ListComponentTypesResponse' => [ 'type' => 'structure', 'required' => [ 'componentTypeSummaries', 'workspaceId', ], 'members' => [ 'componentTypeSummaries' => [ 'shape' => 'ComponentTypeSummaries', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'workspaceId' => [ 'shape' => 'Id', ], ], ], 'ListEntitiesFilter' => [ 'type' => 'structure', 'members' => [ 'componentTypeId' => [ 'shape' => 'ComponentTypeId', ], 'parentEntityId' => [ 'shape' => 'ParentEntityId', ], ], 'union' => true, ], 'ListEntitiesFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListEntitiesFilter', ], ], 'ListEntitiesRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', ], 'members' => [ 'filters' => [ 'shape' => 'ListEntitiesFilters', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], ], ], 'ListEntitiesResponse' => [ 'type' => 'structure', 'members' => [ 'entitySummaries' => [ 'shape' => 'EntitySummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListScenesRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', ], 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], ], ], 'ListScenesResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'sceneSummaries' => [ 'shape' => 'SceneSummaries', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', ], 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'resourceARN' => [ 'shape' => 'TwinMakerArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ListWorkspacesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListWorkspacesResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'workspaceSummaries' => [ 'shape' => 'WorkspaceSummaries', ], ], ], 'Long' => [ 'type' => 'long', 'box' => true, ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 200, 'min' => 0, ], 'Name' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z_\\-0-9]+', ], 'NextToken' => [ 'type' => 'string', 'max' => 17880, 'min' => 0, 'pattern' => '.*', ], 'OrderByTime' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'ParentEntityId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '\\$ROOT|^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}|^[a-zA-Z0-9][a-zA-Z_\\-0-9.:]*[a-zA-Z0-9]+', ], 'ParentEntityUpdateRequest' => [ 'type' => 'structure', 'required' => [ 'updateType', ], 'members' => [ 'parentEntityId' => [ 'shape' => 'ParentEntityId', ], 'updateType' => [ 'shape' => 'ParentEntityUpdateType', ], ], ], 'ParentEntityUpdateType' => [ 'type' => 'string', 'enum' => [ 'UPDATE', 'DELETE', ], ], 'PropertyDefinitionRequest' => [ 'type' => 'structure', 'members' => [ 'configuration' => [ 'shape' => 'Configuration', ], 'dataType' => [ 'shape' => 'DataType', ], 'defaultValue' => [ 'shape' => 'DataValue', ], 'isExternalId' => [ 'shape' => 'Boolean', ], 'isRequiredInEntity' => [ 'shape' => 'Boolean', ], 'isStoredExternally' => [ 'shape' => 'Boolean', ], 'isTimeSeries' => [ 'shape' => 'Boolean', ], ], ], 'PropertyDefinitionResponse' => [ 'type' => 'structure', 'required' => [ 'dataType', 'isExternalId', 'isFinal', 'isImported', 'isInherited', 'isRequiredInEntity', 'isStoredExternally', 'isTimeSeries', ], 'members' => [ 'configuration' => [ 'shape' => 'Configuration', ], 'dataType' => [ 'shape' => 'DataType', ], 'defaultValue' => [ 'shape' => 'DataValue', ], 'isExternalId' => [ 'shape' => 'Boolean', ], 'isFinal' => [ 'shape' => 'Boolean', ], 'isImported' => [ 'shape' => 'Boolean', ], 'isInherited' => [ 'shape' => 'Boolean', ], 'isRequiredInEntity' => [ 'shape' => 'Boolean', ], 'isStoredExternally' => [ 'shape' => 'Boolean', ], 'isTimeSeries' => [ 'shape' => 'Boolean', ], ], ], 'PropertyDefinitionsRequest' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'PropertyDefinitionRequest', ], ], 'PropertyDefinitionsResponse' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'PropertyDefinitionResponse', ], ], 'PropertyFilter' => [ 'type' => 'structure', 'members' => [ 'operator' => [ 'shape' => 'String', ], 'propertyName' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'DataValue', ], ], ], 'PropertyFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'PropertyFilter', ], 'max' => 10, 'min' => 1, ], 'PropertyLatestValue' => [ 'type' => 'structure', 'required' => [ 'propertyReference', ], 'members' => [ 'propertyReference' => [ 'shape' => 'EntityPropertyReference', ], 'propertyValue' => [ 'shape' => 'DataValue', ], ], ], 'PropertyLatestValueMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'PropertyLatestValue', ], ], 'PropertyRequest' => [ 'type' => 'structure', 'members' => [ 'definition' => [ 'shape' => 'PropertyDefinitionRequest', ], 'updateType' => [ 'shape' => 'PropertyUpdateType', ], 'value' => [ 'shape' => 'DataValue', ], ], ], 'PropertyRequests' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'PropertyRequest', ], ], 'PropertyResponse' => [ 'type' => 'structure', 'members' => [ 'definition' => [ 'shape' => 'PropertyDefinitionResponse', ], 'value' => [ 'shape' => 'DataValue', ], ], ], 'PropertyResponses' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'PropertyResponse', ], ], 'PropertyUpdateType' => [ 'type' => 'string', 'enum' => [ 'UPDATE', 'DELETE', ], ], 'PropertyValue' => [ 'type' => 'structure', 'required' => [ 'timestamp', 'value', ], 'members' => [ 'timestamp' => [ 'shape' => 'Timestamp', ], 'value' => [ 'shape' => 'DataValue', ], ], ], 'PropertyValueEntry' => [ 'type' => 'structure', 'required' => [ 'entityPropertyReference', ], 'members' => [ 'entityPropertyReference' => [ 'shape' => 'EntityPropertyReference', ], 'propertyValues' => [ 'shape' => 'PropertyValues', ], ], ], 'PropertyValueHistory' => [ 'type' => 'structure', 'required' => [ 'entityPropertyReference', ], 'members' => [ 'entityPropertyReference' => [ 'shape' => 'EntityPropertyReference', ], 'values' => [ 'shape' => 'Values', ], ], ], 'PropertyValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PropertyValueHistory', ], ], 'PropertyValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'PropertyValue', ], 'max' => 10, 'min' => 1, ], 'Relationship' => [ 'type' => 'structure', 'members' => [ 'relationshipType' => [ 'shape' => 'String', ], 'targetComponentTypeId' => [ 'shape' => 'ComponentTypeId', ], ], ], 'RelationshipValue' => [ 'type' => 'structure', 'members' => [ 'targetComponentName' => [ 'shape' => 'Name', ], 'targetEntityId' => [ 'shape' => 'EntityId', ], ], ], 'RequiredProperties' => [ 'type' => 'list', 'member' => [ 'shape' => 'Name', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'RoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:((aws)|(aws-cn)|(aws-us-gov)):iam::[0-9]{12}:role/.*', ], 'S3Location' => [ 'type' => 'string', ], 'S3Url' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[sS]3://[A-Za-z0-9._/-]+', ], 'SceneCapabilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'SceneCapability', ], 'max' => 50, 'min' => 0, ], 'SceneCapability' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '.*', ], 'SceneSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'SceneSummary', ], ], 'SceneSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'contentLocation', 'creationDateTime', 'sceneId', 'updateDateTime', ], 'members' => [ 'arn' => [ 'shape' => 'TwinMakerArn', ], 'contentLocation' => [ 'shape' => 'S3Url', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'sceneId' => [ 'shape' => 'Id', ], 'updateDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'Scope' => [ 'type' => 'string', 'enum' => [ 'ENTITY', 'WORKSPACE', ], ], 'SelectedPropertyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 10, 'min' => 1, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'State' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'UPDATING', 'DELETING', 'ACTIVE', 'ERROR', ], ], 'Status' => [ 'type' => 'structure', 'members' => [ 'error' => [ 'shape' => 'ErrorDetails', ], 'state' => [ 'shape' => 'State', ], ], ], 'String' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '.*', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 0, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', 'tags', ], 'members' => [ 'resourceARN' => [ 'shape' => 'TwinMakerArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '.*', ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TooManyTagsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TwinMakerArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:((aws)|(aws-cn)|(aws-us-gov)):iottwinmaker:[a-z0-9-]+:[0-9]{12}:[\\/a-zA-Z0-9_\\-\\.:]+', ], 'Type' => [ 'type' => 'string', 'enum' => [ 'RELATIONSHIP', 'STRING', 'LONG', 'BOOLEAN', 'INTEGER', 'DOUBLE', 'LIST', 'MAP', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', 'tagKeys', ], 'members' => [ 'resourceARN' => [ 'shape' => 'TwinMakerArn', 'location' => 'querystring', 'locationName' => 'resourceARN', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateComponentTypeRequest' => [ 'type' => 'structure', 'required' => [ 'componentTypeId', 'workspaceId', ], 'members' => [ 'componentTypeId' => [ 'shape' => 'ComponentTypeId', 'location' => 'uri', 'locationName' => 'componentTypeId', ], 'description' => [ 'shape' => 'Description', ], 'extendsFrom' => [ 'shape' => 'ExtendsFrom', ], 'functions' => [ 'shape' => 'FunctionsRequest', ], 'isSingleton' => [ 'shape' => 'Boolean', ], 'propertyDefinitions' => [ 'shape' => 'PropertyDefinitionsRequest', ], 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], ], ], 'UpdateComponentTypeResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'componentTypeId', 'state', 'workspaceId', ], 'members' => [ 'arn' => [ 'shape' => 'TwinMakerArn', ], 'componentTypeId' => [ 'shape' => 'ComponentTypeId', ], 'state' => [ 'shape' => 'State', ], 'workspaceId' => [ 'shape' => 'Id', ], ], ], 'UpdateEntityRequest' => [ 'type' => 'structure', 'required' => [ 'entityId', 'workspaceId', ], 'members' => [ 'componentUpdates' => [ 'shape' => 'ComponentUpdatesMapRequest', ], 'description' => [ 'shape' => 'Description', ], 'entityId' => [ 'shape' => 'EntityId', 'location' => 'uri', 'locationName' => 'entityId', ], 'entityName' => [ 'shape' => 'EntityName', ], 'parentEntityUpdate' => [ 'shape' => 'ParentEntityUpdateRequest', ], 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], ], ], 'UpdateEntityResponse' => [ 'type' => 'structure', 'required' => [ 'state', 'updateDateTime', ], 'members' => [ 'state' => [ 'shape' => 'State', ], 'updateDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateSceneRequest' => [ 'type' => 'structure', 'required' => [ 'sceneId', 'workspaceId', ], 'members' => [ 'capabilities' => [ 'shape' => 'SceneCapabilities', ], 'contentLocation' => [ 'shape' => 'S3Url', ], 'description' => [ 'shape' => 'Description', ], 'sceneId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'sceneId', ], 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], ], ], 'UpdateSceneResponse' => [ 'type' => 'structure', 'required' => [ 'updateDateTime', ], 'members' => [ 'updateDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateWorkspaceRequest' => [ 'type' => 'structure', 'required' => [ 'workspaceId', ], 'members' => [ 'description' => [ 'shape' => 'Description', ], 'role' => [ 'shape' => 'RoleArn', ], 'workspaceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'workspaceId', ], ], ], 'UpdateWorkspaceResponse' => [ 'type' => 'structure', 'required' => [ 'updateDateTime', ], 'members' => [ 'updateDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Value' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z_\\-0-9]+', ], 'Values' => [ 'type' => 'list', 'member' => [ 'shape' => 'PropertyValue', ], ], 'WorkspaceSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkspaceSummary', ], ], 'WorkspaceSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'creationDateTime', 'updateDateTime', 'workspaceId', ], 'members' => [ 'arn' => [ 'shape' => 'TwinMakerArn', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'Description', ], 'updateDateTime' => [ 'shape' => 'Timestamp', ], 'workspaceId' => [ 'shape' => 'Id', ], ], ], ],];
