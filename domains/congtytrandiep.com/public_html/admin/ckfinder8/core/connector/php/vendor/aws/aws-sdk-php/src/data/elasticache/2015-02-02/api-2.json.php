<?php
// This file was auto-generated from sdk-root/src/data/elasticache/2015-02-02/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2015-02-02', 'endpointPrefix' => 'elasticache', 'protocol' => 'query', 'serviceFullName' => 'Amazon ElastiCache', 'serviceId' => 'ElastiCache', 'signatureVersion' => 'v4', 'uid' => 'elasticache-2015-02-02', 'xmlNamespace' => 'http://elasticache.amazonaws.com/doc/2015-02-02/', ], 'operations' => [ 'AddTagsToResource' => [ 'name' => 'AddTagsToResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddTagsToResourceMessage', ], 'output' => [ 'shape' => 'TagListMessage', 'resultWrapper' => 'AddTagsToResourceResult', ], 'errors' => [ [ 'shape' => 'CacheClusterNotFoundFault', ], [ 'shape' => 'CacheParameterGroupNotFoundFault', ], [ 'shape' => 'CacheSecurityGroupNotFoundFault', ], [ 'shape' => 'CacheSubnetGroupNotFoundFault', ], [ 'shape' => 'InvalidReplicationGroupStateFault', ], [ 'shape' => 'ReplicationGroupNotFoundFault', ], [ 'shape' => 'ReservedCacheNodeNotFoundFault', ], [ 'shape' => 'SnapshotNotFoundFault', ], [ 'shape' => 'UserNotFoundFault', ], [ 'shape' => 'UserGroupNotFoundFault', ], [ 'shape' => 'TagQuotaPerResourceExceeded', ], [ 'shape' => 'InvalidARNFault', ], ], ], 'AuthorizeCacheSecurityGroupIngress' => [ 'name' => 'AuthorizeCacheSecurityGroupIngress', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AuthorizeCacheSecurityGroupIngressMessage', ], 'output' => [ 'shape' => 'AuthorizeCacheSecurityGroupIngressResult', 'resultWrapper' => 'AuthorizeCacheSecurityGroupIngressResult', ], 'errors' => [ [ 'shape' => 'CacheSecurityGroupNotFoundFault', ], [ 'shape' => 'InvalidCacheSecurityGroupStateFault', ], [ 'shape' => 'AuthorizationAlreadyExistsFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'BatchApplyUpdateAction' => [ 'name' => 'BatchApplyUpdateAction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchApplyUpdateActionMessage', ], 'output' => [ 'shape' => 'UpdateActionResultsMessage', 'resultWrapper' => 'BatchApplyUpdateActionResult', ], 'errors' => [ [ 'shape' => 'ServiceUpdateNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'BatchStopUpdateAction' => [ 'name' => 'BatchStopUpdateAction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchStopUpdateActionMessage', ], 'output' => [ 'shape' => 'UpdateActionResultsMessage', 'resultWrapper' => 'BatchStopUpdateActionResult', ], 'errors' => [ [ 'shape' => 'ServiceUpdateNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'CompleteMigration' => [ 'name' => 'CompleteMigration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CompleteMigrationMessage', ], 'output' => [ 'shape' => 'CompleteMigrationResponse', 'resultWrapper' => 'CompleteMigrationResult', ], 'errors' => [ [ 'shape' => 'ReplicationGroupNotFoundFault', ], [ 'shape' => 'InvalidReplicationGroupStateFault', ], [ 'shape' => 'ReplicationGroupNotUnderMigrationFault', ], ], ], 'CopySnapshot' => [ 'name' => 'CopySnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CopySnapshotMessage', ], 'output' => [ 'shape' => 'CopySnapshotResult', 'resultWrapper' => 'CopySnapshotResult', ], 'errors' => [ [ 'shape' => 'SnapshotAlreadyExistsFault', ], [ 'shape' => 'SnapshotNotFoundFault', ], [ 'shape' => 'SnapshotQuotaExceededFault', ], [ 'shape' => 'InvalidSnapshotStateFault', ], [ 'shape' => 'TagQuotaPerResourceExceeded', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'CreateCacheCluster' => [ 'name' => 'CreateCacheCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateCacheClusterMessage', ], 'output' => [ 'shape' => 'CreateCacheClusterResult', 'resultWrapper' => 'CreateCacheClusterResult', ], 'errors' => [ [ 'shape' => 'ReplicationGroupNotFoundFault', ], [ 'shape' => 'InvalidReplicationGroupStateFault', ], [ 'shape' => 'CacheClusterAlreadyExistsFault', ], [ 'shape' => 'InsufficientCacheClusterCapacityFault', ], [ 'shape' => 'CacheSecurityGroupNotFoundFault', ], [ 'shape' => 'CacheSubnetGroupNotFoundFault', ], [ 'shape' => 'ClusterQuotaForCustomerExceededFault', ], [ 'shape' => 'NodeQuotaForClusterExceededFault', ], [ 'shape' => 'NodeQuotaForCustomerExceededFault', ], [ 'shape' => 'CacheParameterGroupNotFoundFault', ], [ 'shape' => 'InvalidVPCNetworkStateFault', ], [ 'shape' => 'TagQuotaPerResourceExceeded', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'CreateCacheParameterGroup' => [ 'name' => 'CreateCacheParameterGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateCacheParameterGroupMessage', ], 'output' => [ 'shape' => 'CreateCacheParameterGroupResult', 'resultWrapper' => 'CreateCacheParameterGroupResult', ], 'errors' => [ [ 'shape' => 'CacheParameterGroupQuotaExceededFault', ], [ 'shape' => 'CacheParameterGroupAlreadyExistsFault', ], [ 'shape' => 'InvalidCacheParameterGroupStateFault', ], [ 'shape' => 'TagQuotaPerResourceExceeded', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'CreateCacheSecurityGroup' => [ 'name' => 'CreateCacheSecurityGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateCacheSecurityGroupMessage', ], 'output' => [ 'shape' => 'CreateCacheSecurityGroupResult', 'resultWrapper' => 'CreateCacheSecurityGroupResult', ], 'errors' => [ [ 'shape' => 'CacheSecurityGroupAlreadyExistsFault', ], [ 'shape' => 'CacheSecurityGroupQuotaExceededFault', ], [ 'shape' => 'TagQuotaPerResourceExceeded', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'CreateCacheSubnetGroup' => [ 'name' => 'CreateCacheSubnetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateCacheSubnetGroupMessage', ], 'output' => [ 'shape' => 'CreateCacheSubnetGroupResult', 'resultWrapper' => 'CreateCacheSubnetGroupResult', ], 'errors' => [ [ 'shape' => 'CacheSubnetGroupAlreadyExistsFault', ], [ 'shape' => 'CacheSubnetGroupQuotaExceededFault', ], [ 'shape' => 'CacheSubnetQuotaExceededFault', ], [ 'shape' => 'TagQuotaPerResourceExceeded', ], [ 'shape' => 'InvalidSubnet', ], [ 'shape' => 'SubnetNotAllowedFault', ], ], ], 'CreateGlobalReplicationGroup' => [ 'name' => 'CreateGlobalReplicationGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateGlobalReplicationGroupMessage', ], 'output' => [ 'shape' => 'CreateGlobalReplicationGroupResult', 'resultWrapper' => 'CreateGlobalReplicationGroupResult', ], 'errors' => [ [ 'shape' => 'ReplicationGroupNotFoundFault', ], [ 'shape' => 'InvalidReplicationGroupStateFault', ], [ 'shape' => 'GlobalReplicationGroupAlreadyExistsFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'CreateReplicationGroup' => [ 'name' => 'CreateReplicationGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateReplicationGroupMessage', ], 'output' => [ 'shape' => 'CreateReplicationGroupResult', 'resultWrapper' => 'CreateReplicationGroupResult', ], 'errors' => [ [ 'shape' => 'CacheClusterNotFoundFault', ], [ 'shape' => 'InvalidCacheClusterStateFault', ], [ 'shape' => 'ReplicationGroupAlreadyExistsFault', ], [ 'shape' => 'InvalidUserGroupStateFault', ], [ 'shape' => 'UserGroupNotFoundFault', ], [ 'shape' => 'InsufficientCacheClusterCapacityFault', ], [ 'shape' => 'CacheSecurityGroupNotFoundFault', ], [ 'shape' => 'CacheSubnetGroupNotFoundFault', ], [ 'shape' => 'ClusterQuotaForCustomerExceededFault', ], [ 'shape' => 'NodeQuotaForClusterExceededFault', ], [ 'shape' => 'NodeQuotaForCustomerExceededFault', ], [ 'shape' => 'CacheParameterGroupNotFoundFault', ], [ 'shape' => 'InvalidVPCNetworkStateFault', ], [ 'shape' => 'TagQuotaPerResourceExceeded', ], [ 'shape' => 'NodeGroupsPerReplicationGroupQuotaExceededFault', ], [ 'shape' => 'GlobalReplicationGroupNotFoundFault', ], [ 'shape' => 'InvalidGlobalReplicationGroupStateFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'CreateSnapshot' => [ 'name' => 'CreateSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSnapshotMessage', ], 'output' => [ 'shape' => 'CreateSnapshotResult', 'resultWrapper' => 'CreateSnapshotResult', ], 'errors' => [ [ 'shape' => 'SnapshotAlreadyExistsFault', ], [ 'shape' => 'CacheClusterNotFoundFault', ], [ 'shape' => 'ReplicationGroupNotFoundFault', ], [ 'shape' => 'InvalidCacheClusterStateFault', ], [ 'shape' => 'InvalidReplicationGroupStateFault', ], [ 'shape' => 'SnapshotQuotaExceededFault', ], [ 'shape' => 'SnapshotFeatureNotSupportedFault', ], [ 'shape' => 'TagQuotaPerResourceExceeded', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'CreateUser' => [ 'name' => 'CreateUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateUserMessage', ], 'output' => [ 'shape' => 'User', 'resultWrapper' => 'CreateUserResult', ], 'errors' => [ [ 'shape' => 'UserAlreadyExistsFault', ], [ 'shape' => 'UserQuotaExceededFault', ], [ 'shape' => 'DuplicateUserNameFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'TagQuotaPerResourceExceeded', ], ], ], 'CreateUserGroup' => [ 'name' => 'CreateUserGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateUserGroupMessage', ], 'output' => [ 'shape' => 'UserGroup', 'resultWrapper' => 'CreateUserGroupResult', ], 'errors' => [ [ 'shape' => 'UserNotFoundFault', ], [ 'shape' => 'DuplicateUserNameFault', ], [ 'shape' => 'UserGroupAlreadyExistsFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'DefaultUserRequired', ], [ 'shape' => 'UserGroupQuotaExceededFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'TagQuotaPerResourceExceeded', ], ], ], 'DecreaseNodeGroupsInGlobalReplicationGroup' => [ 'name' => 'DecreaseNodeGroupsInGlobalReplicationGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DecreaseNodeGroupsInGlobalReplicationGroupMessage', ], 'output' => [ 'shape' => 'DecreaseNodeGroupsInGlobalReplicationGroupResult', 'resultWrapper' => 'DecreaseNodeGroupsInGlobalReplicationGroupResult', ], 'errors' => [ [ 'shape' => 'GlobalReplicationGroupNotFoundFault', ], [ 'shape' => 'InvalidGlobalReplicationGroupStateFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DecreaseReplicaCount' => [ 'name' => 'DecreaseReplicaCount', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DecreaseReplicaCountMessage', ], 'output' => [ 'shape' => 'DecreaseReplicaCountResult', 'resultWrapper' => 'DecreaseReplicaCountResult', ], 'errors' => [ [ 'shape' => 'ReplicationGroupNotFoundFault', ], [ 'shape' => 'InvalidReplicationGroupStateFault', ], [ 'shape' => 'InvalidCacheClusterStateFault', ], [ 'shape' => 'InvalidVPCNetworkStateFault', ], [ 'shape' => 'InsufficientCacheClusterCapacityFault', ], [ 'shape' => 'ClusterQuotaForCustomerExceededFault', ], [ 'shape' => 'NodeGroupsPerReplicationGroupQuotaExceededFault', ], [ 'shape' => 'NodeQuotaForCustomerExceededFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'NoOperationFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DeleteCacheCluster' => [ 'name' => 'DeleteCacheCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCacheClusterMessage', ], 'output' => [ 'shape' => 'DeleteCacheClusterResult', 'resultWrapper' => 'DeleteCacheClusterResult', ], 'errors' => [ [ 'shape' => 'CacheClusterNotFoundFault', ], [ 'shape' => 'InvalidCacheClusterStateFault', ], [ 'shape' => 'SnapshotAlreadyExistsFault', ], [ 'shape' => 'SnapshotFeatureNotSupportedFault', ], [ 'shape' => 'SnapshotQuotaExceededFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DeleteCacheParameterGroup' => [ 'name' => 'DeleteCacheParameterGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCacheParameterGroupMessage', ], 'errors' => [ [ 'shape' => 'InvalidCacheParameterGroupStateFault', ], [ 'shape' => 'CacheParameterGroupNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DeleteCacheSecurityGroup' => [ 'name' => 'DeleteCacheSecurityGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCacheSecurityGroupMessage', ], 'errors' => [ [ 'shape' => 'InvalidCacheSecurityGroupStateFault', ], [ 'shape' => 'CacheSecurityGroupNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DeleteCacheSubnetGroup' => [ 'name' => 'DeleteCacheSubnetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCacheSubnetGroupMessage', ], 'errors' => [ [ 'shape' => 'CacheSubnetGroupInUse', ], [ 'shape' => 'CacheSubnetGroupNotFoundFault', ], ], ], 'DeleteGlobalReplicationGroup' => [ 'name' => 'DeleteGlobalReplicationGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteGlobalReplicationGroupMessage', ], 'output' => [ 'shape' => 'DeleteGlobalReplicationGroupResult', 'resultWrapper' => 'DeleteGlobalReplicationGroupResult', ], 'errors' => [ [ 'shape' => 'GlobalReplicationGroupNotFoundFault', ], [ 'shape' => 'InvalidGlobalReplicationGroupStateFault', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'DeleteReplicationGroup' => [ 'name' => 'DeleteReplicationGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteReplicationGroupMessage', ], 'output' => [ 'shape' => 'DeleteReplicationGroupResult', 'resultWrapper' => 'DeleteReplicationGroupResult', ], 'errors' => [ [ 'shape' => 'ReplicationGroupNotFoundFault', ], [ 'shape' => 'InvalidReplicationGroupStateFault', ], [ 'shape' => 'SnapshotAlreadyExistsFault', ], [ 'shape' => 'SnapshotFeatureNotSupportedFault', ], [ 'shape' => 'SnapshotQuotaExceededFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DeleteSnapshot' => [ 'name' => 'DeleteSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSnapshotMessage', ], 'output' => [ 'shape' => 'DeleteSnapshotResult', 'resultWrapper' => 'DeleteSnapshotResult', ], 'errors' => [ [ 'shape' => 'SnapshotNotFoundFault', ], [ 'shape' => 'InvalidSnapshotStateFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DeleteUser' => [ 'name' => 'DeleteUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteUserMessage', ], 'output' => [ 'shape' => 'User', 'resultWrapper' => 'DeleteUserResult', ], 'errors' => [ [ 'shape' => 'InvalidUserStateFault', ], [ 'shape' => 'UserNotFoundFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'DefaultUserAssociatedToUserGroupFault', ], ], ], 'DeleteUserGroup' => [ 'name' => 'DeleteUserGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteUserGroupMessage', ], 'output' => [ 'shape' => 'UserGroup', 'resultWrapper' => 'DeleteUserGroupResult', ], 'errors' => [ [ 'shape' => 'UserGroupNotFoundFault', ], [ 'shape' => 'InvalidUserGroupStateFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'DescribeCacheClusters' => [ 'name' => 'DescribeCacheClusters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeCacheClustersMessage', ], 'output' => [ 'shape' => 'CacheClusterMessage', 'resultWrapper' => 'DescribeCacheClustersResult', ], 'errors' => [ [ 'shape' => 'CacheClusterNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DescribeCacheEngineVersions' => [ 'name' => 'DescribeCacheEngineVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeCacheEngineVersionsMessage', ], 'output' => [ 'shape' => 'CacheEngineVersionMessage', 'resultWrapper' => 'DescribeCacheEngineVersionsResult', ], ], 'DescribeCacheParameterGroups' => [ 'name' => 'DescribeCacheParameterGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeCacheParameterGroupsMessage', ], 'output' => [ 'shape' => 'CacheParameterGroupsMessage', 'resultWrapper' => 'DescribeCacheParameterGroupsResult', ], 'errors' => [ [ 'shape' => 'CacheParameterGroupNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DescribeCacheParameters' => [ 'name' => 'DescribeCacheParameters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeCacheParametersMessage', ], 'output' => [ 'shape' => 'CacheParameterGroupDetails', 'resultWrapper' => 'DescribeCacheParametersResult', ], 'errors' => [ [ 'shape' => 'CacheParameterGroupNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DescribeCacheSecurityGroups' => [ 'name' => 'DescribeCacheSecurityGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeCacheSecurityGroupsMessage', ], 'output' => [ 'shape' => 'CacheSecurityGroupMessage', 'resultWrapper' => 'DescribeCacheSecurityGroupsResult', ], 'errors' => [ [ 'shape' => 'CacheSecurityGroupNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DescribeCacheSubnetGroups' => [ 'name' => 'DescribeCacheSubnetGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeCacheSubnetGroupsMessage', ], 'output' => [ 'shape' => 'CacheSubnetGroupMessage', 'resultWrapper' => 'DescribeCacheSubnetGroupsResult', ], 'errors' => [ [ 'shape' => 'CacheSubnetGroupNotFoundFault', ], ], ], 'DescribeEngineDefaultParameters' => [ 'name' => 'DescribeEngineDefaultParameters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEngineDefaultParametersMessage', ], 'output' => [ 'shape' => 'DescribeEngineDefaultParametersResult', 'resultWrapper' => 'DescribeEngineDefaultParametersResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DescribeEvents' => [ 'name' => 'DescribeEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEventsMessage', ], 'output' => [ 'shape' => 'EventsMessage', 'resultWrapper' => 'DescribeEventsResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DescribeGlobalReplicationGroups' => [ 'name' => 'DescribeGlobalReplicationGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeGlobalReplicationGroupsMessage', ], 'output' => [ 'shape' => 'DescribeGlobalReplicationGroupsResult', 'resultWrapper' => 'DescribeGlobalReplicationGroupsResult', ], 'errors' => [ [ 'shape' => 'GlobalReplicationGroupNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DescribeReplicationGroups' => [ 'name' => 'DescribeReplicationGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeReplicationGroupsMessage', ], 'output' => [ 'shape' => 'ReplicationGroupMessage', 'resultWrapper' => 'DescribeReplicationGroupsResult', ], 'errors' => [ [ 'shape' => 'ReplicationGroupNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DescribeReservedCacheNodes' => [ 'name' => 'DescribeReservedCacheNodes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeReservedCacheNodesMessage', ], 'output' => [ 'shape' => 'ReservedCacheNodeMessage', 'resultWrapper' => 'DescribeReservedCacheNodesResult', ], 'errors' => [ [ 'shape' => 'ReservedCacheNodeNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DescribeReservedCacheNodesOfferings' => [ 'name' => 'DescribeReservedCacheNodesOfferings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeReservedCacheNodesOfferingsMessage', ], 'output' => [ 'shape' => 'ReservedCacheNodesOfferingMessage', 'resultWrapper' => 'DescribeReservedCacheNodesOfferingsResult', ], 'errors' => [ [ 'shape' => 'ReservedCacheNodesOfferingNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DescribeServiceUpdates' => [ 'name' => 'DescribeServiceUpdates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeServiceUpdatesMessage', ], 'output' => [ 'shape' => 'ServiceUpdatesMessage', 'resultWrapper' => 'DescribeServiceUpdatesResult', ], 'errors' => [ [ 'shape' => 'ServiceUpdateNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DescribeSnapshots' => [ 'name' => 'DescribeSnapshots', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSnapshotsMessage', ], 'output' => [ 'shape' => 'DescribeSnapshotsListMessage', 'resultWrapper' => 'DescribeSnapshotsResult', ], 'errors' => [ [ 'shape' => 'CacheClusterNotFoundFault', ], [ 'shape' => 'SnapshotNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DescribeUpdateActions' => [ 'name' => 'DescribeUpdateActions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeUpdateActionsMessage', ], 'output' => [ 'shape' => 'UpdateActionsMessage', 'resultWrapper' => 'DescribeUpdateActionsResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DescribeUserGroups' => [ 'name' => 'DescribeUserGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeUserGroupsMessage', ], 'output' => [ 'shape' => 'DescribeUserGroupsResult', 'resultWrapper' => 'DescribeUserGroupsResult', ], 'errors' => [ [ 'shape' => 'UserGroupNotFoundFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DescribeUsers' => [ 'name' => 'DescribeUsers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeUsersMessage', ], 'output' => [ 'shape' => 'DescribeUsersResult', 'resultWrapper' => 'DescribeUsersResult', ], 'errors' => [ [ 'shape' => 'UserNotFoundFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'DisassociateGlobalReplicationGroup' => [ 'name' => 'DisassociateGlobalReplicationGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateGlobalReplicationGroupMessage', ], 'output' => [ 'shape' => 'DisassociateGlobalReplicationGroupResult', 'resultWrapper' => 'DisassociateGlobalReplicationGroupResult', ], 'errors' => [ [ 'shape' => 'GlobalReplicationGroupNotFoundFault', ], [ 'shape' => 'InvalidGlobalReplicationGroupStateFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'FailoverGlobalReplicationGroup' => [ 'name' => 'FailoverGlobalReplicationGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'FailoverGlobalReplicationGroupMessage', ], 'output' => [ 'shape' => 'FailoverGlobalReplicationGroupResult', 'resultWrapper' => 'FailoverGlobalReplicationGroupResult', ], 'errors' => [ [ 'shape' => 'GlobalReplicationGroupNotFoundFault', ], [ 'shape' => 'InvalidGlobalReplicationGroupStateFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'IncreaseNodeGroupsInGlobalReplicationGroup' => [ 'name' => 'IncreaseNodeGroupsInGlobalReplicationGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'IncreaseNodeGroupsInGlobalReplicationGroupMessage', ], 'output' => [ 'shape' => 'IncreaseNodeGroupsInGlobalReplicationGroupResult', 'resultWrapper' => 'IncreaseNodeGroupsInGlobalReplicationGroupResult', ], 'errors' => [ [ 'shape' => 'GlobalReplicationGroupNotFoundFault', ], [ 'shape' => 'InvalidGlobalReplicationGroupStateFault', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'IncreaseReplicaCount' => [ 'name' => 'IncreaseReplicaCount', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'IncreaseReplicaCountMessage', ], 'output' => [ 'shape' => 'IncreaseReplicaCountResult', 'resultWrapper' => 'IncreaseReplicaCountResult', ], 'errors' => [ [ 'shape' => 'ReplicationGroupNotFoundFault', ], [ 'shape' => 'InvalidReplicationGroupStateFault', ], [ 'shape' => 'InvalidCacheClusterStateFault', ], [ 'shape' => 'InvalidVPCNetworkStateFault', ], [ 'shape' => 'InsufficientCacheClusterCapacityFault', ], [ 'shape' => 'ClusterQuotaForCustomerExceededFault', ], [ 'shape' => 'NodeGroupsPerReplicationGroupQuotaExceededFault', ], [ 'shape' => 'NodeQuotaForCustomerExceededFault', ], [ 'shape' => 'NoOperationFault', ], [ 'shape' => 'InvalidKMSKeyFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'ListAllowedNodeTypeModifications' => [ 'name' => 'ListAllowedNodeTypeModifications', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAllowedNodeTypeModificationsMessage', ], 'output' => [ 'shape' => 'AllowedNodeTypeModificationsMessage', 'resultWrapper' => 'ListAllowedNodeTypeModificationsResult', ], 'errors' => [ [ 'shape' => 'CacheClusterNotFoundFault', ], [ 'shape' => 'ReplicationGroupNotFoundFault', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceMessage', ], 'output' => [ 'shape' => 'TagListMessage', 'resultWrapper' => 'ListTagsForResourceResult', ], 'errors' => [ [ 'shape' => 'CacheClusterNotFoundFault', ], [ 'shape' => 'CacheParameterGroupNotFoundFault', ], [ 'shape' => 'CacheSecurityGroupNotFoundFault', ], [ 'shape' => 'CacheSubnetGroupNotFoundFault', ], [ 'shape' => 'InvalidReplicationGroupStateFault', ], [ 'shape' => 'ReplicationGroupNotFoundFault', ], [ 'shape' => 'ReservedCacheNodeNotFoundFault', ], [ 'shape' => 'SnapshotNotFoundFault', ], [ 'shape' => 'UserNotFoundFault', ], [ 'shape' => 'UserGroupNotFoundFault', ], [ 'shape' => 'InvalidARNFault', ], ], ], 'ModifyCacheCluster' => [ 'name' => 'ModifyCacheCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyCacheClusterMessage', ], 'output' => [ 'shape' => 'ModifyCacheClusterResult', 'resultWrapper' => 'ModifyCacheClusterResult', ], 'errors' => [ [ 'shape' => 'InvalidCacheClusterStateFault', ], [ 'shape' => 'InvalidCacheSecurityGroupStateFault', ], [ 'shape' => 'InsufficientCacheClusterCapacityFault', ], [ 'shape' => 'CacheClusterNotFoundFault', ], [ 'shape' => 'NodeQuotaForClusterExceededFault', ], [ 'shape' => 'NodeQuotaForCustomerExceededFault', ], [ 'shape' => 'CacheSecurityGroupNotFoundFault', ], [ 'shape' => 'CacheParameterGroupNotFoundFault', ], [ 'shape' => 'InvalidVPCNetworkStateFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'ModifyCacheParameterGroup' => [ 'name' => 'ModifyCacheParameterGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyCacheParameterGroupMessage', ], 'output' => [ 'shape' => 'CacheParameterGroupNameMessage', 'resultWrapper' => 'ModifyCacheParameterGroupResult', ], 'errors' => [ [ 'shape' => 'CacheParameterGroupNotFoundFault', ], [ 'shape' => 'InvalidCacheParameterGroupStateFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'InvalidGlobalReplicationGroupStateFault', ], ], ], 'ModifyCacheSubnetGroup' => [ 'name' => 'ModifyCacheSubnetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyCacheSubnetGroupMessage', ], 'output' => [ 'shape' => 'ModifyCacheSubnetGroupResult', 'resultWrapper' => 'ModifyCacheSubnetGroupResult', ], 'errors' => [ [ 'shape' => 'CacheSubnetGroupNotFoundFault', ], [ 'shape' => 'CacheSubnetQuotaExceededFault', ], [ 'shape' => 'SubnetInUse', ], [ 'shape' => 'InvalidSubnet', ], [ 'shape' => 'SubnetNotAllowedFault', ], ], ], 'ModifyGlobalReplicationGroup' => [ 'name' => 'ModifyGlobalReplicationGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyGlobalReplicationGroupMessage', ], 'output' => [ 'shape' => 'ModifyGlobalReplicationGroupResult', 'resultWrapper' => 'ModifyGlobalReplicationGroupResult', ], 'errors' => [ [ 'shape' => 'GlobalReplicationGroupNotFoundFault', ], [ 'shape' => 'InvalidGlobalReplicationGroupStateFault', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'ModifyReplicationGroup' => [ 'name' => 'ModifyReplicationGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyReplicationGroupMessage', ], 'output' => [ 'shape' => 'ModifyReplicationGroupResult', 'resultWrapper' => 'ModifyReplicationGroupResult', ], 'errors' => [ [ 'shape' => 'ReplicationGroupNotFoundFault', ], [ 'shape' => 'InvalidReplicationGroupStateFault', ], [ 'shape' => 'InvalidUserGroupStateFault', ], [ 'shape' => 'UserGroupNotFoundFault', ], [ 'shape' => 'InvalidCacheClusterStateFault', ], [ 'shape' => 'InvalidCacheSecurityGroupStateFault', ], [ 'shape' => 'InsufficientCacheClusterCapacityFault', ], [ 'shape' => 'CacheClusterNotFoundFault', ], [ 'shape' => 'NodeQuotaForClusterExceededFault', ], [ 'shape' => 'NodeQuotaForCustomerExceededFault', ], [ 'shape' => 'CacheSecurityGroupNotFoundFault', ], [ 'shape' => 'CacheParameterGroupNotFoundFault', ], [ 'shape' => 'InvalidVPCNetworkStateFault', ], [ 'shape' => 'InvalidKMSKeyFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'ModifyReplicationGroupShardConfiguration' => [ 'name' => 'ModifyReplicationGroupShardConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyReplicationGroupShardConfigurationMessage', ], 'output' => [ 'shape' => 'ModifyReplicationGroupShardConfigurationResult', 'resultWrapper' => 'ModifyReplicationGroupShardConfigurationResult', ], 'errors' => [ [ 'shape' => 'ReplicationGroupNotFoundFault', ], [ 'shape' => 'InvalidReplicationGroupStateFault', ], [ 'shape' => 'InvalidCacheClusterStateFault', ], [ 'shape' => 'InvalidVPCNetworkStateFault', ], [ 'shape' => 'InsufficientCacheClusterCapacityFault', ], [ 'shape' => 'NodeGroupsPerReplicationGroupQuotaExceededFault', ], [ 'shape' => 'NodeQuotaForCustomerExceededFault', ], [ 'shape' => 'InvalidKMSKeyFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'ModifyUser' => [ 'name' => 'ModifyUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyUserMessage', ], 'output' => [ 'shape' => 'User', 'resultWrapper' => 'ModifyUserResult', ], 'errors' => [ [ 'shape' => 'UserNotFoundFault', ], [ 'shape' => 'InvalidUserStateFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'ModifyUserGroup' => [ 'name' => 'ModifyUserGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyUserGroupMessage', ], 'output' => [ 'shape' => 'UserGroup', 'resultWrapper' => 'ModifyUserGroupResult', ], 'errors' => [ [ 'shape' => 'UserGroupNotFoundFault', ], [ 'shape' => 'UserNotFoundFault', ], [ 'shape' => 'DuplicateUserNameFault', ], [ 'shape' => 'ServiceLinkedRoleNotFoundFault', ], [ 'shape' => 'DefaultUserRequired', ], [ 'shape' => 'InvalidUserGroupStateFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'PurchaseReservedCacheNodesOffering' => [ 'name' => 'PurchaseReservedCacheNodesOffering', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PurchaseReservedCacheNodesOfferingMessage', ], 'output' => [ 'shape' => 'PurchaseReservedCacheNodesOfferingResult', 'resultWrapper' => 'PurchaseReservedCacheNodesOfferingResult', ], 'errors' => [ [ 'shape' => 'ReservedCacheNodesOfferingNotFoundFault', ], [ 'shape' => 'ReservedCacheNodeAlreadyExistsFault', ], [ 'shape' => 'ReservedCacheNodeQuotaExceededFault', ], [ 'shape' => 'TagQuotaPerResourceExceeded', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'RebalanceSlotsInGlobalReplicationGroup' => [ 'name' => 'RebalanceSlotsInGlobalReplicationGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RebalanceSlotsInGlobalReplicationGroupMessage', ], 'output' => [ 'shape' => 'RebalanceSlotsInGlobalReplicationGroupResult', 'resultWrapper' => 'RebalanceSlotsInGlobalReplicationGroupResult', ], 'errors' => [ [ 'shape' => 'GlobalReplicationGroupNotFoundFault', ], [ 'shape' => 'InvalidGlobalReplicationGroupStateFault', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'RebootCacheCluster' => [ 'name' => 'RebootCacheCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RebootCacheClusterMessage', ], 'output' => [ 'shape' => 'RebootCacheClusterResult', 'resultWrapper' => 'RebootCacheClusterResult', ], 'errors' => [ [ 'shape' => 'InvalidCacheClusterStateFault', ], [ 'shape' => 'CacheClusterNotFoundFault', ], ], ], 'RemoveTagsFromResource' => [ 'name' => 'RemoveTagsFromResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveTagsFromResourceMessage', ], 'output' => [ 'shape' => 'TagListMessage', 'resultWrapper' => 'RemoveTagsFromResourceResult', ], 'errors' => [ [ 'shape' => 'CacheClusterNotFoundFault', ], [ 'shape' => 'CacheParameterGroupNotFoundFault', ], [ 'shape' => 'CacheSecurityGroupNotFoundFault', ], [ 'shape' => 'CacheSubnetGroupNotFoundFault', ], [ 'shape' => 'InvalidReplicationGroupStateFault', ], [ 'shape' => 'ReplicationGroupNotFoundFault', ], [ 'shape' => 'ReservedCacheNodeNotFoundFault', ], [ 'shape' => 'SnapshotNotFoundFault', ], [ 'shape' => 'UserNotFoundFault', ], [ 'shape' => 'UserGroupNotFoundFault', ], [ 'shape' => 'InvalidARNFault', ], [ 'shape' => 'TagNotFoundFault', ], ], ], 'ResetCacheParameterGroup' => [ 'name' => 'ResetCacheParameterGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ResetCacheParameterGroupMessage', ], 'output' => [ 'shape' => 'CacheParameterGroupNameMessage', 'resultWrapper' => 'ResetCacheParameterGroupResult', ], 'errors' => [ [ 'shape' => 'InvalidCacheParameterGroupStateFault', ], [ 'shape' => 'CacheParameterGroupNotFoundFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], [ 'shape' => 'InvalidGlobalReplicationGroupStateFault', ], ], ], 'RevokeCacheSecurityGroupIngress' => [ 'name' => 'RevokeCacheSecurityGroupIngress', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RevokeCacheSecurityGroupIngressMessage', ], 'output' => [ 'shape' => 'RevokeCacheSecurityGroupIngressResult', 'resultWrapper' => 'RevokeCacheSecurityGroupIngressResult', ], 'errors' => [ [ 'shape' => 'CacheSecurityGroupNotFoundFault', ], [ 'shape' => 'AuthorizationNotFoundFault', ], [ 'shape' => 'InvalidCacheSecurityGroupStateFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], 'StartMigration' => [ 'name' => 'StartMigration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartMigrationMessage', ], 'output' => [ 'shape' => 'StartMigrationResponse', 'resultWrapper' => 'StartMigrationResult', ], 'errors' => [ [ 'shape' => 'ReplicationGroupNotFoundFault', ], [ 'shape' => 'InvalidReplicationGroupStateFault', ], [ 'shape' => 'ReplicationGroupAlreadyUnderMigrationFault', ], [ 'shape' => 'InvalidParameterValueException', ], ], ], 'TestFailover' => [ 'name' => 'TestFailover', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TestFailoverMessage', ], 'output' => [ 'shape' => 'TestFailoverResult', 'resultWrapper' => 'TestFailoverResult', ], 'errors' => [ [ 'shape' => 'APICallRateForCustomerExceededFault', ], [ 'shape' => 'InvalidCacheClusterStateFault', ], [ 'shape' => 'InvalidReplicationGroupStateFault', ], [ 'shape' => 'NodeGroupNotFoundFault', ], [ 'shape' => 'ReplicationGroupNotFoundFault', ], [ 'shape' => 'TestFailoverNotAvailableFault', ], [ 'shape' => 'InvalidKMSKeyFault', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'InvalidParameterCombinationException', ], ], ], ], 'shapes' => [ 'APICallRateForCustomerExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'APICallRateForCustomerExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'AZMode' => [ 'type' => 'string', 'enum' => [ 'single-az', 'cross-az', ], ], 'AccessString' => [ 'type' => 'string', 'pattern' => '.*\\S.*', ], 'AddTagsToResourceMessage' => [ 'type' => 'structure', 'required' => [ 'ResourceName', 'Tags', ], 'members' => [ 'ResourceName' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'AllowedNodeGroupId' => [ 'type' => 'string', 'max' => 4, 'min' => 1, 'pattern' => '\\d+', ], 'AllowedNodeTypeModificationsMessage' => [ 'type' => 'structure', 'members' => [ 'ScaleUpModifications' => [ 'shape' => 'NodeTypeList', ], 'ScaleDownModifications' => [ 'shape' => 'NodeTypeList', ], ], ], 'AuthTokenUpdateStatus' => [ 'type' => 'string', 'enum' => [ 'SETTING', 'ROTATING', ], ], 'AuthTokenUpdateStrategyType' => [ 'type' => 'string', 'enum' => [ 'SET', 'ROTATE', 'DELETE', ], ], 'Authentication' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'AuthenticationType', ], 'PasswordCount' => [ 'shape' => 'IntegerOptional', ], ], ], 'AuthenticationType' => [ 'type' => 'string', 'enum' => [ 'password', 'no-password', ], ], 'AuthorizationAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'AuthorizationAlreadyExists', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'AuthorizationNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'AuthorizationNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'AuthorizeCacheSecurityGroupIngressMessage' => [ 'type' => 'structure', 'required' => [ 'CacheSecurityGroupName', 'EC2SecurityGroupName', 'EC2SecurityGroupOwnerId', ], 'members' => [ 'CacheSecurityGroupName' => [ 'shape' => 'String', ], 'EC2SecurityGroupName' => [ 'shape' => 'String', ], 'EC2SecurityGroupOwnerId' => [ 'shape' => 'String', ], ], ], 'AuthorizeCacheSecurityGroupIngressResult' => [ 'type' => 'structure', 'members' => [ 'CacheSecurityGroup' => [ 'shape' => 'CacheSecurityGroup', ], ], ], 'AutomaticFailoverStatus' => [ 'type' => 'string', 'enum' => [ 'enabled', 'disabled', 'enabling', 'disabling', ], ], 'AvailabilityZone' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], ], 'wrapper' => true, ], 'AvailabilityZonesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'AvailabilityZone', ], ], 'AwsQueryErrorMessage' => [ 'type' => 'string', ], 'BatchApplyUpdateActionMessage' => [ 'type' => 'structure', 'required' => [ 'ServiceUpdateName', ], 'members' => [ 'ReplicationGroupIds' => [ 'shape' => 'ReplicationGroupIdList', ], 'CacheClusterIds' => [ 'shape' => 'CacheClusterIdList', ], 'ServiceUpdateName' => [ 'shape' => 'String', ], ], ], 'BatchStopUpdateActionMessage' => [ 'type' => 'structure', 'required' => [ 'ServiceUpdateName', ], 'members' => [ 'ReplicationGroupIds' => [ 'shape' => 'ReplicationGroupIdList', ], 'CacheClusterIds' => [ 'shape' => 'CacheClusterIdList', ], 'ServiceUpdateName' => [ 'shape' => 'String', ], ], ], 'Boolean' => [ 'type' => 'boolean', ], 'BooleanOptional' => [ 'type' => 'boolean', ], 'CacheCluster' => [ 'type' => 'structure', 'members' => [ 'CacheClusterId' => [ 'shape' => 'String', ], 'ConfigurationEndpoint' => [ 'shape' => 'Endpoint', ], 'ClientDownloadLandingPage' => [ 'shape' => 'String', ], 'CacheNodeType' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'CacheClusterStatus' => [ 'shape' => 'String', ], 'NumCacheNodes' => [ 'shape' => 'IntegerOptional', ], 'PreferredAvailabilityZone' => [ 'shape' => 'String', ], 'PreferredOutpostArn' => [ 'shape' => 'String', ], 'CacheClusterCreateTime' => [ 'shape' => 'TStamp', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'String', ], 'PendingModifiedValues' => [ 'shape' => 'PendingModifiedValues', ], 'NotificationConfiguration' => [ 'shape' => 'NotificationConfiguration', ], 'CacheSecurityGroups' => [ 'shape' => 'CacheSecurityGroupMembershipList', ], 'CacheParameterGroup' => [ 'shape' => 'CacheParameterGroupStatus', ], 'CacheSubnetGroupName' => [ 'shape' => 'String', ], 'CacheNodes' => [ 'shape' => 'CacheNodeList', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'Boolean', ], 'SecurityGroups' => [ 'shape' => 'SecurityGroupMembershipList', ], 'ReplicationGroupId' => [ 'shape' => 'String', ], 'SnapshotRetentionLimit' => [ 'shape' => 'IntegerOptional', ], 'SnapshotWindow' => [ 'shape' => 'String', ], 'AuthTokenEnabled' => [ 'shape' => 'BooleanOptional', ], 'AuthTokenLastModifiedDate' => [ 'shape' => 'TStamp', ], 'TransitEncryptionEnabled' => [ 'shape' => 'BooleanOptional', ], 'AtRestEncryptionEnabled' => [ 'shape' => 'BooleanOptional', ], 'ARN' => [ 'shape' => 'String', ], 'ReplicationGroupLogDeliveryEnabled' => [ 'shape' => 'Boolean', ], 'LogDeliveryConfigurations' => [ 'shape' => 'LogDeliveryConfigurationList', ], ], 'wrapper' => true, ], 'CacheClusterAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'CacheClusterAlreadyExists', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'CacheClusterIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 20, ], 'CacheClusterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CacheCluster', 'locationName' => 'CacheCluster', ], ], 'CacheClusterMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'CacheClusters' => [ 'shape' => 'CacheClusterList', ], ], ], 'CacheClusterNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'CacheClusterNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'CacheEngineVersion' => [ 'type' => 'structure', 'members' => [ 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'CacheParameterGroupFamily' => [ 'shape' => 'String', ], 'CacheEngineDescription' => [ 'shape' => 'String', ], 'CacheEngineVersionDescription' => [ 'shape' => 'String', ], ], ], 'CacheEngineVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CacheEngineVersion', 'locationName' => 'CacheEngineVersion', ], ], 'CacheEngineVersionMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'CacheEngineVersions' => [ 'shape' => 'CacheEngineVersionList', ], ], ], 'CacheNode' => [ 'type' => 'structure', 'members' => [ 'CacheNodeId' => [ 'shape' => 'String', ], 'CacheNodeStatus' => [ 'shape' => 'String', ], 'CacheNodeCreateTime' => [ 'shape' => 'TStamp', ], 'Endpoint' => [ 'shape' => 'Endpoint', ], 'ParameterGroupStatus' => [ 'shape' => 'String', ], 'SourceCacheNodeId' => [ 'shape' => 'String', ], 'CustomerAvailabilityZone' => [ 'shape' => 'String', ], 'CustomerOutpostArn' => [ 'shape' => 'String', ], ], ], 'CacheNodeIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'CacheNodeId', ], ], 'CacheNodeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CacheNode', 'locationName' => 'CacheNode', ], ], 'CacheNodeTypeSpecificParameter' => [ 'type' => 'structure', 'members' => [ 'ParameterName' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'Source' => [ 'shape' => 'String', ], 'DataType' => [ 'shape' => 'String', ], 'AllowedValues' => [ 'shape' => 'String', ], 'IsModifiable' => [ 'shape' => 'Boolean', ], 'MinimumEngineVersion' => [ 'shape' => 'String', ], 'CacheNodeTypeSpecificValues' => [ 'shape' => 'CacheNodeTypeSpecificValueList', ], 'ChangeType' => [ 'shape' => 'ChangeType', ], ], ], 'CacheNodeTypeSpecificParametersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CacheNodeTypeSpecificParameter', 'locationName' => 'CacheNodeTypeSpecificParameter', ], ], 'CacheNodeTypeSpecificValue' => [ 'type' => 'structure', 'members' => [ 'CacheNodeType' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], ], ], 'CacheNodeTypeSpecificValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CacheNodeTypeSpecificValue', 'locationName' => 'CacheNodeTypeSpecificValue', ], ], 'CacheNodeUpdateStatus' => [ 'type' => 'structure', 'members' => [ 'CacheNodeId' => [ 'shape' => 'String', ], 'NodeUpdateStatus' => [ 'shape' => 'NodeUpdateStatus', ], 'NodeDeletionDate' => [ 'shape' => 'TStamp', ], 'NodeUpdateStartDate' => [ 'shape' => 'TStamp', ], 'NodeUpdateEndDate' => [ 'shape' => 'TStamp', ], 'NodeUpdateInitiatedBy' => [ 'shape' => 'NodeUpdateInitiatedBy', ], 'NodeUpdateInitiatedDate' => [ 'shape' => 'TStamp', ], 'NodeUpdateStatusModifiedDate' => [ 'shape' => 'TStamp', ], ], ], 'CacheNodeUpdateStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CacheNodeUpdateStatus', 'locationName' => 'CacheNodeUpdateStatus', ], ], 'CacheParameterGroup' => [ 'type' => 'structure', 'members' => [ 'CacheParameterGroupName' => [ 'shape' => 'String', ], 'CacheParameterGroupFamily' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'IsGlobal' => [ 'shape' => 'Boolean', ], 'ARN' => [ 'shape' => 'String', ], ], 'wrapper' => true, ], 'CacheParameterGroupAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'CacheParameterGroupAlreadyExists', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'CacheParameterGroupDetails' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'Parameters' => [ 'shape' => 'ParametersList', ], 'CacheNodeTypeSpecificParameters' => [ 'shape' => 'CacheNodeTypeSpecificParametersList', ], ], ], 'CacheParameterGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CacheParameterGroup', 'locationName' => 'CacheParameterGroup', ], ], 'CacheParameterGroupNameMessage' => [ 'type' => 'structure', 'members' => [ 'CacheParameterGroupName' => [ 'shape' => 'String', ], ], ], 'CacheParameterGroupNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'CacheParameterGroupNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'CacheParameterGroupQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'CacheParameterGroupQuotaExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'CacheParameterGroupStatus' => [ 'type' => 'structure', 'members' => [ 'CacheParameterGroupName' => [ 'shape' => 'String', ], 'ParameterApplyStatus' => [ 'shape' => 'String', ], 'CacheNodeIdsToReboot' => [ 'shape' => 'CacheNodeIdsList', ], ], ], 'CacheParameterGroupsMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'CacheParameterGroups' => [ 'shape' => 'CacheParameterGroupList', ], ], ], 'CacheSecurityGroup' => [ 'type' => 'structure', 'members' => [ 'OwnerId' => [ 'shape' => 'String', ], 'CacheSecurityGroupName' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'EC2SecurityGroups' => [ 'shape' => 'EC2SecurityGroupList', ], 'ARN' => [ 'shape' => 'String', ], ], 'wrapper' => true, ], 'CacheSecurityGroupAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'CacheSecurityGroupAlreadyExists', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'CacheSecurityGroupMembership' => [ 'type' => 'structure', 'members' => [ 'CacheSecurityGroupName' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], ], ], 'CacheSecurityGroupMembershipList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CacheSecurityGroupMembership', 'locationName' => 'CacheSecurityGroup', ], ], 'CacheSecurityGroupMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'CacheSecurityGroups' => [ 'shape' => 'CacheSecurityGroups', ], ], ], 'CacheSecurityGroupNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'CacheSecurityGroupName', ], ], 'CacheSecurityGroupNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'CacheSecurityGroupNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'CacheSecurityGroupQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'QuotaExceeded.CacheSecurityGroup', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'CacheSecurityGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'CacheSecurityGroup', 'locationName' => 'CacheSecurityGroup', ], ], 'CacheSubnetGroup' => [ 'type' => 'structure', 'members' => [ 'CacheSubnetGroupName' => [ 'shape' => 'String', ], 'CacheSubnetGroupDescription' => [ 'shape' => 'String', ], 'VpcId' => [ 'shape' => 'String', ], 'Subnets' => [ 'shape' => 'SubnetList', ], 'ARN' => [ 'shape' => 'String', ], ], 'wrapper' => true, ], 'CacheSubnetGroupAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'CacheSubnetGroupAlreadyExists', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'CacheSubnetGroupInUse' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'CacheSubnetGroupInUse', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'CacheSubnetGroupMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'CacheSubnetGroups' => [ 'shape' => 'CacheSubnetGroups', ], ], ], 'CacheSubnetGroupNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'CacheSubnetGroupNotFoundFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'CacheSubnetGroupQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'CacheSubnetGroupQuotaExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'CacheSubnetGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'CacheSubnetGroup', 'locationName' => 'CacheSubnetGroup', ], ], 'CacheSubnetQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'CacheSubnetQuotaExceededFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ChangeType' => [ 'type' => 'string', 'enum' => [ 'immediate', 'requires-reboot', ], ], 'CloudWatchLogsDestinationDetails' => [ 'type' => 'structure', 'members' => [ 'LogGroup' => [ 'shape' => 'String', ], ], ], 'ClusterIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'ClusterId', ], ], 'ClusterQuotaForCustomerExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ClusterQuotaForCustomerExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'CompleteMigrationMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationGroupId', ], 'members' => [ 'ReplicationGroupId' => [ 'shape' => 'String', ], 'Force' => [ 'shape' => 'Boolean', ], ], ], 'CompleteMigrationResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationGroup' => [ 'shape' => 'ReplicationGroup', ], ], ], 'ConfigureShard' => [ 'type' => 'structure', 'required' => [ 'NodeGroupId', 'NewReplicaCount', ], 'members' => [ 'NodeGroupId' => [ 'shape' => 'AllowedNodeGroupId', ], 'NewReplicaCount' => [ 'shape' => 'Integer', ], 'PreferredAvailabilityZones' => [ 'shape' => 'PreferredAvailabilityZoneList', ], 'PreferredOutpostArns' => [ 'shape' => 'PreferredOutpostArnList', ], ], ], 'CopySnapshotMessage' => [ 'type' => 'structure', 'required' => [ 'SourceSnapshotName', 'TargetSnapshotName', ], 'members' => [ 'SourceSnapshotName' => [ 'shape' => 'String', ], 'TargetSnapshotName' => [ 'shape' => 'String', ], 'TargetBucket' => [ 'shape' => 'String', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CopySnapshotResult' => [ 'type' => 'structure', 'members' => [ 'Snapshot' => [ 'shape' => 'Snapshot', ], ], ], 'CreateCacheClusterMessage' => [ 'type' => 'structure', 'required' => [ 'CacheClusterId', ], 'members' => [ 'CacheClusterId' => [ 'shape' => 'String', ], 'ReplicationGroupId' => [ 'shape' => 'String', ], 'AZMode' => [ 'shape' => 'AZMode', ], 'PreferredAvailabilityZone' => [ 'shape' => 'String', ], 'PreferredAvailabilityZones' => [ 'shape' => 'PreferredAvailabilityZoneList', ], 'NumCacheNodes' => [ 'shape' => 'IntegerOptional', ], 'CacheNodeType' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'CacheParameterGroupName' => [ 'shape' => 'String', ], 'CacheSubnetGroupName' => [ 'shape' => 'String', ], 'CacheSecurityGroupNames' => [ 'shape' => 'CacheSecurityGroupNameList', ], 'SecurityGroupIds' => [ 'shape' => 'SecurityGroupIdsList', ], 'Tags' => [ 'shape' => 'TagList', ], 'SnapshotArns' => [ 'shape' => 'SnapshotArnsList', ], 'SnapshotName' => [ 'shape' => 'String', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'NotificationTopicArn' => [ 'shape' => 'String', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'BooleanOptional', ], 'SnapshotRetentionLimit' => [ 'shape' => 'IntegerOptional', ], 'SnapshotWindow' => [ 'shape' => 'String', ], 'AuthToken' => [ 'shape' => 'String', ], 'OutpostMode' => [ 'shape' => 'OutpostMode', ], 'PreferredOutpostArn' => [ 'shape' => 'String', ], 'PreferredOutpostArns' => [ 'shape' => 'PreferredOutpostArnList', ], 'LogDeliveryConfigurations' => [ 'shape' => 'LogDeliveryConfigurationRequestList', ], ], ], 'CreateCacheClusterResult' => [ 'type' => 'structure', 'members' => [ 'CacheCluster' => [ 'shape' => 'CacheCluster', ], ], ], 'CreateCacheParameterGroupMessage' => [ 'type' => 'structure', 'required' => [ 'CacheParameterGroupName', 'CacheParameterGroupFamily', 'Description', ], 'members' => [ 'CacheParameterGroupName' => [ 'shape' => 'String', ], 'CacheParameterGroupFamily' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateCacheParameterGroupResult' => [ 'type' => 'structure', 'members' => [ 'CacheParameterGroup' => [ 'shape' => 'CacheParameterGroup', ], ], ], 'CreateCacheSecurityGroupMessage' => [ 'type' => 'structure', 'required' => [ 'CacheSecurityGroupName', 'Description', ], 'members' => [ 'CacheSecurityGroupName' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateCacheSecurityGroupResult' => [ 'type' => 'structure', 'members' => [ 'CacheSecurityGroup' => [ 'shape' => 'CacheSecurityGroup', ], ], ], 'CreateCacheSubnetGroupMessage' => [ 'type' => 'structure', 'required' => [ 'CacheSubnetGroupName', 'CacheSubnetGroupDescription', 'SubnetIds', ], 'members' => [ 'CacheSubnetGroupName' => [ 'shape' => 'String', ], 'CacheSubnetGroupDescription' => [ 'shape' => 'String', ], 'SubnetIds' => [ 'shape' => 'SubnetIdentifierList', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateCacheSubnetGroupResult' => [ 'type' => 'structure', 'members' => [ 'CacheSubnetGroup' => [ 'shape' => 'CacheSubnetGroup', ], ], ], 'CreateGlobalReplicationGroupMessage' => [ 'type' => 'structure', 'required' => [ 'GlobalReplicationGroupIdSuffix', 'PrimaryReplicationGroupId', ], 'members' => [ 'GlobalReplicationGroupIdSuffix' => [ 'shape' => 'String', ], 'GlobalReplicationGroupDescription' => [ 'shape' => 'String', ], 'PrimaryReplicationGroupId' => [ 'shape' => 'String', ], ], ], 'CreateGlobalReplicationGroupResult' => [ 'type' => 'structure', 'members' => [ 'GlobalReplicationGroup' => [ 'shape' => 'GlobalReplicationGroup', ], ], ], 'CreateReplicationGroupMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationGroupId', 'ReplicationGroupDescription', ], 'members' => [ 'ReplicationGroupId' => [ 'shape' => 'String', ], 'ReplicationGroupDescription' => [ 'shape' => 'String', ], 'GlobalReplicationGroupId' => [ 'shape' => 'String', ], 'PrimaryClusterId' => [ 'shape' => 'String', ], 'AutomaticFailoverEnabled' => [ 'shape' => 'BooleanOptional', ], 'MultiAZEnabled' => [ 'shape' => 'BooleanOptional', ], 'NumCacheClusters' => [ 'shape' => 'IntegerOptional', ], 'PreferredCacheClusterAZs' => [ 'shape' => 'AvailabilityZonesList', ], 'NumNodeGroups' => [ 'shape' => 'IntegerOptional', ], 'ReplicasPerNodeGroup' => [ 'shape' => 'IntegerOptional', ], 'NodeGroupConfiguration' => [ 'shape' => 'NodeGroupConfigurationList', ], 'CacheNodeType' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'CacheParameterGroupName' => [ 'shape' => 'String', ], 'CacheSubnetGroupName' => [ 'shape' => 'String', ], 'CacheSecurityGroupNames' => [ 'shape' => 'CacheSecurityGroupNameList', ], 'SecurityGroupIds' => [ 'shape' => 'SecurityGroupIdsList', ], 'Tags' => [ 'shape' => 'TagList', ], 'SnapshotArns' => [ 'shape' => 'SnapshotArnsList', ], 'SnapshotName' => [ 'shape' => 'String', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'NotificationTopicArn' => [ 'shape' => 'String', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'BooleanOptional', ], 'SnapshotRetentionLimit' => [ 'shape' => 'IntegerOptional', ], 'SnapshotWindow' => [ 'shape' => 'String', ], 'AuthToken' => [ 'shape' => 'String', ], 'TransitEncryptionEnabled' => [ 'shape' => 'BooleanOptional', ], 'AtRestEncryptionEnabled' => [ 'shape' => 'BooleanOptional', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'UserGroupIds' => [ 'shape' => 'UserGroupIdListInput', ], 'LogDeliveryConfigurations' => [ 'shape' => 'LogDeliveryConfigurationRequestList', ], 'DataTieringEnabled' => [ 'shape' => 'BooleanOptional', ], ], ], 'CreateReplicationGroupResult' => [ 'type' => 'structure', 'members' => [ 'ReplicationGroup' => [ 'shape' => 'ReplicationGroup', ], ], ], 'CreateSnapshotMessage' => [ 'type' => 'structure', 'required' => [ 'SnapshotName', ], 'members' => [ 'ReplicationGroupId' => [ 'shape' => 'String', ], 'CacheClusterId' => [ 'shape' => 'String', ], 'SnapshotName' => [ 'shape' => 'String', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'Snapshot' => [ 'shape' => 'Snapshot', ], ], ], 'CreateUserGroupMessage' => [ 'type' => 'structure', 'required' => [ 'UserGroupId', 'Engine', ], 'members' => [ 'UserGroupId' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'EngineType', ], 'UserIds' => [ 'shape' => 'UserIdListInput', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateUserMessage' => [ 'type' => 'structure', 'required' => [ 'UserId', 'UserName', 'Engine', 'AccessString', ], 'members' => [ 'UserId' => [ 'shape' => 'UserId', ], 'UserName' => [ 'shape' => 'UserName', ], 'Engine' => [ 'shape' => 'EngineType', ], 'Passwords' => [ 'shape' => 'PasswordListInput', ], 'AccessString' => [ 'shape' => 'AccessString', ], 'NoPasswordRequired' => [ 'shape' => 'BooleanOptional', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CustomerNodeEndpoint' => [ 'type' => 'structure', 'members' => [ 'Address' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], ], ], 'CustomerNodeEndpointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomerNodeEndpoint', ], ], 'DataTieringStatus' => [ 'type' => 'string', 'enum' => [ 'enabled', 'disabled', ], ], 'DecreaseNodeGroupsInGlobalReplicationGroupMessage' => [ 'type' => 'structure', 'required' => [ 'GlobalReplicationGroupId', 'NodeGroupCount', 'ApplyImmediately', ], 'members' => [ 'GlobalReplicationGroupId' => [ 'shape' => 'String', ], 'NodeGroupCount' => [ 'shape' => 'Integer', ], 'GlobalNodeGroupsToRemove' => [ 'shape' => 'GlobalNodeGroupIdList', ], 'GlobalNodeGroupsToRetain' => [ 'shape' => 'GlobalNodeGroupIdList', ], 'ApplyImmediately' => [ 'shape' => 'Boolean', ], ], ], 'DecreaseNodeGroupsInGlobalReplicationGroupResult' => [ 'type' => 'structure', 'members' => [ 'GlobalReplicationGroup' => [ 'shape' => 'GlobalReplicationGroup', ], ], ], 'DecreaseReplicaCountMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationGroupId', 'ApplyImmediately', ], 'members' => [ 'ReplicationGroupId' => [ 'shape' => 'String', ], 'NewReplicaCount' => [ 'shape' => 'IntegerOptional', ], 'ReplicaConfiguration' => [ 'shape' => 'ReplicaConfigurationList', ], 'ReplicasToRemove' => [ 'shape' => 'RemoveReplicasList', ], 'ApplyImmediately' => [ 'shape' => 'Boolean', ], ], ], 'DecreaseReplicaCountResult' => [ 'type' => 'structure', 'members' => [ 'ReplicationGroup' => [ 'shape' => 'ReplicationGroup', ], ], ], 'DefaultUserAssociatedToUserGroupFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DefaultUserAssociatedToUserGroup', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DefaultUserRequired' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DefaultUserRequired', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DeleteCacheClusterMessage' => [ 'type' => 'structure', 'required' => [ 'CacheClusterId', ], 'members' => [ 'CacheClusterId' => [ 'shape' => 'String', ], 'FinalSnapshotIdentifier' => [ 'shape' => 'String', ], ], ], 'DeleteCacheClusterResult' => [ 'type' => 'structure', 'members' => [ 'CacheCluster' => [ 'shape' => 'CacheCluster', ], ], ], 'DeleteCacheParameterGroupMessage' => [ 'type' => 'structure', 'required' => [ 'CacheParameterGroupName', ], 'members' => [ 'CacheParameterGroupName' => [ 'shape' => 'String', ], ], ], 'DeleteCacheSecurityGroupMessage' => [ 'type' => 'structure', 'required' => [ 'CacheSecurityGroupName', ], 'members' => [ 'CacheSecurityGroupName' => [ 'shape' => 'String', ], ], ], 'DeleteCacheSubnetGroupMessage' => [ 'type' => 'structure', 'required' => [ 'CacheSubnetGroupName', ], 'members' => [ 'CacheSubnetGroupName' => [ 'shape' => 'String', ], ], ], 'DeleteGlobalReplicationGroupMessage' => [ 'type' => 'structure', 'required' => [ 'GlobalReplicationGroupId', 'RetainPrimaryReplicationGroup', ], 'members' => [ 'GlobalReplicationGroupId' => [ 'shape' => 'String', ], 'RetainPrimaryReplicationGroup' => [ 'shape' => 'Boolean', ], ], ], 'DeleteGlobalReplicationGroupResult' => [ 'type' => 'structure', 'members' => [ 'GlobalReplicationGroup' => [ 'shape' => 'GlobalReplicationGroup', ], ], ], 'DeleteReplicationGroupMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationGroupId', ], 'members' => [ 'ReplicationGroupId' => [ 'shape' => 'String', ], 'RetainPrimaryCluster' => [ 'shape' => 'BooleanOptional', ], 'FinalSnapshotIdentifier' => [ 'shape' => 'String', ], ], ], 'DeleteReplicationGroupResult' => [ 'type' => 'structure', 'members' => [ 'ReplicationGroup' => [ 'shape' => 'ReplicationGroup', ], ], ], 'DeleteSnapshotMessage' => [ 'type' => 'structure', 'required' => [ 'SnapshotName', ], 'members' => [ 'SnapshotName' => [ 'shape' => 'String', ], ], ], 'DeleteSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'Snapshot' => [ 'shape' => 'Snapshot', ], ], ], 'DeleteUserGroupMessage' => [ 'type' => 'structure', 'required' => [ 'UserGroupId', ], 'members' => [ 'UserGroupId' => [ 'shape' => 'String', ], ], ], 'DeleteUserMessage' => [ 'type' => 'structure', 'required' => [ 'UserId', ], 'members' => [ 'UserId' => [ 'shape' => 'UserId', ], ], ], 'DescribeCacheClustersMessage' => [ 'type' => 'structure', 'members' => [ 'CacheClusterId' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], 'ShowCacheNodeInfo' => [ 'shape' => 'BooleanOptional', ], 'ShowCacheClustersNotInReplicationGroups' => [ 'shape' => 'BooleanOptional', ], ], ], 'DescribeCacheEngineVersionsMessage' => [ 'type' => 'structure', 'members' => [ 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'CacheParameterGroupFamily' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], 'DefaultOnly' => [ 'shape' => 'Boolean', ], ], ], 'DescribeCacheParameterGroupsMessage' => [ 'type' => 'structure', 'members' => [ 'CacheParameterGroupName' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeCacheParametersMessage' => [ 'type' => 'structure', 'required' => [ 'CacheParameterGroupName', ], 'members' => [ 'CacheParameterGroupName' => [ 'shape' => 'String', ], 'Source' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeCacheSecurityGroupsMessage' => [ 'type' => 'structure', 'members' => [ 'CacheSecurityGroupName' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeCacheSubnetGroupsMessage' => [ 'type' => 'structure', 'members' => [ 'CacheSubnetGroupName' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeEngineDefaultParametersMessage' => [ 'type' => 'structure', 'required' => [ 'CacheParameterGroupFamily', ], 'members' => [ 'CacheParameterGroupFamily' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeEngineDefaultParametersResult' => [ 'type' => 'structure', 'members' => [ 'EngineDefaults' => [ 'shape' => 'EngineDefaults', ], ], ], 'DescribeEventsMessage' => [ 'type' => 'structure', 'members' => [ 'SourceIdentifier' => [ 'shape' => 'String', ], 'SourceType' => [ 'shape' => 'SourceType', ], 'StartTime' => [ 'shape' => 'TStamp', ], 'EndTime' => [ 'shape' => 'TStamp', ], 'Duration' => [ 'shape' => 'IntegerOptional', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeGlobalReplicationGroupsMessage' => [ 'type' => 'structure', 'members' => [ 'GlobalReplicationGroupId' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], 'ShowMemberInfo' => [ 'shape' => 'BooleanOptional', ], ], ], 'DescribeGlobalReplicationGroupsResult' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'GlobalReplicationGroups' => [ 'shape' => 'GlobalReplicationGroupList', ], ], ], 'DescribeReplicationGroupsMessage' => [ 'type' => 'structure', 'members' => [ 'ReplicationGroupId' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeReservedCacheNodesMessage' => [ 'type' => 'structure', 'members' => [ 'ReservedCacheNodeId' => [ 'shape' => 'String', ], 'ReservedCacheNodesOfferingId' => [ 'shape' => 'String', ], 'CacheNodeType' => [ 'shape' => 'String', ], 'Duration' => [ 'shape' => 'String', ], 'ProductDescription' => [ 'shape' => 'String', ], 'OfferingType' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeReservedCacheNodesOfferingsMessage' => [ 'type' => 'structure', 'members' => [ 'ReservedCacheNodesOfferingId' => [ 'shape' => 'String', ], 'CacheNodeType' => [ 'shape' => 'String', ], 'Duration' => [ 'shape' => 'String', ], 'ProductDescription' => [ 'shape' => 'String', ], 'OfferingType' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeServiceUpdatesMessage' => [ 'type' => 'structure', 'members' => [ 'ServiceUpdateName' => [ 'shape' => 'String', ], 'ServiceUpdateStatus' => [ 'shape' => 'ServiceUpdateStatusList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeSnapshotsListMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'Snapshots' => [ 'shape' => 'SnapshotList', ], ], ], 'DescribeSnapshotsMessage' => [ 'type' => 'structure', 'members' => [ 'ReplicationGroupId' => [ 'shape' => 'String', ], 'CacheClusterId' => [ 'shape' => 'String', ], 'SnapshotName' => [ 'shape' => 'String', ], 'SnapshotSource' => [ 'shape' => 'String', ], 'Marker' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'ShowNodeGroupConfig' => [ 'shape' => 'BooleanOptional', ], ], ], 'DescribeUpdateActionsMessage' => [ 'type' => 'structure', 'members' => [ 'ServiceUpdateName' => [ 'shape' => 'String', ], 'ReplicationGroupIds' => [ 'shape' => 'ReplicationGroupIdList', ], 'CacheClusterIds' => [ 'shape' => 'CacheClusterIdList', ], 'Engine' => [ 'shape' => 'String', ], 'ServiceUpdateStatus' => [ 'shape' => 'ServiceUpdateStatusList', ], 'ServiceUpdateTimeRange' => [ 'shape' => 'TimeRangeFilter', ], 'UpdateActionStatus' => [ 'shape' => 'UpdateActionStatusList', ], 'ShowNodeLevelUpdateStatus' => [ 'shape' => 'BooleanOptional', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeUserGroupsMessage' => [ 'type' => 'structure', 'members' => [ 'UserGroupId' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeUserGroupsResult' => [ 'type' => 'structure', 'members' => [ 'UserGroups' => [ 'shape' => 'UserGroupList', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeUsersMessage' => [ 'type' => 'structure', 'members' => [ 'Engine' => [ 'shape' => 'EngineType', ], 'UserId' => [ 'shape' => 'UserId', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeUsersResult' => [ 'type' => 'structure', 'members' => [ 'Users' => [ 'shape' => 'UserList', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DestinationDetails' => [ 'type' => 'structure', 'members' => [ 'CloudWatchLogsDetails' => [ 'shape' => 'CloudWatchLogsDestinationDetails', ], 'KinesisFirehoseDetails' => [ 'shape' => 'KinesisFirehoseDestinationDetails', ], ], ], 'DestinationType' => [ 'type' => 'string', 'enum' => [ 'cloudwatch-logs', 'kinesis-firehose', ], ], 'DisassociateGlobalReplicationGroupMessage' => [ 'type' => 'structure', 'required' => [ 'GlobalReplicationGroupId', 'ReplicationGroupId', 'ReplicationGroupRegion', ], 'members' => [ 'GlobalReplicationGroupId' => [ 'shape' => 'String', ], 'ReplicationGroupId' => [ 'shape' => 'String', ], 'ReplicationGroupRegion' => [ 'shape' => 'String', ], ], ], 'DisassociateGlobalReplicationGroupResult' => [ 'type' => 'structure', 'members' => [ 'GlobalReplicationGroup' => [ 'shape' => 'GlobalReplicationGroup', ], ], ], 'Double' => [ 'type' => 'double', ], 'DuplicateUserNameFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DuplicateUserName', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'EC2SecurityGroup' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'String', ], 'EC2SecurityGroupName' => [ 'shape' => 'String', ], 'EC2SecurityGroupOwnerId' => [ 'shape' => 'String', ], ], ], 'EC2SecurityGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EC2SecurityGroup', 'locationName' => 'EC2SecurityGroup', ], ], 'Endpoint' => [ 'type' => 'structure', 'members' => [ 'Address' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'Integer', ], ], ], 'EngineDefaults' => [ 'type' => 'structure', 'members' => [ 'CacheParameterGroupFamily' => [ 'shape' => 'String', ], 'Marker' => [ 'shape' => 'String', ], 'Parameters' => [ 'shape' => 'ParametersList', ], 'CacheNodeTypeSpecificParameters' => [ 'shape' => 'CacheNodeTypeSpecificParametersList', ], ], 'wrapper' => true, ], 'EngineType' => [ 'type' => 'string', 'pattern' => '[a-zA-Z]*', ], 'Event' => [ 'type' => 'structure', 'members' => [ 'SourceIdentifier' => [ 'shape' => 'String', ], 'SourceType' => [ 'shape' => 'SourceType', ], 'Message' => [ 'shape' => 'String', ], 'Date' => [ 'shape' => 'TStamp', ], ], ], 'EventList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Event', 'locationName' => 'Event', ], ], 'EventsMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'Events' => [ 'shape' => 'EventList', ], ], ], 'FailoverGlobalReplicationGroupMessage' => [ 'type' => 'structure', 'required' => [ 'GlobalReplicationGroupId', 'PrimaryRegion', 'PrimaryReplicationGroupId', ], 'members' => [ 'GlobalReplicationGroupId' => [ 'shape' => 'String', ], 'PrimaryRegion' => [ 'shape' => 'String', ], 'PrimaryReplicationGroupId' => [ 'shape' => 'String', ], ], ], 'FailoverGlobalReplicationGroupResult' => [ 'type' => 'structure', 'members' => [ 'GlobalReplicationGroup' => [ 'shape' => 'GlobalReplicationGroup', ], ], ], 'Filter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'FilterName', ], 'Values' => [ 'shape' => 'FilterValueList', ], ], ], 'FilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], ], 'FilterName' => [ 'type' => 'string', 'pattern' => '.*\\S.*', ], 'FilterValue' => [ 'type' => 'string', 'pattern' => '.*\\S.*', ], 'FilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterValue', ], 'min' => 1, ], 'GlobalNodeGroup' => [ 'type' => 'structure', 'members' => [ 'GlobalNodeGroupId' => [ 'shape' => 'String', ], 'Slots' => [ 'shape' => 'String', ], ], ], 'GlobalNodeGroupIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'GlobalNodeGroupId', ], ], 'GlobalNodeGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GlobalNodeGroup', 'locationName' => 'GlobalNodeGroup', ], ], 'GlobalReplicationGroup' => [ 'type' => 'structure', 'members' => [ 'GlobalReplicationGroupId' => [ 'shape' => 'String', ], 'GlobalReplicationGroupDescription' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'CacheNodeType' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'Members' => [ 'shape' => 'GlobalReplicationGroupMemberList', ], 'ClusterEnabled' => [ 'shape' => 'BooleanOptional', ], 'GlobalNodeGroups' => [ 'shape' => 'GlobalNodeGroupList', ], 'AuthTokenEnabled' => [ 'shape' => 'BooleanOptional', ], 'TransitEncryptionEnabled' => [ 'shape' => 'BooleanOptional', ], 'AtRestEncryptionEnabled' => [ 'shape' => 'BooleanOptional', ], 'ARN' => [ 'shape' => 'String', ], ], 'wrapper' => true, ], 'GlobalReplicationGroupAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'GlobalReplicationGroupAlreadyExistsFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'GlobalReplicationGroupInfo' => [ 'type' => 'structure', 'members' => [ 'GlobalReplicationGroupId' => [ 'shape' => 'String', ], 'GlobalReplicationGroupMemberRole' => [ 'shape' => 'String', ], ], ], 'GlobalReplicationGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GlobalReplicationGroup', 'locationName' => 'GlobalReplicationGroup', ], ], 'GlobalReplicationGroupMember' => [ 'type' => 'structure', 'members' => [ 'ReplicationGroupId' => [ 'shape' => 'String', ], 'ReplicationGroupRegion' => [ 'shape' => 'String', ], 'Role' => [ 'shape' => 'String', ], 'AutomaticFailover' => [ 'shape' => 'AutomaticFailoverStatus', ], 'Status' => [ 'shape' => 'String', ], ], 'wrapper' => true, ], 'GlobalReplicationGroupMemberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GlobalReplicationGroupMember', 'locationName' => 'GlobalReplicationGroupMember', ], ], 'GlobalReplicationGroupNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'GlobalReplicationGroupNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'IncreaseNodeGroupsInGlobalReplicationGroupMessage' => [ 'type' => 'structure', 'required' => [ 'GlobalReplicationGroupId', 'NodeGroupCount', 'ApplyImmediately', ], 'members' => [ 'GlobalReplicationGroupId' => [ 'shape' => 'String', ], 'NodeGroupCount' => [ 'shape' => 'Integer', ], 'RegionalConfigurations' => [ 'shape' => 'RegionalConfigurationList', ], 'ApplyImmediately' => [ 'shape' => 'Boolean', ], ], ], 'IncreaseNodeGroupsInGlobalReplicationGroupResult' => [ 'type' => 'structure', 'members' => [ 'GlobalReplicationGroup' => [ 'shape' => 'GlobalReplicationGroup', ], ], ], 'IncreaseReplicaCountMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationGroupId', 'ApplyImmediately', ], 'members' => [ 'ReplicationGroupId' => [ 'shape' => 'String', ], 'NewReplicaCount' => [ 'shape' => 'IntegerOptional', ], 'ReplicaConfiguration' => [ 'shape' => 'ReplicaConfigurationList', ], 'ApplyImmediately' => [ 'shape' => 'Boolean', ], ], ], 'IncreaseReplicaCountResult' => [ 'type' => 'structure', 'members' => [ 'ReplicationGroup' => [ 'shape' => 'ReplicationGroup', ], ], ], 'InsufficientCacheClusterCapacityFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InsufficientCacheClusterCapacity', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Integer' => [ 'type' => 'integer', ], 'IntegerOptional' => [ 'type' => 'integer', ], 'InvalidARNFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidARN', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidCacheClusterStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidCacheClusterState', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidCacheParameterGroupStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidCacheParameterGroupState', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidCacheSecurityGroupStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidCacheSecurityGroupState', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidGlobalReplicationGroupStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidGlobalReplicationGroupState', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidKMSKeyFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidKMSKeyFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidParameterCombinationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'AwsQueryErrorMessage', ], ], 'error' => [ 'code' => 'InvalidParameterCombination', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, 'synthetic' => true, ], 'InvalidParameterValueException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'AwsQueryErrorMessage', ], ], 'error' => [ 'code' => 'InvalidParameterValue', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, 'synthetic' => true, ], 'InvalidReplicationGroupStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidReplicationGroupState', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidSnapshotStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidSnapshotState', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidSubnet' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidSubnet', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidUserGroupStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidUserGroupState', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidUserStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidUserState', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidVPCNetworkStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidVPCNetworkStateFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'KeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'KinesisFirehoseDestinationDetails' => [ 'type' => 'structure', 'members' => [ 'DeliveryStream' => [ 'shape' => 'String', ], ], ], 'ListAllowedNodeTypeModificationsMessage' => [ 'type' => 'structure', 'members' => [ 'CacheClusterId' => [ 'shape' => 'String', ], 'ReplicationGroupId' => [ 'shape' => 'String', ], ], ], 'ListTagsForResourceMessage' => [ 'type' => 'structure', 'required' => [ 'ResourceName', ], 'members' => [ 'ResourceName' => [ 'shape' => 'String', ], ], ], 'LogDeliveryConfiguration' => [ 'type' => 'structure', 'members' => [ 'LogType' => [ 'shape' => 'LogType', ], 'DestinationType' => [ 'shape' => 'DestinationType', ], 'DestinationDetails' => [ 'shape' => 'DestinationDetails', ], 'LogFormat' => [ 'shape' => 'LogFormat', ], 'Status' => [ 'shape' => 'LogDeliveryConfigurationStatus', ], 'Message' => [ 'shape' => 'String', ], ], ], 'LogDeliveryConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogDeliveryConfiguration', 'locationName' => 'LogDeliveryConfiguration', ], ], 'LogDeliveryConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'LogType' => [ 'shape' => 'LogType', ], 'DestinationType' => [ 'shape' => 'DestinationType', ], 'DestinationDetails' => [ 'shape' => 'DestinationDetails', ], 'LogFormat' => [ 'shape' => 'LogFormat', ], 'Enabled' => [ 'shape' => 'BooleanOptional', ], ], ], 'LogDeliveryConfigurationRequestList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogDeliveryConfigurationRequest', 'locationName' => 'LogDeliveryConfigurationRequest', ], ], 'LogDeliveryConfigurationStatus' => [ 'type' => 'string', 'enum' => [ 'active', 'enabling', 'modifying', 'disabling', 'error', ], ], 'LogFormat' => [ 'type' => 'string', 'enum' => [ 'text', 'json', ], ], 'LogType' => [ 'type' => 'string', 'enum' => [ 'slow-log', ], ], 'ModifyCacheClusterMessage' => [ 'type' => 'structure', 'required' => [ 'CacheClusterId', ], 'members' => [ 'CacheClusterId' => [ 'shape' => 'String', ], 'NumCacheNodes' => [ 'shape' => 'IntegerOptional', ], 'CacheNodeIdsToRemove' => [ 'shape' => 'CacheNodeIdsList', ], 'AZMode' => [ 'shape' => 'AZMode', ], 'NewAvailabilityZones' => [ 'shape' => 'PreferredAvailabilityZoneList', ], 'CacheSecurityGroupNames' => [ 'shape' => 'CacheSecurityGroupNameList', ], 'SecurityGroupIds' => [ 'shape' => 'SecurityGroupIdsList', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'String', ], 'NotificationTopicArn' => [ 'shape' => 'String', ], 'CacheParameterGroupName' => [ 'shape' => 'String', ], 'NotificationTopicStatus' => [ 'shape' => 'String', ], 'ApplyImmediately' => [ 'shape' => 'Boolean', ], 'EngineVersion' => [ 'shape' => 'String', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'BooleanOptional', ], 'SnapshotRetentionLimit' => [ 'shape' => 'IntegerOptional', ], 'SnapshotWindow' => [ 'shape' => 'String', ], 'CacheNodeType' => [ 'shape' => 'String', ], 'AuthToken' => [ 'shape' => 'String', ], 'AuthTokenUpdateStrategy' => [ 'shape' => 'AuthTokenUpdateStrategyType', ], 'LogDeliveryConfigurations' => [ 'shape' => 'LogDeliveryConfigurationRequestList', ], ], ], 'ModifyCacheClusterResult' => [ 'type' => 'structure', 'members' => [ 'CacheCluster' => [ 'shape' => 'CacheCluster', ], ], ], 'ModifyCacheParameterGroupMessage' => [ 'type' => 'structure', 'required' => [ 'CacheParameterGroupName', 'ParameterNameValues', ], 'members' => [ 'CacheParameterGroupName' => [ 'shape' => 'String', ], 'ParameterNameValues' => [ 'shape' => 'ParameterNameValueList', ], ], ], 'ModifyCacheSubnetGroupMessage' => [ 'type' => 'structure', 'required' => [ 'CacheSubnetGroupName', ], 'members' => [ 'CacheSubnetGroupName' => [ 'shape' => 'String', ], 'CacheSubnetGroupDescription' => [ 'shape' => 'String', ], 'SubnetIds' => [ 'shape' => 'SubnetIdentifierList', ], ], ], 'ModifyCacheSubnetGroupResult' => [ 'type' => 'structure', 'members' => [ 'CacheSubnetGroup' => [ 'shape' => 'CacheSubnetGroup', ], ], ], 'ModifyGlobalReplicationGroupMessage' => [ 'type' => 'structure', 'required' => [ 'GlobalReplicationGroupId', 'ApplyImmediately', ], 'members' => [ 'GlobalReplicationGroupId' => [ 'shape' => 'String', ], 'ApplyImmediately' => [ 'shape' => 'Boolean', ], 'CacheNodeType' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'CacheParameterGroupName' => [ 'shape' => 'String', ], 'GlobalReplicationGroupDescription' => [ 'shape' => 'String', ], 'AutomaticFailoverEnabled' => [ 'shape' => 'BooleanOptional', ], ], ], 'ModifyGlobalReplicationGroupResult' => [ 'type' => 'structure', 'members' => [ 'GlobalReplicationGroup' => [ 'shape' => 'GlobalReplicationGroup', ], ], ], 'ModifyReplicationGroupMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationGroupId', ], 'members' => [ 'ReplicationGroupId' => [ 'shape' => 'String', ], 'ReplicationGroupDescription' => [ 'shape' => 'String', ], 'PrimaryClusterId' => [ 'shape' => 'String', ], 'SnapshottingClusterId' => [ 'shape' => 'String', ], 'AutomaticFailoverEnabled' => [ 'shape' => 'BooleanOptional', ], 'MultiAZEnabled' => [ 'shape' => 'BooleanOptional', ], 'NodeGroupId' => [ 'shape' => 'String', 'deprecated' => true, ], 'CacheSecurityGroupNames' => [ 'shape' => 'CacheSecurityGroupNameList', ], 'SecurityGroupIds' => [ 'shape' => 'SecurityGroupIdsList', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'String', ], 'NotificationTopicArn' => [ 'shape' => 'String', ], 'CacheParameterGroupName' => [ 'shape' => 'String', ], 'NotificationTopicStatus' => [ 'shape' => 'String', ], 'ApplyImmediately' => [ 'shape' => 'Boolean', ], 'EngineVersion' => [ 'shape' => 'String', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'BooleanOptional', ], 'SnapshotRetentionLimit' => [ 'shape' => 'IntegerOptional', ], 'SnapshotWindow' => [ 'shape' => 'String', ], 'CacheNodeType' => [ 'shape' => 'String', ], 'AuthToken' => [ 'shape' => 'String', ], 'AuthTokenUpdateStrategy' => [ 'shape' => 'AuthTokenUpdateStrategyType', ], 'UserGroupIdsToAdd' => [ 'shape' => 'UserGroupIdList', ], 'UserGroupIdsToRemove' => [ 'shape' => 'UserGroupIdList', ], 'RemoveUserGroups' => [ 'shape' => 'BooleanOptional', ], 'LogDeliveryConfigurations' => [ 'shape' => 'LogDeliveryConfigurationRequestList', ], ], ], 'ModifyReplicationGroupResult' => [ 'type' => 'structure', 'members' => [ 'ReplicationGroup' => [ 'shape' => 'ReplicationGroup', ], ], ], 'ModifyReplicationGroupShardConfigurationMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationGroupId', 'NodeGroupCount', 'ApplyImmediately', ], 'members' => [ 'ReplicationGroupId' => [ 'shape' => 'String', ], 'NodeGroupCount' => [ 'shape' => 'Integer', ], 'ApplyImmediately' => [ 'shape' => 'Boolean', ], 'ReshardingConfiguration' => [ 'shape' => 'ReshardingConfigurationList', ], 'NodeGroupsToRemove' => [ 'shape' => 'NodeGroupsToRemoveList', ], 'NodeGroupsToRetain' => [ 'shape' => 'NodeGroupsToRetainList', ], ], ], 'ModifyReplicationGroupShardConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'ReplicationGroup' => [ 'shape' => 'ReplicationGroup', ], ], ], 'ModifyUserGroupMessage' => [ 'type' => 'structure', 'required' => [ 'UserGroupId', ], 'members' => [ 'UserGroupId' => [ 'shape' => 'String', ], 'UserIdsToAdd' => [ 'shape' => 'UserIdListInput', ], 'UserIdsToRemove' => [ 'shape' => 'UserIdListInput', ], ], ], 'ModifyUserMessage' => [ 'type' => 'structure', 'required' => [ 'UserId', ], 'members' => [ 'UserId' => [ 'shape' => 'UserId', ], 'AccessString' => [ 'shape' => 'AccessString', ], 'AppendAccessString' => [ 'shape' => 'AccessString', ], 'Passwords' => [ 'shape' => 'PasswordListInput', ], 'NoPasswordRequired' => [ 'shape' => 'BooleanOptional', ], ], ], 'MultiAZStatus' => [ 'type' => 'string', 'enum' => [ 'enabled', 'disabled', ], ], 'NoOperationFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'NoOperationFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'NodeGroup' => [ 'type' => 'structure', 'members' => [ 'NodeGroupId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'PrimaryEndpoint' => [ 'shape' => 'Endpoint', ], 'ReaderEndpoint' => [ 'shape' => 'Endpoint', ], 'Slots' => [ 'shape' => 'String', ], 'NodeGroupMembers' => [ 'shape' => 'NodeGroupMemberList', ], ], ], 'NodeGroupConfiguration' => [ 'type' => 'structure', 'members' => [ 'NodeGroupId' => [ 'shape' => 'AllowedNodeGroupId', ], 'Slots' => [ 'shape' => 'String', ], 'ReplicaCount' => [ 'shape' => 'IntegerOptional', ], 'PrimaryAvailabilityZone' => [ 'shape' => 'String', ], 'ReplicaAvailabilityZones' => [ 'shape' => 'AvailabilityZonesList', ], 'PrimaryOutpostArn' => [ 'shape' => 'String', ], 'ReplicaOutpostArns' => [ 'shape' => 'OutpostArnsList', ], ], ], 'NodeGroupConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NodeGroupConfiguration', 'locationName' => 'NodeGroupConfiguration', ], ], 'NodeGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NodeGroup', 'locationName' => 'NodeGroup', ], ], 'NodeGroupMember' => [ 'type' => 'structure', 'members' => [ 'CacheClusterId' => [ 'shape' => 'String', ], 'CacheNodeId' => [ 'shape' => 'String', ], 'ReadEndpoint' => [ 'shape' => 'Endpoint', ], 'PreferredAvailabilityZone' => [ 'shape' => 'String', ], 'PreferredOutpostArn' => [ 'shape' => 'String', ], 'CurrentRole' => [ 'shape' => 'String', ], ], ], 'NodeGroupMemberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NodeGroupMember', 'locationName' => 'NodeGroupMember', ], ], 'NodeGroupMemberUpdateStatus' => [ 'type' => 'structure', 'members' => [ 'CacheClusterId' => [ 'shape' => 'String', ], 'CacheNodeId' => [ 'shape' => 'String', ], 'NodeUpdateStatus' => [ 'shape' => 'NodeUpdateStatus', ], 'NodeDeletionDate' => [ 'shape' => 'TStamp', ], 'NodeUpdateStartDate' => [ 'shape' => 'TStamp', ], 'NodeUpdateEndDate' => [ 'shape' => 'TStamp', ], 'NodeUpdateInitiatedBy' => [ 'shape' => 'NodeUpdateInitiatedBy', ], 'NodeUpdateInitiatedDate' => [ 'shape' => 'TStamp', ], 'NodeUpdateStatusModifiedDate' => [ 'shape' => 'TStamp', ], ], ], 'NodeGroupMemberUpdateStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NodeGroupMemberUpdateStatus', 'locationName' => 'NodeGroupMemberUpdateStatus', ], ], 'NodeGroupNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'NodeGroupNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'NodeGroupUpdateStatus' => [ 'type' => 'structure', 'members' => [ 'NodeGroupId' => [ 'shape' => 'String', ], 'NodeGroupMemberUpdateStatus' => [ 'shape' => 'NodeGroupMemberUpdateStatusList', ], ], ], 'NodeGroupUpdateStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NodeGroupUpdateStatus', 'locationName' => 'NodeGroupUpdateStatus', ], ], 'NodeGroupsPerReplicationGroupQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'NodeGroupsPerReplicationGroupQuotaExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'NodeGroupsToRemoveList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AllowedNodeGroupId', 'locationName' => 'NodeGroupToRemove', ], ], 'NodeGroupsToRetainList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AllowedNodeGroupId', 'locationName' => 'NodeGroupToRetain', ], ], 'NodeQuotaForClusterExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'NodeQuotaForClusterExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'NodeQuotaForCustomerExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'NodeQuotaForCustomerExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'NodeSnapshot' => [ 'type' => 'structure', 'members' => [ 'CacheClusterId' => [ 'shape' => 'String', ], 'NodeGroupId' => [ 'shape' => 'String', ], 'CacheNodeId' => [ 'shape' => 'String', ], 'NodeGroupConfiguration' => [ 'shape' => 'NodeGroupConfiguration', ], 'CacheSize' => [ 'shape' => 'String', ], 'CacheNodeCreateTime' => [ 'shape' => 'TStamp', ], 'SnapshotCreateTime' => [ 'shape' => 'TStamp', ], ], 'wrapper' => true, ], 'NodeSnapshotList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NodeSnapshot', 'locationName' => 'NodeSnapshot', ], ], 'NodeTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'NodeUpdateInitiatedBy' => [ 'type' => 'string', 'enum' => [ 'system', 'customer', ], ], 'NodeUpdateStatus' => [ 'type' => 'string', 'enum' => [ 'not-applied', 'waiting-to-start', 'in-progress', 'stopping', 'stopped', 'complete', ], ], 'NotificationConfiguration' => [ 'type' => 'structure', 'members' => [ 'TopicArn' => [ 'shape' => 'String', ], 'TopicStatus' => [ 'shape' => 'String', ], ], ], 'OutpostArnsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'OutpostArn', ], ], 'OutpostMode' => [ 'type' => 'string', 'enum' => [ 'single-outpost', 'cross-outpost', ], ], 'Parameter' => [ 'type' => 'structure', 'members' => [ 'ParameterName' => [ 'shape' => 'String', ], 'ParameterValue' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'Source' => [ 'shape' => 'String', ], 'DataType' => [ 'shape' => 'String', ], 'AllowedValues' => [ 'shape' => 'String', ], 'IsModifiable' => [ 'shape' => 'Boolean', ], 'MinimumEngineVersion' => [ 'shape' => 'String', ], 'ChangeType' => [ 'shape' => 'ChangeType', ], ], ], 'ParameterNameValue' => [ 'type' => 'structure', 'members' => [ 'ParameterName' => [ 'shape' => 'String', ], 'ParameterValue' => [ 'shape' => 'String', ], ], ], 'ParameterNameValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParameterNameValue', 'locationName' => 'ParameterNameValue', ], ], 'ParametersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Parameter', 'locationName' => 'Parameter', ], ], 'PasswordListInput' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'min' => 1, ], 'PendingAutomaticFailoverStatus' => [ 'type' => 'string', 'enum' => [ 'enabled', 'disabled', ], ], 'PendingLogDeliveryConfiguration' => [ 'type' => 'structure', 'members' => [ 'LogType' => [ 'shape' => 'LogType', ], 'DestinationType' => [ 'shape' => 'DestinationType', ], 'DestinationDetails' => [ 'shape' => 'DestinationDetails', ], 'LogFormat' => [ 'shape' => 'LogFormat', ], ], ], 'PendingLogDeliveryConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PendingLogDeliveryConfiguration', ], 'locationName' => 'PendingLogDeliveryConfiguration', ], 'PendingModifiedValues' => [ 'type' => 'structure', 'members' => [ 'NumCacheNodes' => [ 'shape' => 'IntegerOptional', ], 'CacheNodeIdsToRemove' => [ 'shape' => 'CacheNodeIdsList', ], 'EngineVersion' => [ 'shape' => 'String', ], 'CacheNodeType' => [ 'shape' => 'String', ], 'AuthTokenStatus' => [ 'shape' => 'AuthTokenUpdateStatus', ], 'LogDeliveryConfigurations' => [ 'shape' => 'PendingLogDeliveryConfigurationList', ], ], ], 'PreferredAvailabilityZoneList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'PreferredAvailabilityZone', ], ], 'PreferredOutpostArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'PreferredOutpostArn', ], ], 'ProcessedUpdateAction' => [ 'type' => 'structure', 'members' => [ 'ReplicationGroupId' => [ 'shape' => 'String', ], 'CacheClusterId' => [ 'shape' => 'String', ], 'ServiceUpdateName' => [ 'shape' => 'String', ], 'UpdateActionStatus' => [ 'shape' => 'UpdateActionStatus', ], ], ], 'ProcessedUpdateActionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProcessedUpdateAction', 'locationName' => 'ProcessedUpdateAction', ], ], 'PurchaseReservedCacheNodesOfferingMessage' => [ 'type' => 'structure', 'required' => [ 'ReservedCacheNodesOfferingId', ], 'members' => [ 'ReservedCacheNodesOfferingId' => [ 'shape' => 'String', ], 'ReservedCacheNodeId' => [ 'shape' => 'String', ], 'CacheNodeCount' => [ 'shape' => 'IntegerOptional', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'PurchaseReservedCacheNodesOfferingResult' => [ 'type' => 'structure', 'members' => [ 'ReservedCacheNode' => [ 'shape' => 'ReservedCacheNode', ], ], ], 'RebalanceSlotsInGlobalReplicationGroupMessage' => [ 'type' => 'structure', 'required' => [ 'GlobalReplicationGroupId', 'ApplyImmediately', ], 'members' => [ 'GlobalReplicationGroupId' => [ 'shape' => 'String', ], 'ApplyImmediately' => [ 'shape' => 'Boolean', ], ], ], 'RebalanceSlotsInGlobalReplicationGroupResult' => [ 'type' => 'structure', 'members' => [ 'GlobalReplicationGroup' => [ 'shape' => 'GlobalReplicationGroup', ], ], ], 'RebootCacheClusterMessage' => [ 'type' => 'structure', 'required' => [ 'CacheClusterId', 'CacheNodeIdsToReboot', ], 'members' => [ 'CacheClusterId' => [ 'shape' => 'String', ], 'CacheNodeIdsToReboot' => [ 'shape' => 'CacheNodeIdsList', ], ], ], 'RebootCacheClusterResult' => [ 'type' => 'structure', 'members' => [ 'CacheCluster' => [ 'shape' => 'CacheCluster', ], ], ], 'RecurringCharge' => [ 'type' => 'structure', 'members' => [ 'RecurringChargeAmount' => [ 'shape' => 'Double', ], 'RecurringChargeFrequency' => [ 'shape' => 'String', ], ], 'wrapper' => true, ], 'RecurringChargeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecurringCharge', 'locationName' => 'RecurringCharge', ], ], 'RegionalConfiguration' => [ 'type' => 'structure', 'required' => [ 'ReplicationGroupId', 'ReplicationGroupRegion', 'ReshardingConfiguration', ], 'members' => [ 'ReplicationGroupId' => [ 'shape' => 'String', ], 'ReplicationGroupRegion' => [ 'shape' => 'String', ], 'ReshardingConfiguration' => [ 'shape' => 'ReshardingConfigurationList', ], ], ], 'RegionalConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegionalConfiguration', 'locationName' => 'RegionalConfiguration', ], ], 'RemoveReplicasList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'RemoveTagsFromResourceMessage' => [ 'type' => 'structure', 'required' => [ 'ResourceName', 'TagKeys', ], 'members' => [ 'ResourceName' => [ 'shape' => 'String', ], 'TagKeys' => [ 'shape' => 'KeyList', ], ], ], 'ReplicaConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigureShard', 'locationName' => 'ConfigureShard', ], ], 'ReplicationGroup' => [ 'type' => 'structure', 'members' => [ 'ReplicationGroupId' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'GlobalReplicationGroupInfo' => [ 'shape' => 'GlobalReplicationGroupInfo', ], 'Status' => [ 'shape' => 'String', ], 'PendingModifiedValues' => [ 'shape' => 'ReplicationGroupPendingModifiedValues', ], 'MemberClusters' => [ 'shape' => 'ClusterIdList', ], 'NodeGroups' => [ 'shape' => 'NodeGroupList', ], 'SnapshottingClusterId' => [ 'shape' => 'String', ], 'AutomaticFailover' => [ 'shape' => 'AutomaticFailoverStatus', ], 'MultiAZ' => [ 'shape' => 'MultiAZStatus', ], 'ConfigurationEndpoint' => [ 'shape' => 'Endpoint', ], 'SnapshotRetentionLimit' => [ 'shape' => 'IntegerOptional', ], 'SnapshotWindow' => [ 'shape' => 'String', ], 'ClusterEnabled' => [ 'shape' => 'BooleanOptional', ], 'CacheNodeType' => [ 'shape' => 'String', ], 'AuthTokenEnabled' => [ 'shape' => 'BooleanOptional', ], 'AuthTokenLastModifiedDate' => [ 'shape' => 'TStamp', ], 'TransitEncryptionEnabled' => [ 'shape' => 'BooleanOptional', ], 'AtRestEncryptionEnabled' => [ 'shape' => 'BooleanOptional', ], 'MemberClustersOutpostArns' => [ 'shape' => 'ReplicationGroupOutpostArnList', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'ARN' => [ 'shape' => 'String', ], 'UserGroupIds' => [ 'shape' => 'UserGroupIdList', ], 'LogDeliveryConfigurations' => [ 'shape' => 'LogDeliveryConfigurationList', ], 'ReplicationGroupCreateTime' => [ 'shape' => 'TStamp', ], 'DataTiering' => [ 'shape' => 'DataTieringStatus', ], ], 'wrapper' => true, ], 'ReplicationGroupAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ReplicationGroupAlreadyExists', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ReplicationGroupAlreadyUnderMigrationFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ReplicationGroupAlreadyUnderMigrationFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ReplicationGroupIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 20, ], 'ReplicationGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationGroup', 'locationName' => 'ReplicationGroup', ], ], 'ReplicationGroupMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'ReplicationGroups' => [ 'shape' => 'ReplicationGroupList', ], ], ], 'ReplicationGroupNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ReplicationGroupNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ReplicationGroupNotUnderMigrationFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ReplicationGroupNotUnderMigrationFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ReplicationGroupOutpostArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'ReplicationGroupOutpostArn', ], ], 'ReplicationGroupPendingModifiedValues' => [ 'type' => 'structure', 'members' => [ 'PrimaryClusterId' => [ 'shape' => 'String', ], 'AutomaticFailoverStatus' => [ 'shape' => 'PendingAutomaticFailoverStatus', ], 'Resharding' => [ 'shape' => 'ReshardingStatus', ], 'AuthTokenStatus' => [ 'shape' => 'AuthTokenUpdateStatus', ], 'UserGroups' => [ 'shape' => 'UserGroupsUpdateStatus', ], 'LogDeliveryConfigurations' => [ 'shape' => 'PendingLogDeliveryConfigurationList', ], ], ], 'ReservedCacheNode' => [ 'type' => 'structure', 'members' => [ 'ReservedCacheNodeId' => [ 'shape' => 'String', ], 'ReservedCacheNodesOfferingId' => [ 'shape' => 'String', ], 'CacheNodeType' => [ 'shape' => 'String', ], 'StartTime' => [ 'shape' => 'TStamp', ], 'Duration' => [ 'shape' => 'Integer', ], 'FixedPrice' => [ 'shape' => 'Double', ], 'UsagePrice' => [ 'shape' => 'Double', ], 'CacheNodeCount' => [ 'shape' => 'Integer', ], 'ProductDescription' => [ 'shape' => 'String', ], 'OfferingType' => [ 'shape' => 'String', ], 'State' => [ 'shape' => 'String', ], 'RecurringCharges' => [ 'shape' => 'RecurringChargeList', ], 'ReservationARN' => [ 'shape' => 'String', ], ], 'wrapper' => true, ], 'ReservedCacheNodeAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ReservedCacheNodeAlreadyExists', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ReservedCacheNodeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReservedCacheNode', 'locationName' => 'ReservedCacheNode', ], ], 'ReservedCacheNodeMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'ReservedCacheNodes' => [ 'shape' => 'ReservedCacheNodeList', ], ], ], 'ReservedCacheNodeNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ReservedCacheNodeNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ReservedCacheNodeQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ReservedCacheNodeQuotaExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ReservedCacheNodesOffering' => [ 'type' => 'structure', 'members' => [ 'ReservedCacheNodesOfferingId' => [ 'shape' => 'String', ], 'CacheNodeType' => [ 'shape' => 'String', ], 'Duration' => [ 'shape' => 'Integer', ], 'FixedPrice' => [ 'shape' => 'Double', ], 'UsagePrice' => [ 'shape' => 'Double', ], 'ProductDescription' => [ 'shape' => 'String', ], 'OfferingType' => [ 'shape' => 'String', ], 'RecurringCharges' => [ 'shape' => 'RecurringChargeList', ], ], 'wrapper' => true, ], 'ReservedCacheNodesOfferingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReservedCacheNodesOffering', 'locationName' => 'ReservedCacheNodesOffering', ], ], 'ReservedCacheNodesOfferingMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'ReservedCacheNodesOfferings' => [ 'shape' => 'ReservedCacheNodesOfferingList', ], ], ], 'ReservedCacheNodesOfferingNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ReservedCacheNodesOfferingNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResetCacheParameterGroupMessage' => [ 'type' => 'structure', 'required' => [ 'CacheParameterGroupName', ], 'members' => [ 'CacheParameterGroupName' => [ 'shape' => 'String', ], 'ResetAllParameters' => [ 'shape' => 'Boolean', ], 'ParameterNameValues' => [ 'shape' => 'ParameterNameValueList', ], ], ], 'ReshardingConfiguration' => [ 'type' => 'structure', 'members' => [ 'NodeGroupId' => [ 'shape' => 'AllowedNodeGroupId', ], 'PreferredAvailabilityZones' => [ 'shape' => 'AvailabilityZonesList', ], ], ], 'ReshardingConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReshardingConfiguration', 'locationName' => 'ReshardingConfiguration', ], ], 'ReshardingStatus' => [ 'type' => 'structure', 'members' => [ 'SlotMigration' => [ 'shape' => 'SlotMigration', ], ], ], 'RevokeCacheSecurityGroupIngressMessage' => [ 'type' => 'structure', 'required' => [ 'CacheSecurityGroupName', 'EC2SecurityGroupName', 'EC2SecurityGroupOwnerId', ], 'members' => [ 'CacheSecurityGroupName' => [ 'shape' => 'String', ], 'EC2SecurityGroupName' => [ 'shape' => 'String', ], 'EC2SecurityGroupOwnerId' => [ 'shape' => 'String', ], ], ], 'RevokeCacheSecurityGroupIngressResult' => [ 'type' => 'structure', 'members' => [ 'CacheSecurityGroup' => [ 'shape' => 'CacheSecurityGroup', ], ], ], 'SecurityGroupIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'SecurityGroupId', ], ], 'SecurityGroupMembership' => [ 'type' => 'structure', 'members' => [ 'SecurityGroupId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], ], ], 'SecurityGroupMembershipList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupMembership', ], ], 'ServiceLinkedRoleNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ServiceLinkedRoleNotFoundFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ServiceUpdate' => [ 'type' => 'structure', 'members' => [ 'ServiceUpdateName' => [ 'shape' => 'String', ], 'ServiceUpdateReleaseDate' => [ 'shape' => 'TStamp', ], 'ServiceUpdateEndDate' => [ 'shape' => 'TStamp', ], 'ServiceUpdateSeverity' => [ 'shape' => 'ServiceUpdateSeverity', ], 'ServiceUpdateRecommendedApplyByDate' => [ 'shape' => 'TStamp', ], 'ServiceUpdateStatus' => [ 'shape' => 'ServiceUpdateStatus', ], 'ServiceUpdateDescription' => [ 'shape' => 'String', ], 'ServiceUpdateType' => [ 'shape' => 'ServiceUpdateType', ], 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'AutoUpdateAfterRecommendedApplyByDate' => [ 'shape' => 'BooleanOptional', ], 'EstimatedUpdateTime' => [ 'shape' => 'String', ], ], ], 'ServiceUpdateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServiceUpdate', 'locationName' => 'ServiceUpdate', ], ], 'ServiceUpdateNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ServiceUpdateNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ServiceUpdateSeverity' => [ 'type' => 'string', 'enum' => [ 'critical', 'important', 'medium', 'low', ], ], 'ServiceUpdateStatus' => [ 'type' => 'string', 'enum' => [ 'available', 'cancelled', 'expired', ], ], 'ServiceUpdateStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServiceUpdateStatus', ], 'max' => 3, ], 'ServiceUpdateType' => [ 'type' => 'string', 'enum' => [ 'security-update', ], ], 'ServiceUpdatesMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'ServiceUpdates' => [ 'shape' => 'ServiceUpdateList', ], ], ], 'SlaMet' => [ 'type' => 'string', 'enum' => [ 'yes', 'no', 'n/a', ], ], 'SlotMigration' => [ 'type' => 'structure', 'members' => [ 'ProgressPercentage' => [ 'shape' => 'Double', ], ], ], 'Snapshot' => [ 'type' => 'structure', 'members' => [ 'SnapshotName' => [ 'shape' => 'String', ], 'ReplicationGroupId' => [ 'shape' => 'String', ], 'ReplicationGroupDescription' => [ 'shape' => 'String', ], 'CacheClusterId' => [ 'shape' => 'String', ], 'SnapshotStatus' => [ 'shape' => 'String', ], 'SnapshotSource' => [ 'shape' => 'String', ], 'CacheNodeType' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'NumCacheNodes' => [ 'shape' => 'IntegerOptional', ], 'PreferredAvailabilityZone' => [ 'shape' => 'String', ], 'PreferredOutpostArn' => [ 'shape' => 'String', ], 'CacheClusterCreateTime' => [ 'shape' => 'TStamp', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'String', ], 'TopicArn' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'CacheParameterGroupName' => [ 'shape' => 'String', ], 'CacheSubnetGroupName' => [ 'shape' => 'String', ], 'VpcId' => [ 'shape' => 'String', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'Boolean', ], 'SnapshotRetentionLimit' => [ 'shape' => 'IntegerOptional', ], 'SnapshotWindow' => [ 'shape' => 'String', ], 'NumNodeGroups' => [ 'shape' => 'IntegerOptional', ], 'AutomaticFailover' => [ 'shape' => 'AutomaticFailoverStatus', ], 'NodeSnapshots' => [ 'shape' => 'NodeSnapshotList', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'ARN' => [ 'shape' => 'String', ], 'DataTiering' => [ 'shape' => 'DataTieringStatus', ], ], 'wrapper' => true, ], 'SnapshotAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SnapshotAlreadyExistsFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'SnapshotArnsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'SnapshotArn', ], ], 'SnapshotFeatureNotSupportedFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SnapshotFeatureNotSupportedFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'SnapshotList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Snapshot', 'locationName' => 'Snapshot', ], ], 'SnapshotNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SnapshotNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'SnapshotQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SnapshotQuotaExceededFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'SourceType' => [ 'type' => 'string', 'enum' => [ 'cache-cluster', 'cache-parameter-group', 'cache-security-group', 'cache-subnet-group', 'replication-group', 'user', 'user-group', ], ], 'StartMigrationMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationGroupId', 'CustomerNodeEndpointList', ], 'members' => [ 'ReplicationGroupId' => [ 'shape' => 'String', ], 'CustomerNodeEndpointList' => [ 'shape' => 'CustomerNodeEndpointList', ], ], ], 'StartMigrationResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationGroup' => [ 'shape' => 'ReplicationGroup', ], ], ], 'String' => [ 'type' => 'string', ], 'Subnet' => [ 'type' => 'structure', 'members' => [ 'SubnetIdentifier' => [ 'shape' => 'String', ], 'SubnetAvailabilityZone' => [ 'shape' => 'AvailabilityZone', ], 'SubnetOutpost' => [ 'shape' => 'SubnetOutpost', ], ], ], 'SubnetIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'SubnetIdentifier', ], ], 'SubnetInUse' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SubnetInUse', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'SubnetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Subnet', 'locationName' => 'Subnet', ], ], 'SubnetNotAllowedFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SubnetNotAllowedFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'SubnetOutpost' => [ 'type' => 'structure', 'members' => [ 'SubnetOutpostArn' => [ 'shape' => 'String', ], ], ], 'TStamp' => [ 'type' => 'timestamp', ], 'Tag' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], ], ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', 'locationName' => 'Tag', ], ], 'TagListMessage' => [ 'type' => 'structure', 'members' => [ 'TagList' => [ 'shape' => 'TagList', ], ], ], 'TagNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'TagNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'TagQuotaPerResourceExceeded' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'TagQuotaPerResourceExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TestFailoverMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationGroupId', 'NodeGroupId', ], 'members' => [ 'ReplicationGroupId' => [ 'shape' => 'String', ], 'NodeGroupId' => [ 'shape' => 'AllowedNodeGroupId', ], ], ], 'TestFailoverNotAvailableFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'TestFailoverNotAvailableFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TestFailoverResult' => [ 'type' => 'structure', 'members' => [ 'ReplicationGroup' => [ 'shape' => 'ReplicationGroup', ], ], ], 'TimeRangeFilter' => [ 'type' => 'structure', 'members' => [ 'StartTime' => [ 'shape' => 'TStamp', ], 'EndTime' => [ 'shape' => 'TStamp', ], ], ], 'UGReplicationGroupIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'UnprocessedUpdateAction' => [ 'type' => 'structure', 'members' => [ 'ReplicationGroupId' => [ 'shape' => 'String', ], 'CacheClusterId' => [ 'shape' => 'String', ], 'ServiceUpdateName' => [ 'shape' => 'String', ], 'ErrorType' => [ 'shape' => 'String', ], 'ErrorMessage' => [ 'shape' => 'String', ], ], ], 'UnprocessedUpdateActionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UnprocessedUpdateAction', 'locationName' => 'UnprocessedUpdateAction', ], ], 'UpdateAction' => [ 'type' => 'structure', 'members' => [ 'ReplicationGroupId' => [ 'shape' => 'String', ], 'CacheClusterId' => [ 'shape' => 'String', ], 'ServiceUpdateName' => [ 'shape' => 'String', ], 'ServiceUpdateReleaseDate' => [ 'shape' => 'TStamp', ], 'ServiceUpdateSeverity' => [ 'shape' => 'ServiceUpdateSeverity', ], 'ServiceUpdateStatus' => [ 'shape' => 'ServiceUpdateStatus', ], 'ServiceUpdateRecommendedApplyByDate' => [ 'shape' => 'TStamp', ], 'ServiceUpdateType' => [ 'shape' => 'ServiceUpdateType', ], 'UpdateActionAvailableDate' => [ 'shape' => 'TStamp', ], 'UpdateActionStatus' => [ 'shape' => 'UpdateActionStatus', ], 'NodesUpdated' => [ 'shape' => 'String', ], 'UpdateActionStatusModifiedDate' => [ 'shape' => 'TStamp', ], 'SlaMet' => [ 'shape' => 'SlaMet', ], 'NodeGroupUpdateStatus' => [ 'shape' => 'NodeGroupUpdateStatusList', ], 'CacheNodeUpdateStatus' => [ 'shape' => 'CacheNodeUpdateStatusList', ], 'EstimatedUpdateTime' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], ], ], 'UpdateActionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpdateAction', 'locationName' => 'UpdateAction', ], ], 'UpdateActionResultsMessage' => [ 'type' => 'structure', 'members' => [ 'ProcessedUpdateActions' => [ 'shape' => 'ProcessedUpdateActionList', ], 'UnprocessedUpdateActions' => [ 'shape' => 'UnprocessedUpdateActionList', ], ], ], 'UpdateActionStatus' => [ 'type' => 'string', 'enum' => [ 'not-applied', 'waiting-to-start', 'in-progress', 'stopping', 'stopped', 'complete', 'scheduling', 'scheduled', 'not-applicable', ], ], 'UpdateActionStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpdateActionStatus', ], 'max' => 9, ], 'UpdateActionsMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'UpdateActions' => [ 'shape' => 'UpdateActionList', ], ], ], 'User' => [ 'type' => 'structure', 'members' => [ 'UserId' => [ 'shape' => 'String', ], 'UserName' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'EngineType', ], 'MinimumEngineVersion' => [ 'shape' => 'String', ], 'AccessString' => [ 'shape' => 'String', ], 'UserGroupIds' => [ 'shape' => 'UserGroupIdList', ], 'Authentication' => [ 'shape' => 'Authentication', ], 'ARN' => [ 'shape' => 'String', ], ], ], 'UserAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'UserAlreadyExists', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'UserGroup' => [ 'type' => 'structure', 'members' => [ 'UserGroupId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'EngineType', ], 'UserIds' => [ 'shape' => 'UserIdList', ], 'MinimumEngineVersion' => [ 'shape' => 'String', ], 'PendingChanges' => [ 'shape' => 'UserGroupPendingChanges', ], 'ReplicationGroups' => [ 'shape' => 'UGReplicationGroupIdList', ], 'ARN' => [ 'shape' => 'String', ], ], ], 'UserGroupAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'UserGroupAlreadyExists', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'UserGroupId' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[a-zA-Z][a-zA-Z0-9\\-]*', ], 'UserGroupIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserGroupId', ], ], 'UserGroupIdListInput' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserGroupId', ], 'min' => 1, ], 'UserGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserGroup', ], ], 'UserGroupNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'UserGroupNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'UserGroupPendingChanges' => [ 'type' => 'structure', 'members' => [ 'UserIdsToRemove' => [ 'shape' => 'UserIdList', ], 'UserIdsToAdd' => [ 'shape' => 'UserIdList', ], ], ], 'UserGroupQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'UserGroupQuotaExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'UserGroupsUpdateStatus' => [ 'type' => 'structure', 'members' => [ 'UserGroupIdsToAdd' => [ 'shape' => 'UserGroupIdList', ], 'UserGroupIdsToRemove' => [ 'shape' => 'UserGroupIdList', ], ], ], 'UserId' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[a-zA-Z][a-zA-Z0-9\\-]*', ], 'UserIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserId', ], ], 'UserIdListInput' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserId', ], 'min' => 1, ], 'UserList' => [ 'type' => 'list', 'member' => [ 'shape' => 'User', ], ], 'UserName' => [ 'type' => 'string', 'min' => 1, ], 'UserNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'UserNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'UserQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'UserQuotaExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], ],];
