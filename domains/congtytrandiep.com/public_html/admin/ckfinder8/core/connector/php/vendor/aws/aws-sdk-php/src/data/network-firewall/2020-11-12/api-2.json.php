<?php
// This file was auto-generated from sdk-root/src/data/network-firewall/2020-11-12/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-11-12', 'endpointPrefix' => 'network-firewall', 'jsonVersion' => '1.0', 'protocol' => 'json', 'serviceAbbreviation' => 'Network Firewall', 'serviceFullName' => 'AWS Network Firewall', 'serviceId' => 'Network Firewall', 'signatureVersion' => 'v4', 'signingName' => 'network-firewall', 'targetPrefix' => 'NetworkFirewall_20201112', 'uid' => 'network-firewall-2020-11-12', ], 'operations' => [ 'AssociateFirewallPolicy' => [ 'name' => 'AssociateFirewallPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateFirewallPolicyRequest', ], 'output' => [ 'shape' => 'AssociateFirewallPolicyResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidTokenException', ], [ 'shape' => 'InvalidOperationException', ], ], ], 'AssociateSubnets' => [ 'name' => 'AssociateSubnets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateSubnetsRequest', ], 'output' => [ 'shape' => 'AssociateSubnetsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidTokenException', ], [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'InsufficientCapacityException', ], ], ], 'CreateFirewall' => [ 'name' => 'CreateFirewall', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateFirewallRequest', ], 'output' => [ 'shape' => 'CreateFirewallResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InsufficientCapacityException', ], [ 'shape' => 'InvalidOperationException', ], ], ], 'CreateFirewallPolicy' => [ 'name' => 'CreateFirewallPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateFirewallPolicyRequest', ], 'output' => [ 'shape' => 'CreateFirewallPolicyResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InsufficientCapacityException', ], ], ], 'CreateRuleGroup' => [ 'name' => 'CreateRuleGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRuleGroupRequest', ], 'output' => [ 'shape' => 'CreateRuleGroupResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InsufficientCapacityException', ], ], ], 'DeleteFirewall' => [ 'name' => 'DeleteFirewall', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteFirewallRequest', ], 'output' => [ 'shape' => 'DeleteFirewallResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'InvalidOperationException', ], ], ], 'DeleteFirewallPolicy' => [ 'name' => 'DeleteFirewallPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteFirewallPolicyRequest', ], 'output' => [ 'shape' => 'DeleteFirewallPolicyResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'InvalidOperationException', ], ], ], 'DeleteResourcePolicy' => [ 'name' => 'DeleteResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteResourcePolicyRequest', ], 'output' => [ 'shape' => 'DeleteResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidResourcePolicyException', ], ], ], 'DeleteRuleGroup' => [ 'name' => 'DeleteRuleGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRuleGroupRequest', ], 'output' => [ 'shape' => 'DeleteRuleGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'InvalidOperationException', ], ], ], 'DescribeFirewall' => [ 'name' => 'DescribeFirewall', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFirewallRequest', ], 'output' => [ 'shape' => 'DescribeFirewallResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DescribeFirewallPolicy' => [ 'name' => 'DescribeFirewallPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeFirewallPolicyRequest', ], 'output' => [ 'shape' => 'DescribeFirewallPolicyResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeLoggingConfiguration' => [ 'name' => 'DescribeLoggingConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeLoggingConfigurationRequest', ], 'output' => [ 'shape' => 'DescribeLoggingConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DescribeResourcePolicy' => [ 'name' => 'DescribeResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeResourcePolicyRequest', ], 'output' => [ 'shape' => 'DescribeResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DescribeRuleGroup' => [ 'name' => 'DescribeRuleGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRuleGroupRequest', ], 'output' => [ 'shape' => 'DescribeRuleGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DescribeRuleGroupMetadata' => [ 'name' => 'DescribeRuleGroupMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRuleGroupMetadataRequest', ], 'output' => [ 'shape' => 'DescribeRuleGroupMetadataResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerError', ], ], ], 'DisassociateSubnets' => [ 'name' => 'DisassociateSubnets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateSubnetsRequest', ], 'output' => [ 'shape' => 'DisassociateSubnetsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidTokenException', ], [ 'shape' => 'InvalidOperationException', ], ], ], 'ListFirewallPolicies' => [ 'name' => 'ListFirewallPolicies', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListFirewallPoliciesRequest', ], 'output' => [ 'shape' => 'ListFirewallPoliciesResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerError', ], ], ], 'ListFirewalls' => [ 'name' => 'ListFirewalls', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListFirewallsRequest', ], 'output' => [ 'shape' => 'ListFirewallsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListRuleGroups' => [ 'name' => 'ListRuleGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRuleGroupsRequest', ], 'output' => [ 'shape' => 'ListRuleGroupsResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerError', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'PutResourcePolicy' => [ 'name' => 'PutResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutResourcePolicyRequest', ], 'output' => [ 'shape' => 'PutResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidResourcePolicyException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'UpdateFirewallDeleteProtection' => [ 'name' => 'UpdateFirewallDeleteProtection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateFirewallDeleteProtectionRequest', ], 'output' => [ 'shape' => 'UpdateFirewallDeleteProtectionResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidTokenException', ], [ 'shape' => 'ResourceOwnerCheckException', ], ], ], 'UpdateFirewallDescription' => [ 'name' => 'UpdateFirewallDescription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateFirewallDescriptionRequest', ], 'output' => [ 'shape' => 'UpdateFirewallDescriptionResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidTokenException', ], ], ], 'UpdateFirewallPolicy' => [ 'name' => 'UpdateFirewallPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateFirewallPolicyRequest', ], 'output' => [ 'shape' => 'UpdateFirewallPolicyResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidTokenException', ], ], ], 'UpdateFirewallPolicyChangeProtection' => [ 'name' => 'UpdateFirewallPolicyChangeProtection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateFirewallPolicyChangeProtectionRequest', ], 'output' => [ 'shape' => 'UpdateFirewallPolicyChangeProtectionResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidTokenException', ], [ 'shape' => 'ResourceOwnerCheckException', ], ], ], 'UpdateLoggingConfiguration' => [ 'name' => 'UpdateLoggingConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateLoggingConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateLoggingConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidTokenException', ], [ 'shape' => 'LogDestinationPermissionException', ], ], ], 'UpdateRuleGroup' => [ 'name' => 'UpdateRuleGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRuleGroupRequest', ], 'output' => [ 'shape' => 'UpdateRuleGroupResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'InvalidTokenException', ], ], ], 'UpdateSubnetChangeProtection' => [ 'name' => 'UpdateSubnetChangeProtection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateSubnetChangeProtectionRequest', ], 'output' => [ 'shape' => 'UpdateSubnetChangeProtectionResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServerError', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidTokenException', ], [ 'shape' => 'ResourceOwnerCheckException', ], ], ], ], 'shapes' => [ 'ActionDefinition' => [ 'type' => 'structure', 'members' => [ 'PublishMetricAction' => [ 'shape' => 'PublishMetricAction', ], ], ], 'ActionName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9]+$', ], 'Address' => [ 'type' => 'structure', 'required' => [ 'AddressDefinition', ], 'members' => [ 'AddressDefinition' => [ 'shape' => 'AddressDefinition', ], ], ], 'AddressDefinition' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^([a-fA-F\\d:\\.]+($|/\\d{1,3}))$', ], 'Addresses' => [ 'type' => 'list', 'member' => [ 'shape' => 'Address', ], ], 'AssociateFirewallPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'FirewallPolicyArn', ], 'members' => [ 'UpdateToken' => [ 'shape' => 'UpdateToken', ], 'FirewallArn' => [ 'shape' => 'ResourceArn', ], 'FirewallName' => [ 'shape' => 'ResourceName', ], 'FirewallPolicyArn' => [ 'shape' => 'ResourceArn', ], ], ], 'AssociateFirewallPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'FirewallArn' => [ 'shape' => 'ResourceArn', ], 'FirewallName' => [ 'shape' => 'ResourceName', ], 'FirewallPolicyArn' => [ 'shape' => 'ResourceArn', ], 'UpdateToken' => [ 'shape' => 'UpdateToken', ], ], ], 'AssociateSubnetsRequest' => [ 'type' => 'structure', 'required' => [ 'SubnetMappings', ], 'members' => [ 'UpdateToken' => [ 'shape' => 'UpdateToken', ], 'FirewallArn' => [ 'shape' => 'ResourceArn', ], 'FirewallName' => [ 'shape' => 'ResourceName', ], 'SubnetMappings' => [ 'shape' => 'SubnetMappings', ], ], ], 'AssociateSubnetsResponse' => [ 'type' => 'structure', 'members' => [ 'FirewallArn' => [ 'shape' => 'ResourceArn', ], 'FirewallName' => [ 'shape' => 'ResourceName', ], 'SubnetMappings' => [ 'shape' => 'SubnetMappings', ], 'UpdateToken' => [ 'shape' => 'UpdateToken', ], ], ], 'Attachment' => [ 'type' => 'structure', 'members' => [ 'SubnetId' => [ 'shape' => 'AzSubnet', ], 'EndpointId' => [ 'shape' => 'EndpointId', ], 'Status' => [ 'shape' => 'AttachmentStatus', ], ], ], 'AttachmentStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'DELETING', 'SCALING', 'READY', ], ], 'AvailabilityZone' => [ 'type' => 'string', ], 'AzSubnet' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^subnet-[0-9a-f]+$', ], 'AzSubnets' => [ 'type' => 'list', 'member' => [ 'shape' => 'AzSubnet', ], ], 'Boolean' => [ 'type' => 'boolean', ], 'CollectionMember_String' => [ 'type' => 'string', ], 'ConfigurationSyncState' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'IN_SYNC', ], ], 'CreateFirewallPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'FirewallPolicyName', 'FirewallPolicy', ], 'members' => [ 'FirewallPolicyName' => [ 'shape' => 'ResourceName', ], 'FirewallPolicy' => [ 'shape' => 'FirewallPolicy', ], 'Description' => [ 'shape' => 'Description', ], 'Tags' => [ 'shape' => 'TagList', ], 'DryRun' => [ 'shape' => 'Boolean', ], ], ], 'CreateFirewallPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'UpdateToken', 'FirewallPolicyResponse', ], 'members' => [ 'UpdateToken' => [ 'shape' => 'UpdateToken', ], 'FirewallPolicyResponse' => [ 'shape' => 'FirewallPolicyResponse', ], ], ], 'CreateFirewallRequest' => [ 'type' => 'structure', 'required' => [ 'FirewallName', 'FirewallPolicyArn', 'VpcId', 'SubnetMappings', ], 'members' => [ 'FirewallName' => [ 'shape' => 'ResourceName', ], 'FirewallPolicyArn' => [ 'shape' => 'ResourceArn', ], 'VpcId' => [ 'shape' => 'VpcId', ], 'SubnetMappings' => [ 'shape' => 'SubnetMappings', ], 'DeleteProtection' => [ 'shape' => 'Boolean', ], 'SubnetChangeProtection' => [ 'shape' => 'Boolean', ], 'FirewallPolicyChangeProtection' => [ 'shape' => 'Boolean', ], 'Description' => [ 'shape' => 'Description', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateFirewallResponse' => [ 'type' => 'structure', 'members' => [ 'Firewall' => [ 'shape' => 'Firewall', ], 'FirewallStatus' => [ 'shape' => 'FirewallStatus', ], ], ], 'CreateRuleGroupRequest' => [ 'type' => 'structure', 'required' => [ 'RuleGroupName', 'Type', 'Capacity', ], 'members' => [ 'RuleGroupName' => [ 'shape' => 'ResourceName', ], 'RuleGroup' => [ 'shape' => 'RuleGroup', ], 'Rules' => [ 'shape' => 'RulesString', ], 'Type' => [ 'shape' => 'RuleGroupType', ], 'Description' => [ 'shape' => 'Description', ], 'Capacity' => [ 'shape' => 'RuleCapacity', ], 'Tags' => [ 'shape' => 'TagList', ], 'DryRun' => [ 'shape' => 'Boolean', ], ], ], 'CreateRuleGroupResponse' => [ 'type' => 'structure', 'required' => [ 'UpdateToken', 'RuleGroupResponse', ], 'members' => [ 'UpdateToken' => [ 'shape' => 'UpdateToken', ], 'RuleGroupResponse' => [ 'shape' => 'RuleGroupResponse', ], ], ], 'CustomAction' => [ 'type' => 'structure', 'required' => [ 'ActionName', 'ActionDefinition', ], 'members' => [ 'ActionName' => [ 'shape' => 'ActionName', ], 'ActionDefinition' => [ 'shape' => 'ActionDefinition', ], ], ], 'CustomActions' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomAction', ], ], 'DeleteFirewallPolicyRequest' => [ 'type' => 'structure', 'members' => [ 'FirewallPolicyName' => [ 'shape' => 'ResourceName', ], 'FirewallPolicyArn' => [ 'shape' => 'ResourceArn', ], ], ], 'DeleteFirewallPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'FirewallPolicyResponse', ], 'members' => [ 'FirewallPolicyResponse' => [ 'shape' => 'FirewallPolicyResponse', ], ], ], 'DeleteFirewallRequest' => [ 'type' => 'structure', 'members' => [ 'FirewallName' => [ 'shape' => 'ResourceName', ], 'FirewallArn' => [ 'shape' => 'ResourceArn', ], ], ], 'DeleteFirewallResponse' => [ 'type' => 'structure', 'members' => [ 'Firewall' => [ 'shape' => 'Firewall', ], 'FirewallStatus' => [ 'shape' => 'FirewallStatus', ], ], ], 'DeleteResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], ], ], 'DeleteResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRuleGroupRequest' => [ 'type' => 'structure', 'members' => [ 'RuleGroupName' => [ 'shape' => 'ResourceName', ], 'RuleGroupArn' => [ 'shape' => 'ResourceArn', ], 'Type' => [ 'shape' => 'RuleGroupType', ], ], ], 'DeleteRuleGroupResponse' => [ 'type' => 'structure', 'required' => [ 'RuleGroupResponse', ], 'members' => [ 'RuleGroupResponse' => [ 'shape' => 'RuleGroupResponse', ], ], ], 'DescribeFirewallPolicyRequest' => [ 'type' => 'structure', 'members' => [ 'FirewallPolicyName' => [ 'shape' => 'ResourceName', ], 'FirewallPolicyArn' => [ 'shape' => 'ResourceArn', ], ], ], 'DescribeFirewallPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'UpdateToken', 'FirewallPolicyResponse', ], 'members' => [ 'UpdateToken' => [ 'shape' => 'UpdateToken', ], 'FirewallPolicyResponse' => [ 'shape' => 'FirewallPolicyResponse', ], 'FirewallPolicy' => [ 'shape' => 'FirewallPolicy', ], ], ], 'DescribeFirewallRequest' => [ 'type' => 'structure', 'members' => [ 'FirewallName' => [ 'shape' => 'ResourceName', ], 'FirewallArn' => [ 'shape' => 'ResourceArn', ], ], ], 'DescribeFirewallResponse' => [ 'type' => 'structure', 'members' => [ 'UpdateToken' => [ 'shape' => 'UpdateToken', ], 'Firewall' => [ 'shape' => 'Firewall', ], 'FirewallStatus' => [ 'shape' => 'FirewallStatus', ], ], ], 'DescribeLoggingConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'FirewallArn' => [ 'shape' => 'ResourceArn', ], 'FirewallName' => [ 'shape' => 'ResourceName', ], ], ], 'DescribeLoggingConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'FirewallArn' => [ 'shape' => 'ResourceArn', ], 'LoggingConfiguration' => [ 'shape' => 'LoggingConfiguration', ], ], ], 'DescribeResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], ], ], 'DescribeResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'Policy' => [ 'shape' => 'PolicyString', ], ], ], 'DescribeRuleGroupMetadataRequest' => [ 'type' => 'structure', 'members' => [ 'RuleGroupName' => [ 'shape' => 'ResourceName', ], 'RuleGroupArn' => [ 'shape' => 'ResourceArn', ], 'Type' => [ 'shape' => 'RuleGroupType', ], ], ], 'DescribeRuleGroupMetadataResponse' => [ 'type' => 'structure', 'required' => [ 'RuleGroupArn', 'RuleGroupName', ], 'members' => [ 'RuleGroupArn' => [ 'shape' => 'ResourceArn', ], 'RuleGroupName' => [ 'shape' => 'ResourceName', ], 'Description' => [ 'shape' => 'Description', ], 'Type' => [ 'shape' => 'RuleGroupType', ], 'Capacity' => [ 'shape' => 'RuleCapacity', ], 'StatefulRuleOptions' => [ 'shape' => 'StatefulRuleOptions', ], ], ], 'DescribeRuleGroupRequest' => [ 'type' => 'structure', 'members' => [ 'RuleGroupName' => [ 'shape' => 'ResourceName', ], 'RuleGroupArn' => [ 'shape' => 'ResourceArn', ], 'Type' => [ 'shape' => 'RuleGroupType', ], ], ], 'DescribeRuleGroupResponse' => [ 'type' => 'structure', 'required' => [ 'UpdateToken', 'RuleGroupResponse', ], 'members' => [ 'UpdateToken' => [ 'shape' => 'UpdateToken', ], 'RuleGroup' => [ 'shape' => 'RuleGroup', ], 'RuleGroupResponse' => [ 'shape' => 'RuleGroupResponse', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 512, 'pattern' => '^.*$', ], 'Destination' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^.*$', ], 'Dimension' => [ 'type' => 'structure', 'required' => [ 'Value', ], 'members' => [ 'Value' => [ 'shape' => 'DimensionValue', ], ], ], 'DimensionValue' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-_ ]+$', ], 'Dimensions' => [ 'type' => 'list', 'member' => [ 'shape' => 'Dimension', ], 'max' => 1, 'min' => 1, ], 'DisassociateSubnetsRequest' => [ 'type' => 'structure', 'required' => [ 'SubnetIds', ], 'members' => [ 'UpdateToken' => [ 'shape' => 'UpdateToken', ], 'FirewallArn' => [ 'shape' => 'ResourceArn', ], 'FirewallName' => [ 'shape' => 'ResourceName', ], 'SubnetIds' => [ 'shape' => 'AzSubnets', ], ], ], 'DisassociateSubnetsResponse' => [ 'type' => 'structure', 'members' => [ 'FirewallArn' => [ 'shape' => 'ResourceArn', ], 'FirewallName' => [ 'shape' => 'ResourceName', ], 'SubnetMappings' => [ 'shape' => 'SubnetMappings', ], 'UpdateToken' => [ 'shape' => 'UpdateToken', ], ], ], 'EndpointId' => [ 'type' => 'string', ], 'ErrorMessage' => [ 'type' => 'string', ], 'Firewall' => [ 'type' => 'structure', 'required' => [ 'FirewallPolicyArn', 'VpcId', 'SubnetMappings', 'FirewallId', ], 'members' => [ 'FirewallName' => [ 'shape' => 'ResourceName', ], 'FirewallArn' => [ 'shape' => 'ResourceArn', ], 'FirewallPolicyArn' => [ 'shape' => 'ResourceArn', ], 'VpcId' => [ 'shape' => 'VpcId', ], 'SubnetMappings' => [ 'shape' => 'SubnetMappings', ], 'DeleteProtection' => [ 'shape' => 'Boolean', ], 'SubnetChangeProtection' => [ 'shape' => 'Boolean', ], 'FirewallPolicyChangeProtection' => [ 'shape' => 'Boolean', ], 'Description' => [ 'shape' => 'Description', ], 'FirewallId' => [ 'shape' => 'ResourceId', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'FirewallMetadata' => [ 'type' => 'structure', 'members' => [ 'FirewallName' => [ 'shape' => 'ResourceName', ], 'FirewallArn' => [ 'shape' => 'ResourceArn', ], ], ], 'FirewallPolicies' => [ 'type' => 'list', 'member' => [ 'shape' => 'FirewallPolicyMetadata', ], ], 'FirewallPolicy' => [ 'type' => 'structure', 'required' => [ 'StatelessDefaultActions', 'StatelessFragmentDefaultActions', ], 'members' => [ 'StatelessRuleGroupReferences' => [ 'shape' => 'StatelessRuleGroupReferences', ], 'StatelessDefaultActions' => [ 'shape' => 'StatelessActions', ], 'StatelessFragmentDefaultActions' => [ 'shape' => 'StatelessActions', ], 'StatelessCustomActions' => [ 'shape' => 'CustomActions', ], 'StatefulRuleGroupReferences' => [ 'shape' => 'StatefulRuleGroupReferences', ], 'StatefulDefaultActions' => [ 'shape' => 'StatefulActions', ], 'StatefulEngineOptions' => [ 'shape' => 'StatefulEngineOptions', ], ], ], 'FirewallPolicyMetadata' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ResourceName', ], 'Arn' => [ 'shape' => 'ResourceArn', ], ], ], 'FirewallPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'FirewallPolicyName', 'FirewallPolicyArn', 'FirewallPolicyId', ], 'members' => [ 'FirewallPolicyName' => [ 'shape' => 'ResourceName', ], 'FirewallPolicyArn' => [ 'shape' => 'ResourceArn', ], 'FirewallPolicyId' => [ 'shape' => 'ResourceId', ], 'Description' => [ 'shape' => 'Description', ], 'FirewallPolicyStatus' => [ 'shape' => 'ResourceStatus', ], 'Tags' => [ 'shape' => 'TagList', ], 'ConsumedStatelessRuleCapacity' => [ 'shape' => 'RuleCapacity', ], 'ConsumedStatefulRuleCapacity' => [ 'shape' => 'RuleCapacity', ], 'NumberOfAssociations' => [ 'shape' => 'NumberOfAssociations', ], ], ], 'FirewallStatus' => [ 'type' => 'structure', 'required' => [ 'Status', 'ConfigurationSyncStateSummary', ], 'members' => [ 'Status' => [ 'shape' => 'FirewallStatusValue', ], 'ConfigurationSyncStateSummary' => [ 'shape' => 'ConfigurationSyncState', ], 'SyncStates' => [ 'shape' => 'SyncStates', ], ], ], 'FirewallStatusValue' => [ 'type' => 'string', 'enum' => [ 'PROVISIONING', 'DELETING', 'READY', ], ], 'Firewalls' => [ 'type' => 'list', 'member' => [ 'shape' => 'FirewallMetadata', ], ], 'Flags' => [ 'type' => 'list', 'member' => [ 'shape' => 'TCPFlag', ], ], 'GeneratedRulesType' => [ 'type' => 'string', 'enum' => [ 'ALLOWLIST', 'DENYLIST', ], ], 'HashMapKey' => [ 'type' => 'string', 'max' => 50, 'min' => 3, 'pattern' => '^[0-9A-Za-z.\\-_@\\/]+$', ], 'HashMapValue' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[\\s\\S]*$', ], 'Header' => [ 'type' => 'structure', 'required' => [ 'Protocol', 'Source', 'SourcePort', 'Direction', 'Destination', 'DestinationPort', ], 'members' => [ 'Protocol' => [ 'shape' => 'StatefulRuleProtocol', ], 'Source' => [ 'shape' => 'Source', ], 'SourcePort' => [ 'shape' => 'Port', ], 'Direction' => [ 'shape' => 'StatefulRuleDirection', ], 'Destination' => [ 'shape' => 'Destination', ], 'DestinationPort' => [ 'shape' => 'Port', ], ], ], 'IPSet' => [ 'type' => 'structure', 'required' => [ 'Definition', ], 'members' => [ 'Definition' => [ 'shape' => 'VariableDefinitionList', ], ], ], 'IPSets' => [ 'type' => 'map', 'key' => [ 'shape' => 'RuleVariableName', ], 'value' => [ 'shape' => 'IPSet', ], ], 'InsufficientCapacityException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, 'fault' => true, ], 'InternalServerError' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, 'fault' => true, ], 'InvalidOperationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InvalidRequestException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InvalidResourcePolicyException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InvalidTokenException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'Keyword' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '.*', ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ListFirewallPoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'PaginationMaxResults', ], ], ], 'ListFirewallPoliciesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'FirewallPolicies' => [ 'shape' => 'FirewallPolicies', ], ], ], 'ListFirewallsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'VpcIds' => [ 'shape' => 'VpcIds', ], 'MaxResults' => [ 'shape' => 'PaginationMaxResults', ], ], ], 'ListFirewallsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'Firewalls' => [ 'shape' => 'Firewalls', ], ], ], 'ListRuleGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'PaginationMaxResults', ], 'Scope' => [ 'shape' => 'ResourceManagedStatus', ], ], ], 'ListRuleGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'RuleGroups' => [ 'shape' => 'RuleGroups', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'TagsPaginationMaxResults', ], 'ResourceArn' => [ 'shape' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'LogDestinationConfig' => [ 'type' => 'structure', 'required' => [ 'LogType', 'LogDestinationType', 'LogDestination', ], 'members' => [ 'LogType' => [ 'shape' => 'LogType', ], 'LogDestinationType' => [ 'shape' => 'LogDestinationType', ], 'LogDestination' => [ 'shape' => 'LogDestinationMap', ], ], ], 'LogDestinationConfigs' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogDestinationConfig', ], ], 'LogDestinationMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'HashMapKey', ], 'value' => [ 'shape' => 'HashMapValue', ], ], 'LogDestinationPermissionException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'LogDestinationType' => [ 'type' => 'string', 'enum' => [ 'S3', 'CloudWatchLogs', 'KinesisDataFirehose', ], 'max' => 30, 'min' => 2, 'pattern' => '[0-9A-Za-z]+', ], 'LogType' => [ 'type' => 'string', 'enum' => [ 'ALERT', 'FLOW', ], ], 'LoggingConfiguration' => [ 'type' => 'structure', 'required' => [ 'LogDestinationConfigs', ], 'members' => [ 'LogDestinationConfigs' => [ 'shape' => 'LogDestinationConfigs', ], ], ], 'MatchAttributes' => [ 'type' => 'structure', 'members' => [ 'Sources' => [ 'shape' => 'Addresses', ], 'Destinations' => [ 'shape' => 'Addresses', ], 'SourcePorts' => [ 'shape' => 'PortRanges', ], 'DestinationPorts' => [ 'shape' => 'PortRanges', ], 'Protocols' => [ 'shape' => 'ProtocolNumbers', ], 'TCPFlags' => [ 'shape' => 'TCPFlags', ], ], ], 'NumberOfAssociations' => [ 'type' => 'integer', ], 'OverrideAction' => [ 'type' => 'string', 'enum' => [ 'DROP_TO_ALERT', ], ], 'PaginationMaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'PaginationToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '[0-9A-Za-z:\\/+=]+$', ], 'PerObjectStatus' => [ 'type' => 'structure', 'members' => [ 'SyncStatus' => [ 'shape' => 'PerObjectSyncStatus', ], 'UpdateToken' => [ 'shape' => 'UpdateToken', ], ], ], 'PerObjectSyncStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'IN_SYNC', ], ], 'PolicyString' => [ 'type' => 'string', 'max' => 395000, 'min' => 1, 'pattern' => '.*\\S.*', ], 'Port' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^.*$', ], 'PortRange' => [ 'type' => 'structure', 'required' => [ 'FromPort', 'ToPort', ], 'members' => [ 'FromPort' => [ 'shape' => 'PortRangeBound', ], 'ToPort' => [ 'shape' => 'PortRangeBound', ], ], ], 'PortRangeBound' => [ 'type' => 'integer', 'max' => 65535, 'min' => 0, ], 'PortRanges' => [ 'type' => 'list', 'member' => [ 'shape' => 'PortRange', ], ], 'PortSet' => [ 'type' => 'structure', 'members' => [ 'Definition' => [ 'shape' => 'VariableDefinitionList', ], ], ], 'PortSets' => [ 'type' => 'map', 'key' => [ 'shape' => 'RuleVariableName', ], 'value' => [ 'shape' => 'PortSet', ], ], 'Priority' => [ 'type' => 'integer', 'max' => 65535, 'min' => 1, ], 'ProtocolNumber' => [ 'type' => 'integer', 'max' => 255, 'min' => 0, ], 'ProtocolNumbers' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProtocolNumber', ], ], 'PublishMetricAction' => [ 'type' => 'structure', 'required' => [ 'Dimensions', ], 'members' => [ 'Dimensions' => [ 'shape' => 'Dimensions', ], ], ], 'PutResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Policy', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'Policy' => [ 'shape' => 'PolicyString', ], ], ], 'PutResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'ResourceArn' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^arn:aws.*', ], 'ResourceId' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '^([0-9a-f]{8})-([0-9a-f]{4}-){3}([0-9a-f]{12})$', ], 'ResourceManagedStatus' => [ 'type' => 'string', 'enum' => [ 'MANAGED', 'ACCOUNT', ], ], 'ResourceName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-]+$', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResourceOwnerCheckException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResourceStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'DELETING', ], ], 'RuleCapacity' => [ 'type' => 'integer', ], 'RuleDefinition' => [ 'type' => 'structure', 'required' => [ 'MatchAttributes', 'Actions', ], 'members' => [ 'MatchAttributes' => [ 'shape' => 'MatchAttributes', ], 'Actions' => [ 'shape' => 'StatelessActions', ], ], ], 'RuleGroup' => [ 'type' => 'structure', 'required' => [ 'RulesSource', ], 'members' => [ 'RuleVariables' => [ 'shape' => 'RuleVariables', ], 'RulesSource' => [ 'shape' => 'RulesSource', ], 'StatefulRuleOptions' => [ 'shape' => 'StatefulRuleOptions', ], ], ], 'RuleGroupMetadata' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ResourceName', ], 'Arn' => [ 'shape' => 'ResourceArn', ], ], ], 'RuleGroupResponse' => [ 'type' => 'structure', 'required' => [ 'RuleGroupArn', 'RuleGroupName', 'RuleGroupId', ], 'members' => [ 'RuleGroupArn' => [ 'shape' => 'ResourceArn', ], 'RuleGroupName' => [ 'shape' => 'ResourceName', ], 'RuleGroupId' => [ 'shape' => 'ResourceId', ], 'Description' => [ 'shape' => 'Description', ], 'Type' => [ 'shape' => 'RuleGroupType', ], 'Capacity' => [ 'shape' => 'RuleCapacity', ], 'RuleGroupStatus' => [ 'shape' => 'ResourceStatus', ], 'Tags' => [ 'shape' => 'TagList', ], 'ConsumedCapacity' => [ 'shape' => 'RuleCapacity', ], 'NumberOfAssociations' => [ 'shape' => 'NumberOfAssociations', ], ], ], 'RuleGroupType' => [ 'type' => 'string', 'enum' => [ 'STATELESS', 'STATEFUL', ], ], 'RuleGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleGroupMetadata', ], ], 'RuleOption' => [ 'type' => 'structure', 'required' => [ 'Keyword', ], 'members' => [ 'Keyword' => [ 'shape' => 'Keyword', ], 'Settings' => [ 'shape' => 'Settings', ], ], ], 'RuleOptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'RuleOption', ], ], 'RuleOrder' => [ 'type' => 'string', 'enum' => [ 'DEFAULT_ACTION_ORDER', 'STRICT_ORDER', ], ], 'RuleTargets' => [ 'type' => 'list', 'member' => [ 'shape' => 'CollectionMember_String', ], ], 'RuleVariableName' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '^[A-Za-z][A-Za-z0-9_]*$', ], 'RuleVariables' => [ 'type' => 'structure', 'members' => [ 'IPSets' => [ 'shape' => 'IPSets', ], 'PortSets' => [ 'shape' => 'PortSets', ], ], ], 'RulesSource' => [ 'type' => 'structure', 'members' => [ 'RulesString' => [ 'shape' => 'RulesString', ], 'RulesSourceList' => [ 'shape' => 'RulesSourceList', ], 'StatefulRules' => [ 'shape' => 'StatefulRules', ], 'StatelessRulesAndCustomActions' => [ 'shape' => 'StatelessRulesAndCustomActions', ], ], ], 'RulesSourceList' => [ 'type' => 'structure', 'required' => [ 'Targets', 'TargetTypes', 'GeneratedRulesType', ], 'members' => [ 'Targets' => [ 'shape' => 'RuleTargets', ], 'TargetTypes' => [ 'shape' => 'TargetTypes', ], 'GeneratedRulesType' => [ 'shape' => 'GeneratedRulesType', ], ], ], 'RulesString' => [ 'type' => 'string', 'max' => 2000000, 'min' => 0, ], 'Setting' => [ 'type' => 'string', 'max' => 8192, 'min' => 1, 'pattern' => '.*', ], 'Settings' => [ 'type' => 'list', 'member' => [ 'shape' => 'Setting', ], ], 'Source' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^.*$', ], 'StatefulAction' => [ 'type' => 'string', 'enum' => [ 'PASS', 'DROP', 'ALERT', ], ], 'StatefulActions' => [ 'type' => 'list', 'member' => [ 'shape' => 'CollectionMember_String', ], ], 'StatefulEngineOptions' => [ 'type' => 'structure', 'members' => [ 'RuleOrder' => [ 'shape' => 'RuleOrder', ], ], ], 'StatefulRule' => [ 'type' => 'structure', 'required' => [ 'Action', 'Header', 'RuleOptions', ], 'members' => [ 'Action' => [ 'shape' => 'StatefulAction', ], 'Header' => [ 'shape' => 'Header', ], 'RuleOptions' => [ 'shape' => 'RuleOptions', ], ], ], 'StatefulRuleDirection' => [ 'type' => 'string', 'enum' => [ 'FORWARD', 'ANY', ], ], 'StatefulRuleGroupOverride' => [ 'type' => 'structure', 'members' => [ 'Action' => [ 'shape' => 'OverrideAction', ], ], ], 'StatefulRuleGroupReference' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'Priority' => [ 'shape' => 'Priority', 'box' => true, ], 'Override' => [ 'shape' => 'StatefulRuleGroupOverride', ], ], ], 'StatefulRuleGroupReferences' => [ 'type' => 'list', 'member' => [ 'shape' => 'StatefulRuleGroupReference', ], ], 'StatefulRuleOptions' => [ 'type' => 'structure', 'members' => [ 'RuleOrder' => [ 'shape' => 'RuleOrder', ], ], ], 'StatefulRuleProtocol' => [ 'type' => 'string', 'enum' => [ 'IP', 'TCP', 'UDP', 'ICMP', 'HTTP', 'FTP', 'TLS', 'SMB', 'DNS', 'DCERPC', 'SSH', 'SMTP', 'IMAP', 'MSN', 'KRB5', 'IKEV2', 'TFTP', 'NTP', 'DHCP', ], ], 'StatefulRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'StatefulRule', ], ], 'StatelessActions' => [ 'type' => 'list', 'member' => [ 'shape' => 'CollectionMember_String', ], ], 'StatelessRule' => [ 'type' => 'structure', 'required' => [ 'RuleDefinition', 'Priority', ], 'members' => [ 'RuleDefinition' => [ 'shape' => 'RuleDefinition', ], 'Priority' => [ 'shape' => 'Priority', ], ], ], 'StatelessRuleGroupReference' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Priority', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'Priority' => [ 'shape' => 'Priority', ], ], ], 'StatelessRuleGroupReferences' => [ 'type' => 'list', 'member' => [ 'shape' => 'StatelessRuleGroupReference', ], ], 'StatelessRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'StatelessRule', ], ], 'StatelessRulesAndCustomActions' => [ 'type' => 'structure', 'required' => [ 'StatelessRules', ], 'members' => [ 'StatelessRules' => [ 'shape' => 'StatelessRules', ], 'CustomActions' => [ 'shape' => 'CustomActions', ], ], ], 'SubnetMapping' => [ 'type' => 'structure', 'required' => [ 'SubnetId', ], 'members' => [ 'SubnetId' => [ 'shape' => 'CollectionMember_String', ], ], ], 'SubnetMappings' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetMapping', ], ], 'SyncState' => [ 'type' => 'structure', 'members' => [ 'Attachment' => [ 'shape' => 'Attachment', ], 'Config' => [ 'shape' => 'SyncStateConfig', ], ], ], 'SyncStateConfig' => [ 'type' => 'map', 'key' => [ 'shape' => 'ResourceName', ], 'value' => [ 'shape' => 'PerObjectStatus', ], ], 'SyncStates' => [ 'type' => 'map', 'key' => [ 'shape' => 'AvailabilityZone', ], 'value' => [ 'shape' => 'SyncState', ], ], 'TCPFlag' => [ 'type' => 'string', 'enum' => [ 'FIN', 'SYN', 'RST', 'PSH', 'ACK', 'URG', 'ECE', 'CWR', ], ], 'TCPFlagField' => [ 'type' => 'structure', 'required' => [ 'Flags', ], 'members' => [ 'Flags' => [ 'shape' => 'Flags', ], 'Masks' => [ 'shape' => 'Flags', ], ], ], 'TCPFlags' => [ 'type' => 'list', 'member' => [ 'shape' => 'TCPFlagField', ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^.*$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 1, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^.*$', ], 'TagsPaginationMaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'TargetType' => [ 'type' => 'string', 'enum' => [ 'TLS_SNI', 'HTTP_HOST', ], ], 'TargetTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetType', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'UnsupportedOperationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateFirewallDeleteProtectionRequest' => [ 'type' => 'structure', 'required' => [ 'DeleteProtection', ], 'members' => [ 'UpdateToken' => [ 'shape' => 'UpdateToken', ], 'FirewallArn' => [ 'shape' => 'ResourceArn', ], 'FirewallName' => [ 'shape' => 'ResourceName', ], 'DeleteProtection' => [ 'shape' => 'Boolean', ], ], ], 'UpdateFirewallDeleteProtectionResponse' => [ 'type' => 'structure', 'members' => [ 'FirewallArn' => [ 'shape' => 'ResourceArn', ], 'FirewallName' => [ 'shape' => 'ResourceName', ], 'DeleteProtection' => [ 'shape' => 'Boolean', ], 'UpdateToken' => [ 'shape' => 'UpdateToken', ], ], ], 'UpdateFirewallDescriptionRequest' => [ 'type' => 'structure', 'members' => [ 'UpdateToken' => [ 'shape' => 'UpdateToken', ], 'FirewallArn' => [ 'shape' => 'ResourceArn', ], 'FirewallName' => [ 'shape' => 'ResourceName', ], 'Description' => [ 'shape' => 'Description', ], ], ], 'UpdateFirewallDescriptionResponse' => [ 'type' => 'structure', 'members' => [ 'FirewallArn' => [ 'shape' => 'ResourceArn', ], 'FirewallName' => [ 'shape' => 'ResourceName', ], 'Description' => [ 'shape' => 'Description', ], 'UpdateToken' => [ 'shape' => 'UpdateToken', ], ], ], 'UpdateFirewallPolicyChangeProtectionRequest' => [ 'type' => 'structure', 'required' => [ 'FirewallPolicyChangeProtection', ], 'members' => [ 'UpdateToken' => [ 'shape' => 'UpdateToken', ], 'FirewallArn' => [ 'shape' => 'ResourceArn', ], 'FirewallName' => [ 'shape' => 'ResourceName', ], 'FirewallPolicyChangeProtection' => [ 'shape' => 'Boolean', ], ], ], 'UpdateFirewallPolicyChangeProtectionResponse' => [ 'type' => 'structure', 'members' => [ 'UpdateToken' => [ 'shape' => 'UpdateToken', ], 'FirewallArn' => [ 'shape' => 'ResourceArn', ], 'FirewallName' => [ 'shape' => 'ResourceName', ], 'FirewallPolicyChangeProtection' => [ 'shape' => 'Boolean', ], ], ], 'UpdateFirewallPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'UpdateToken', 'FirewallPolicy', ], 'members' => [ 'UpdateToken' => [ 'shape' => 'UpdateToken', ], 'FirewallPolicyArn' => [ 'shape' => 'ResourceArn', ], 'FirewallPolicyName' => [ 'shape' => 'ResourceName', ], 'FirewallPolicy' => [ 'shape' => 'FirewallPolicy', ], 'Description' => [ 'shape' => 'Description', ], 'DryRun' => [ 'shape' => 'Boolean', ], ], ], 'UpdateFirewallPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'UpdateToken', 'FirewallPolicyResponse', ], 'members' => [ 'UpdateToken' => [ 'shape' => 'UpdateToken', ], 'FirewallPolicyResponse' => [ 'shape' => 'FirewallPolicyResponse', ], ], ], 'UpdateLoggingConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'FirewallArn' => [ 'shape' => 'ResourceArn', ], 'FirewallName' => [ 'shape' => 'ResourceName', ], 'LoggingConfiguration' => [ 'shape' => 'LoggingConfiguration', ], ], ], 'UpdateLoggingConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'FirewallArn' => [ 'shape' => 'ResourceArn', ], 'FirewallName' => [ 'shape' => 'ResourceName', ], 'LoggingConfiguration' => [ 'shape' => 'LoggingConfiguration', ], ], ], 'UpdateRuleGroupRequest' => [ 'type' => 'structure', 'required' => [ 'UpdateToken', ], 'members' => [ 'UpdateToken' => [ 'shape' => 'UpdateToken', ], 'RuleGroupArn' => [ 'shape' => 'ResourceArn', ], 'RuleGroupName' => [ 'shape' => 'ResourceName', ], 'RuleGroup' => [ 'shape' => 'RuleGroup', ], 'Rules' => [ 'shape' => 'RulesString', ], 'Type' => [ 'shape' => 'RuleGroupType', ], 'Description' => [ 'shape' => 'Description', ], 'DryRun' => [ 'shape' => 'Boolean', ], ], ], 'UpdateRuleGroupResponse' => [ 'type' => 'structure', 'required' => [ 'UpdateToken', 'RuleGroupResponse', ], 'members' => [ 'UpdateToken' => [ 'shape' => 'UpdateToken', ], 'RuleGroupResponse' => [ 'shape' => 'RuleGroupResponse', ], ], ], 'UpdateSubnetChangeProtectionRequest' => [ 'type' => 'structure', 'required' => [ 'SubnetChangeProtection', ], 'members' => [ 'UpdateToken' => [ 'shape' => 'UpdateToken', ], 'FirewallArn' => [ 'shape' => 'ResourceArn', ], 'FirewallName' => [ 'shape' => 'ResourceName', ], 'SubnetChangeProtection' => [ 'shape' => 'Boolean', ], ], ], 'UpdateSubnetChangeProtectionResponse' => [ 'type' => 'structure', 'members' => [ 'UpdateToken' => [ 'shape' => 'UpdateToken', ], 'FirewallArn' => [ 'shape' => 'ResourceArn', ], 'FirewallName' => [ 'shape' => 'ResourceName', ], 'SubnetChangeProtection' => [ 'shape' => 'Boolean', ], ], ], 'UpdateToken' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^([0-9a-f]{8})-([0-9a-f]{4}-){3}([0-9a-f]{12})$', ], 'VariableDefinition' => [ 'type' => 'string', 'min' => 1, 'pattern' => '^.*$', ], 'VariableDefinitionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VariableDefinition', ], ], 'VpcId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^vpc-[0-9a-f]+$', ], 'VpcIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'VpcId', ], ], ],];
