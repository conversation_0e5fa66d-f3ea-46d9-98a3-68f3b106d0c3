<?php
// This file was auto-generated from sdk-root/src/data/s3control/2018-08-20/paginators-1.json
return [ 'pagination' => [ 'ListAccessPoints' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', ], 'ListAccessPointsForObjectLambda' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'ObjectLambdaAccessPointList', ], 'ListJobs' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', ], 'ListMultiRegionAccessPoints' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', ], 'ListRegionalBuckets' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', ], 'ListStorageLensConfigurations' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', ], ],];
