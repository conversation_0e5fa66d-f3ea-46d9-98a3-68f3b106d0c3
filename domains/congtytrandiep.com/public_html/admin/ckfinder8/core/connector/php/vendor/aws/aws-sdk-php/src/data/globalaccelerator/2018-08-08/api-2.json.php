<?php
// This file was auto-generated from sdk-root/src/data/globalaccelerator/2018-08-08/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-08-08', 'endpointPrefix' => 'globalaccelerator', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceFullName' => 'AWS Global Accelerator', 'serviceId' => 'Global Accelerator', 'signatureVersion' => 'v4', 'signingName' => 'globalaccelerator', 'targetPrefix' => 'GlobalAccelerator_V20180706', 'uid' => 'globalaccelerator-2018-08-08', ], 'operations' => [ 'AddCustomRoutingEndpoints' => [ 'name' => 'AddCustomRoutingEndpoints', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddCustomRoutingEndpointsRequest', ], 'output' => [ 'shape' => 'AddCustomRoutingEndpointsResponse', ], 'errors' => [ [ 'shape' => 'EndpointAlreadyExistsException', ], [ 'shape' => 'EndpointGroupNotFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'AdvertiseByoipCidr' => [ 'name' => 'AdvertiseByoipCidr', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AdvertiseByoipCidrRequest', ], 'output' => [ 'shape' => 'AdvertiseByoipCidrResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ByoipCidrNotFoundException', ], [ 'shape' => 'IncorrectCidrStateException', ], ], ], 'AllowCustomRoutingTraffic' => [ 'name' => 'AllowCustomRoutingTraffic', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AllowCustomRoutingTrafficRequest', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'InternalServiceErrorException', ], ], ], 'CreateAccelerator' => [ 'name' => 'CreateAccelerator', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAcceleratorRequest', ], 'output' => [ 'shape' => 'CreateAcceleratorResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateCustomRoutingAccelerator' => [ 'name' => 'CreateCustomRoutingAccelerator', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateCustomRoutingAcceleratorRequest', ], 'output' => [ 'shape' => 'CreateCustomRoutingAcceleratorResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateCustomRoutingEndpointGroup' => [ 'name' => 'CreateCustomRoutingEndpointGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateCustomRoutingEndpointGroupRequest', ], 'output' => [ 'shape' => 'CreateCustomRoutingEndpointGroupResponse', ], 'errors' => [ [ 'shape' => 'AcceleratorNotFoundException', ], [ 'shape' => 'EndpointGroupAlreadyExistsException', ], [ 'shape' => 'ListenerNotFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'InvalidPortRangeException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateCustomRoutingListener' => [ 'name' => 'CreateCustomRoutingListener', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateCustomRoutingListenerRequest', ], 'output' => [ 'shape' => 'CreateCustomRoutingListenerResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'AcceleratorNotFoundException', ], [ 'shape' => 'InvalidPortRangeException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateEndpointGroup' => [ 'name' => 'CreateEndpointGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateEndpointGroupRequest', ], 'output' => [ 'shape' => 'CreateEndpointGroupResponse', ], 'errors' => [ [ 'shape' => 'AcceleratorNotFoundException', ], [ 'shape' => 'EndpointGroupAlreadyExistsException', ], [ 'shape' => 'ListenerNotFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateListener' => [ 'name' => 'CreateListener', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateListenerRequest', ], 'output' => [ 'shape' => 'CreateListenerResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'AcceleratorNotFoundException', ], [ 'shape' => 'InvalidPortRangeException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'DeleteAccelerator' => [ 'name' => 'DeleteAccelerator', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAcceleratorRequest', ], 'errors' => [ [ 'shape' => 'AcceleratorNotFoundException', ], [ 'shape' => 'AcceleratorNotDisabledException', ], [ 'shape' => 'AssociatedListenerFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'InvalidArgumentException', ], ], ], 'DeleteCustomRoutingAccelerator' => [ 'name' => 'DeleteCustomRoutingAccelerator', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCustomRoutingAcceleratorRequest', ], 'errors' => [ [ 'shape' => 'AcceleratorNotFoundException', ], [ 'shape' => 'AcceleratorNotDisabledException', ], [ 'shape' => 'AssociatedListenerFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'InvalidArgumentException', ], ], ], 'DeleteCustomRoutingEndpointGroup' => [ 'name' => 'DeleteCustomRoutingEndpointGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCustomRoutingEndpointGroupRequest', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'EndpointGroupNotFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], ], ], 'DeleteCustomRoutingListener' => [ 'name' => 'DeleteCustomRoutingListener', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCustomRoutingListenerRequest', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'ListenerNotFoundException', ], [ 'shape' => 'AssociatedEndpointGroupFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], ], ], 'DeleteEndpointGroup' => [ 'name' => 'DeleteEndpointGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEndpointGroupRequest', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'EndpointGroupNotFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], ], ], 'DeleteListener' => [ 'name' => 'DeleteListener', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteListenerRequest', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'ListenerNotFoundException', ], [ 'shape' => 'AssociatedEndpointGroupFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], ], ], 'DenyCustomRoutingTraffic' => [ 'name' => 'DenyCustomRoutingTraffic', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DenyCustomRoutingTrafficRequest', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'InternalServiceErrorException', ], ], ], 'DeprovisionByoipCidr' => [ 'name' => 'DeprovisionByoipCidr', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeprovisionByoipCidrRequest', ], 'output' => [ 'shape' => 'DeprovisionByoipCidrResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ByoipCidrNotFoundException', ], [ 'shape' => 'IncorrectCidrStateException', ], ], ], 'DescribeAccelerator' => [ 'name' => 'DescribeAccelerator', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAcceleratorRequest', ], 'output' => [ 'shape' => 'DescribeAcceleratorResponse', ], 'errors' => [ [ 'shape' => 'AcceleratorNotFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'InvalidArgumentException', ], ], ], 'DescribeAcceleratorAttributes' => [ 'name' => 'DescribeAcceleratorAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAcceleratorAttributesRequest', ], 'output' => [ 'shape' => 'DescribeAcceleratorAttributesResponse', ], 'errors' => [ [ 'shape' => 'AcceleratorNotFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'InvalidArgumentException', ], ], ], 'DescribeCustomRoutingAccelerator' => [ 'name' => 'DescribeCustomRoutingAccelerator', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeCustomRoutingAcceleratorRequest', ], 'output' => [ 'shape' => 'DescribeCustomRoutingAcceleratorResponse', ], 'errors' => [ [ 'shape' => 'AcceleratorNotFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'InvalidArgumentException', ], ], ], 'DescribeCustomRoutingAcceleratorAttributes' => [ 'name' => 'DescribeCustomRoutingAcceleratorAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeCustomRoutingAcceleratorAttributesRequest', ], 'output' => [ 'shape' => 'DescribeCustomRoutingAcceleratorAttributesResponse', ], 'errors' => [ [ 'shape' => 'AcceleratorNotFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'InvalidArgumentException', ], ], ], 'DescribeCustomRoutingEndpointGroup' => [ 'name' => 'DescribeCustomRoutingEndpointGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeCustomRoutingEndpointGroupRequest', ], 'output' => [ 'shape' => 'DescribeCustomRoutingEndpointGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'EndpointGroupNotFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], ], ], 'DescribeCustomRoutingListener' => [ 'name' => 'DescribeCustomRoutingListener', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeCustomRoutingListenerRequest', ], 'output' => [ 'shape' => 'DescribeCustomRoutingListenerResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'ListenerNotFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], ], ], 'DescribeEndpointGroup' => [ 'name' => 'DescribeEndpointGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEndpointGroupRequest', ], 'output' => [ 'shape' => 'DescribeEndpointGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'EndpointGroupNotFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], ], ], 'DescribeListener' => [ 'name' => 'DescribeListener', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeListenerRequest', ], 'output' => [ 'shape' => 'DescribeListenerResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'ListenerNotFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], ], ], 'ListAccelerators' => [ 'name' => 'ListAccelerators', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAcceleratorsRequest', ], 'output' => [ 'shape' => 'ListAcceleratorsResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InternalServiceErrorException', ], ], ], 'ListByoipCidrs' => [ 'name' => 'ListByoipCidrs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListByoipCidrsRequest', ], 'output' => [ 'shape' => 'ListByoipCidrsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidNextTokenException', ], ], ], 'ListCustomRoutingAccelerators' => [ 'name' => 'ListCustomRoutingAccelerators', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCustomRoutingAcceleratorsRequest', ], 'output' => [ 'shape' => 'ListCustomRoutingAcceleratorsResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InternalServiceErrorException', ], ], ], 'ListCustomRoutingEndpointGroups' => [ 'name' => 'ListCustomRoutingEndpointGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCustomRoutingEndpointGroupsRequest', ], 'output' => [ 'shape' => 'ListCustomRoutingEndpointGroupsResponse', ], 'errors' => [ [ 'shape' => 'ListenerNotFoundException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'InternalServiceErrorException', ], ], ], 'ListCustomRoutingListeners' => [ 'name' => 'ListCustomRoutingListeners', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCustomRoutingListenersRequest', ], 'output' => [ 'shape' => 'ListCustomRoutingListenersResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'AcceleratorNotFoundException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InternalServiceErrorException', ], ], ], 'ListCustomRoutingPortMappings' => [ 'name' => 'ListCustomRoutingPortMappings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCustomRoutingPortMappingsRequest', ], 'output' => [ 'shape' => 'ListCustomRoutingPortMappingsResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'EndpointGroupNotFoundException', ], [ 'shape' => 'AcceleratorNotFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], ], ], 'ListCustomRoutingPortMappingsByDestination' => [ 'name' => 'ListCustomRoutingPortMappingsByDestination', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCustomRoutingPortMappingsByDestinationRequest', ], 'output' => [ 'shape' => 'ListCustomRoutingPortMappingsByDestinationResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'EndpointNotFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], ], ], 'ListEndpointGroups' => [ 'name' => 'ListEndpointGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEndpointGroupsRequest', ], 'output' => [ 'shape' => 'ListEndpointGroupsResponse', ], 'errors' => [ [ 'shape' => 'ListenerNotFoundException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'InternalServiceErrorException', ], ], ], 'ListListeners' => [ 'name' => 'ListListeners', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListListenersRequest', ], 'output' => [ 'shape' => 'ListListenersResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'AcceleratorNotFoundException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InternalServiceErrorException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'AcceleratorNotFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'InvalidArgumentException', ], ], ], 'ProvisionByoipCidr' => [ 'name' => 'ProvisionByoipCidr', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ProvisionByoipCidrRequest', ], 'output' => [ 'shape' => 'ProvisionByoipCidrResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'IncorrectCidrStateException', ], ], ], 'RemoveCustomRoutingEndpoints' => [ 'name' => 'RemoveCustomRoutingEndpoints', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveCustomRoutingEndpointsRequest', ], 'errors' => [ [ 'shape' => 'EndpointGroupNotFoundException', ], [ 'shape' => 'EndpointNotFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'AcceleratorNotFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'InvalidArgumentException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'AcceleratorNotFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'InvalidArgumentException', ], ], ], 'UpdateAccelerator' => [ 'name' => 'UpdateAccelerator', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateAcceleratorRequest', ], 'output' => [ 'shape' => 'UpdateAcceleratorResponse', ], 'errors' => [ [ 'shape' => 'AcceleratorNotFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'InvalidArgumentException', ], ], ], 'UpdateAcceleratorAttributes' => [ 'name' => 'UpdateAcceleratorAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateAcceleratorAttributesRequest', ], 'output' => [ 'shape' => 'UpdateAcceleratorAttributesResponse', ], 'errors' => [ [ 'shape' => 'AcceleratorNotFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateCustomRoutingAccelerator' => [ 'name' => 'UpdateCustomRoutingAccelerator', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateCustomRoutingAcceleratorRequest', ], 'output' => [ 'shape' => 'UpdateCustomRoutingAcceleratorResponse', ], 'errors' => [ [ 'shape' => 'AcceleratorNotFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'InvalidArgumentException', ], ], ], 'UpdateCustomRoutingAcceleratorAttributes' => [ 'name' => 'UpdateCustomRoutingAcceleratorAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateCustomRoutingAcceleratorAttributesRequest', ], 'output' => [ 'shape' => 'UpdateCustomRoutingAcceleratorAttributesResponse', ], 'errors' => [ [ 'shape' => 'AcceleratorNotFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateCustomRoutingListener' => [ 'name' => 'UpdateCustomRoutingListener', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateCustomRoutingListenerRequest', ], 'output' => [ 'shape' => 'UpdateCustomRoutingListenerResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'InvalidPortRangeException', ], [ 'shape' => 'ListenerNotFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'UpdateEndpointGroup' => [ 'name' => 'UpdateEndpointGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateEndpointGroupRequest', ], 'output' => [ 'shape' => 'UpdateEndpointGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'EndpointGroupNotFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateListener' => [ 'name' => 'UpdateListener', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateListenerRequest', ], 'output' => [ 'shape' => 'UpdateListenerResponse', ], 'errors' => [ [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'InvalidPortRangeException', ], [ 'shape' => 'ListenerNotFoundException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'WithdrawByoipCidr' => [ 'name' => 'WithdrawByoipCidr', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'WithdrawByoipCidrRequest', ], 'output' => [ 'shape' => 'WithdrawByoipCidrResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'InvalidArgumentException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ByoipCidrNotFoundException', ], [ 'shape' => 'IncorrectCidrStateException', ], ], ], ], 'shapes' => [ 'Accelerator' => [ 'type' => 'structure', 'members' => [ 'AcceleratorArn' => [ 'shape' => 'GenericString', ], 'Name' => [ 'shape' => 'GenericString', ], 'IpAddressType' => [ 'shape' => 'IpAddressType', ], 'Enabled' => [ 'shape' => 'GenericBoolean', ], 'IpSets' => [ 'shape' => 'IpSets', ], 'DnsName' => [ 'shape' => 'GenericString', ], 'Status' => [ 'shape' => 'AcceleratorStatus', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'AcceleratorAttributes' => [ 'type' => 'structure', 'members' => [ 'FlowLogsEnabled' => [ 'shape' => 'GenericBoolean', ], 'FlowLogsS3Bucket' => [ 'shape' => 'GenericString', ], 'FlowLogsS3Prefix' => [ 'shape' => 'GenericString', ], ], ], 'AcceleratorNotDisabledException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'AcceleratorNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'AcceleratorStatus' => [ 'type' => 'string', 'enum' => [ 'DEPLOYED', 'IN_PROGRESS', ], ], 'Accelerators' => [ 'type' => 'list', 'member' => [ 'shape' => 'Accelerator', ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'AddCustomRoutingEndpointsRequest' => [ 'type' => 'structure', 'required' => [ 'EndpointConfigurations', 'EndpointGroupArn', ], 'members' => [ 'EndpointConfigurations' => [ 'shape' => 'CustomRoutingEndpointConfigurations', ], 'EndpointGroupArn' => [ 'shape' => 'GenericString', ], ], ], 'AddCustomRoutingEndpointsResponse' => [ 'type' => 'structure', 'members' => [ 'EndpointDescriptions' => [ 'shape' => 'CustomRoutingEndpointDescriptions', ], 'EndpointGroupArn' => [ 'shape' => 'GenericString', ], ], ], 'AdvertiseByoipCidrRequest' => [ 'type' => 'structure', 'required' => [ 'Cidr', ], 'members' => [ 'Cidr' => [ 'shape' => 'GenericString', ], ], ], 'AdvertiseByoipCidrResponse' => [ 'type' => 'structure', 'members' => [ 'ByoipCidr' => [ 'shape' => 'ByoipCidr', ], ], ], 'AllowCustomRoutingTrafficRequest' => [ 'type' => 'structure', 'required' => [ 'EndpointGroupArn', 'EndpointId', ], 'members' => [ 'EndpointGroupArn' => [ 'shape' => 'GenericString', ], 'EndpointId' => [ 'shape' => 'GenericString', ], 'DestinationAddresses' => [ 'shape' => 'DestinationAddresses', ], 'DestinationPorts' => [ 'shape' => 'DestinationPorts', ], 'AllowAllTrafficToEndpoint' => [ 'shape' => 'GenericBoolean', ], ], ], 'AssociatedEndpointGroupFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'AssociatedListenerFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ByoipCidr' => [ 'type' => 'structure', 'members' => [ 'Cidr' => [ 'shape' => 'GenericString', ], 'State' => [ 'shape' => 'ByoipCidrState', ], 'Events' => [ 'shape' => 'ByoipCidrEvents', ], ], ], 'ByoipCidrEvent' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'GenericString', ], 'Timestamp' => [ 'shape' => 'Timestamp', ], ], ], 'ByoipCidrEvents' => [ 'type' => 'list', 'member' => [ 'shape' => 'ByoipCidrEvent', ], ], 'ByoipCidrNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ByoipCidrState' => [ 'type' => 'string', 'enum' => [ 'PENDING_PROVISIONING', 'READY', 'PENDING_ADVERTISING', 'ADVERTISING', 'PENDING_WITHDRAWING', 'PENDING_DEPROVISIONING', 'DEPROVISIONED', 'FAILED_PROVISION', 'FAILED_ADVERTISING', 'FAILED_WITHDRAW', 'FAILED_DEPROVISION', ], ], 'ByoipCidrs' => [ 'type' => 'list', 'member' => [ 'shape' => 'ByoipCidr', ], ], 'CidrAuthorizationContext' => [ 'type' => 'structure', 'required' => [ 'Message', 'Signature', ], 'members' => [ 'Message' => [ 'shape' => 'GenericString', ], 'Signature' => [ 'shape' => 'GenericString', ], ], ], 'ClientAffinity' => [ 'type' => 'string', 'enum' => [ 'NONE', 'SOURCE_IP', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'CreateAcceleratorRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'IdempotencyToken', ], 'members' => [ 'Name' => [ 'shape' => 'GenericString', ], 'IpAddressType' => [ 'shape' => 'IpAddressType', ], 'IpAddresses' => [ 'shape' => 'IpAddresses', ], 'Enabled' => [ 'shape' => 'GenericBoolean', ], 'IdempotencyToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateAcceleratorResponse' => [ 'type' => 'structure', 'members' => [ 'Accelerator' => [ 'shape' => 'Accelerator', ], ], ], 'CreateCustomRoutingAcceleratorRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'IdempotencyToken', ], 'members' => [ 'Name' => [ 'shape' => 'GenericString', ], 'IpAddressType' => [ 'shape' => 'IpAddressType', ], 'IpAddresses' => [ 'shape' => 'IpAddresses', ], 'Enabled' => [ 'shape' => 'GenericBoolean', ], 'IdempotencyToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateCustomRoutingAcceleratorResponse' => [ 'type' => 'structure', 'members' => [ 'Accelerator' => [ 'shape' => 'CustomRoutingAccelerator', ], ], ], 'CreateCustomRoutingEndpointGroupRequest' => [ 'type' => 'structure', 'required' => [ 'ListenerArn', 'EndpointGroupRegion', 'DestinationConfigurations', 'IdempotencyToken', ], 'members' => [ 'ListenerArn' => [ 'shape' => 'GenericString', ], 'EndpointGroupRegion' => [ 'shape' => 'GenericString', ], 'DestinationConfigurations' => [ 'shape' => 'CustomRoutingDestinationConfigurations', ], 'IdempotencyToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], ], ], 'CreateCustomRoutingEndpointGroupResponse' => [ 'type' => 'structure', 'members' => [ 'EndpointGroup' => [ 'shape' => 'CustomRoutingEndpointGroup', ], ], ], 'CreateCustomRoutingListenerRequest' => [ 'type' => 'structure', 'required' => [ 'AcceleratorArn', 'PortRanges', 'IdempotencyToken', ], 'members' => [ 'AcceleratorArn' => [ 'shape' => 'GenericString', ], 'PortRanges' => [ 'shape' => 'PortRanges', ], 'IdempotencyToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], ], ], 'CreateCustomRoutingListenerResponse' => [ 'type' => 'structure', 'members' => [ 'Listener' => [ 'shape' => 'CustomRoutingListener', ], ], ], 'CreateEndpointGroupRequest' => [ 'type' => 'structure', 'required' => [ 'ListenerArn', 'EndpointGroupRegion', 'IdempotencyToken', ], 'members' => [ 'ListenerArn' => [ 'shape' => 'GenericString', ], 'EndpointGroupRegion' => [ 'shape' => 'GenericString', ], 'EndpointConfigurations' => [ 'shape' => 'EndpointConfigurations', ], 'TrafficDialPercentage' => [ 'shape' => 'TrafficDialPercentage', ], 'HealthCheckPort' => [ 'shape' => 'HealthCheckPort', ], 'HealthCheckProtocol' => [ 'shape' => 'HealthCheckProtocol', ], 'HealthCheckPath' => [ 'shape' => 'HealthCheckPath', ], 'HealthCheckIntervalSeconds' => [ 'shape' => 'HealthCheckIntervalSeconds', ], 'ThresholdCount' => [ 'shape' => 'ThresholdCount', ], 'IdempotencyToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'PortOverrides' => [ 'shape' => 'PortOverrides', ], ], ], 'CreateEndpointGroupResponse' => [ 'type' => 'structure', 'members' => [ 'EndpointGroup' => [ 'shape' => 'EndpointGroup', ], ], ], 'CreateListenerRequest' => [ 'type' => 'structure', 'required' => [ 'AcceleratorArn', 'PortRanges', 'Protocol', 'IdempotencyToken', ], 'members' => [ 'AcceleratorArn' => [ 'shape' => 'GenericString', ], 'PortRanges' => [ 'shape' => 'PortRanges', ], 'Protocol' => [ 'shape' => 'Protocol', ], 'ClientAffinity' => [ 'shape' => 'ClientAffinity', ], 'IdempotencyToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], ], ], 'CreateListenerResponse' => [ 'type' => 'structure', 'members' => [ 'Listener' => [ 'shape' => 'Listener', ], ], ], 'CustomRoutingAccelerator' => [ 'type' => 'structure', 'members' => [ 'AcceleratorArn' => [ 'shape' => 'GenericString', ], 'Name' => [ 'shape' => 'GenericString', ], 'IpAddressType' => [ 'shape' => 'IpAddressType', ], 'Enabled' => [ 'shape' => 'GenericBoolean', ], 'IpSets' => [ 'shape' => 'IpSets', ], 'DnsName' => [ 'shape' => 'GenericString', ], 'Status' => [ 'shape' => 'CustomRoutingAcceleratorStatus', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'CustomRoutingAcceleratorAttributes' => [ 'type' => 'structure', 'members' => [ 'FlowLogsEnabled' => [ 'shape' => 'GenericBoolean', ], 'FlowLogsS3Bucket' => [ 'shape' => 'GenericString', ], 'FlowLogsS3Prefix' => [ 'shape' => 'GenericString', ], ], ], 'CustomRoutingAcceleratorStatus' => [ 'type' => 'string', 'enum' => [ 'DEPLOYED', 'IN_PROGRESS', ], ], 'CustomRoutingAccelerators' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomRoutingAccelerator', ], ], 'CustomRoutingDestinationConfiguration' => [ 'type' => 'structure', 'required' => [ 'FromPort', 'ToPort', 'Protocols', ], 'members' => [ 'FromPort' => [ 'shape' => 'PortNumber', ], 'ToPort' => [ 'shape' => 'PortNumber', ], 'Protocols' => [ 'shape' => 'CustomRoutingProtocols', ], ], ], 'CustomRoutingDestinationConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomRoutingDestinationConfiguration', ], 'max' => 100, 'min' => 1, ], 'CustomRoutingDestinationDescription' => [ 'type' => 'structure', 'members' => [ 'FromPort' => [ 'shape' => 'PortNumber', ], 'ToPort' => [ 'shape' => 'PortNumber', ], 'Protocols' => [ 'shape' => 'Protocols', ], ], ], 'CustomRoutingDestinationDescriptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomRoutingDestinationDescription', ], ], 'CustomRoutingDestinationTrafficState' => [ 'type' => 'string', 'enum' => [ 'ALLOW', 'DENY', ], ], 'CustomRoutingEndpointConfiguration' => [ 'type' => 'structure', 'members' => [ 'EndpointId' => [ 'shape' => 'GenericString', ], ], ], 'CustomRoutingEndpointConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomRoutingEndpointConfiguration', ], 'max' => 20, 'min' => 1, ], 'CustomRoutingEndpointDescription' => [ 'type' => 'structure', 'members' => [ 'EndpointId' => [ 'shape' => 'GenericString', ], ], ], 'CustomRoutingEndpointDescriptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomRoutingEndpointDescription', ], ], 'CustomRoutingEndpointGroup' => [ 'type' => 'structure', 'members' => [ 'EndpointGroupArn' => [ 'shape' => 'GenericString', ], 'EndpointGroupRegion' => [ 'shape' => 'GenericString', ], 'DestinationDescriptions' => [ 'shape' => 'CustomRoutingDestinationDescriptions', ], 'EndpointDescriptions' => [ 'shape' => 'CustomRoutingEndpointDescriptions', ], ], ], 'CustomRoutingEndpointGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomRoutingEndpointGroup', ], ], 'CustomRoutingListener' => [ 'type' => 'structure', 'members' => [ 'ListenerArn' => [ 'shape' => 'GenericString', ], 'PortRanges' => [ 'shape' => 'PortRanges', ], ], ], 'CustomRoutingListeners' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomRoutingListener', ], ], 'CustomRoutingProtocol' => [ 'type' => 'string', 'enum' => [ 'TCP', 'UDP', ], ], 'CustomRoutingProtocols' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomRoutingProtocol', ], 'max' => 2, 'min' => 1, ], 'DeleteAcceleratorRequest' => [ 'type' => 'structure', 'required' => [ 'AcceleratorArn', ], 'members' => [ 'AcceleratorArn' => [ 'shape' => 'GenericString', ], ], ], 'DeleteCustomRoutingAcceleratorRequest' => [ 'type' => 'structure', 'required' => [ 'AcceleratorArn', ], 'members' => [ 'AcceleratorArn' => [ 'shape' => 'GenericString', ], ], ], 'DeleteCustomRoutingEndpointGroupRequest' => [ 'type' => 'structure', 'required' => [ 'EndpointGroupArn', ], 'members' => [ 'EndpointGroupArn' => [ 'shape' => 'GenericString', ], ], ], 'DeleteCustomRoutingListenerRequest' => [ 'type' => 'structure', 'required' => [ 'ListenerArn', ], 'members' => [ 'ListenerArn' => [ 'shape' => 'GenericString', ], ], ], 'DeleteEndpointGroupRequest' => [ 'type' => 'structure', 'required' => [ 'EndpointGroupArn', ], 'members' => [ 'EndpointGroupArn' => [ 'shape' => 'GenericString', ], ], ], 'DeleteListenerRequest' => [ 'type' => 'structure', 'required' => [ 'ListenerArn', ], 'members' => [ 'ListenerArn' => [ 'shape' => 'GenericString', ], ], ], 'DenyCustomRoutingTrafficRequest' => [ 'type' => 'structure', 'required' => [ 'EndpointGroupArn', 'EndpointId', ], 'members' => [ 'EndpointGroupArn' => [ 'shape' => 'GenericString', ], 'EndpointId' => [ 'shape' => 'GenericString', ], 'DestinationAddresses' => [ 'shape' => 'DestinationAddresses', ], 'DestinationPorts' => [ 'shape' => 'DestinationPorts', ], 'DenyAllTrafficToEndpoint' => [ 'shape' => 'GenericBoolean', ], ], ], 'DeprovisionByoipCidrRequest' => [ 'type' => 'structure', 'required' => [ 'Cidr', ], 'members' => [ 'Cidr' => [ 'shape' => 'GenericString', ], ], ], 'DeprovisionByoipCidrResponse' => [ 'type' => 'structure', 'members' => [ 'ByoipCidr' => [ 'shape' => 'ByoipCidr', ], ], ], 'DescribeAcceleratorAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'AcceleratorArn', ], 'members' => [ 'AcceleratorArn' => [ 'shape' => 'GenericString', ], ], ], 'DescribeAcceleratorAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'AcceleratorAttributes' => [ 'shape' => 'AcceleratorAttributes', ], ], ], 'DescribeAcceleratorRequest' => [ 'type' => 'structure', 'required' => [ 'AcceleratorArn', ], 'members' => [ 'AcceleratorArn' => [ 'shape' => 'GenericString', ], ], ], 'DescribeAcceleratorResponse' => [ 'type' => 'structure', 'members' => [ 'Accelerator' => [ 'shape' => 'Accelerator', ], ], ], 'DescribeCustomRoutingAcceleratorAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'AcceleratorArn', ], 'members' => [ 'AcceleratorArn' => [ 'shape' => 'GenericString', ], ], ], 'DescribeCustomRoutingAcceleratorAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'AcceleratorAttributes' => [ 'shape' => 'CustomRoutingAcceleratorAttributes', ], ], ], 'DescribeCustomRoutingAcceleratorRequest' => [ 'type' => 'structure', 'required' => [ 'AcceleratorArn', ], 'members' => [ 'AcceleratorArn' => [ 'shape' => 'GenericString', ], ], ], 'DescribeCustomRoutingAcceleratorResponse' => [ 'type' => 'structure', 'members' => [ 'Accelerator' => [ 'shape' => 'CustomRoutingAccelerator', ], ], ], 'DescribeCustomRoutingEndpointGroupRequest' => [ 'type' => 'structure', 'required' => [ 'EndpointGroupArn', ], 'members' => [ 'EndpointGroupArn' => [ 'shape' => 'GenericString', ], ], ], 'DescribeCustomRoutingEndpointGroupResponse' => [ 'type' => 'structure', 'members' => [ 'EndpointGroup' => [ 'shape' => 'CustomRoutingEndpointGroup', ], ], ], 'DescribeCustomRoutingListenerRequest' => [ 'type' => 'structure', 'required' => [ 'ListenerArn', ], 'members' => [ 'ListenerArn' => [ 'shape' => 'GenericString', ], ], ], 'DescribeCustomRoutingListenerResponse' => [ 'type' => 'structure', 'members' => [ 'Listener' => [ 'shape' => 'CustomRoutingListener', ], ], ], 'DescribeEndpointGroupRequest' => [ 'type' => 'structure', 'required' => [ 'EndpointGroupArn', ], 'members' => [ 'EndpointGroupArn' => [ 'shape' => 'GenericString', ], ], ], 'DescribeEndpointGroupResponse' => [ 'type' => 'structure', 'members' => [ 'EndpointGroup' => [ 'shape' => 'EndpointGroup', ], ], ], 'DescribeListenerRequest' => [ 'type' => 'structure', 'required' => [ 'ListenerArn', ], 'members' => [ 'ListenerArn' => [ 'shape' => 'GenericString', ], ], ], 'DescribeListenerResponse' => [ 'type' => 'structure', 'members' => [ 'Listener' => [ 'shape' => 'Listener', ], ], ], 'DestinationAddresses' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpAddress', ], 'max' => 100, ], 'DestinationPortMapping' => [ 'type' => 'structure', 'members' => [ 'AcceleratorArn' => [ 'shape' => 'GenericString', ], 'AcceleratorSocketAddresses' => [ 'shape' => 'SocketAddresses', ], 'EndpointGroupArn' => [ 'shape' => 'GenericString', ], 'EndpointId' => [ 'shape' => 'GenericString', ], 'EndpointGroupRegion' => [ 'shape' => 'GenericString', ], 'DestinationSocketAddress' => [ 'shape' => 'SocketAddress', ], 'IpAddressType' => [ 'shape' => 'IpAddressType', ], 'DestinationTrafficState' => [ 'shape' => 'CustomRoutingDestinationTrafficState', ], ], ], 'DestinationPortMappings' => [ 'type' => 'list', 'member' => [ 'shape' => 'DestinationPortMapping', ], ], 'DestinationPorts' => [ 'type' => 'list', 'member' => [ 'shape' => 'PortNumber', ], 'max' => 100, ], 'EndpointAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'EndpointConfiguration' => [ 'type' => 'structure', 'members' => [ 'EndpointId' => [ 'shape' => 'GenericString', ], 'Weight' => [ 'shape' => 'EndpointWeight', ], 'ClientIPPreservationEnabled' => [ 'shape' => 'GenericBoolean', ], ], ], 'EndpointConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'EndpointConfiguration', ], 'max' => 10, 'min' => 0, ], 'EndpointDescription' => [ 'type' => 'structure', 'members' => [ 'EndpointId' => [ 'shape' => 'GenericString', ], 'Weight' => [ 'shape' => 'EndpointWeight', ], 'HealthState' => [ 'shape' => 'HealthState', ], 'HealthReason' => [ 'shape' => 'GenericString', ], 'ClientIPPreservationEnabled' => [ 'shape' => 'GenericBoolean', ], ], ], 'EndpointDescriptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'EndpointDescription', ], ], 'EndpointGroup' => [ 'type' => 'structure', 'members' => [ 'EndpointGroupArn' => [ 'shape' => 'GenericString', ], 'EndpointGroupRegion' => [ 'shape' => 'GenericString', ], 'EndpointDescriptions' => [ 'shape' => 'EndpointDescriptions', ], 'TrafficDialPercentage' => [ 'shape' => 'TrafficDialPercentage', ], 'HealthCheckPort' => [ 'shape' => 'HealthCheckPort', ], 'HealthCheckProtocol' => [ 'shape' => 'HealthCheckProtocol', ], 'HealthCheckPath' => [ 'shape' => 'HealthCheckPath', ], 'HealthCheckIntervalSeconds' => [ 'shape' => 'HealthCheckIntervalSeconds', ], 'ThresholdCount' => [ 'shape' => 'ThresholdCount', ], 'PortOverrides' => [ 'shape' => 'PortOverrides', ], ], ], 'EndpointGroupAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'EndpointGroupNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'EndpointGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'EndpointGroup', ], ], 'EndpointIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'GenericString', ], ], 'EndpointNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'EndpointWeight' => [ 'type' => 'integer', 'max' => 255, 'min' => 0, ], 'ErrorMessage' => [ 'type' => 'string', ], 'GenericBoolean' => [ 'type' => 'boolean', ], 'GenericString' => [ 'type' => 'string', 'max' => 255, ], 'HealthCheckIntervalSeconds' => [ 'type' => 'integer', 'max' => 30, 'min' => 10, ], 'HealthCheckPath' => [ 'type' => 'string', 'max' => 255, 'pattern' => '^/[-a-zA-Z0-9@:%_\\\\+.~#?&/=]*$', ], 'HealthCheckPort' => [ 'type' => 'integer', 'max' => 65535, 'min' => 1, ], 'HealthCheckProtocol' => [ 'type' => 'string', 'enum' => [ 'TCP', 'HTTP', 'HTTPS', ], ], 'HealthState' => [ 'type' => 'string', 'enum' => [ 'INITIAL', 'HEALTHY', 'UNHEALTHY', ], ], 'IdempotencyToken' => [ 'type' => 'string', 'max' => 255, ], 'IncorrectCidrStateException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InternalServiceErrorException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InvalidArgumentException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InvalidNextTokenException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InvalidPortRangeException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'IpAddress' => [ 'type' => 'string', 'max' => 45, ], 'IpAddressType' => [ 'type' => 'string', 'enum' => [ 'IPV4', ], ], 'IpAddresses' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpAddress', ], 'max' => 2, 'min' => 0, ], 'IpSet' => [ 'type' => 'structure', 'members' => [ 'IpFamily' => [ 'shape' => 'GenericString', ], 'IpAddresses' => [ 'shape' => 'IpAddresses', ], ], ], 'IpSets' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpSet', ], ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ListAcceleratorsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'ListAcceleratorsResponse' => [ 'type' => 'structure', 'members' => [ 'Accelerators' => [ 'shape' => 'Accelerators', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'ListByoipCidrsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'ListByoipCidrsResponse' => [ 'type' => 'structure', 'members' => [ 'ByoipCidrs' => [ 'shape' => 'ByoipCidrs', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'ListCustomRoutingAcceleratorsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'ListCustomRoutingAcceleratorsResponse' => [ 'type' => 'structure', 'members' => [ 'Accelerators' => [ 'shape' => 'CustomRoutingAccelerators', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'ListCustomRoutingEndpointGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'ListenerArn', ], 'members' => [ 'ListenerArn' => [ 'shape' => 'GenericString', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'ListCustomRoutingEndpointGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'EndpointGroups' => [ 'shape' => 'CustomRoutingEndpointGroups', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'ListCustomRoutingListenersRequest' => [ 'type' => 'structure', 'required' => [ 'AcceleratorArn', ], 'members' => [ 'AcceleratorArn' => [ 'shape' => 'GenericString', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'ListCustomRoutingListenersResponse' => [ 'type' => 'structure', 'members' => [ 'Listeners' => [ 'shape' => 'CustomRoutingListeners', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'ListCustomRoutingPortMappingsByDestinationRequest' => [ 'type' => 'structure', 'required' => [ 'EndpointId', 'DestinationAddress', ], 'members' => [ 'EndpointId' => [ 'shape' => 'GenericString', ], 'DestinationAddress' => [ 'shape' => 'GenericString', ], 'MaxResults' => [ 'shape' => 'PortMappingsMaxResults', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'ListCustomRoutingPortMappingsByDestinationResponse' => [ 'type' => 'structure', 'members' => [ 'DestinationPortMappings' => [ 'shape' => 'DestinationPortMappings', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'ListCustomRoutingPortMappingsRequest' => [ 'type' => 'structure', 'required' => [ 'AcceleratorArn', ], 'members' => [ 'AcceleratorArn' => [ 'shape' => 'GenericString', ], 'EndpointGroupArn' => [ 'shape' => 'GenericString', ], 'MaxResults' => [ 'shape' => 'PortMappingsMaxResults', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'ListCustomRoutingPortMappingsResponse' => [ 'type' => 'structure', 'members' => [ 'PortMappings' => [ 'shape' => 'PortMappings', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'ListEndpointGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'ListenerArn', ], 'members' => [ 'ListenerArn' => [ 'shape' => 'GenericString', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'ListEndpointGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'EndpointGroups' => [ 'shape' => 'EndpointGroups', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'ListListenersRequest' => [ 'type' => 'structure', 'required' => [ 'AcceleratorArn', ], 'members' => [ 'AcceleratorArn' => [ 'shape' => 'GenericString', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'ListListenersResponse' => [ 'type' => 'structure', 'members' => [ 'Listeners' => [ 'shape' => 'Listeners', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'Tags', ], ], ], 'Listener' => [ 'type' => 'structure', 'members' => [ 'ListenerArn' => [ 'shape' => 'GenericString', ], 'PortRanges' => [ 'shape' => 'PortRanges', ], 'Protocol' => [ 'shape' => 'Protocol', ], 'ClientAffinity' => [ 'shape' => 'ClientAffinity', ], ], ], 'ListenerNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'Listeners' => [ 'type' => 'list', 'member' => [ 'shape' => 'Listener', ], ], 'MaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'PortMapping' => [ 'type' => 'structure', 'members' => [ 'AcceleratorPort' => [ 'shape' => 'PortNumber', ], 'EndpointGroupArn' => [ 'shape' => 'GenericString', ], 'EndpointId' => [ 'shape' => 'GenericString', ], 'DestinationSocketAddress' => [ 'shape' => 'SocketAddress', ], 'Protocols' => [ 'shape' => 'CustomRoutingProtocols', ], 'DestinationTrafficState' => [ 'shape' => 'CustomRoutingDestinationTrafficState', ], ], ], 'PortMappings' => [ 'type' => 'list', 'member' => [ 'shape' => 'PortMapping', ], ], 'PortMappingsMaxResults' => [ 'type' => 'integer', 'max' => 20000, 'min' => 1, ], 'PortNumber' => [ 'type' => 'integer', 'max' => 65535, 'min' => 1, ], 'PortOverride' => [ 'type' => 'structure', 'members' => [ 'ListenerPort' => [ 'shape' => 'PortNumber', ], 'EndpointPort' => [ 'shape' => 'PortNumber', ], ], ], 'PortOverrides' => [ 'type' => 'list', 'member' => [ 'shape' => 'PortOverride', ], 'max' => 10, 'min' => 0, ], 'PortRange' => [ 'type' => 'structure', 'members' => [ 'FromPort' => [ 'shape' => 'PortNumber', ], 'ToPort' => [ 'shape' => 'PortNumber', ], ], ], 'PortRanges' => [ 'type' => 'list', 'member' => [ 'shape' => 'PortRange', ], 'max' => 10, 'min' => 1, ], 'Protocol' => [ 'type' => 'string', 'enum' => [ 'TCP', 'UDP', ], ], 'Protocols' => [ 'type' => 'list', 'member' => [ 'shape' => 'Protocol', ], ], 'ProvisionByoipCidrRequest' => [ 'type' => 'structure', 'required' => [ 'Cidr', 'CidrAuthorizationContext', ], 'members' => [ 'Cidr' => [ 'shape' => 'GenericString', ], 'CidrAuthorizationContext' => [ 'shape' => 'CidrAuthorizationContext', ], ], ], 'ProvisionByoipCidrResponse' => [ 'type' => 'structure', 'members' => [ 'ByoipCidr' => [ 'shape' => 'ByoipCidr', ], ], ], 'RemoveCustomRoutingEndpointsRequest' => [ 'type' => 'structure', 'required' => [ 'EndpointIds', 'EndpointGroupArn', ], 'members' => [ 'EndpointIds' => [ 'shape' => 'EndpointIds', ], 'EndpointGroupArn' => [ 'shape' => 'GenericString', ], ], ], 'ResourceArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, ], 'SocketAddress' => [ 'type' => 'structure', 'members' => [ 'IpAddress' => [ 'shape' => 'GenericString', ], 'Port' => [ 'shape' => 'PortNumber', ], ], ], 'SocketAddresses' => [ 'type' => 'list', 'member' => [ 'shape' => 'SocketAddress', ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'Tags' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'ThresholdCount' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TrafficDialPercentage' => [ 'type' => 'float', 'max' => 100, 'min' => 0, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAcceleratorAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'AcceleratorArn', ], 'members' => [ 'AcceleratorArn' => [ 'shape' => 'GenericString', ], 'FlowLogsEnabled' => [ 'shape' => 'GenericBoolean', ], 'FlowLogsS3Bucket' => [ 'shape' => 'GenericString', ], 'FlowLogsS3Prefix' => [ 'shape' => 'GenericString', ], ], ], 'UpdateAcceleratorAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'AcceleratorAttributes' => [ 'shape' => 'AcceleratorAttributes', ], ], ], 'UpdateAcceleratorRequest' => [ 'type' => 'structure', 'required' => [ 'AcceleratorArn', ], 'members' => [ 'AcceleratorArn' => [ 'shape' => 'GenericString', ], 'Name' => [ 'shape' => 'GenericString', ], 'IpAddressType' => [ 'shape' => 'IpAddressType', ], 'Enabled' => [ 'shape' => 'GenericBoolean', ], ], ], 'UpdateAcceleratorResponse' => [ 'type' => 'structure', 'members' => [ 'Accelerator' => [ 'shape' => 'Accelerator', ], ], ], 'UpdateCustomRoutingAcceleratorAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'AcceleratorArn', ], 'members' => [ 'AcceleratorArn' => [ 'shape' => 'GenericString', ], 'FlowLogsEnabled' => [ 'shape' => 'GenericBoolean', ], 'FlowLogsS3Bucket' => [ 'shape' => 'GenericString', ], 'FlowLogsS3Prefix' => [ 'shape' => 'GenericString', ], ], ], 'UpdateCustomRoutingAcceleratorAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'AcceleratorAttributes' => [ 'shape' => 'CustomRoutingAcceleratorAttributes', ], ], ], 'UpdateCustomRoutingAcceleratorRequest' => [ 'type' => 'structure', 'required' => [ 'AcceleratorArn', ], 'members' => [ 'AcceleratorArn' => [ 'shape' => 'GenericString', ], 'Name' => [ 'shape' => 'GenericString', ], 'IpAddressType' => [ 'shape' => 'IpAddressType', ], 'Enabled' => [ 'shape' => 'GenericBoolean', ], ], ], 'UpdateCustomRoutingAcceleratorResponse' => [ 'type' => 'structure', 'members' => [ 'Accelerator' => [ 'shape' => 'CustomRoutingAccelerator', ], ], ], 'UpdateCustomRoutingListenerRequest' => [ 'type' => 'structure', 'required' => [ 'ListenerArn', 'PortRanges', ], 'members' => [ 'ListenerArn' => [ 'shape' => 'GenericString', ], 'PortRanges' => [ 'shape' => 'PortRanges', ], ], ], 'UpdateCustomRoutingListenerResponse' => [ 'type' => 'structure', 'members' => [ 'Listener' => [ 'shape' => 'CustomRoutingListener', ], ], ], 'UpdateEndpointGroupRequest' => [ 'type' => 'structure', 'required' => [ 'EndpointGroupArn', ], 'members' => [ 'EndpointGroupArn' => [ 'shape' => 'GenericString', ], 'EndpointConfigurations' => [ 'shape' => 'EndpointConfigurations', ], 'TrafficDialPercentage' => [ 'shape' => 'TrafficDialPercentage', ], 'HealthCheckPort' => [ 'shape' => 'HealthCheckPort', ], 'HealthCheckProtocol' => [ 'shape' => 'HealthCheckProtocol', ], 'HealthCheckPath' => [ 'shape' => 'HealthCheckPath', ], 'HealthCheckIntervalSeconds' => [ 'shape' => 'HealthCheckIntervalSeconds', ], 'ThresholdCount' => [ 'shape' => 'ThresholdCount', ], 'PortOverrides' => [ 'shape' => 'PortOverrides', ], ], ], 'UpdateEndpointGroupResponse' => [ 'type' => 'structure', 'members' => [ 'EndpointGroup' => [ 'shape' => 'EndpointGroup', ], ], ], 'UpdateListenerRequest' => [ 'type' => 'structure', 'required' => [ 'ListenerArn', ], 'members' => [ 'ListenerArn' => [ 'shape' => 'GenericString', ], 'PortRanges' => [ 'shape' => 'PortRanges', ], 'Protocol' => [ 'shape' => 'Protocol', ], 'ClientAffinity' => [ 'shape' => 'ClientAffinity', ], ], ], 'UpdateListenerResponse' => [ 'type' => 'structure', 'members' => [ 'Listener' => [ 'shape' => 'Listener', ], ], ], 'WithdrawByoipCidrRequest' => [ 'type' => 'structure', 'required' => [ 'Cidr', ], 'members' => [ 'Cidr' => [ 'shape' => 'GenericString', ], ], ], 'WithdrawByoipCidrResponse' => [ 'type' => 'structure', 'members' => [ 'ByoipCidr' => [ 'shape' => 'ByoipCidr', ], ], ], ],];
