<?php
// This file was auto-generated from sdk-root/src/data/eks/2017-11-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-11-01', 'endpointPrefix' => 'eks', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'Amazon EKS', 'serviceFullName' => 'Amazon Elastic Kubernetes Service', 'serviceId' => 'EKS', 'signatureVersion' => 'v4', 'signingName' => 'eks', 'uid' => 'eks-2017-11-01', ], 'operations' => [ 'AssociateEncryptionConfig' => [ 'name' => 'AssociateEncryptionConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/encryption-config/associate', ], 'input' => [ 'shape' => 'AssociateEncryptionConfigRequest', ], 'output' => [ 'shape' => 'AssociateEncryptionConfigResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'AssociateIdentityProviderConfig' => [ 'name' => 'AssociateIdentityProviderConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/identity-provider-configs/associate', ], 'input' => [ 'shape' => 'AssociateIdentityProviderConfigRequest', ], 'output' => [ 'shape' => 'AssociateIdentityProviderConfigResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'CreateAddon' => [ 'name' => 'CreateAddon', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/addons', ], 'input' => [ 'shape' => 'CreateAddonRequest', ], 'output' => [ 'shape' => 'CreateAddonResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], ], ], 'CreateCluster' => [ 'name' => 'CreateCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters', ], 'input' => [ 'shape' => 'CreateClusterRequest', ], 'output' => [ 'shape' => 'CreateClusterResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'UnsupportedAvailabilityZoneException', ], ], ], 'CreateFargateProfile' => [ 'name' => 'CreateFargateProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/fargate-profiles', ], 'input' => [ 'shape' => 'CreateFargateProfileRequest', ], 'output' => [ 'shape' => 'CreateFargateProfileResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'UnsupportedAvailabilityZoneException', ], ], ], 'CreateNodegroup' => [ 'name' => 'CreateNodegroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/node-groups', ], 'input' => [ 'shape' => 'CreateNodegroupRequest', ], 'output' => [ 'shape' => 'CreateNodegroupResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeleteAddon' => [ 'name' => 'DeleteAddon', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/clusters/{name}/addons/{addonName}', ], 'input' => [ 'shape' => 'DeleteAddonRequest', ], 'output' => [ 'shape' => 'DeleteAddonResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], ], ], 'DeleteCluster' => [ 'name' => 'DeleteCluster', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/clusters/{name}', ], 'input' => [ 'shape' => 'DeleteClusterRequest', ], 'output' => [ 'shape' => 'DeleteClusterResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeleteFargateProfile' => [ 'name' => 'DeleteFargateProfile', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/clusters/{name}/fargate-profiles/{fargateProfileName}', ], 'input' => [ 'shape' => 'DeleteFargateProfileRequest', ], 'output' => [ 'shape' => 'DeleteFargateProfileResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteNodegroup' => [ 'name' => 'DeleteNodegroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/clusters/{name}/node-groups/{nodegroupName}', ], 'input' => [ 'shape' => 'DeleteNodegroupRequest', ], 'output' => [ 'shape' => 'DeleteNodegroupResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeregisterCluster' => [ 'name' => 'DeregisterCluster', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/cluster-registrations/{name}', ], 'input' => [ 'shape' => 'DeregisterClusterRequest', ], 'output' => [ 'shape' => 'DeregisterClusterResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeAddon' => [ 'name' => 'DescribeAddon', 'http' => [ 'method' => 'GET', 'requestUri' => '/clusters/{name}/addons/{addonName}', ], 'input' => [ 'shape' => 'DescribeAddonRequest', ], 'output' => [ 'shape' => 'DescribeAddonResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], ], ], 'DescribeAddonVersions' => [ 'name' => 'DescribeAddonVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/addons/supported-versions', ], 'input' => [ 'shape' => 'DescribeAddonVersionsRequest', ], 'output' => [ 'shape' => 'DescribeAddonVersionsResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], ], ], 'DescribeCluster' => [ 'name' => 'DescribeCluster', 'http' => [ 'method' => 'GET', 'requestUri' => '/clusters/{name}', ], 'input' => [ 'shape' => 'DescribeClusterRequest', ], 'output' => [ 'shape' => 'DescribeClusterResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeFargateProfile' => [ 'name' => 'DescribeFargateProfile', 'http' => [ 'method' => 'GET', 'requestUri' => '/clusters/{name}/fargate-profiles/{fargateProfileName}', ], 'input' => [ 'shape' => 'DescribeFargateProfileRequest', ], 'output' => [ 'shape' => 'DescribeFargateProfileResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeIdentityProviderConfig' => [ 'name' => 'DescribeIdentityProviderConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/identity-provider-configs/describe', ], 'input' => [ 'shape' => 'DescribeIdentityProviderConfigRequest', ], 'output' => [ 'shape' => 'DescribeIdentityProviderConfigResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeNodegroup' => [ 'name' => 'DescribeNodegroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/clusters/{name}/node-groups/{nodegroupName}', ], 'input' => [ 'shape' => 'DescribeNodegroupRequest', ], 'output' => [ 'shape' => 'DescribeNodegroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeUpdate' => [ 'name' => 'DescribeUpdate', 'http' => [ 'method' => 'GET', 'requestUri' => '/clusters/{name}/updates/{updateId}', ], 'input' => [ 'shape' => 'DescribeUpdateRequest', ], 'output' => [ 'shape' => 'DescribeUpdateResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DisassociateIdentityProviderConfig' => [ 'name' => 'DisassociateIdentityProviderConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/identity-provider-configs/disassociate', ], 'input' => [ 'shape' => 'DisassociateIdentityProviderConfigRequest', ], 'output' => [ 'shape' => 'DisassociateIdentityProviderConfigResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'ListAddons' => [ 'name' => 'ListAddons', 'http' => [ 'method' => 'GET', 'requestUri' => '/clusters/{name}/addons', ], 'input' => [ 'shape' => 'ListAddonsRequest', ], 'output' => [ 'shape' => 'ListAddonsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServerException', ], ], ], 'ListClusters' => [ 'name' => 'ListClusters', 'http' => [ 'method' => 'GET', 'requestUri' => '/clusters', ], 'input' => [ 'shape' => 'ListClustersRequest', ], 'output' => [ 'shape' => 'ListClustersResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListFargateProfiles' => [ 'name' => 'ListFargateProfiles', 'http' => [ 'method' => 'GET', 'requestUri' => '/clusters/{name}/fargate-profiles', ], 'input' => [ 'shape' => 'ListFargateProfilesRequest', ], 'output' => [ 'shape' => 'ListFargateProfilesResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], ], ], 'ListIdentityProviderConfigs' => [ 'name' => 'ListIdentityProviderConfigs', 'http' => [ 'method' => 'GET', 'requestUri' => '/clusters/{name}/identity-provider-configs', ], 'input' => [ 'shape' => 'ListIdentityProviderConfigsRequest', ], 'output' => [ 'shape' => 'ListIdentityProviderConfigsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListNodegroups' => [ 'name' => 'ListNodegroups', 'http' => [ 'method' => 'GET', 'requestUri' => '/clusters/{name}/node-groups', ], 'input' => [ 'shape' => 'ListNodegroupsRequest', ], 'output' => [ 'shape' => 'ListNodegroupsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], ], ], 'ListUpdates' => [ 'name' => 'ListUpdates', 'http' => [ 'method' => 'GET', 'requestUri' => '/clusters/{name}/updates', ], 'input' => [ 'shape' => 'ListUpdatesRequest', ], 'output' => [ 'shape' => 'ListUpdatesResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'RegisterCluster' => [ 'name' => 'RegisterCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/cluster-registrations', ], 'input' => [ 'shape' => 'RegisterClusterRequest', ], 'output' => [ 'shape' => 'RegisterClusterResponse', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourcePropagationDelayException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], ], ], 'UpdateAddon' => [ 'name' => 'UpdateAddon', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/addons/{addonName}/update', ], 'input' => [ 'shape' => 'UpdateAddonRequest', ], 'output' => [ 'shape' => 'UpdateAddonResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], ], ], 'UpdateClusterConfig' => [ 'name' => 'UpdateClusterConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/update-config', ], 'input' => [ 'shape' => 'UpdateClusterConfigRequest', ], 'output' => [ 'shape' => 'UpdateClusterConfigResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'UpdateClusterVersion' => [ 'name' => 'UpdateClusterVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/updates', ], 'input' => [ 'shape' => 'UpdateClusterVersionRequest', ], 'output' => [ 'shape' => 'UpdateClusterVersionResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'UpdateNodegroupConfig' => [ 'name' => 'UpdateNodegroupConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/node-groups/{nodegroupName}/update-config', ], 'input' => [ 'shape' => 'UpdateNodegroupConfigRequest', ], 'output' => [ 'shape' => 'UpdateNodegroupConfigResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'UpdateNodegroupVersion' => [ 'name' => 'UpdateNodegroupVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/node-groups/{nodegroupName}/update-version', ], 'input' => [ 'shape' => 'UpdateNodegroupVersionRequest', ], 'output' => [ 'shape' => 'UpdateNodegroupVersionResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], ], ], ], 'shapes' => [ 'AMITypes' => [ 'type' => 'string', 'enum' => [ 'AL2_x86_64', 'AL2_x86_64_GPU', 'AL2_ARM_64', 'CUSTOM', 'BOTTLEROCKET_ARM_64', 'BOTTLEROCKET_x86_64', ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'Addon' => [ 'type' => 'structure', 'members' => [ 'addonName' => [ 'shape' => 'String', ], 'clusterName' => [ 'shape' => 'ClusterName', ], 'status' => [ 'shape' => 'AddonStatus', ], 'addonVersion' => [ 'shape' => 'String', ], 'health' => [ 'shape' => 'AddonHealth', ], 'addonArn' => [ 'shape' => 'String', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'modifiedAt' => [ 'shape' => 'Timestamp', ], 'serviceAccountRoleArn' => [ 'shape' => 'String', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'AddonHealth' => [ 'type' => 'structure', 'members' => [ 'issues' => [ 'shape' => 'AddonIssueList', ], ], ], 'AddonInfo' => [ 'type' => 'structure', 'members' => [ 'addonName' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'String', ], 'addonVersions' => [ 'shape' => 'AddonVersionInfoList', ], ], ], 'AddonIssue' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'AddonIssueCode', ], 'message' => [ 'shape' => 'String', ], 'resourceIds' => [ 'shape' => 'StringList', ], ], ], 'AddonIssueCode' => [ 'type' => 'string', 'enum' => [ 'AccessDenied', 'InternalFailure', 'ClusterUnreachable', 'InsufficientNumberOfReplicas', 'ConfigurationConflict', 'AdmissionRequestDenied', 'UnsupportedAddonModification', 'K8sResourceNotFound', ], ], 'AddonIssueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AddonIssue', ], ], 'AddonStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'CREATE_FAILED', 'UPDATING', 'DELETING', 'DELETE_FAILED', 'DEGRADED', ], ], 'AddonVersionInfo' => [ 'type' => 'structure', 'members' => [ 'addonVersion' => [ 'shape' => 'String', ], 'architecture' => [ 'shape' => 'StringList', ], 'compatibilities' => [ 'shape' => 'Compatibilities', ], ], ], 'AddonVersionInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AddonVersionInfo', ], ], 'Addons' => [ 'type' => 'list', 'member' => [ 'shape' => 'AddonInfo', ], ], 'AssociateEncryptionConfigRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'encryptionConfig', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'encryptionConfig' => [ 'shape' => 'EncryptionConfigList', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], ], ], 'AssociateEncryptionConfigResponse' => [ 'type' => 'structure', 'members' => [ 'update' => [ 'shape' => 'Update', ], ], ], 'AssociateIdentityProviderConfigRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'oidc', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'oidc' => [ 'shape' => 'OidcIdentityProviderConfigRequest', ], 'tags' => [ 'shape' => 'TagMap', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], ], ], 'AssociateIdentityProviderConfigResponse' => [ 'type' => 'structure', 'members' => [ 'update' => [ 'shape' => 'Update', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'AutoScalingGroup' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], ], ], 'AutoScalingGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutoScalingGroup', ], ], 'BadRequestException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'Boolean' => [ 'type' => 'boolean', ], 'BoxedBoolean' => [ 'type' => 'boolean', 'box' => true, ], 'BoxedInteger' => [ 'type' => 'integer', 'box' => true, ], 'Capacity' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'CapacityTypes' => [ 'type' => 'string', 'enum' => [ 'ON_DEMAND', 'SPOT', ], ], 'Certificate' => [ 'type' => 'structure', 'members' => [ 'data' => [ 'shape' => 'String', ], ], ], 'ClientException' => [ 'type' => 'structure', 'members' => [ 'clusterName' => [ 'shape' => 'String', ], 'nodegroupName' => [ 'shape' => 'String', ], 'addonName' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'Cluster' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'arn' => [ 'shape' => 'String', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'version' => [ 'shape' => 'String', ], 'endpoint' => [ 'shape' => 'String', ], 'roleArn' => [ 'shape' => 'String', ], 'resourcesVpcConfig' => [ 'shape' => 'VpcConfigResponse', ], 'kubernetesNetworkConfig' => [ 'shape' => 'KubernetesNetworkConfigResponse', ], 'logging' => [ 'shape' => 'Logging', ], 'identity' => [ 'shape' => 'Identity', ], 'status' => [ 'shape' => 'ClusterStatus', ], 'certificateAuthority' => [ 'shape' => 'Certificate', ], 'clientRequestToken' => [ 'shape' => 'String', ], 'platformVersion' => [ 'shape' => 'String', ], 'tags' => [ 'shape' => 'TagMap', ], 'encryptionConfig' => [ 'shape' => 'EncryptionConfigList', ], 'connectorConfig' => [ 'shape' => 'ConnectorConfigResponse', ], ], ], 'ClusterName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[0-9A-Za-z][A-Za-z0-9\\-_]*', ], 'ClusterStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'DELETING', 'FAILED', 'UPDATING', 'PENDING', ], ], 'Compatibilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'Compatibility', ], ], 'Compatibility' => [ 'type' => 'structure', 'members' => [ 'clusterVersion' => [ 'shape' => 'String', ], 'platformVersions' => [ 'shape' => 'StringList', ], 'defaultVersion' => [ 'shape' => 'Boolean', ], ], ], 'ConnectorConfigProvider' => [ 'type' => 'string', 'enum' => [ 'EKS_ANYWHERE', 'ANTHOS', 'GKE', 'AKS', 'OPENSHIFT', 'TANZU', 'RANCHER', 'EC2', 'OTHER', ], ], 'ConnectorConfigRequest' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'provider', ], 'members' => [ 'roleArn' => [ 'shape' => 'String', ], 'provider' => [ 'shape' => 'ConnectorConfigProvider', ], ], ], 'ConnectorConfigResponse' => [ 'type' => 'structure', 'members' => [ 'activationId' => [ 'shape' => 'String', ], 'activationCode' => [ 'shape' => 'String', ], 'activationExpiry' => [ 'shape' => 'Timestamp', ], 'provider' => [ 'shape' => 'String', ], 'roleArn' => [ 'shape' => 'String', ], ], ], 'CreateAddonRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'addonName', ], 'members' => [ 'clusterName' => [ 'shape' => 'ClusterName', 'location' => 'uri', 'locationName' => 'name', ], 'addonName' => [ 'shape' => 'String', ], 'addonVersion' => [ 'shape' => 'String', ], 'serviceAccountRoleArn' => [ 'shape' => 'RoleArn', ], 'resolveConflicts' => [ 'shape' => 'ResolveConflicts', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateAddonResponse' => [ 'type' => 'structure', 'members' => [ 'addon' => [ 'shape' => 'Addon', ], ], ], 'CreateClusterRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'roleArn', 'resourcesVpcConfig', ], 'members' => [ 'name' => [ 'shape' => 'ClusterName', ], 'version' => [ 'shape' => 'String', ], 'roleArn' => [ 'shape' => 'String', ], 'resourcesVpcConfig' => [ 'shape' => 'VpcConfigRequest', ], 'kubernetesNetworkConfig' => [ 'shape' => 'KubernetesNetworkConfigRequest', ], 'logging' => [ 'shape' => 'Logging', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], 'encryptionConfig' => [ 'shape' => 'EncryptionConfigList', ], ], ], 'CreateClusterResponse' => [ 'type' => 'structure', 'members' => [ 'cluster' => [ 'shape' => 'Cluster', ], ], ], 'CreateFargateProfileRequest' => [ 'type' => 'structure', 'required' => [ 'fargateProfileName', 'clusterName', 'podExecutionRoleArn', ], 'members' => [ 'fargateProfileName' => [ 'shape' => 'String', ], 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'podExecutionRoleArn' => [ 'shape' => 'String', ], 'subnets' => [ 'shape' => 'StringList', ], 'selectors' => [ 'shape' => 'FargateProfileSelectors', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateFargateProfileResponse' => [ 'type' => 'structure', 'members' => [ 'fargateProfile' => [ 'shape' => 'FargateProfile', ], ], ], 'CreateNodegroupRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'nodegroupName', 'subnets', 'nodeRole', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'nodegroupName' => [ 'shape' => 'String', ], 'scalingConfig' => [ 'shape' => 'NodegroupScalingConfig', ], 'diskSize' => [ 'shape' => 'BoxedInteger', ], 'subnets' => [ 'shape' => 'StringList', ], 'instanceTypes' => [ 'shape' => 'StringList', ], 'amiType' => [ 'shape' => 'AMITypes', ], 'remoteAccess' => [ 'shape' => 'RemoteAccessConfig', ], 'nodeRole' => [ 'shape' => 'String', ], 'labels' => [ 'shape' => 'labelsMap', ], 'taints' => [ 'shape' => 'taintsList', ], 'tags' => [ 'shape' => 'TagMap', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'launchTemplate' => [ 'shape' => 'LaunchTemplateSpecification', ], 'updateConfig' => [ 'shape' => 'NodegroupUpdateConfig', ], 'capacityType' => [ 'shape' => 'CapacityTypes', ], 'version' => [ 'shape' => 'String', ], 'releaseVersion' => [ 'shape' => 'String', ], ], ], 'CreateNodegroupResponse' => [ 'type' => 'structure', 'members' => [ 'nodegroup' => [ 'shape' => 'Nodegroup', ], ], ], 'DeleteAddonRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'addonName', ], 'members' => [ 'clusterName' => [ 'shape' => 'ClusterName', 'location' => 'uri', 'locationName' => 'name', ], 'addonName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'addonName', ], 'preserve' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'preserve', ], ], ], 'DeleteAddonResponse' => [ 'type' => 'structure', 'members' => [ 'addon' => [ 'shape' => 'Addon', ], ], ], 'DeleteClusterRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeleteClusterResponse' => [ 'type' => 'structure', 'members' => [ 'cluster' => [ 'shape' => 'Cluster', ], ], ], 'DeleteFargateProfileRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'fargateProfileName', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'fargateProfileName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'fargateProfileName', ], ], ], 'DeleteFargateProfileResponse' => [ 'type' => 'structure', 'members' => [ 'fargateProfile' => [ 'shape' => 'FargateProfile', ], ], ], 'DeleteNodegroupRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'nodegroupName', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'nodegroupName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'nodegroupName', ], ], ], 'DeleteNodegroupResponse' => [ 'type' => 'structure', 'members' => [ 'nodegroup' => [ 'shape' => 'Nodegroup', ], ], ], 'DeregisterClusterRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeregisterClusterResponse' => [ 'type' => 'structure', 'members' => [ 'cluster' => [ 'shape' => 'Cluster', ], ], ], 'DescribeAddonRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'addonName', ], 'members' => [ 'clusterName' => [ 'shape' => 'ClusterName', 'location' => 'uri', 'locationName' => 'name', ], 'addonName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'addonName', ], ], ], 'DescribeAddonResponse' => [ 'type' => 'structure', 'members' => [ 'addon' => [ 'shape' => 'Addon', ], ], ], 'DescribeAddonVersionsRequest' => [ 'type' => 'structure', 'members' => [ 'kubernetesVersion' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'kubernetesVersion', ], 'maxResults' => [ 'shape' => 'DescribeAddonVersionsRequestMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'addonName' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'addonName', ], ], ], 'DescribeAddonVersionsRequestMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'DescribeAddonVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'addons' => [ 'shape' => 'Addons', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'DescribeClusterRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DescribeClusterResponse' => [ 'type' => 'structure', 'members' => [ 'cluster' => [ 'shape' => 'Cluster', ], ], ], 'DescribeFargateProfileRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'fargateProfileName', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'fargateProfileName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'fargateProfileName', ], ], ], 'DescribeFargateProfileResponse' => [ 'type' => 'structure', 'members' => [ 'fargateProfile' => [ 'shape' => 'FargateProfile', ], ], ], 'DescribeIdentityProviderConfigRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'identityProviderConfig', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'identityProviderConfig' => [ 'shape' => 'IdentityProviderConfig', ], ], ], 'DescribeIdentityProviderConfigResponse' => [ 'type' => 'structure', 'members' => [ 'identityProviderConfig' => [ 'shape' => 'IdentityProviderConfigResponse', ], ], ], 'DescribeNodegroupRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'nodegroupName', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'nodegroupName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'nodegroupName', ], ], ], 'DescribeNodegroupResponse' => [ 'type' => 'structure', 'members' => [ 'nodegroup' => [ 'shape' => 'Nodegroup', ], ], ], 'DescribeUpdateRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'updateId', ], 'members' => [ 'name' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'updateId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'updateId', ], 'nodegroupName' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nodegroupName', ], 'addonName' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'addonName', ], ], ], 'DescribeUpdateResponse' => [ 'type' => 'structure', 'members' => [ 'update' => [ 'shape' => 'Update', ], ], ], 'DisassociateIdentityProviderConfigRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'identityProviderConfig', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'identityProviderConfig' => [ 'shape' => 'IdentityProviderConfig', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], ], ], 'DisassociateIdentityProviderConfigResponse' => [ 'type' => 'structure', 'members' => [ 'update' => [ 'shape' => 'Update', ], ], ], 'EncryptionConfig' => [ 'type' => 'structure', 'members' => [ 'resources' => [ 'shape' => 'StringList', ], 'provider' => [ 'shape' => 'Provider', ], ], ], 'EncryptionConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EncryptionConfig', ], 'max' => 1, ], 'ErrorCode' => [ 'type' => 'string', 'enum' => [ 'SubnetNotFound', 'SecurityGroupNotFound', 'EniLimitReached', 'IpNotAvailable', 'AccessDenied', 'OperationNotPermitted', 'VpcIdNotFound', 'Unknown', 'NodeCreationFailure', 'PodEvictionFailure', 'InsufficientFreeAddresses', 'ClusterUnreachable', 'InsufficientNumberOfReplicas', 'ConfigurationConflict', 'AdmissionRequestDenied', 'UnsupportedAddonModification', 'K8sResourceNotFound', ], ], 'ErrorDetail' => [ 'type' => 'structure', 'members' => [ 'errorCode' => [ 'shape' => 'ErrorCode', ], 'errorMessage' => [ 'shape' => 'String', ], 'resourceIds' => [ 'shape' => 'StringList', ], ], ], 'ErrorDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'ErrorDetail', ], ], 'FargateProfile' => [ 'type' => 'structure', 'members' => [ 'fargateProfileName' => [ 'shape' => 'String', ], 'fargateProfileArn' => [ 'shape' => 'String', ], 'clusterName' => [ 'shape' => 'String', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'podExecutionRoleArn' => [ 'shape' => 'String', ], 'subnets' => [ 'shape' => 'StringList', ], 'selectors' => [ 'shape' => 'FargateProfileSelectors', ], 'status' => [ 'shape' => 'FargateProfileStatus', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'FargateProfileLabel' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'FargateProfileSelector' => [ 'type' => 'structure', 'members' => [ 'namespace' => [ 'shape' => 'String', ], 'labels' => [ 'shape' => 'FargateProfileLabel', ], ], ], 'FargateProfileSelectors' => [ 'type' => 'list', 'member' => [ 'shape' => 'FargateProfileSelector', ], ], 'FargateProfileStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'DELETING', 'CREATE_FAILED', 'DELETE_FAILED', ], ], 'FargateProfilesRequestMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'Identity' => [ 'type' => 'structure', 'members' => [ 'oidc' => [ 'shape' => 'OIDC', ], ], ], 'IdentityProviderConfig' => [ 'type' => 'structure', 'required' => [ 'type', 'name', ], 'members' => [ 'type' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], ], ], 'IdentityProviderConfigResponse' => [ 'type' => 'structure', 'members' => [ 'oidc' => [ 'shape' => 'OidcIdentityProviderConfig', ], ], ], 'IdentityProviderConfigs' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdentityProviderConfig', ], ], 'IncludeClustersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'InvalidParameterException' => [ 'type' => 'structure', 'members' => [ 'clusterName' => [ 'shape' => 'String', ], 'nodegroupName' => [ 'shape' => 'String', ], 'fargateProfileName' => [ 'shape' => 'String', ], 'addonName' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidRequestException' => [ 'type' => 'structure', 'members' => [ 'clusterName' => [ 'shape' => 'String', ], 'nodegroupName' => [ 'shape' => 'String', ], 'addonName' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'Issue' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'NodegroupIssueCode', ], 'message' => [ 'shape' => 'String', ], 'resourceIds' => [ 'shape' => 'StringList', ], ], ], 'IssueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Issue', ], ], 'KubernetesNetworkConfigRequest' => [ 'type' => 'structure', 'members' => [ 'serviceIpv4Cidr' => [ 'shape' => 'String', ], ], ], 'KubernetesNetworkConfigResponse' => [ 'type' => 'structure', 'members' => [ 'serviceIpv4Cidr' => [ 'shape' => 'String', ], ], ], 'LaunchTemplateSpecification' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'version' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'String', ], ], ], 'ListAddonsRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', ], 'members' => [ 'clusterName' => [ 'shape' => 'ClusterName', 'location' => 'uri', 'locationName' => 'name', ], 'maxResults' => [ 'shape' => 'ListAddonsRequestMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListAddonsRequestMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListAddonsResponse' => [ 'type' => 'structure', 'members' => [ 'addons' => [ 'shape' => 'StringList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListClustersRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListClustersRequestMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'include' => [ 'shape' => 'IncludeClustersList', 'location' => 'querystring', 'locationName' => 'include', ], ], ], 'ListClustersRequestMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListClustersResponse' => [ 'type' => 'structure', 'members' => [ 'clusters' => [ 'shape' => 'StringList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListFargateProfilesRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'maxResults' => [ 'shape' => 'FargateProfilesRequestMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListFargateProfilesResponse' => [ 'type' => 'structure', 'members' => [ 'fargateProfileNames' => [ 'shape' => 'StringList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListIdentityProviderConfigsRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'maxResults' => [ 'shape' => 'ListIdentityProviderConfigsRequestMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListIdentityProviderConfigsRequestMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListIdentityProviderConfigsResponse' => [ 'type' => 'structure', 'members' => [ 'identityProviderConfigs' => [ 'shape' => 'IdentityProviderConfigs', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListNodegroupsRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'maxResults' => [ 'shape' => 'ListNodegroupsRequestMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListNodegroupsRequestMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListNodegroupsResponse' => [ 'type' => 'structure', 'members' => [ 'nodegroups' => [ 'shape' => 'StringList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ListUpdatesRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'nodegroupName' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nodegroupName', ], 'addonName' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'addonName', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'ListUpdatesRequestMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListUpdatesRequestMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListUpdatesResponse' => [ 'type' => 'structure', 'members' => [ 'updateIds' => [ 'shape' => 'StringList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'LogSetup' => [ 'type' => 'structure', 'members' => [ 'types' => [ 'shape' => 'LogTypes', ], 'enabled' => [ 'shape' => 'BoxedBoolean', ], ], ], 'LogSetups' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogSetup', ], ], 'LogType' => [ 'type' => 'string', 'enum' => [ 'api', 'audit', 'authenticator', 'controllerManager', 'scheduler', ], ], 'LogTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogType', ], ], 'Logging' => [ 'type' => 'structure', 'members' => [ 'clusterLogging' => [ 'shape' => 'LogSetups', ], ], ], 'Nodegroup' => [ 'type' => 'structure', 'members' => [ 'nodegroupName' => [ 'shape' => 'String', ], 'nodegroupArn' => [ 'shape' => 'String', ], 'clusterName' => [ 'shape' => 'String', ], 'version' => [ 'shape' => 'String', ], 'releaseVersion' => [ 'shape' => 'String', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'modifiedAt' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'NodegroupStatus', ], 'capacityType' => [ 'shape' => 'CapacityTypes', ], 'scalingConfig' => [ 'shape' => 'NodegroupScalingConfig', ], 'instanceTypes' => [ 'shape' => 'StringList', ], 'subnets' => [ 'shape' => 'StringList', ], 'remoteAccess' => [ 'shape' => 'RemoteAccessConfig', ], 'amiType' => [ 'shape' => 'AMITypes', ], 'nodeRole' => [ 'shape' => 'String', ], 'labels' => [ 'shape' => 'labelsMap', ], 'taints' => [ 'shape' => 'taintsList', ], 'resources' => [ 'shape' => 'NodegroupResources', ], 'diskSize' => [ 'shape' => 'BoxedInteger', ], 'health' => [ 'shape' => 'NodegroupHealth', ], 'updateConfig' => [ 'shape' => 'NodegroupUpdateConfig', ], 'launchTemplate' => [ 'shape' => 'LaunchTemplateSpecification', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'NodegroupHealth' => [ 'type' => 'structure', 'members' => [ 'issues' => [ 'shape' => 'IssueList', ], ], ], 'NodegroupIssueCode' => [ 'type' => 'string', 'enum' => [ 'AutoScalingGroupNotFound', 'AutoScalingGroupInvalidConfiguration', 'Ec2SecurityGroupNotFound', 'Ec2SecurityGroupDeletionFailure', 'Ec2LaunchTemplateNotFound', 'Ec2LaunchTemplateVersionMismatch', 'Ec2SubnetNotFound', 'Ec2SubnetInvalidConfiguration', 'IamInstanceProfileNotFound', 'IamLimitExceeded', 'IamNodeRoleNotFound', 'NodeCreationFailure', 'AsgInstanceLaunchFailures', 'InstanceLimitExceeded', 'InsufficientFreeAddresses', 'AccessDenied', 'InternalFailure', 'ClusterUnreachable', ], ], 'NodegroupResources' => [ 'type' => 'structure', 'members' => [ 'autoScalingGroups' => [ 'shape' => 'AutoScalingGroupList', ], 'remoteAccessSecurityGroup' => [ 'shape' => 'String', ], ], ], 'NodegroupScalingConfig' => [ 'type' => 'structure', 'members' => [ 'minSize' => [ 'shape' => 'ZeroCapacity', ], 'maxSize' => [ 'shape' => 'Capacity', ], 'desiredSize' => [ 'shape' => 'ZeroCapacity', ], ], ], 'NodegroupStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'UPDATING', 'DELETING', 'CREATE_FAILED', 'DELETE_FAILED', 'DEGRADED', ], ], 'NodegroupUpdateConfig' => [ 'type' => 'structure', 'members' => [ 'maxUnavailable' => [ 'shape' => 'NonZeroInteger', ], 'maxUnavailablePercentage' => [ 'shape' => 'PercentCapacity', ], ], ], 'NonZeroInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'OIDC' => [ 'type' => 'structure', 'members' => [ 'issuer' => [ 'shape' => 'String', ], ], ], 'OidcIdentityProviderConfig' => [ 'type' => 'structure', 'members' => [ 'identityProviderConfigName' => [ 'shape' => 'String', ], 'identityProviderConfigArn' => [ 'shape' => 'String', ], 'clusterName' => [ 'shape' => 'String', ], 'issuerUrl' => [ 'shape' => 'String', ], 'clientId' => [ 'shape' => 'String', ], 'usernameClaim' => [ 'shape' => 'String', ], 'usernamePrefix' => [ 'shape' => 'String', ], 'groupsClaim' => [ 'shape' => 'String', ], 'groupsPrefix' => [ 'shape' => 'String', ], 'requiredClaims' => [ 'shape' => 'requiredClaimsMap', ], 'tags' => [ 'shape' => 'TagMap', ], 'status' => [ 'shape' => 'configStatus', ], ], ], 'OidcIdentityProviderConfigRequest' => [ 'type' => 'structure', 'required' => [ 'identityProviderConfigName', 'issuerUrl', 'clientId', ], 'members' => [ 'identityProviderConfigName' => [ 'shape' => 'String', ], 'issuerUrl' => [ 'shape' => 'String', ], 'clientId' => [ 'shape' => 'String', ], 'usernameClaim' => [ 'shape' => 'String', ], 'usernamePrefix' => [ 'shape' => 'String', ], 'groupsClaim' => [ 'shape' => 'String', ], 'groupsPrefix' => [ 'shape' => 'String', ], 'requiredClaims' => [ 'shape' => 'requiredClaimsMap', ], ], ], 'PercentCapacity' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'Provider' => [ 'type' => 'structure', 'members' => [ 'keyArn' => [ 'shape' => 'String', ], ], ], 'RegisterClusterRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'connectorConfig', ], 'members' => [ 'name' => [ 'shape' => 'ClusterName', ], 'connectorConfig' => [ 'shape' => 'ConnectorConfigRequest', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'RegisterClusterResponse' => [ 'type' => 'structure', 'members' => [ 'cluster' => [ 'shape' => 'Cluster', ], ], ], 'RemoteAccessConfig' => [ 'type' => 'structure', 'members' => [ 'ec2SshKey' => [ 'shape' => 'String', ], 'sourceSecurityGroups' => [ 'shape' => 'StringList', ], ], ], 'ResolveConflicts' => [ 'type' => 'string', 'enum' => [ 'OVERWRITE', 'NONE', ], ], 'ResourceInUseException' => [ 'type' => 'structure', 'members' => [ 'clusterName' => [ 'shape' => 'String', ], 'nodegroupName' => [ 'shape' => 'String', ], 'addonName' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ResourceLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'clusterName' => [ 'shape' => 'String', ], 'nodegroupName' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'clusterName' => [ 'shape' => 'String', ], 'nodegroupName' => [ 'shape' => 'String', ], 'fargateProfileName' => [ 'shape' => 'String', ], 'addonName' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ResourcePropagationDelayException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 428, ], 'exception' => true, ], 'RoleArn' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ServerException' => [ 'type' => 'structure', 'members' => [ 'clusterName' => [ 'shape' => 'String', ], 'nodegroupName' => [ 'shape' => 'String', ], 'addonName' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], 'String' => [ 'type' => 'string', ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, ], 'Taint' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => 'taintKey', ], 'value' => [ 'shape' => 'taintValue', ], 'effect' => [ 'shape' => 'TaintEffect', ], ], ], 'TaintEffect' => [ 'type' => 'string', 'enum' => [ 'NO_SCHEDULE', 'NO_EXECUTE', 'PREFER_NO_SCHEDULE', ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UnsupportedAvailabilityZoneException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], 'clusterName' => [ 'shape' => 'String', ], 'nodegroupName' => [ 'shape' => 'String', ], 'validZones' => [ 'shape' => 'StringList', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'Update' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'UpdateStatus', ], 'type' => [ 'shape' => 'UpdateType', ], 'params' => [ 'shape' => 'UpdateParams', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'errors' => [ 'shape' => 'ErrorDetails', ], ], ], 'UpdateAddonRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'addonName', ], 'members' => [ 'clusterName' => [ 'shape' => 'ClusterName', 'location' => 'uri', 'locationName' => 'name', ], 'addonName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'addonName', ], 'addonVersion' => [ 'shape' => 'String', ], 'serviceAccountRoleArn' => [ 'shape' => 'RoleArn', ], 'resolveConflicts' => [ 'shape' => 'ResolveConflicts', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], ], ], 'UpdateAddonResponse' => [ 'type' => 'structure', 'members' => [ 'update' => [ 'shape' => 'Update', ], ], ], 'UpdateClusterConfigRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'resourcesVpcConfig' => [ 'shape' => 'VpcConfigRequest', ], 'logging' => [ 'shape' => 'Logging', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], ], ], 'UpdateClusterConfigResponse' => [ 'type' => 'structure', 'members' => [ 'update' => [ 'shape' => 'Update', ], ], ], 'UpdateClusterVersionRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'version', ], 'members' => [ 'name' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'version' => [ 'shape' => 'String', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], ], ], 'UpdateClusterVersionResponse' => [ 'type' => 'structure', 'members' => [ 'update' => [ 'shape' => 'Update', ], ], ], 'UpdateLabelsPayload' => [ 'type' => 'structure', 'members' => [ 'addOrUpdateLabels' => [ 'shape' => 'labelsMap', ], 'removeLabels' => [ 'shape' => 'labelsKeyList', ], ], ], 'UpdateNodegroupConfigRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'nodegroupName', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'nodegroupName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'nodegroupName', ], 'labels' => [ 'shape' => 'UpdateLabelsPayload', ], 'taints' => [ 'shape' => 'UpdateTaintsPayload', ], 'scalingConfig' => [ 'shape' => 'NodegroupScalingConfig', ], 'updateConfig' => [ 'shape' => 'NodegroupUpdateConfig', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], ], ], 'UpdateNodegroupConfigResponse' => [ 'type' => 'structure', 'members' => [ 'update' => [ 'shape' => 'Update', ], ], ], 'UpdateNodegroupVersionRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'nodegroupName', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'nodegroupName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'nodegroupName', ], 'version' => [ 'shape' => 'String', ], 'releaseVersion' => [ 'shape' => 'String', ], 'launchTemplate' => [ 'shape' => 'LaunchTemplateSpecification', ], 'force' => [ 'shape' => 'Boolean', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], ], ], 'UpdateNodegroupVersionResponse' => [ 'type' => 'structure', 'members' => [ 'update' => [ 'shape' => 'Update', ], ], ], 'UpdateParam' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'UpdateParamType', ], 'value' => [ 'shape' => 'String', ], ], ], 'UpdateParamType' => [ 'type' => 'string', 'enum' => [ 'Version', 'PlatformVersion', 'EndpointPrivateAccess', 'EndpointPublicAccess', 'ClusterLogging', 'DesiredSize', 'LabelsToAdd', 'LabelsToRemove', 'TaintsToAdd', 'TaintsToRemove', 'MaxSize', 'MinSize', 'ReleaseVersion', 'PublicAccessCidrs', 'LaunchTemplateName', 'LaunchTemplateVersion', 'IdentityProviderConfig', 'EncryptionConfig', 'AddonVersion', 'ServiceAccountRoleArn', 'ResolveConflicts', 'MaxUnavailable', 'MaxUnavailablePercentage', ], ], 'UpdateParams' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpdateParam', ], ], 'UpdateStatus' => [ 'type' => 'string', 'enum' => [ 'InProgress', 'Failed', 'Cancelled', 'Successful', ], ], 'UpdateTaintsPayload' => [ 'type' => 'structure', 'members' => [ 'addOrUpdateTaints' => [ 'shape' => 'taintsList', ], 'removeTaints' => [ 'shape' => 'taintsList', ], ], ], 'UpdateType' => [ 'type' => 'string', 'enum' => [ 'VersionUpdate', 'EndpointAccessUpdate', 'LoggingUpdate', 'ConfigUpdate', 'AssociateIdentityProviderConfig', 'DisassociateIdentityProviderConfig', 'AssociateEncryptionConfig', 'AddonUpdate', ], ], 'VpcConfigRequest' => [ 'type' => 'structure', 'members' => [ 'subnetIds' => [ 'shape' => 'StringList', ], 'securityGroupIds' => [ 'shape' => 'StringList', ], 'endpointPublicAccess' => [ 'shape' => 'BoxedBoolean', ], 'endpointPrivateAccess' => [ 'shape' => 'BoxedBoolean', ], 'publicAccessCidrs' => [ 'shape' => 'StringList', ], ], ], 'VpcConfigResponse' => [ 'type' => 'structure', 'members' => [ 'subnetIds' => [ 'shape' => 'StringList', ], 'securityGroupIds' => [ 'shape' => 'StringList', ], 'clusterSecurityGroupId' => [ 'shape' => 'String', ], 'vpcId' => [ 'shape' => 'String', ], 'endpointPublicAccess' => [ 'shape' => 'Boolean', ], 'endpointPrivateAccess' => [ 'shape' => 'Boolean', ], 'publicAccessCidrs' => [ 'shape' => 'StringList', ], ], ], 'ZeroCapacity' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'configStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'DELETING', 'ACTIVE', ], ], 'labelKey' => [ 'type' => 'string', 'max' => 63, 'min' => 1, ], 'labelValue' => [ 'type' => 'string', 'max' => 63, 'min' => 1, ], 'labelsKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'labelsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'labelKey', ], 'value' => [ 'shape' => 'labelValue', ], ], 'requiredClaimsKey' => [ 'type' => 'string', 'max' => 63, 'min' => 1, ], 'requiredClaimsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'requiredClaimsKey', ], 'value' => [ 'shape' => 'requiredClaimsValue', ], ], 'requiredClaimsValue' => [ 'type' => 'string', 'max' => 253, 'min' => 1, ], 'taintKey' => [ 'type' => 'string', 'max' => 63, 'min' => 1, ], 'taintValue' => [ 'type' => 'string', 'max' => 63, 'min' => 0, ], 'taintsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Taint', ], ], ],];
