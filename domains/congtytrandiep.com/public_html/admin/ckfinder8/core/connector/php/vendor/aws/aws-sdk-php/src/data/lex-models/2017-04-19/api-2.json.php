<?php
// This file was auto-generated from sdk-root/src/data/lex-models/2017-04-19/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-04-19', 'endpointPrefix' => 'models.lex', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'Amazon Lex Model Building Service', 'serviceId' => 'Lex Model Building Service', 'signatureVersion' => 'v4', 'signingName' => 'lex', 'uid' => 'lex-models-2017-04-19', ], 'operations' => [ 'CreateBotVersion' => [ 'name' => 'CreateBotVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{name}/versions', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateBotVersionRequest', ], 'output' => [ 'shape' => 'CreateBotVersionResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'PreconditionFailedException', ], ], ], 'CreateIntentVersion' => [ 'name' => 'CreateIntentVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/intents/{name}/versions', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateIntentVersionRequest', ], 'output' => [ 'shape' => 'CreateIntentVersionResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'PreconditionFailedException', ], ], ], 'CreateSlotTypeVersion' => [ 'name' => 'CreateSlotTypeVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/slottypes/{name}/versions', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateSlotTypeVersionRequest', ], 'output' => [ 'shape' => 'CreateSlotTypeVersionResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'PreconditionFailedException', ], ], ], 'DeleteBot' => [ 'name' => 'DeleteBot', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/bots/{name}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteBotRequest', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'DeleteBotAlias' => [ 'name' => 'DeleteBotAlias', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/bots/{botName}/aliases/{name}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteBotAliasRequest', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'DeleteBotChannelAssociation' => [ 'name' => 'DeleteBotChannelAssociation', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/bots/{botName}/aliases/{aliasName}/channels/{name}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteBotChannelAssociationRequest', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'DeleteBotVersion' => [ 'name' => 'DeleteBotVersion', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/bots/{name}/versions/{version}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteBotVersionRequest', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'DeleteIntent' => [ 'name' => 'DeleteIntent', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/intents/{name}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteIntentRequest', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'DeleteIntentVersion' => [ 'name' => 'DeleteIntentVersion', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/intents/{name}/versions/{version}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteIntentVersionRequest', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'DeleteSlotType' => [ 'name' => 'DeleteSlotType', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/slottypes/{name}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteSlotTypeRequest', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'DeleteSlotTypeVersion' => [ 'name' => 'DeleteSlotTypeVersion', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/slottypes/{name}/version/{version}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteSlotTypeVersionRequest', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'DeleteUtterances' => [ 'name' => 'DeleteUtterances', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/bots/{botName}/utterances/{userId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteUtterancesRequest', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetBot' => [ 'name' => 'GetBot', 'http' => [ 'method' => 'GET', 'requestUri' => '/bots/{name}/versions/{versionoralias}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetBotRequest', ], 'output' => [ 'shape' => 'GetBotResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetBotAlias' => [ 'name' => 'GetBotAlias', 'http' => [ 'method' => 'GET', 'requestUri' => '/bots/{botName}/aliases/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetBotAliasRequest', ], 'output' => [ 'shape' => 'GetBotAliasResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetBotAliases' => [ 'name' => 'GetBotAliases', 'http' => [ 'method' => 'GET', 'requestUri' => '/bots/{botName}/aliases/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetBotAliasesRequest', ], 'output' => [ 'shape' => 'GetBotAliasesResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetBotChannelAssociation' => [ 'name' => 'GetBotChannelAssociation', 'http' => [ 'method' => 'GET', 'requestUri' => '/bots/{botName}/aliases/{aliasName}/channels/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetBotChannelAssociationRequest', ], 'output' => [ 'shape' => 'GetBotChannelAssociationResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetBotChannelAssociations' => [ 'name' => 'GetBotChannelAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/bots/{botName}/aliases/{aliasName}/channels/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetBotChannelAssociationsRequest', ], 'output' => [ 'shape' => 'GetBotChannelAssociationsResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetBotVersions' => [ 'name' => 'GetBotVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/bots/{name}/versions/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetBotVersionsRequest', ], 'output' => [ 'shape' => 'GetBotVersionsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetBots' => [ 'name' => 'GetBots', 'http' => [ 'method' => 'GET', 'requestUri' => '/bots/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetBotsRequest', ], 'output' => [ 'shape' => 'GetBotsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetBuiltinIntent' => [ 'name' => 'GetBuiltinIntent', 'http' => [ 'method' => 'GET', 'requestUri' => '/builtins/intents/{signature}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetBuiltinIntentRequest', ], 'output' => [ 'shape' => 'GetBuiltinIntentResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetBuiltinIntents' => [ 'name' => 'GetBuiltinIntents', 'http' => [ 'method' => 'GET', 'requestUri' => '/builtins/intents/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetBuiltinIntentsRequest', ], 'output' => [ 'shape' => 'GetBuiltinIntentsResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetBuiltinSlotTypes' => [ 'name' => 'GetBuiltinSlotTypes', 'http' => [ 'method' => 'GET', 'requestUri' => '/builtins/slottypes/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetBuiltinSlotTypesRequest', ], 'output' => [ 'shape' => 'GetBuiltinSlotTypesResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetExport' => [ 'name' => 'GetExport', 'http' => [ 'method' => 'GET', 'requestUri' => '/exports/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetExportRequest', ], 'output' => [ 'shape' => 'GetExportResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetImport' => [ 'name' => 'GetImport', 'http' => [ 'method' => 'GET', 'requestUri' => '/imports/{importId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetImportRequest', ], 'output' => [ 'shape' => 'GetImportResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetIntent' => [ 'name' => 'GetIntent', 'http' => [ 'method' => 'GET', 'requestUri' => '/intents/{name}/versions/{version}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetIntentRequest', ], 'output' => [ 'shape' => 'GetIntentResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetIntentVersions' => [ 'name' => 'GetIntentVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/intents/{name}/versions/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetIntentVersionsRequest', ], 'output' => [ 'shape' => 'GetIntentVersionsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetIntents' => [ 'name' => 'GetIntents', 'http' => [ 'method' => 'GET', 'requestUri' => '/intents/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetIntentsRequest', ], 'output' => [ 'shape' => 'GetIntentsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetMigration' => [ 'name' => 'GetMigration', 'http' => [ 'method' => 'GET', 'requestUri' => '/migrations/{migrationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMigrationRequest', ], 'output' => [ 'shape' => 'GetMigrationResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], ], ], 'GetMigrations' => [ 'name' => 'GetMigrations', 'http' => [ 'method' => 'GET', 'requestUri' => '/migrations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMigrationsRequest', ], 'output' => [ 'shape' => 'GetMigrationsResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetSlotType' => [ 'name' => 'GetSlotType', 'http' => [ 'method' => 'GET', 'requestUri' => '/slottypes/{name}/versions/{version}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSlotTypeRequest', ], 'output' => [ 'shape' => 'GetSlotTypeResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetSlotTypeVersions' => [ 'name' => 'GetSlotTypeVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/slottypes/{name}/versions/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSlotTypeVersionsRequest', ], 'output' => [ 'shape' => 'GetSlotTypeVersionsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetSlotTypes' => [ 'name' => 'GetSlotTypes', 'http' => [ 'method' => 'GET', 'requestUri' => '/slottypes/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSlotTypesRequest', ], 'output' => [ 'shape' => 'GetSlotTypesResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetUtterancesView' => [ 'name' => 'GetUtterancesView', 'http' => [ 'method' => 'GET', 'requestUri' => '/bots/{botname}/utterances?view=aggregation', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetUtterancesViewRequest', ], 'output' => [ 'shape' => 'GetUtterancesViewResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'PutBot' => [ 'name' => 'PutBot', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{name}/versions/$LATEST', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutBotRequest', ], 'output' => [ 'shape' => 'PutBotResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'PreconditionFailedException', ], ], ], 'PutBotAlias' => [ 'name' => 'PutBotAlias', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botName}/aliases/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutBotAliasRequest', ], 'output' => [ 'shape' => 'PutBotAliasResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'PreconditionFailedException', ], ], ], 'PutIntent' => [ 'name' => 'PutIntent', 'http' => [ 'method' => 'PUT', 'requestUri' => '/intents/{name}/versions/$LATEST', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutIntentRequest', ], 'output' => [ 'shape' => 'PutIntentResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'PreconditionFailedException', ], ], ], 'PutSlotType' => [ 'name' => 'PutSlotType', 'http' => [ 'method' => 'PUT', 'requestUri' => '/slottypes/{name}/versions/$LATEST', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutSlotTypeRequest', ], 'output' => [ 'shape' => 'PutSlotTypeResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'PreconditionFailedException', ], ], ], 'StartImport' => [ 'name' => 'StartImport', 'http' => [ 'method' => 'POST', 'requestUri' => '/imports/', 'responseCode' => 201, ], 'input' => [ 'shape' => 'StartImportRequest', ], 'output' => [ 'shape' => 'StartImportResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], ], ], 'StartMigration' => [ 'name' => 'StartMigration', 'http' => [ 'method' => 'POST', 'requestUri' => '/migrations', 'responseCode' => 202, ], 'input' => [ 'shape' => 'StartMigrationRequest', ], 'output' => [ 'shape' => 'StartMigrationResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'NotFoundException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalFailureException', ], [ 'shape' => 'LimitExceededException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'AliasName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^([A-Za-z]_?)+$', ], 'AliasNameOrListAll' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^(-|^([A-Za-z]_?)+$)$', ], 'AmazonResourceName' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, ], 'BadRequestException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'Blob' => [ 'type' => 'blob', ], 'Boolean' => [ 'type' => 'boolean', ], 'BotAliasMetadata' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'AliasName', ], 'description' => [ 'shape' => 'Description', ], 'botVersion' => [ 'shape' => 'Version', ], 'botName' => [ 'shape' => 'BotName', ], 'lastUpdatedDate' => [ 'shape' => 'Timestamp', ], 'createdDate' => [ 'shape' => 'Timestamp', ], 'checksum' => [ 'shape' => 'String', ], 'conversationLogs' => [ 'shape' => 'ConversationLogsResponse', ], ], ], 'BotAliasMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BotAliasMetadata', ], ], 'BotChannelAssociation' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'BotChannelName', ], 'description' => [ 'shape' => 'Description', ], 'botAlias' => [ 'shape' => 'AliasName', ], 'botName' => [ 'shape' => 'BotName', ], 'createdDate' => [ 'shape' => 'Timestamp', ], 'type' => [ 'shape' => 'ChannelType', ], 'botConfiguration' => [ 'shape' => 'ChannelConfigurationMap', ], 'status' => [ 'shape' => 'ChannelStatus', ], 'failureReason' => [ 'shape' => 'String', ], ], ], 'BotChannelAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BotChannelAssociation', ], ], 'BotChannelName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^([A-Za-z]_?)+$', ], 'BotMetadata' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'BotName', ], 'description' => [ 'shape' => 'Description', ], 'status' => [ 'shape' => 'Status', ], 'lastUpdatedDate' => [ 'shape' => 'Timestamp', ], 'createdDate' => [ 'shape' => 'Timestamp', ], 'version' => [ 'shape' => 'Version', ], ], ], 'BotMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BotMetadata', ], ], 'BotName' => [ 'type' => 'string', 'max' => 50, 'min' => 2, 'pattern' => '^([A-Za-z]_?)+$', ], 'BotVersions' => [ 'type' => 'list', 'member' => [ 'shape' => 'Version', ], 'max' => 5, 'min' => 1, ], 'BuiltinIntentMetadata' => [ 'type' => 'structure', 'members' => [ 'signature' => [ 'shape' => 'BuiltinIntentSignature', ], 'supportedLocales' => [ 'shape' => 'LocaleList', ], ], ], 'BuiltinIntentMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BuiltinIntentMetadata', ], ], 'BuiltinIntentSignature' => [ 'type' => 'string', ], 'BuiltinIntentSlot' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], ], ], 'BuiltinIntentSlotList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BuiltinIntentSlot', ], ], 'BuiltinSlotTypeMetadata' => [ 'type' => 'structure', 'members' => [ 'signature' => [ 'shape' => 'BuiltinSlotTypeSignature', ], 'supportedLocales' => [ 'shape' => 'LocaleList', ], ], ], 'BuiltinSlotTypeMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BuiltinSlotTypeMetadata', ], ], 'BuiltinSlotTypeSignature' => [ 'type' => 'string', ], 'ChannelConfigurationMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], 'max' => 10, 'min' => 1, 'sensitive' => true, ], 'ChannelStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'CREATED', 'FAILED', ], ], 'ChannelType' => [ 'type' => 'string', 'enum' => [ 'Facebook', 'Slack', 'Twilio-Sms', 'Kik', ], ], 'CodeHook' => [ 'type' => 'structure', 'required' => [ 'uri', 'messageVersion', ], 'members' => [ 'uri' => [ 'shape' => 'LambdaARN', ], 'messageVersion' => [ 'shape' => 'MessageVersion', ], ], ], 'ConfidenceThreshold' => [ 'type' => 'double', 'max' => 1, 'min' => 0, ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ContentString' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'ContentType' => [ 'type' => 'string', 'enum' => [ 'PlainText', 'SSML', 'CustomPayload', ], ], 'ContextTimeToLiveInSeconds' => [ 'type' => 'integer', 'max' => 86400, 'min' => 5, ], 'ContextTurnsToLive' => [ 'type' => 'integer', 'max' => 20, 'min' => 1, ], 'ConversationLogsRequest' => [ 'type' => 'structure', 'required' => [ 'logSettings', 'iamRoleArn', ], 'members' => [ 'logSettings' => [ 'shape' => 'LogSettingsRequestList', ], 'iamRoleArn' => [ 'shape' => 'IamRoleArn', ], ], ], 'ConversationLogsResponse' => [ 'type' => 'structure', 'members' => [ 'logSettings' => [ 'shape' => 'LogSettingsResponseList', ], 'iamRoleArn' => [ 'shape' => 'IamRoleArn', ], ], ], 'Count' => [ 'type' => 'integer', ], 'CreateBotVersionRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'BotName', 'location' => 'uri', 'locationName' => 'name', ], 'checksum' => [ 'shape' => 'String', ], ], ], 'CreateBotVersionResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'BotName', ], 'description' => [ 'shape' => 'Description', ], 'intents' => [ 'shape' => 'IntentList', ], 'clarificationPrompt' => [ 'shape' => 'Prompt', ], 'abortStatement' => [ 'shape' => 'Statement', ], 'status' => [ 'shape' => 'Status', ], 'failureReason' => [ 'shape' => 'String', ], 'lastUpdatedDate' => [ 'shape' => 'Timestamp', ], 'createdDate' => [ 'shape' => 'Timestamp', ], 'idleSessionTTLInSeconds' => [ 'shape' => 'SessionTTL', ], 'voiceId' => [ 'shape' => 'String', ], 'checksum' => [ 'shape' => 'String', ], 'version' => [ 'shape' => 'Version', ], 'locale' => [ 'shape' => 'Locale', ], 'childDirected' => [ 'shape' => 'Boolean', ], 'enableModelImprovements' => [ 'shape' => 'Boolean', ], 'detectSentiment' => [ 'shape' => 'Boolean', ], ], ], 'CreateIntentVersionRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'IntentName', 'location' => 'uri', 'locationName' => 'name', ], 'checksum' => [ 'shape' => 'String', ], ], ], 'CreateIntentVersionResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'IntentName', ], 'description' => [ 'shape' => 'Description', ], 'slots' => [ 'shape' => 'SlotList', ], 'sampleUtterances' => [ 'shape' => 'IntentUtteranceList', ], 'confirmationPrompt' => [ 'shape' => 'Prompt', ], 'rejectionStatement' => [ 'shape' => 'Statement', ], 'followUpPrompt' => [ 'shape' => 'FollowUpPrompt', ], 'conclusionStatement' => [ 'shape' => 'Statement', ], 'dialogCodeHook' => [ 'shape' => 'CodeHook', ], 'fulfillmentActivity' => [ 'shape' => 'FulfillmentActivity', ], 'parentIntentSignature' => [ 'shape' => 'BuiltinIntentSignature', ], 'lastUpdatedDate' => [ 'shape' => 'Timestamp', ], 'createdDate' => [ 'shape' => 'Timestamp', ], 'version' => [ 'shape' => 'Version', ], 'checksum' => [ 'shape' => 'String', ], 'kendraConfiguration' => [ 'shape' => 'KendraConfiguration', ], 'inputContexts' => [ 'shape' => 'InputContextList', ], 'outputContexts' => [ 'shape' => 'OutputContextList', ], ], ], 'CreateSlotTypeVersionRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'SlotTypeName', 'location' => 'uri', 'locationName' => 'name', ], 'checksum' => [ 'shape' => 'String', ], ], ], 'CreateSlotTypeVersionResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'SlotTypeName', ], 'description' => [ 'shape' => 'Description', ], 'enumerationValues' => [ 'shape' => 'EnumerationValues', ], 'lastUpdatedDate' => [ 'shape' => 'Timestamp', ], 'createdDate' => [ 'shape' => 'Timestamp', ], 'version' => [ 'shape' => 'Version', ], 'checksum' => [ 'shape' => 'String', ], 'valueSelectionStrategy' => [ 'shape' => 'SlotValueSelectionStrategy', ], 'parentSlotTypeSignature' => [ 'shape' => 'CustomOrBuiltinSlotTypeName', ], 'slotTypeConfigurations' => [ 'shape' => 'SlotTypeConfigurations', ], ], ], 'CustomOrBuiltinSlotTypeName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^((AMAZON\\.)_?|[A-Za-z]_?)+', ], 'DeleteBotAliasRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'botName', ], 'members' => [ 'name' => [ 'shape' => 'AliasName', 'location' => 'uri', 'locationName' => 'name', ], 'botName' => [ 'shape' => 'BotName', 'location' => 'uri', 'locationName' => 'botName', ], ], ], 'DeleteBotChannelAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'botName', 'botAlias', ], 'members' => [ 'name' => [ 'shape' => 'BotChannelName', 'location' => 'uri', 'locationName' => 'name', ], 'botName' => [ 'shape' => 'BotName', 'location' => 'uri', 'locationName' => 'botName', ], 'botAlias' => [ 'shape' => 'AliasName', 'location' => 'uri', 'locationName' => 'aliasName', ], ], ], 'DeleteBotRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'BotName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeleteBotVersionRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'version', ], 'members' => [ 'name' => [ 'shape' => 'BotName', 'location' => 'uri', 'locationName' => 'name', ], 'version' => [ 'shape' => 'NumericalVersion', 'location' => 'uri', 'locationName' => 'version', ], ], ], 'DeleteIntentRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'IntentName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeleteIntentVersionRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'version', ], 'members' => [ 'name' => [ 'shape' => 'IntentName', 'location' => 'uri', 'locationName' => 'name', ], 'version' => [ 'shape' => 'NumericalVersion', 'location' => 'uri', 'locationName' => 'version', ], ], ], 'DeleteSlotTypeRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'SlotTypeName', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeleteSlotTypeVersionRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'version', ], 'members' => [ 'name' => [ 'shape' => 'SlotTypeName', 'location' => 'uri', 'locationName' => 'name', ], 'version' => [ 'shape' => 'NumericalVersion', 'location' => 'uri', 'locationName' => 'version', ], ], ], 'DeleteUtterancesRequest' => [ 'type' => 'structure', 'required' => [ 'botName', 'userId', ], 'members' => [ 'botName' => [ 'shape' => 'BotName', 'location' => 'uri', 'locationName' => 'botName', ], 'userId' => [ 'shape' => 'UserId', 'location' => 'uri', 'locationName' => 'userId', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 200, 'min' => 0, ], 'Destination' => [ 'type' => 'string', 'enum' => [ 'CLOUDWATCH_LOGS', 'S3', ], ], 'EnumerationValue' => [ 'type' => 'structure', 'required' => [ 'value', ], 'members' => [ 'value' => [ 'shape' => 'Value', ], 'synonyms' => [ 'shape' => 'SynonymList', ], ], ], 'EnumerationValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnumerationValue', ], 'max' => 10000, 'min' => 0, ], 'ExportStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'READY', 'FAILED', ], ], 'ExportType' => [ 'type' => 'string', 'enum' => [ 'ALEXA_SKILLS_KIT', 'LEX', ], ], 'FollowUpPrompt' => [ 'type' => 'structure', 'required' => [ 'prompt', 'rejectionStatement', ], 'members' => [ 'prompt' => [ 'shape' => 'Prompt', ], 'rejectionStatement' => [ 'shape' => 'Statement', ], ], ], 'FulfillmentActivity' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'FulfillmentActivityType', ], 'codeHook' => [ 'shape' => 'CodeHook', ], ], ], 'FulfillmentActivityType' => [ 'type' => 'string', 'enum' => [ 'ReturnIntent', 'CodeHook', ], ], 'GetBotAliasRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'botName', ], 'members' => [ 'name' => [ 'shape' => 'AliasName', 'location' => 'uri', 'locationName' => 'name', ], 'botName' => [ 'shape' => 'BotName', 'location' => 'uri', 'locationName' => 'botName', ], ], ], 'GetBotAliasResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'AliasName', ], 'description' => [ 'shape' => 'Description', ], 'botVersion' => [ 'shape' => 'Version', ], 'botName' => [ 'shape' => 'BotName', ], 'lastUpdatedDate' => [ 'shape' => 'Timestamp', ], 'createdDate' => [ 'shape' => 'Timestamp', ], 'checksum' => [ 'shape' => 'String', ], 'conversationLogs' => [ 'shape' => 'ConversationLogsResponse', ], ], ], 'GetBotAliasesRequest' => [ 'type' => 'structure', 'required' => [ 'botName', ], 'members' => [ 'botName' => [ 'shape' => 'BotName', 'location' => 'uri', 'locationName' => 'botName', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nameContains' => [ 'shape' => 'AliasName', 'location' => 'querystring', 'locationName' => 'nameContains', ], ], ], 'GetBotAliasesResponse' => [ 'type' => 'structure', 'members' => [ 'BotAliases' => [ 'shape' => 'BotAliasMetadataList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetBotChannelAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'botName', 'botAlias', ], 'members' => [ 'name' => [ 'shape' => 'BotChannelName', 'location' => 'uri', 'locationName' => 'name', ], 'botName' => [ 'shape' => 'BotName', 'location' => 'uri', 'locationName' => 'botName', ], 'botAlias' => [ 'shape' => 'AliasName', 'location' => 'uri', 'locationName' => 'aliasName', ], ], ], 'GetBotChannelAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'BotChannelName', ], 'description' => [ 'shape' => 'Description', ], 'botAlias' => [ 'shape' => 'AliasName', ], 'botName' => [ 'shape' => 'BotName', ], 'createdDate' => [ 'shape' => 'Timestamp', ], 'type' => [ 'shape' => 'ChannelType', ], 'botConfiguration' => [ 'shape' => 'ChannelConfigurationMap', ], 'status' => [ 'shape' => 'ChannelStatus', ], 'failureReason' => [ 'shape' => 'String', ], ], ], 'GetBotChannelAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'botName', 'botAlias', ], 'members' => [ 'botName' => [ 'shape' => 'BotName', 'location' => 'uri', 'locationName' => 'botName', ], 'botAlias' => [ 'shape' => 'AliasNameOrListAll', 'location' => 'uri', 'locationName' => 'aliasName', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nameContains' => [ 'shape' => 'BotChannelName', 'location' => 'querystring', 'locationName' => 'nameContains', ], ], ], 'GetBotChannelAssociationsResponse' => [ 'type' => 'structure', 'members' => [ 'botChannelAssociations' => [ 'shape' => 'BotChannelAssociationList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetBotRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'versionOrAlias', ], 'members' => [ 'name' => [ 'shape' => 'BotName', 'location' => 'uri', 'locationName' => 'name', ], 'versionOrAlias' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'versionoralias', ], ], ], 'GetBotResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'BotName', ], 'description' => [ 'shape' => 'Description', ], 'intents' => [ 'shape' => 'IntentList', ], 'enableModelImprovements' => [ 'shape' => 'Boolean', ], 'nluIntentConfidenceThreshold' => [ 'shape' => 'ConfidenceThreshold', ], 'clarificationPrompt' => [ 'shape' => 'Prompt', ], 'abortStatement' => [ 'shape' => 'Statement', ], 'status' => [ 'shape' => 'Status', ], 'failureReason' => [ 'shape' => 'String', ], 'lastUpdatedDate' => [ 'shape' => 'Timestamp', ], 'createdDate' => [ 'shape' => 'Timestamp', ], 'idleSessionTTLInSeconds' => [ 'shape' => 'SessionTTL', ], 'voiceId' => [ 'shape' => 'String', ], 'checksum' => [ 'shape' => 'String', ], 'version' => [ 'shape' => 'Version', ], 'locale' => [ 'shape' => 'Locale', ], 'childDirected' => [ 'shape' => 'Boolean', ], 'detectSentiment' => [ 'shape' => 'Boolean', ], ], ], 'GetBotVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'BotName', 'location' => 'uri', 'locationName' => 'name', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'GetBotVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'bots' => [ 'shape' => 'BotMetadataList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetBotsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nameContains' => [ 'shape' => 'BotName', 'location' => 'querystring', 'locationName' => 'nameContains', ], ], ], 'GetBotsResponse' => [ 'type' => 'structure', 'members' => [ 'bots' => [ 'shape' => 'BotMetadataList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetBuiltinIntentRequest' => [ 'type' => 'structure', 'required' => [ 'signature', ], 'members' => [ 'signature' => [ 'shape' => 'BuiltinIntentSignature', 'location' => 'uri', 'locationName' => 'signature', ], ], ], 'GetBuiltinIntentResponse' => [ 'type' => 'structure', 'members' => [ 'signature' => [ 'shape' => 'BuiltinIntentSignature', ], 'supportedLocales' => [ 'shape' => 'LocaleList', ], 'slots' => [ 'shape' => 'BuiltinIntentSlotList', ], ], ], 'GetBuiltinIntentsRequest' => [ 'type' => 'structure', 'members' => [ 'locale' => [ 'shape' => 'Locale', 'location' => 'querystring', 'locationName' => 'locale', ], 'signatureContains' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'signatureContains', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'GetBuiltinIntentsResponse' => [ 'type' => 'structure', 'members' => [ 'intents' => [ 'shape' => 'BuiltinIntentMetadataList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetBuiltinSlotTypesRequest' => [ 'type' => 'structure', 'members' => [ 'locale' => [ 'shape' => 'Locale', 'location' => 'querystring', 'locationName' => 'locale', ], 'signatureContains' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'signatureContains', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'GetBuiltinSlotTypesResponse' => [ 'type' => 'structure', 'members' => [ 'slotTypes' => [ 'shape' => 'BuiltinSlotTypeMetadataList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetExportRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'version', 'resourceType', 'exportType', ], 'members' => [ 'name' => [ 'shape' => 'Name', 'location' => 'querystring', 'locationName' => 'name', ], 'version' => [ 'shape' => 'NumericalVersion', 'location' => 'querystring', 'locationName' => 'version', ], 'resourceType' => [ 'shape' => 'ResourceType', 'location' => 'querystring', 'locationName' => 'resourceType', ], 'exportType' => [ 'shape' => 'ExportType', 'location' => 'querystring', 'locationName' => 'exportType', ], ], ], 'GetExportResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'version' => [ 'shape' => 'NumericalVersion', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'exportType' => [ 'shape' => 'ExportType', ], 'exportStatus' => [ 'shape' => 'ExportStatus', ], 'failureReason' => [ 'shape' => 'String', ], 'url' => [ 'shape' => 'String', ], ], ], 'GetImportRequest' => [ 'type' => 'structure', 'required' => [ 'importId', ], 'members' => [ 'importId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'importId', ], ], ], 'GetImportResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'mergeStrategy' => [ 'shape' => 'MergeStrategy', ], 'importId' => [ 'shape' => 'String', ], 'importStatus' => [ 'shape' => 'ImportStatus', ], 'failureReason' => [ 'shape' => 'StringList', ], 'createdDate' => [ 'shape' => 'Timestamp', ], ], ], 'GetIntentRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'version', ], 'members' => [ 'name' => [ 'shape' => 'IntentName', 'location' => 'uri', 'locationName' => 'name', ], 'version' => [ 'shape' => 'Version', 'location' => 'uri', 'locationName' => 'version', ], ], ], 'GetIntentResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'IntentName', ], 'description' => [ 'shape' => 'Description', ], 'slots' => [ 'shape' => 'SlotList', ], 'sampleUtterances' => [ 'shape' => 'IntentUtteranceList', ], 'confirmationPrompt' => [ 'shape' => 'Prompt', ], 'rejectionStatement' => [ 'shape' => 'Statement', ], 'followUpPrompt' => [ 'shape' => 'FollowUpPrompt', ], 'conclusionStatement' => [ 'shape' => 'Statement', ], 'dialogCodeHook' => [ 'shape' => 'CodeHook', ], 'fulfillmentActivity' => [ 'shape' => 'FulfillmentActivity', ], 'parentIntentSignature' => [ 'shape' => 'BuiltinIntentSignature', ], 'lastUpdatedDate' => [ 'shape' => 'Timestamp', ], 'createdDate' => [ 'shape' => 'Timestamp', ], 'version' => [ 'shape' => 'Version', ], 'checksum' => [ 'shape' => 'String', ], 'kendraConfiguration' => [ 'shape' => 'KendraConfiguration', ], 'inputContexts' => [ 'shape' => 'InputContextList', ], 'outputContexts' => [ 'shape' => 'OutputContextList', ], ], ], 'GetIntentVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'IntentName', 'location' => 'uri', 'locationName' => 'name', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'GetIntentVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'intents' => [ 'shape' => 'IntentMetadataList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetIntentsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nameContains' => [ 'shape' => 'IntentName', 'location' => 'querystring', 'locationName' => 'nameContains', ], ], ], 'GetIntentsResponse' => [ 'type' => 'structure', 'members' => [ 'intents' => [ 'shape' => 'IntentMetadataList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetMigrationRequest' => [ 'type' => 'structure', 'required' => [ 'migrationId', ], 'members' => [ 'migrationId' => [ 'shape' => 'MigrationId', 'location' => 'uri', 'locationName' => 'migrationId', ], ], ], 'GetMigrationResponse' => [ 'type' => 'structure', 'members' => [ 'migrationId' => [ 'shape' => 'MigrationId', ], 'v1BotName' => [ 'shape' => 'BotName', ], 'v1BotVersion' => [ 'shape' => 'Version', ], 'v1BotLocale' => [ 'shape' => 'Locale', ], 'v2BotId' => [ 'shape' => 'V2BotId', ], 'v2BotRole' => [ 'shape' => 'IamRoleArn', ], 'migrationStatus' => [ 'shape' => 'MigrationStatus', ], 'migrationStrategy' => [ 'shape' => 'MigrationStrategy', ], 'migrationTimestamp' => [ 'shape' => 'Timestamp', ], 'alerts' => [ 'shape' => 'MigrationAlerts', ], ], ], 'GetMigrationsRequest' => [ 'type' => 'structure', 'members' => [ 'sortByAttribute' => [ 'shape' => 'MigrationSortAttribute', 'location' => 'querystring', 'locationName' => 'sortByAttribute', ], 'sortByOrder' => [ 'shape' => 'SortOrder', 'location' => 'querystring', 'locationName' => 'sortByOrder', ], 'v1BotNameContains' => [ 'shape' => 'BotName', 'location' => 'querystring', 'locationName' => 'v1BotNameContains', ], 'migrationStatusEquals' => [ 'shape' => 'MigrationStatus', 'location' => 'querystring', 'locationName' => 'migrationStatusEquals', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetMigrationsResponse' => [ 'type' => 'structure', 'members' => [ 'migrationSummaries' => [ 'shape' => 'MigrationSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetSlotTypeRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'version', ], 'members' => [ 'name' => [ 'shape' => 'SlotTypeName', 'location' => 'uri', 'locationName' => 'name', ], 'version' => [ 'shape' => 'Version', 'location' => 'uri', 'locationName' => 'version', ], ], ], 'GetSlotTypeResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'SlotTypeName', ], 'description' => [ 'shape' => 'Description', ], 'enumerationValues' => [ 'shape' => 'EnumerationValues', ], 'lastUpdatedDate' => [ 'shape' => 'Timestamp', ], 'createdDate' => [ 'shape' => 'Timestamp', ], 'version' => [ 'shape' => 'Version', ], 'checksum' => [ 'shape' => 'String', ], 'valueSelectionStrategy' => [ 'shape' => 'SlotValueSelectionStrategy', ], 'parentSlotTypeSignature' => [ 'shape' => 'CustomOrBuiltinSlotTypeName', ], 'slotTypeConfigurations' => [ 'shape' => 'SlotTypeConfigurations', ], ], ], 'GetSlotTypeVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'SlotTypeName', 'location' => 'uri', 'locationName' => 'name', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'GetSlotTypeVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'slotTypes' => [ 'shape' => 'SlotTypeMetadataList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetSlotTypesRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nameContains' => [ 'shape' => 'SlotTypeName', 'location' => 'querystring', 'locationName' => 'nameContains', ], ], ], 'GetSlotTypesResponse' => [ 'type' => 'structure', 'members' => [ 'slotTypes' => [ 'shape' => 'SlotTypeMetadataList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetUtterancesViewRequest' => [ 'type' => 'structure', 'required' => [ 'botName', 'botVersions', 'statusType', ], 'members' => [ 'botName' => [ 'shape' => 'BotName', 'location' => 'uri', 'locationName' => 'botname', ], 'botVersions' => [ 'shape' => 'BotVersions', 'location' => 'querystring', 'locationName' => 'bot_versions', ], 'statusType' => [ 'shape' => 'StatusType', 'location' => 'querystring', 'locationName' => 'status_type', ], ], ], 'GetUtterancesViewResponse' => [ 'type' => 'structure', 'members' => [ 'botName' => [ 'shape' => 'BotName', ], 'utterances' => [ 'shape' => 'ListsOfUtterances', ], ], ], 'GroupNumber' => [ 'type' => 'integer', 'box' => true, 'max' => 5, 'min' => 1, ], 'IamRoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:[\\w\\-]+:iam::[\\d]{12}:role/.+$', ], 'ImportStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'COMPLETE', 'FAILED', ], ], 'InputContext' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'InputContextName', ], ], ], 'InputContextList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputContext', ], 'max' => 5, 'min' => 0, ], 'InputContextName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^([A-Za-z]_?)+$', ], 'Intent' => [ 'type' => 'structure', 'required' => [ 'intentName', 'intentVersion', ], 'members' => [ 'intentName' => [ 'shape' => 'IntentName', ], 'intentVersion' => [ 'shape' => 'Version', ], ], ], 'IntentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Intent', ], ], 'IntentMetadata' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'IntentName', ], 'description' => [ 'shape' => 'Description', ], 'lastUpdatedDate' => [ 'shape' => 'Timestamp', ], 'createdDate' => [ 'shape' => 'Timestamp', ], 'version' => [ 'shape' => 'Version', ], ], ], 'IntentMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IntentMetadata', ], ], 'IntentName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^([A-Za-z]_?)+$', ], 'IntentUtteranceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Utterance', ], 'max' => 1500, 'min' => 0, ], 'InternalFailureException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'KendraConfiguration' => [ 'type' => 'structure', 'required' => [ 'kendraIndex', 'role', ], 'members' => [ 'kendraIndex' => [ 'shape' => 'KendraIndexArn', ], 'queryFilterString' => [ 'shape' => 'QueryFilterString', ], 'role' => [ 'shape' => 'roleArn', ], ], ], 'KendraIndexArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws:kendra:[a-z]+-[a-z]+-[0-9]:[0-9]{12}:index\\/[a-zA-Z0-9][a-zA-Z0-9_-]*', ], 'KmsKeyArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:[\\w\\-]+:kms:[\\w\\-]+:[\\d]{12}:(?:key\\/[\\w\\-]+|alias\\/[a-zA-Z0-9:\\/_\\-]{1,256})$', ], 'LambdaARN' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws[a-zA-Z-]*:lambda:[a-z]+-[a-z]+(-[a-z]+)*-[0-9]:[0-9]{12}:function:[a-zA-Z0-9-_]+(\\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})?(:[a-zA-Z0-9-_]+)?', ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'retryAfterSeconds' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Retry-After', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'ListOfUtterance' => [ 'type' => 'list', 'member' => [ 'shape' => 'UtteranceData', ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagList', ], ], ], 'ListsOfUtterances' => [ 'type' => 'list', 'member' => [ 'shape' => 'UtteranceList', ], ], 'Locale' => [ 'type' => 'string', 'enum' => [ 'de-DE', 'en-AU', 'en-GB', 'en-IN', 'en-US', 'es-419', 'es-ES', 'es-US', 'fr-FR', 'fr-CA', 'it-IT', 'ja-JP', 'ko-KR', ], ], 'LocaleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Locale', ], ], 'LogSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'logType', 'destination', 'resourceArn', ], 'members' => [ 'logType' => [ 'shape' => 'LogType', ], 'destination' => [ 'shape' => 'Destination', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'resourceArn' => [ 'shape' => 'ResourceArn', ], ], ], 'LogSettingsRequestList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogSettingsRequest', ], ], 'LogSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'logType' => [ 'shape' => 'LogType', ], 'destination' => [ 'shape' => 'Destination', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'resourceArn' => [ 'shape' => 'ResourceArn', ], 'resourcePrefix' => [ 'shape' => 'ResourcePrefix', ], ], ], 'LogSettingsResponseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogSettingsResponse', ], ], 'LogType' => [ 'type' => 'string', 'enum' => [ 'AUDIO', 'TEXT', ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'MergeStrategy' => [ 'type' => 'string', 'enum' => [ 'OVERWRITE_LATEST', 'FAIL_ON_CONFLICT', ], ], 'Message' => [ 'type' => 'structure', 'required' => [ 'contentType', 'content', ], 'members' => [ 'contentType' => [ 'shape' => 'ContentType', ], 'content' => [ 'shape' => 'ContentString', ], 'groupNumber' => [ 'shape' => 'GroupNumber', ], ], ], 'MessageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Message', ], 'max' => 15, 'min' => 1, ], 'MessageVersion' => [ 'type' => 'string', 'max' => 5, 'min' => 1, ], 'MigrationAlert' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'MigrationAlertType', ], 'message' => [ 'shape' => 'MigrationAlertMessage', ], 'details' => [ 'shape' => 'MigrationAlertDetails', ], 'referenceURLs' => [ 'shape' => 'MigrationAlertReferenceURLs', ], ], ], 'MigrationAlertDetail' => [ 'type' => 'string', ], 'MigrationAlertDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'MigrationAlertDetail', ], ], 'MigrationAlertMessage' => [ 'type' => 'string', ], 'MigrationAlertReferenceURL' => [ 'type' => 'string', ], 'MigrationAlertReferenceURLs' => [ 'type' => 'list', 'member' => [ 'shape' => 'MigrationAlertReferenceURL', ], ], 'MigrationAlertType' => [ 'type' => 'string', 'enum' => [ 'ERROR', 'WARN', ], ], 'MigrationAlerts' => [ 'type' => 'list', 'member' => [ 'shape' => 'MigrationAlert', ], ], 'MigrationId' => [ 'type' => 'string', 'max' => 10, 'min' => 10, 'pattern' => '^[0-9a-zA-Z]+$', ], 'MigrationSortAttribute' => [ 'type' => 'string', 'enum' => [ 'V1_BOT_NAME', 'MIGRATION_DATE_TIME', ], ], 'MigrationStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'COMPLETED', 'FAILED', ], ], 'MigrationStrategy' => [ 'type' => 'string', 'enum' => [ 'CREATE_NEW', 'UPDATE_EXISTING', ], ], 'MigrationSummary' => [ 'type' => 'structure', 'members' => [ 'migrationId' => [ 'shape' => 'MigrationId', ], 'v1BotName' => [ 'shape' => 'BotName', ], 'v1BotVersion' => [ 'shape' => 'Version', ], 'v1BotLocale' => [ 'shape' => 'Locale', ], 'v2BotId' => [ 'shape' => 'V2BotId', ], 'v2BotRole' => [ 'shape' => 'IamRoleArn', ], 'migrationStatus' => [ 'shape' => 'MigrationStatus', ], 'migrationStrategy' => [ 'shape' => 'MigrationStrategy', ], 'migrationTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'MigrationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MigrationSummary', ], ], 'Name' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[a-zA-Z_]+', ], 'NextToken' => [ 'type' => 'string', ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'NumericalVersion' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[0-9]+', ], 'ObfuscationSetting' => [ 'type' => 'string', 'enum' => [ 'NONE', 'DEFAULT_OBFUSCATION', ], ], 'OutputContext' => [ 'type' => 'structure', 'required' => [ 'name', 'timeToLiveInSeconds', 'turnsToLive', ], 'members' => [ 'name' => [ 'shape' => 'OutputContextName', ], 'timeToLiveInSeconds' => [ 'shape' => 'ContextTimeToLiveInSeconds', ], 'turnsToLive' => [ 'shape' => 'ContextTurnsToLive', ], ], ], 'OutputContextList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OutputContext', ], 'max' => 10, 'min' => 0, ], 'OutputContextName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^([A-Za-z]_?)+$', ], 'PreconditionFailedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 412, ], 'exception' => true, ], 'Priority' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'ProcessBehavior' => [ 'type' => 'string', 'enum' => [ 'SAVE', 'BUILD', ], ], 'Prompt' => [ 'type' => 'structure', 'required' => [ 'messages', 'maxAttempts', ], 'members' => [ 'messages' => [ 'shape' => 'MessageList', ], 'maxAttempts' => [ 'shape' => 'PromptMaxAttempts', ], 'responseCard' => [ 'shape' => 'ResponseCard', ], ], ], 'PromptMaxAttempts' => [ 'type' => 'integer', 'max' => 5, 'min' => 1, ], 'PutBotAliasRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'botVersion', 'botName', ], 'members' => [ 'name' => [ 'shape' => 'AliasName', 'location' => 'uri', 'locationName' => 'name', ], 'description' => [ 'shape' => 'Description', ], 'botVersion' => [ 'shape' => 'Version', ], 'botName' => [ 'shape' => 'BotName', 'location' => 'uri', 'locationName' => 'botName', ], 'checksum' => [ 'shape' => 'String', ], 'conversationLogs' => [ 'shape' => 'ConversationLogsRequest', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'PutBotAliasResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'AliasName', ], 'description' => [ 'shape' => 'Description', ], 'botVersion' => [ 'shape' => 'Version', ], 'botName' => [ 'shape' => 'BotName', ], 'lastUpdatedDate' => [ 'shape' => 'Timestamp', ], 'createdDate' => [ 'shape' => 'Timestamp', ], 'checksum' => [ 'shape' => 'String', ], 'conversationLogs' => [ 'shape' => 'ConversationLogsResponse', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'PutBotRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'locale', 'childDirected', ], 'members' => [ 'name' => [ 'shape' => 'BotName', 'location' => 'uri', 'locationName' => 'name', ], 'description' => [ 'shape' => 'Description', ], 'intents' => [ 'shape' => 'IntentList', ], 'enableModelImprovements' => [ 'shape' => 'Boolean', ], 'nluIntentConfidenceThreshold' => [ 'shape' => 'ConfidenceThreshold', ], 'clarificationPrompt' => [ 'shape' => 'Prompt', ], 'abortStatement' => [ 'shape' => 'Statement', ], 'idleSessionTTLInSeconds' => [ 'shape' => 'SessionTTL', ], 'voiceId' => [ 'shape' => 'String', ], 'checksum' => [ 'shape' => 'String', ], 'processBehavior' => [ 'shape' => 'ProcessBehavior', ], 'locale' => [ 'shape' => 'Locale', ], 'childDirected' => [ 'shape' => 'Boolean', ], 'detectSentiment' => [ 'shape' => 'Boolean', ], 'createVersion' => [ 'shape' => 'Boolean', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'PutBotResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'BotName', ], 'description' => [ 'shape' => 'Description', ], 'intents' => [ 'shape' => 'IntentList', ], 'enableModelImprovements' => [ 'shape' => 'Boolean', ], 'nluIntentConfidenceThreshold' => [ 'shape' => 'ConfidenceThreshold', ], 'clarificationPrompt' => [ 'shape' => 'Prompt', ], 'abortStatement' => [ 'shape' => 'Statement', ], 'status' => [ 'shape' => 'Status', ], 'failureReason' => [ 'shape' => 'String', ], 'lastUpdatedDate' => [ 'shape' => 'Timestamp', ], 'createdDate' => [ 'shape' => 'Timestamp', ], 'idleSessionTTLInSeconds' => [ 'shape' => 'SessionTTL', ], 'voiceId' => [ 'shape' => 'String', ], 'checksum' => [ 'shape' => 'String', ], 'version' => [ 'shape' => 'Version', ], 'locale' => [ 'shape' => 'Locale', ], 'childDirected' => [ 'shape' => 'Boolean', ], 'createVersion' => [ 'shape' => 'Boolean', ], 'detectSentiment' => [ 'shape' => 'Boolean', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'PutIntentRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'IntentName', 'location' => 'uri', 'locationName' => 'name', ], 'description' => [ 'shape' => 'Description', ], 'slots' => [ 'shape' => 'SlotList', ], 'sampleUtterances' => [ 'shape' => 'IntentUtteranceList', ], 'confirmationPrompt' => [ 'shape' => 'Prompt', ], 'rejectionStatement' => [ 'shape' => 'Statement', ], 'followUpPrompt' => [ 'shape' => 'FollowUpPrompt', ], 'conclusionStatement' => [ 'shape' => 'Statement', ], 'dialogCodeHook' => [ 'shape' => 'CodeHook', ], 'fulfillmentActivity' => [ 'shape' => 'FulfillmentActivity', ], 'parentIntentSignature' => [ 'shape' => 'BuiltinIntentSignature', ], 'checksum' => [ 'shape' => 'String', ], 'createVersion' => [ 'shape' => 'Boolean', ], 'kendraConfiguration' => [ 'shape' => 'KendraConfiguration', ], 'inputContexts' => [ 'shape' => 'InputContextList', ], 'outputContexts' => [ 'shape' => 'OutputContextList', ], ], ], 'PutIntentResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'IntentName', ], 'description' => [ 'shape' => 'Description', ], 'slots' => [ 'shape' => 'SlotList', ], 'sampleUtterances' => [ 'shape' => 'IntentUtteranceList', ], 'confirmationPrompt' => [ 'shape' => 'Prompt', ], 'rejectionStatement' => [ 'shape' => 'Statement', ], 'followUpPrompt' => [ 'shape' => 'FollowUpPrompt', ], 'conclusionStatement' => [ 'shape' => 'Statement', ], 'dialogCodeHook' => [ 'shape' => 'CodeHook', ], 'fulfillmentActivity' => [ 'shape' => 'FulfillmentActivity', ], 'parentIntentSignature' => [ 'shape' => 'BuiltinIntentSignature', ], 'lastUpdatedDate' => [ 'shape' => 'Timestamp', ], 'createdDate' => [ 'shape' => 'Timestamp', ], 'version' => [ 'shape' => 'Version', ], 'checksum' => [ 'shape' => 'String', ], 'createVersion' => [ 'shape' => 'Boolean', ], 'kendraConfiguration' => [ 'shape' => 'KendraConfiguration', ], 'inputContexts' => [ 'shape' => 'InputContextList', ], 'outputContexts' => [ 'shape' => 'OutputContextList', ], ], ], 'PutSlotTypeRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'SlotTypeName', 'location' => 'uri', 'locationName' => 'name', ], 'description' => [ 'shape' => 'Description', ], 'enumerationValues' => [ 'shape' => 'EnumerationValues', ], 'checksum' => [ 'shape' => 'String', ], 'valueSelectionStrategy' => [ 'shape' => 'SlotValueSelectionStrategy', ], 'createVersion' => [ 'shape' => 'Boolean', ], 'parentSlotTypeSignature' => [ 'shape' => 'CustomOrBuiltinSlotTypeName', ], 'slotTypeConfigurations' => [ 'shape' => 'SlotTypeConfigurations', ], ], ], 'PutSlotTypeResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'SlotTypeName', ], 'description' => [ 'shape' => 'Description', ], 'enumerationValues' => [ 'shape' => 'EnumerationValues', ], 'lastUpdatedDate' => [ 'shape' => 'Timestamp', ], 'createdDate' => [ 'shape' => 'Timestamp', ], 'version' => [ 'shape' => 'Version', ], 'checksum' => [ 'shape' => 'String', ], 'valueSelectionStrategy' => [ 'shape' => 'SlotValueSelectionStrategy', ], 'createVersion' => [ 'shape' => 'Boolean', ], 'parentSlotTypeSignature' => [ 'shape' => 'CustomOrBuiltinSlotTypeName', ], 'slotTypeConfigurations' => [ 'shape' => 'SlotTypeConfigurations', ], ], ], 'QueryFilterString' => [ 'type' => 'string', 'min' => 0, ], 'ReferenceType' => [ 'type' => 'string', 'enum' => [ 'Intent', 'Bot', 'BotAlias', 'BotChannel', ], ], 'RegexPattern' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'ResourceArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^arn:[\\w\\-]+:(?:logs:[\\w\\-]+:[\\d]{12}:log-group:[\\.\\-_/#A-Za-z0-9]{1,512}(?::\\*)?|s3:::[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9])$', ], 'ResourceInUseException' => [ 'type' => 'structure', 'members' => [ 'referenceType' => [ 'shape' => 'ReferenceType', ], 'exampleReference' => [ 'shape' => 'ResourceReference', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ResourcePrefix' => [ 'type' => 'string', 'max' => 1024, ], 'ResourceReference' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'version' => [ 'shape' => 'Version', ], ], ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'BOT', 'INTENT', 'SLOT_TYPE', ], ], 'ResponseCard' => [ 'type' => 'string', 'max' => 50000, 'min' => 1, ], 'SessionTTL' => [ 'type' => 'integer', 'max' => 86400, 'min' => 60, ], 'Slot' => [ 'type' => 'structure', 'required' => [ 'name', 'slotConstraint', ], 'members' => [ 'name' => [ 'shape' => 'SlotName', ], 'description' => [ 'shape' => 'Description', ], 'slotConstraint' => [ 'shape' => 'SlotConstraint', ], 'slotType' => [ 'shape' => 'CustomOrBuiltinSlotTypeName', ], 'slotTypeVersion' => [ 'shape' => 'Version', ], 'valueElicitationPrompt' => [ 'shape' => 'Prompt', ], 'priority' => [ 'shape' => 'Priority', ], 'sampleUtterances' => [ 'shape' => 'SlotUtteranceList', ], 'responseCard' => [ 'shape' => 'ResponseCard', ], 'obfuscationSetting' => [ 'shape' => 'ObfuscationSetting', ], 'defaultValueSpec' => [ 'shape' => 'SlotDefaultValueSpec', ], ], ], 'SlotConstraint' => [ 'type' => 'string', 'enum' => [ 'Required', 'Optional', ], ], 'SlotDefaultValue' => [ 'type' => 'structure', 'required' => [ 'defaultValue', ], 'members' => [ 'defaultValue' => [ 'shape' => 'SlotDefaultValueString', ], ], ], 'SlotDefaultValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SlotDefaultValue', ], 'max' => 10, 'min' => 0, ], 'SlotDefaultValueSpec' => [ 'type' => 'structure', 'required' => [ 'defaultValueList', ], 'members' => [ 'defaultValueList' => [ 'shape' => 'SlotDefaultValueList', ], ], ], 'SlotDefaultValueString' => [ 'type' => 'string', 'max' => 202, 'min' => 1, ], 'SlotList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Slot', ], 'max' => 100, 'min' => 0, ], 'SlotName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^([A-Za-z](-|_|.)?)+$', ], 'SlotTypeConfiguration' => [ 'type' => 'structure', 'members' => [ 'regexConfiguration' => [ 'shape' => 'SlotTypeRegexConfiguration', ], ], ], 'SlotTypeConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'SlotTypeConfiguration', ], 'max' => 10, 'min' => 0, ], 'SlotTypeMetadata' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'SlotTypeName', ], 'description' => [ 'shape' => 'Description', ], 'lastUpdatedDate' => [ 'shape' => 'Timestamp', ], 'createdDate' => [ 'shape' => 'Timestamp', ], 'version' => [ 'shape' => 'Version', ], ], ], 'SlotTypeMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SlotTypeMetadata', ], ], 'SlotTypeName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^([A-Za-z]_?)+$', ], 'SlotTypeRegexConfiguration' => [ 'type' => 'structure', 'required' => [ 'pattern', ], 'members' => [ 'pattern' => [ 'shape' => 'RegexPattern', ], ], ], 'SlotUtteranceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Utterance', ], 'max' => 10, 'min' => 0, ], 'SlotValueSelectionStrategy' => [ 'type' => 'string', 'enum' => [ 'ORIGINAL_VALUE', 'TOP_RESOLUTION', ], ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'StartImportRequest' => [ 'type' => 'structure', 'required' => [ 'payload', 'resourceType', 'mergeStrategy', ], 'members' => [ 'payload' => [ 'shape' => 'Blob', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'mergeStrategy' => [ 'shape' => 'MergeStrategy', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'StartImportResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'mergeStrategy' => [ 'shape' => 'MergeStrategy', ], 'importId' => [ 'shape' => 'String', ], 'importStatus' => [ 'shape' => 'ImportStatus', ], 'tags' => [ 'shape' => 'TagList', ], 'createdDate' => [ 'shape' => 'Timestamp', ], ], ], 'StartMigrationRequest' => [ 'type' => 'structure', 'required' => [ 'v1BotName', 'v1BotVersion', 'v2BotName', 'v2BotRole', 'migrationStrategy', ], 'members' => [ 'v1BotName' => [ 'shape' => 'BotName', ], 'v1BotVersion' => [ 'shape' => 'Version', ], 'v2BotName' => [ 'shape' => 'V2BotName', ], 'v2BotRole' => [ 'shape' => 'IamRoleArn', ], 'migrationStrategy' => [ 'shape' => 'MigrationStrategy', ], ], ], 'StartMigrationResponse' => [ 'type' => 'structure', 'members' => [ 'v1BotName' => [ 'shape' => 'BotName', ], 'v1BotVersion' => [ 'shape' => 'Version', ], 'v1BotLocale' => [ 'shape' => 'Locale', ], 'v2BotId' => [ 'shape' => 'V2BotId', ], 'v2BotRole' => [ 'shape' => 'IamRoleArn', ], 'migrationId' => [ 'shape' => 'MigrationId', ], 'migrationStrategy' => [ 'shape' => 'MigrationStrategy', ], 'migrationTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'Statement' => [ 'type' => 'structure', 'required' => [ 'messages', ], 'members' => [ 'messages' => [ 'shape' => 'MessageList', ], 'responseCard' => [ 'shape' => 'ResponseCard', ], ], ], 'Status' => [ 'type' => 'string', 'enum' => [ 'BUILDING', 'READY', 'READY_BASIC_TESTING', 'FAILED', 'NOT_BUILT', ], ], 'StatusType' => [ 'type' => 'string', 'enum' => [ 'Detected', 'Missed', ], ], 'String' => [ 'type' => 'string', ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SynonymList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Value', ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UserId' => [ 'type' => 'string', 'max' => 100, 'min' => 2, ], 'Utterance' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'UtteranceData' => [ 'type' => 'structure', 'members' => [ 'utteranceString' => [ 'shape' => 'UtteranceString', ], 'count' => [ 'shape' => 'Count', ], 'distinctUsers' => [ 'shape' => 'Count', ], 'firstUtteredDate' => [ 'shape' => 'Timestamp', ], 'lastUtteredDate' => [ 'shape' => 'Timestamp', ], ], ], 'UtteranceList' => [ 'type' => 'structure', 'members' => [ 'botVersion' => [ 'shape' => 'Version', ], 'utterances' => [ 'shape' => 'ListOfUtterance', ], ], ], 'UtteranceString' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, ], 'V2BotId' => [ 'type' => 'string', 'max' => 10, 'min' => 10, 'pattern' => '^[0-9a-zA-Z]+$', ], 'V2BotName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^([0-9a-zA-Z][_-]?)+$', ], 'Value' => [ 'type' => 'string', 'max' => 140, 'min' => 1, ], 'Version' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '\\$LATEST|[0-9]+', ], 'roleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws:iam::[0-9]{12}:role/.*', ], ],];
