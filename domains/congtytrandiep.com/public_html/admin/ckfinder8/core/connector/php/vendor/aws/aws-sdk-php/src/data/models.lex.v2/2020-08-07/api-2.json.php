<?php
// This file was auto-generated from sdk-root/src/data/models.lex.v2/2020-08-07/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-08-07', 'endpointPrefix' => 'models-v2-lex', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'Lex Models V2', 'serviceFullName' => 'Amazon Lex Model Building V2', 'serviceId' => 'Lex Models V2', 'signatureVersion' => 'v4', 'signingName' => 'lex', 'uid' => 'models.lex.v2-2020-08-07', ], 'operations' => [ 'BuildBotLocale' => [ 'name' => 'BuildBotLocale', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'BuildBotLocaleRequest', ], 'output' => [ 'shape' => 'BuildBotLocaleResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateBot' => [ 'name' => 'CreateBot', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateBotRequest', ], 'output' => [ 'shape' => 'CreateBotResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateBotAlias' => [ 'name' => 'CreateBotAlias', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botaliases/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateBotAliasRequest', ], 'output' => [ 'shape' => 'CreateBotAliasResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateBotLocale' => [ 'name' => 'CreateBotLocale', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateBotLocaleRequest', ], 'output' => [ 'shape' => 'CreateBotLocaleResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateBotVersion' => [ 'name' => 'CreateBotVersion', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botversions/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateBotVersionRequest', ], 'output' => [ 'shape' => 'CreateBotVersionResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateExport' => [ 'name' => 'CreateExport', 'http' => [ 'method' => 'PUT', 'requestUri' => '/exports/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateExportRequest', ], 'output' => [ 'shape' => 'CreateExportResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateIntent' => [ 'name' => 'CreateIntent', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateIntentRequest', ], 'output' => [ 'shape' => 'CreateIntentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateResourcePolicy' => [ 'name' => 'CreateResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/policy/{resourceArn}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateResourcePolicyRequest', ], 'output' => [ 'shape' => 'CreateResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateResourcePolicyStatement' => [ 'name' => 'CreateResourcePolicyStatement', 'http' => [ 'method' => 'POST', 'requestUri' => '/policy/{resourceArn}/statements/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateResourcePolicyStatementRequest', ], 'output' => [ 'shape' => 'CreateResourcePolicyStatementResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateSlot' => [ 'name' => 'CreateSlot', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/{intentId}/slots/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateSlotRequest', ], 'output' => [ 'shape' => 'CreateSlotResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateSlotType' => [ 'name' => 'CreateSlotType', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/slottypes/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateSlotTypeRequest', ], 'output' => [ 'shape' => 'CreateSlotTypeResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateUploadUrl' => [ 'name' => 'CreateUploadUrl', 'http' => [ 'method' => 'POST', 'requestUri' => '/createuploadurl/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateUploadUrlRequest', ], 'output' => [ 'shape' => 'CreateUploadUrlResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteBot' => [ 'name' => 'DeleteBot', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/bots/{botId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteBotRequest', ], 'output' => [ 'shape' => 'DeleteBotResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteBotAlias' => [ 'name' => 'DeleteBotAlias', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/bots/{botId}/botaliases/{botAliasId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteBotAliasRequest', ], 'output' => [ 'shape' => 'DeleteBotAliasResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteBotLocale' => [ 'name' => 'DeleteBotLocale', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteBotLocaleRequest', ], 'output' => [ 'shape' => 'DeleteBotLocaleResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteBotVersion' => [ 'name' => 'DeleteBotVersion', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteBotVersionRequest', ], 'output' => [ 'shape' => 'DeleteBotVersionResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteExport' => [ 'name' => 'DeleteExport', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/exports/{exportId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteExportRequest', ], 'output' => [ 'shape' => 'DeleteExportResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteImport' => [ 'name' => 'DeleteImport', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/imports/{importId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteImportRequest', ], 'output' => [ 'shape' => 'DeleteImportResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteIntent' => [ 'name' => 'DeleteIntent', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/{intentId}/', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteIntentRequest', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteResourcePolicy' => [ 'name' => 'DeleteResourcePolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/policy/{resourceArn}/', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteResourcePolicyRequest', ], 'output' => [ 'shape' => 'DeleteResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteResourcePolicyStatement' => [ 'name' => 'DeleteResourcePolicyStatement', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/policy/{resourceArn}/statements/{statementId}/', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteResourcePolicyStatementRequest', ], 'output' => [ 'shape' => 'DeleteResourcePolicyStatementResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteSlot' => [ 'name' => 'DeleteSlot', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/{intentId}/slots/{slotId}/', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteSlotRequest', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteSlotType' => [ 'name' => 'DeleteSlotType', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/slottypes/{slotTypeId}/', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteSlotTypeRequest', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteUtterances' => [ 'name' => 'DeleteUtterances', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/bots/{botId}/utterances/', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteUtterancesRequest', ], 'output' => [ 'shape' => 'DeleteUtterancesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeBot' => [ 'name' => 'DescribeBot', 'http' => [ 'method' => 'GET', 'requestUri' => '/bots/{botId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeBotRequest', ], 'output' => [ 'shape' => 'DescribeBotResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeBotAlias' => [ 'name' => 'DescribeBotAlias', 'http' => [ 'method' => 'GET', 'requestUri' => '/bots/{botId}/botaliases/{botAliasId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeBotAliasRequest', ], 'output' => [ 'shape' => 'DescribeBotAliasResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeBotLocale' => [ 'name' => 'DescribeBotLocale', 'http' => [ 'method' => 'GET', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeBotLocaleRequest', ], 'output' => [ 'shape' => 'DescribeBotLocaleResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeBotRecommendation' => [ 'name' => 'DescribeBotRecommendation', 'http' => [ 'method' => 'GET', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/botrecommendations/{botRecommendationId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeBotRecommendationRequest', ], 'output' => [ 'shape' => 'DescribeBotRecommendationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeBotVersion' => [ 'name' => 'DescribeBotVersion', 'http' => [ 'method' => 'GET', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeBotVersionRequest', ], 'output' => [ 'shape' => 'DescribeBotVersionResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeExport' => [ 'name' => 'DescribeExport', 'http' => [ 'method' => 'GET', 'requestUri' => '/exports/{exportId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeExportRequest', ], 'output' => [ 'shape' => 'DescribeExportResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeImport' => [ 'name' => 'DescribeImport', 'http' => [ 'method' => 'GET', 'requestUri' => '/imports/{importId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeImportRequest', ], 'output' => [ 'shape' => 'DescribeImportResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeIntent' => [ 'name' => 'DescribeIntent', 'http' => [ 'method' => 'GET', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/{intentId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeIntentRequest', ], 'output' => [ 'shape' => 'DescribeIntentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeResourcePolicy' => [ 'name' => 'DescribeResourcePolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/policy/{resourceArn}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeResourcePolicyRequest', ], 'output' => [ 'shape' => 'DescribeResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DescribeSlot' => [ 'name' => 'DescribeSlot', 'http' => [ 'method' => 'GET', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/{intentId}/slots/{slotId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeSlotRequest', ], 'output' => [ 'shape' => 'DescribeSlotResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DescribeSlotType' => [ 'name' => 'DescribeSlotType', 'http' => [ 'method' => 'GET', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/slottypes/{slotTypeId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeSlotTypeRequest', ], 'output' => [ 'shape' => 'DescribeSlotTypeResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListAggregatedUtterances' => [ 'name' => 'ListAggregatedUtterances', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/aggregatedutterances/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAggregatedUtterancesRequest', ], 'output' => [ 'shape' => 'ListAggregatedUtterancesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListBotAliases' => [ 'name' => 'ListBotAliases', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/botaliases/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBotAliasesRequest', ], 'output' => [ 'shape' => 'ListBotAliasesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListBotLocales' => [ 'name' => 'ListBotLocales', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBotLocalesRequest', ], 'output' => [ 'shape' => 'ListBotLocalesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListBotRecommendations' => [ 'name' => 'ListBotRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/botrecommendations/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBotRecommendationsRequest', ], 'output' => [ 'shape' => 'ListBotRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListBotVersions' => [ 'name' => 'ListBotVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/botversions/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBotVersionsRequest', ], 'output' => [ 'shape' => 'ListBotVersionsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListBots' => [ 'name' => 'ListBots', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBotsRequest', ], 'output' => [ 'shape' => 'ListBotsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListBuiltInIntents' => [ 'name' => 'ListBuiltInIntents', 'http' => [ 'method' => 'POST', 'requestUri' => '/builtins/locales/{localeId}/intents/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBuiltInIntentsRequest', ], 'output' => [ 'shape' => 'ListBuiltInIntentsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListBuiltInSlotTypes' => [ 'name' => 'ListBuiltInSlotTypes', 'http' => [ 'method' => 'POST', 'requestUri' => '/builtins/locales/{localeId}/slottypes/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBuiltInSlotTypesRequest', ], 'output' => [ 'shape' => 'ListBuiltInSlotTypesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListExports' => [ 'name' => 'ListExports', 'http' => [ 'method' => 'POST', 'requestUri' => '/exports/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListExportsRequest', ], 'output' => [ 'shape' => 'ListExportsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListImports' => [ 'name' => 'ListImports', 'http' => [ 'method' => 'POST', 'requestUri' => '/imports/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListImportsRequest', ], 'output' => [ 'shape' => 'ListImportsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListIntents' => [ 'name' => 'ListIntents', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIntentsRequest', ], 'output' => [ 'shape' => 'ListIntentsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListRecommendedIntents' => [ 'name' => 'ListRecommendedIntents', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/botrecommendations/{botRecommendationId}/intents', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRecommendedIntentsRequest', ], 'output' => [ 'shape' => 'ListRecommendedIntentsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListSlotTypes' => [ 'name' => 'ListSlotTypes', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/slottypes/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSlotTypesRequest', ], 'output' => [ 'shape' => 'ListSlotTypesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListSlots' => [ 'name' => 'ListSlots', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/{intentId}/slots/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSlotsRequest', ], 'output' => [ 'shape' => 'ListSlotsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceARN}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'SearchAssociatedTranscripts' => [ 'name' => 'SearchAssociatedTranscripts', 'http' => [ 'method' => 'POST', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/botrecommendations/{botRecommendationId}/associatedtranscripts', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchAssociatedTranscriptsRequest', ], 'output' => [ 'shape' => 'SearchAssociatedTranscriptsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'StartBotRecommendation' => [ 'name' => 'StartBotRecommendation', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/botrecommendations/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'StartBotRecommendationRequest', ], 'output' => [ 'shape' => 'StartBotRecommendationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartImport' => [ 'name' => 'StartImport', 'http' => [ 'method' => 'PUT', 'requestUri' => '/imports/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'StartImportRequest', ], 'output' => [ 'shape' => 'StartImportResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceARN}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceARN}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateBot' => [ 'name' => 'UpdateBot', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateBotRequest', ], 'output' => [ 'shape' => 'UpdateBotResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateBotAlias' => [ 'name' => 'UpdateBotAlias', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botaliases/{botAliasId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateBotAliasRequest', ], 'output' => [ 'shape' => 'UpdateBotAliasResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateBotLocale' => [ 'name' => 'UpdateBotLocale', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateBotLocaleRequest', ], 'output' => [ 'shape' => 'UpdateBotLocaleResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateBotRecommendation' => [ 'name' => 'UpdateBotRecommendation', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/botrecommendations/{botRecommendationId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateBotRecommendationRequest', ], 'output' => [ 'shape' => 'UpdateBotRecommendationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateExport' => [ 'name' => 'UpdateExport', 'http' => [ 'method' => 'PUT', 'requestUri' => '/exports/{exportId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateExportRequest', ], 'output' => [ 'shape' => 'UpdateExportResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateIntent' => [ 'name' => 'UpdateIntent', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/{intentId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateIntentRequest', ], 'output' => [ 'shape' => 'UpdateIntentResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateResourcePolicy' => [ 'name' => 'UpdateResourcePolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/policy/{resourceArn}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateResourcePolicyRequest', ], 'output' => [ 'shape' => 'UpdateResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateSlot' => [ 'name' => 'UpdateSlot', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/intents/{intentId}/slots/{slotId}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSlotRequest', ], 'output' => [ 'shape' => 'UpdateSlotResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateSlotType' => [ 'name' => 'UpdateSlotType', 'http' => [ 'method' => 'PUT', 'requestUri' => '/bots/{botId}/botversions/{botVersion}/botlocales/{localeId}/slottypes/{slotTypeId}/', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateSlotTypeRequest', ], 'output' => [ 'shape' => 'UpdateSlotTypeResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'PreconditionFailedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], ], 'shapes' => [ 'AggregatedUtterancesFilter' => [ 'type' => 'structure', 'required' => [ 'name', 'values', 'operator', ], 'members' => [ 'name' => [ 'shape' => 'AggregatedUtterancesFilterName', ], 'values' => [ 'shape' => 'FilterValues', ], 'operator' => [ 'shape' => 'AggregatedUtterancesFilterOperator', ], ], ], 'AggregatedUtterancesFilterName' => [ 'type' => 'string', 'enum' => [ 'Utterance', ], ], 'AggregatedUtterancesFilterOperator' => [ 'type' => 'string', 'enum' => [ 'CO', 'EQ', ], ], 'AggregatedUtterancesFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'AggregatedUtterancesFilter', ], 'max' => 1, 'min' => 1, ], 'AggregatedUtterancesSortAttribute' => [ 'type' => 'string', 'enum' => [ 'HitCount', 'MissedCount', ], ], 'AggregatedUtterancesSortBy' => [ 'type' => 'structure', 'required' => [ 'attribute', 'order', ], 'members' => [ 'attribute' => [ 'shape' => 'AggregatedUtterancesSortAttribute', ], 'order' => [ 'shape' => 'SortOrder', ], ], ], 'AggregatedUtterancesSummary' => [ 'type' => 'structure', 'members' => [ 'utterance' => [ 'shape' => 'Utterance', ], 'hitCount' => [ 'shape' => 'HitCount', ], 'missedCount' => [ 'shape' => 'MissedCount', ], 'utteranceFirstRecordedInAggregationDuration' => [ 'shape' => 'Timestamp', ], 'utteranceLastRecordedInAggregationDuration' => [ 'shape' => 'Timestamp', ], 'containsDataFromDeletedResources' => [ 'shape' => 'BoxedBoolean', ], ], ], 'AggregatedUtterancesSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AggregatedUtterancesSummary', ], ], 'AmazonResourceName' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, ], 'AssociatedTranscript' => [ 'type' => 'structure', 'members' => [ 'transcript' => [ 'shape' => 'Transcript', ], ], ], 'AssociatedTranscriptFilter' => [ 'type' => 'structure', 'required' => [ 'name', 'values', ], 'members' => [ 'name' => [ 'shape' => 'AssociatedTranscriptFilterName', ], 'values' => [ 'shape' => 'FilterValues', ], ], ], 'AssociatedTranscriptFilterName' => [ 'type' => 'string', 'enum' => [ 'IntentId', 'SlotTypeId', ], ], 'AssociatedTranscriptFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssociatedTranscriptFilter', ], 'max' => 1, 'min' => 1, ], 'AssociatedTranscriptList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssociatedTranscript', ], ], 'AttachmentTitle' => [ 'type' => 'string', 'max' => 250, 'min' => 1, ], 'AttachmentUrl' => [ 'type' => 'string', 'max' => 250, 'min' => 1, ], 'AudioLogDestination' => [ 'type' => 'structure', 'required' => [ 's3Bucket', ], 'members' => [ 's3Bucket' => [ 'shape' => 'S3BucketLogDestination', ], ], ], 'AudioLogSetting' => [ 'type' => 'structure', 'required' => [ 'enabled', 'destination', ], 'members' => [ 'enabled' => [ 'shape' => 'Boolean', ], 'destination' => [ 'shape' => 'AudioLogDestination', ], ], ], 'AudioLogSettingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AudioLogSetting', ], 'max' => 1, 'min' => 1, ], 'Boolean' => [ 'type' => 'boolean', ], 'BotAliasHistoryEvent' => [ 'type' => 'structure', 'members' => [ 'botVersion' => [ 'shape' => 'BotVersion', ], 'startDate' => [ 'shape' => 'Timestamp', ], 'endDate' => [ 'shape' => 'Timestamp', ], ], ], 'BotAliasHistoryEventsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BotAliasHistoryEvent', ], ], 'BotAliasId' => [ 'type' => 'string', 'max' => 10, 'min' => 10, 'pattern' => '^(\\bTSTALIASID\\b|[0-9a-zA-Z]+)$', ], 'BotAliasLocaleSettings' => [ 'type' => 'structure', 'required' => [ 'enabled', ], 'members' => [ 'enabled' => [ 'shape' => 'Boolean', ], 'codeHookSpecification' => [ 'shape' => 'CodeHookSpecification', ], ], ], 'BotAliasLocaleSettingsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'LocaleId', ], 'value' => [ 'shape' => 'BotAliasLocaleSettings', ], 'min' => 1, ], 'BotAliasStatus' => [ 'type' => 'string', 'enum' => [ 'Creating', 'Available', 'Deleting', 'Failed', ], ], 'BotAliasSummary' => [ 'type' => 'structure', 'members' => [ 'botAliasId' => [ 'shape' => 'BotAliasId', ], 'botAliasName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'botAliasStatus' => [ 'shape' => 'BotAliasStatus', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'BotAliasSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BotAliasSummary', ], ], 'BotExportSpecification' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', ], 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], ], ], 'BotFilter' => [ 'type' => 'structure', 'required' => [ 'name', 'values', 'operator', ], 'members' => [ 'name' => [ 'shape' => 'BotFilterName', ], 'values' => [ 'shape' => 'FilterValues', ], 'operator' => [ 'shape' => 'BotFilterOperator', ], ], ], 'BotFilterName' => [ 'type' => 'string', 'enum' => [ 'BotName', ], ], 'BotFilterOperator' => [ 'type' => 'string', 'enum' => [ 'CO', 'EQ', ], ], 'BotFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'BotFilter', ], 'max' => 1, 'min' => 1, ], 'BotImportSpecification' => [ 'type' => 'structure', 'required' => [ 'botName', 'roleArn', 'dataPrivacy', ], 'members' => [ 'botName' => [ 'shape' => 'Name', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'dataPrivacy' => [ 'shape' => 'DataPrivacy', ], 'idleSessionTTLInSeconds' => [ 'shape' => 'SessionTTL', ], 'botTags' => [ 'shape' => 'TagMap', ], 'testBotAliasTags' => [ 'shape' => 'TagMap', ], ], ], 'BotLocaleExportSpecification' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], ], ], 'BotLocaleFilter' => [ 'type' => 'structure', 'required' => [ 'name', 'values', 'operator', ], 'members' => [ 'name' => [ 'shape' => 'BotLocaleFilterName', ], 'values' => [ 'shape' => 'FilterValues', ], 'operator' => [ 'shape' => 'BotLocaleFilterOperator', ], ], ], 'BotLocaleFilterName' => [ 'type' => 'string', 'enum' => [ 'BotLocaleName', ], ], 'BotLocaleFilterOperator' => [ 'type' => 'string', 'enum' => [ 'CO', 'EQ', ], ], 'BotLocaleFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'BotLocaleFilter', ], 'max' => 1, 'min' => 1, ], 'BotLocaleHistoryEvent' => [ 'type' => 'structure', 'required' => [ 'event', 'eventDate', ], 'members' => [ 'event' => [ 'shape' => 'BotLocaleHistoryEventDescription', ], 'eventDate' => [ 'shape' => 'Timestamp', ], ], ], 'BotLocaleHistoryEventDescription' => [ 'type' => 'string', ], 'BotLocaleHistoryEventsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BotLocaleHistoryEvent', ], ], 'BotLocaleImportSpecification' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'nluIntentConfidenceThreshold' => [ 'shape' => 'ConfidenceThreshold', ], 'voiceSettings' => [ 'shape' => 'VoiceSettings', ], ], ], 'BotLocaleSortAttribute' => [ 'type' => 'string', 'enum' => [ 'BotLocaleName', ], ], 'BotLocaleSortBy' => [ 'type' => 'structure', 'required' => [ 'attribute', 'order', ], 'members' => [ 'attribute' => [ 'shape' => 'BotLocaleSortAttribute', ], 'order' => [ 'shape' => 'SortOrder', ], ], ], 'BotLocaleStatus' => [ 'type' => 'string', 'enum' => [ 'Creating', 'Building', 'Built', 'ReadyExpressTesting', 'Failed', 'Deleting', 'NotBuilt', 'Importing', 'Processing', ], ], 'BotLocaleSummary' => [ 'type' => 'structure', 'members' => [ 'localeId' => [ 'shape' => 'LocaleId', ], 'localeName' => [ 'shape' => 'LocaleName', ], 'description' => [ 'shape' => 'Description', ], 'botLocaleStatus' => [ 'shape' => 'BotLocaleStatus', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'lastBuildSubmittedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'BotLocaleSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BotLocaleSummary', ], ], 'BotRecommendationResultStatistics' => [ 'type' => 'structure', 'members' => [ 'intents' => [ 'shape' => 'IntentStatistics', ], 'slotTypes' => [ 'shape' => 'SlotTypeStatistics', ], ], ], 'BotRecommendationResults' => [ 'type' => 'structure', 'members' => [ 'botLocaleExportUrl' => [ 'shape' => 'PresignedS3Url', ], 'associatedTranscriptsUrl' => [ 'shape' => 'PresignedS3Url', ], 'statistics' => [ 'shape' => 'BotRecommendationResultStatistics', ], ], ], 'BotRecommendationStatus' => [ 'type' => 'string', 'enum' => [ 'Processing', 'Deleting', 'Deleted', 'Downloading', 'Updating', 'Available', 'Failed', ], ], 'BotRecommendationSummary' => [ 'type' => 'structure', 'required' => [ 'botRecommendationStatus', 'botRecommendationId', ], 'members' => [ 'botRecommendationStatus' => [ 'shape' => 'BotRecommendationStatus', ], 'botRecommendationId' => [ 'shape' => 'Id', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'BotRecommendationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BotRecommendationSummary', ], ], 'BotSortAttribute' => [ 'type' => 'string', 'enum' => [ 'BotName', ], ], 'BotSortBy' => [ 'type' => 'structure', 'required' => [ 'attribute', 'order', ], 'members' => [ 'attribute' => [ 'shape' => 'BotSortAttribute', ], 'order' => [ 'shape' => 'SortOrder', ], ], ], 'BotStatus' => [ 'type' => 'string', 'enum' => [ 'Creating', 'Available', 'Inactive', 'Deleting', 'Failed', 'Versioning', 'Importing', ], ], 'BotSummary' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'botStatus' => [ 'shape' => 'BotStatus', ], 'latestBotVersion' => [ 'shape' => 'NumericalBotVersion', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'BotSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BotSummary', ], ], 'BotVersion' => [ 'type' => 'string', 'max' => 5, 'min' => 1, 'pattern' => '^(DRAFT|[0-9]+)$', ], 'BotVersionLocaleDetails' => [ 'type' => 'structure', 'required' => [ 'sourceBotVersion', ], 'members' => [ 'sourceBotVersion' => [ 'shape' => 'BotVersion', ], ], ], 'BotVersionLocaleSpecification' => [ 'type' => 'map', 'key' => [ 'shape' => 'LocaleId', ], 'value' => [ 'shape' => 'BotVersionLocaleDetails', ], 'min' => 1, ], 'BotVersionSortAttribute' => [ 'type' => 'string', 'enum' => [ 'BotVersion', ], ], 'BotVersionSortBy' => [ 'type' => 'structure', 'required' => [ 'attribute', 'order', ], 'members' => [ 'attribute' => [ 'shape' => 'BotVersionSortAttribute', ], 'order' => [ 'shape' => 'SortOrder', ], ], ], 'BotVersionSummary' => [ 'type' => 'structure', 'members' => [ 'botName' => [ 'shape' => 'Name', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'description' => [ 'shape' => 'Description', ], 'botStatus' => [ 'shape' => 'BotStatus', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'BotVersionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BotVersionSummary', ], ], 'BoxedBoolean' => [ 'type' => 'boolean', 'box' => true, ], 'BuildBotLocaleRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], ], ], 'BuildBotLocaleResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'botLocaleStatus' => [ 'shape' => 'BotLocaleStatus', ], 'lastBuildSubmittedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'BuiltInIntentSortAttribute' => [ 'type' => 'string', 'enum' => [ 'IntentSignature', ], ], 'BuiltInIntentSortBy' => [ 'type' => 'structure', 'required' => [ 'attribute', 'order', ], 'members' => [ 'attribute' => [ 'shape' => 'BuiltInIntentSortAttribute', ], 'order' => [ 'shape' => 'SortOrder', ], ], ], 'BuiltInIntentSummary' => [ 'type' => 'structure', 'members' => [ 'intentSignature' => [ 'shape' => 'IntentSignature', ], 'description' => [ 'shape' => 'Description', ], ], ], 'BuiltInIntentSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BuiltInIntentSummary', ], ], 'BuiltInOrCustomSlotTypeId' => [ 'type' => 'string', 'max' => 25, 'min' => 1, 'pattern' => '^((AMAZON\\.)[a-zA-Z_]+?|[0-9a-zA-Z]+)$', ], 'BuiltInSlotTypeSortAttribute' => [ 'type' => 'string', 'enum' => [ 'SlotTypeSignature', ], ], 'BuiltInSlotTypeSortBy' => [ 'type' => 'structure', 'required' => [ 'attribute', 'order', ], 'members' => [ 'attribute' => [ 'shape' => 'BuiltInSlotTypeSortAttribute', ], 'order' => [ 'shape' => 'SortOrder', ], ], ], 'BuiltInSlotTypeSummary' => [ 'type' => 'structure', 'members' => [ 'slotTypeSignature' => [ 'shape' => 'SlotTypeSignature', ], 'description' => [ 'shape' => 'Description', ], ], ], 'BuiltInSlotTypeSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BuiltInSlotTypeSummary', ], ], 'BuiltInsMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 20, 'min' => 20, ], 'Button' => [ 'type' => 'structure', 'required' => [ 'text', 'value', ], 'members' => [ 'text' => [ 'shape' => 'ButtonText', ], 'value' => [ 'shape' => 'ButtonValue', ], ], ], 'ButtonText' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'ButtonValue' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'ButtonsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Button', ], 'max' => 5, 'min' => 0, ], 'ChildDirected' => [ 'type' => 'boolean', ], 'CloudWatchLogGroupArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^arn:[\\w\\-]+:logs:[\\w\\-]+:[\\d]{12}:log-group:[\\.\\-_/#A-Za-z0-9]{1,512}(?::\\*)?$', ], 'CloudWatchLogGroupLogDestination' => [ 'type' => 'structure', 'required' => [ 'cloudWatchLogGroupArn', 'logPrefix', ], 'members' => [ 'cloudWatchLogGroupArn' => [ 'shape' => 'CloudWatchLogGroupArn', ], 'logPrefix' => [ 'shape' => 'LogPrefix', ], ], ], 'CodeHookInterfaceVersion' => [ 'type' => 'string', 'max' => 5, 'min' => 1, ], 'CodeHookSpecification' => [ 'type' => 'structure', 'required' => [ 'lambdaCodeHook', ], 'members' => [ 'lambdaCodeHook' => [ 'shape' => 'LambdaCodeHook', ], ], ], 'ConditionKey' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ConditionKeyValueMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ConditionKey', ], 'value' => [ 'shape' => 'ConditionValue', ], 'max' => 10, 'min' => 0, ], 'ConditionMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ConditionOperator', ], 'value' => [ 'shape' => 'ConditionKeyValueMap', ], 'max' => 10, 'min' => 0, ], 'ConditionOperator' => [ 'type' => 'string', 'min' => 1, ], 'ConditionValue' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ConfidenceThreshold' => [ 'type' => 'double', 'max' => 1, 'min' => 0, ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ContextTimeToLiveInSeconds' => [ 'type' => 'integer', 'max' => 86400, 'min' => 5, ], 'ContextTurnsToLive' => [ 'type' => 'integer', 'max' => 20, 'min' => 1, ], 'ConversationLogSettings' => [ 'type' => 'structure', 'members' => [ 'textLogSettings' => [ 'shape' => 'TextLogSettingsList', ], 'audioLogSettings' => [ 'shape' => 'AudioLogSettingsList', ], ], ], 'Count' => [ 'type' => 'integer', ], 'CreateBotAliasRequest' => [ 'type' => 'structure', 'required' => [ 'botAliasName', 'botId', ], 'members' => [ 'botAliasName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'botVersion' => [ 'shape' => 'NumericalBotVersion', ], 'botAliasLocaleSettings' => [ 'shape' => 'BotAliasLocaleSettingsMap', ], 'conversationLogSettings' => [ 'shape' => 'ConversationLogSettings', ], 'sentimentAnalysisSettings' => [ 'shape' => 'SentimentAnalysisSettings', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateBotAliasResponse' => [ 'type' => 'structure', 'members' => [ 'botAliasId' => [ 'shape' => 'BotAliasId', ], 'botAliasName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'botVersion' => [ 'shape' => 'NumericalBotVersion', ], 'botAliasLocaleSettings' => [ 'shape' => 'BotAliasLocaleSettingsMap', ], 'conversationLogSettings' => [ 'shape' => 'ConversationLogSettings', ], 'sentimentAnalysisSettings' => [ 'shape' => 'SentimentAnalysisSettings', ], 'botAliasStatus' => [ 'shape' => 'BotAliasStatus', ], 'botId' => [ 'shape' => 'Id', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateBotLocaleRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', 'nluIntentConfidenceThreshold', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'description' => [ 'shape' => 'Description', ], 'nluIntentConfidenceThreshold' => [ 'shape' => 'ConfidenceThreshold', ], 'voiceSettings' => [ 'shape' => 'VoiceSettings', ], ], ], 'CreateBotLocaleResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeName' => [ 'shape' => 'LocaleName', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'description' => [ 'shape' => 'Description', ], 'nluIntentConfidenceThreshold' => [ 'shape' => 'ConfidenceThreshold', ], 'voiceSettings' => [ 'shape' => 'VoiceSettings', ], 'botLocaleStatus' => [ 'shape' => 'BotLocaleStatus', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'CreateBotRequest' => [ 'type' => 'structure', 'required' => [ 'botName', 'roleArn', 'dataPrivacy', 'idleSessionTTLInSeconds', ], 'members' => [ 'botName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'dataPrivacy' => [ 'shape' => 'DataPrivacy', ], 'idleSessionTTLInSeconds' => [ 'shape' => 'SessionTTL', ], 'botTags' => [ 'shape' => 'TagMap', ], 'testBotAliasTags' => [ 'shape' => 'TagMap', ], ], ], 'CreateBotResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'dataPrivacy' => [ 'shape' => 'DataPrivacy', ], 'idleSessionTTLInSeconds' => [ 'shape' => 'SessionTTL', ], 'botStatus' => [ 'shape' => 'BotStatus', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'botTags' => [ 'shape' => 'TagMap', ], 'testBotAliasTags' => [ 'shape' => 'TagMap', ], ], ], 'CreateBotVersionRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersionLocaleSpecification', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'description' => [ 'shape' => 'Description', ], 'botVersionLocaleSpecification' => [ 'shape' => 'BotVersionLocaleSpecification', ], ], ], 'CreateBotVersionResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'description' => [ 'shape' => 'Description', ], 'botVersion' => [ 'shape' => 'NumericalBotVersion', ], 'botVersionLocaleSpecification' => [ 'shape' => 'BotVersionLocaleSpecification', ], 'botStatus' => [ 'shape' => 'BotStatus', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'CreateExportRequest' => [ 'type' => 'structure', 'required' => [ 'resourceSpecification', 'fileFormat', ], 'members' => [ 'resourceSpecification' => [ 'shape' => 'ExportResourceSpecification', ], 'fileFormat' => [ 'shape' => 'ImportExportFileFormat', ], 'filePassword' => [ 'shape' => 'ImportExportFilePassword', ], ], ], 'CreateExportResponse' => [ 'type' => 'structure', 'members' => [ 'exportId' => [ 'shape' => 'Id', ], 'resourceSpecification' => [ 'shape' => 'ExportResourceSpecification', ], 'fileFormat' => [ 'shape' => 'ImportExportFileFormat', ], 'exportStatus' => [ 'shape' => 'ExportStatus', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'CreateIntentRequest' => [ 'type' => 'structure', 'required' => [ 'intentName', 'botId', 'botVersion', 'localeId', ], 'members' => [ 'intentName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'parentIntentSignature' => [ 'shape' => 'IntentSignature', ], 'sampleUtterances' => [ 'shape' => 'SampleUtterancesList', ], 'dialogCodeHook' => [ 'shape' => 'DialogCodeHookSettings', ], 'fulfillmentCodeHook' => [ 'shape' => 'FulfillmentCodeHookSettings', ], 'intentConfirmationSetting' => [ 'shape' => 'IntentConfirmationSetting', ], 'intentClosingSetting' => [ 'shape' => 'IntentClosingSetting', ], 'inputContexts' => [ 'shape' => 'InputContextsList', ], 'outputContexts' => [ 'shape' => 'OutputContextsList', ], 'kendraConfiguration' => [ 'shape' => 'KendraConfiguration', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], ], ], 'CreateIntentResponse' => [ 'type' => 'structure', 'members' => [ 'intentId' => [ 'shape' => 'Id', ], 'intentName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'parentIntentSignature' => [ 'shape' => 'IntentSignature', ], 'sampleUtterances' => [ 'shape' => 'SampleUtterancesList', ], 'dialogCodeHook' => [ 'shape' => 'DialogCodeHookSettings', ], 'fulfillmentCodeHook' => [ 'shape' => 'FulfillmentCodeHookSettings', ], 'intentConfirmationSetting' => [ 'shape' => 'IntentConfirmationSetting', ], 'intentClosingSetting' => [ 'shape' => 'IntentClosingSetting', ], 'inputContexts' => [ 'shape' => 'InputContextsList', ], 'outputContexts' => [ 'shape' => 'OutputContextsList', ], 'kendraConfiguration' => [ 'shape' => 'KendraConfiguration', ], 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'CreateResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'policy', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'policy' => [ 'shape' => 'Policy', ], ], ], 'CreateResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', ], 'revisionId' => [ 'shape' => 'RevisionId', ], ], ], 'CreateResourcePolicyStatementRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'statementId', 'effect', 'principal', 'action', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'statementId' => [ 'shape' => 'Name', ], 'effect' => [ 'shape' => 'Effect', ], 'principal' => [ 'shape' => 'PrincipalList', ], 'action' => [ 'shape' => 'OperationList', ], 'condition' => [ 'shape' => 'ConditionMap', ], 'expectedRevisionId' => [ 'shape' => 'RevisionId', 'location' => 'querystring', 'locationName' => 'expectedRevisionId', ], ], ], 'CreateResourcePolicyStatementResponse' => [ 'type' => 'structure', 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', ], 'revisionId' => [ 'shape' => 'RevisionId', ], ], ], 'CreateSlotRequest' => [ 'type' => 'structure', 'required' => [ 'slotName', 'slotTypeId', 'valueElicitationSetting', 'botId', 'botVersion', 'localeId', 'intentId', ], 'members' => [ 'slotName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'slotTypeId' => [ 'shape' => 'BuiltInOrCustomSlotTypeId', ], 'valueElicitationSetting' => [ 'shape' => 'SlotValueElicitationSetting', ], 'obfuscationSetting' => [ 'shape' => 'ObfuscationSetting', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'intentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'intentId', ], 'multipleValuesSetting' => [ 'shape' => 'MultipleValuesSetting', ], ], ], 'CreateSlotResponse' => [ 'type' => 'structure', 'members' => [ 'slotId' => [ 'shape' => 'Id', ], 'slotName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'slotTypeId' => [ 'shape' => 'BuiltInOrCustomSlotTypeId', ], 'valueElicitationSetting' => [ 'shape' => 'SlotValueElicitationSetting', ], 'obfuscationSetting' => [ 'shape' => 'ObfuscationSetting', ], 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'intentId' => [ 'shape' => 'Id', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'multipleValuesSetting' => [ 'shape' => 'MultipleValuesSetting', ], ], ], 'CreateSlotTypeRequest' => [ 'type' => 'structure', 'required' => [ 'slotTypeName', 'botId', 'botVersion', 'localeId', ], 'members' => [ 'slotTypeName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'slotTypeValues' => [ 'shape' => 'SlotTypeValues', ], 'valueSelectionSetting' => [ 'shape' => 'SlotValueSelectionSetting', ], 'parentSlotTypeSignature' => [ 'shape' => 'SlotTypeSignature', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'externalSourceSetting' => [ 'shape' => 'ExternalSourceSetting', ], ], ], 'CreateSlotTypeResponse' => [ 'type' => 'structure', 'members' => [ 'slotTypeId' => [ 'shape' => 'Id', ], 'slotTypeName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'slotTypeValues' => [ 'shape' => 'SlotTypeValues', ], 'valueSelectionSetting' => [ 'shape' => 'SlotValueSelectionSetting', ], 'parentSlotTypeSignature' => [ 'shape' => 'SlotTypeSignature', ], 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'externalSourceSetting' => [ 'shape' => 'ExternalSourceSetting', ], ], ], 'CreateUploadUrlRequest' => [ 'type' => 'structure', 'members' => [], ], 'CreateUploadUrlResponse' => [ 'type' => 'structure', 'members' => [ 'importId' => [ 'shape' => 'Id', ], 'uploadUrl' => [ 'shape' => 'PresignedS3Url', ], ], ], 'CustomPayload' => [ 'type' => 'structure', 'required' => [ 'value', ], 'members' => [ 'value' => [ 'shape' => 'CustomPayloadValue', ], ], ], 'CustomPayloadValue' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'DataPrivacy' => [ 'type' => 'structure', 'required' => [ 'childDirected', ], 'members' => [ 'childDirected' => [ 'shape' => 'ChildDirected', ], ], ], 'DateRangeFilter' => [ 'type' => 'structure', 'required' => [ 'startDateTime', 'endDateTime', ], 'members' => [ 'startDateTime' => [ 'shape' => 'Timestamp', ], 'endDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'DeleteBotAliasRequest' => [ 'type' => 'structure', 'required' => [ 'botAliasId', 'botId', ], 'members' => [ 'botAliasId' => [ 'shape' => 'BotAliasId', 'location' => 'uri', 'locationName' => 'botAliasId', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'skipResourceInUseCheck' => [ 'shape' => 'SkipResourceInUseCheck', 'location' => 'querystring', 'locationName' => 'skipResourceInUseCheck', ], ], ], 'DeleteBotAliasResponse' => [ 'type' => 'structure', 'members' => [ 'botAliasId' => [ 'shape' => 'BotAliasId', ], 'botId' => [ 'shape' => 'Id', ], 'botAliasStatus' => [ 'shape' => 'BotAliasStatus', ], ], ], 'DeleteBotLocaleRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], ], ], 'DeleteBotLocaleResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'botLocaleStatus' => [ 'shape' => 'BotLocaleStatus', ], ], ], 'DeleteBotRequest' => [ 'type' => 'structure', 'required' => [ 'botId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'skipResourceInUseCheck' => [ 'shape' => 'SkipResourceInUseCheck', 'location' => 'querystring', 'locationName' => 'skipResourceInUseCheck', ], ], ], 'DeleteBotResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botStatus' => [ 'shape' => 'BotStatus', ], ], ], 'DeleteBotVersionRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'NumericalBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'skipResourceInUseCheck' => [ 'shape' => 'SkipResourceInUseCheck', 'location' => 'querystring', 'locationName' => 'skipResourceInUseCheck', ], ], ], 'DeleteBotVersionResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'NumericalBotVersion', ], 'botStatus' => [ 'shape' => 'BotStatus', ], ], ], 'DeleteExportRequest' => [ 'type' => 'structure', 'required' => [ 'exportId', ], 'members' => [ 'exportId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'exportId', ], ], ], 'DeleteExportResponse' => [ 'type' => 'structure', 'members' => [ 'exportId' => [ 'shape' => 'Id', ], 'exportStatus' => [ 'shape' => 'ExportStatus', ], ], ], 'DeleteImportRequest' => [ 'type' => 'structure', 'required' => [ 'importId', ], 'members' => [ 'importId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'importId', ], ], ], 'DeleteImportResponse' => [ 'type' => 'structure', 'members' => [ 'importId' => [ 'shape' => 'Id', ], 'importStatus' => [ 'shape' => 'ImportStatus', ], ], ], 'DeleteIntentRequest' => [ 'type' => 'structure', 'required' => [ 'intentId', 'botId', 'botVersion', 'localeId', ], 'members' => [ 'intentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'intentId', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], ], ], 'DeleteResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'expectedRevisionId' => [ 'shape' => 'RevisionId', 'location' => 'querystring', 'locationName' => 'expectedRevisionId', ], ], ], 'DeleteResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', ], 'revisionId' => [ 'shape' => 'RevisionId', ], ], ], 'DeleteResourcePolicyStatementRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'statementId', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'statementId' => [ 'shape' => 'Name', 'location' => 'uri', 'locationName' => 'statementId', ], 'expectedRevisionId' => [ 'shape' => 'RevisionId', 'location' => 'querystring', 'locationName' => 'expectedRevisionId', ], ], ], 'DeleteResourcePolicyStatementResponse' => [ 'type' => 'structure', 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', ], 'revisionId' => [ 'shape' => 'RevisionId', ], ], ], 'DeleteSlotRequest' => [ 'type' => 'structure', 'required' => [ 'slotId', 'botId', 'botVersion', 'localeId', 'intentId', ], 'members' => [ 'slotId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'slotId', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'intentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'intentId', ], ], ], 'DeleteSlotTypeRequest' => [ 'type' => 'structure', 'required' => [ 'slotTypeId', 'botId', 'botVersion', 'localeId', ], 'members' => [ 'slotTypeId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'slotTypeId', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'skipResourceInUseCheck' => [ 'shape' => 'SkipResourceInUseCheck', 'location' => 'querystring', 'locationName' => 'skipResourceInUseCheck', ], ], ], 'DeleteUtterancesRequest' => [ 'type' => 'structure', 'required' => [ 'botId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'querystring', 'locationName' => 'localeId', ], 'sessionId' => [ 'shape' => 'SessionId', 'location' => 'querystring', 'locationName' => 'sessionId', ], ], ], 'DeleteUtterancesResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeBotAliasRequest' => [ 'type' => 'structure', 'required' => [ 'botAliasId', 'botId', ], 'members' => [ 'botAliasId' => [ 'shape' => 'BotAliasId', 'location' => 'uri', 'locationName' => 'botAliasId', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], ], ], 'DescribeBotAliasResponse' => [ 'type' => 'structure', 'members' => [ 'botAliasId' => [ 'shape' => 'BotAliasId', ], 'botAliasName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'botAliasLocaleSettings' => [ 'shape' => 'BotAliasLocaleSettingsMap', ], 'conversationLogSettings' => [ 'shape' => 'ConversationLogSettings', ], 'sentimentAnalysisSettings' => [ 'shape' => 'SentimentAnalysisSettings', ], 'botAliasHistoryEvents' => [ 'shape' => 'BotAliasHistoryEventsList', ], 'botAliasStatus' => [ 'shape' => 'BotAliasStatus', ], 'botId' => [ 'shape' => 'Id', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeBotLocaleRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'BotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], ], ], 'DescribeBotLocaleResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'localeName' => [ 'shape' => 'LocaleName', ], 'description' => [ 'shape' => 'Description', ], 'nluIntentConfidenceThreshold' => [ 'shape' => 'ConfidenceThreshold', ], 'voiceSettings' => [ 'shape' => 'VoiceSettings', ], 'intentsCount' => [ 'shape' => 'ResourceCount', ], 'slotTypesCount' => [ 'shape' => 'ResourceCount', ], 'botLocaleStatus' => [ 'shape' => 'BotLocaleStatus', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'lastBuildSubmittedDateTime' => [ 'shape' => 'Timestamp', ], 'botLocaleHistoryEvents' => [ 'shape' => 'BotLocaleHistoryEventsList', ], 'recommendedActions' => [ 'shape' => 'RecommendedActions', ], ], ], 'DescribeBotRecommendationRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', 'botRecommendationId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'botRecommendationId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botRecommendationId', ], ], ], 'DescribeBotRecommendationResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'botRecommendationStatus' => [ 'shape' => 'BotRecommendationStatus', ], 'botRecommendationId' => [ 'shape' => 'Id', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'transcriptSourceSetting' => [ 'shape' => 'TranscriptSourceSetting', ], 'encryptionSetting' => [ 'shape' => 'EncryptionSetting', ], 'botRecommendationResults' => [ 'shape' => 'BotRecommendationResults', ], ], ], 'DescribeBotRequest' => [ 'type' => 'structure', 'required' => [ 'botId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], ], ], 'DescribeBotResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'dataPrivacy' => [ 'shape' => 'DataPrivacy', ], 'idleSessionTTLInSeconds' => [ 'shape' => 'SessionTTL', ], 'botStatus' => [ 'shape' => 'BotStatus', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeBotVersionRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'NumericalBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], ], ], 'DescribeBotVersionResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botName' => [ 'shape' => 'Name', ], 'botVersion' => [ 'shape' => 'NumericalBotVersion', ], 'description' => [ 'shape' => 'Description', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'dataPrivacy' => [ 'shape' => 'DataPrivacy', ], 'idleSessionTTLInSeconds' => [ 'shape' => 'SessionTTL', ], 'botStatus' => [ 'shape' => 'BotStatus', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeExportRequest' => [ 'type' => 'structure', 'required' => [ 'exportId', ], 'members' => [ 'exportId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'exportId', ], ], ], 'DescribeExportResponse' => [ 'type' => 'structure', 'members' => [ 'exportId' => [ 'shape' => 'Id', ], 'resourceSpecification' => [ 'shape' => 'ExportResourceSpecification', ], 'fileFormat' => [ 'shape' => 'ImportExportFileFormat', ], 'exportStatus' => [ 'shape' => 'ExportStatus', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], 'downloadUrl' => [ 'shape' => 'PresignedS3Url', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeImportRequest' => [ 'type' => 'structure', 'required' => [ 'importId', ], 'members' => [ 'importId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'importId', ], ], ], 'DescribeImportResponse' => [ 'type' => 'structure', 'members' => [ 'importId' => [ 'shape' => 'Id', ], 'resourceSpecification' => [ 'shape' => 'ImportResourceSpecification', ], 'importedResourceId' => [ 'shape' => 'ImportedResourceId', ], 'importedResourceName' => [ 'shape' => 'Name', ], 'mergeStrategy' => [ 'shape' => 'MergeStrategy', ], 'importStatus' => [ 'shape' => 'ImportStatus', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeIntentRequest' => [ 'type' => 'structure', 'required' => [ 'intentId', 'botId', 'botVersion', 'localeId', ], 'members' => [ 'intentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'intentId', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'BotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], ], ], 'DescribeIntentResponse' => [ 'type' => 'structure', 'members' => [ 'intentId' => [ 'shape' => 'Id', ], 'intentName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'parentIntentSignature' => [ 'shape' => 'IntentSignature', ], 'sampleUtterances' => [ 'shape' => 'SampleUtterancesList', ], 'dialogCodeHook' => [ 'shape' => 'DialogCodeHookSettings', ], 'fulfillmentCodeHook' => [ 'shape' => 'FulfillmentCodeHookSettings', ], 'slotPriorities' => [ 'shape' => 'SlotPrioritiesList', ], 'intentConfirmationSetting' => [ 'shape' => 'IntentConfirmationSetting', ], 'intentClosingSetting' => [ 'shape' => 'IntentClosingSetting', ], 'inputContexts' => [ 'shape' => 'InputContextsList', ], 'outputContexts' => [ 'shape' => 'OutputContextsList', ], 'kendraConfiguration' => [ 'shape' => 'KendraConfiguration', ], 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'DescribeResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', ], 'policy' => [ 'shape' => 'Policy', ], 'revisionId' => [ 'shape' => 'RevisionId', ], ], ], 'DescribeSlotRequest' => [ 'type' => 'structure', 'required' => [ 'slotId', 'botId', 'botVersion', 'localeId', 'intentId', ], 'members' => [ 'slotId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'slotId', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'BotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'intentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'intentId', ], ], ], 'DescribeSlotResponse' => [ 'type' => 'structure', 'members' => [ 'slotId' => [ 'shape' => 'Id', ], 'slotName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'slotTypeId' => [ 'shape' => 'BuiltInOrCustomSlotTypeId', ], 'valueElicitationSetting' => [ 'shape' => 'SlotValueElicitationSetting', ], 'obfuscationSetting' => [ 'shape' => 'ObfuscationSetting', ], 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'intentId' => [ 'shape' => 'Id', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'multipleValuesSetting' => [ 'shape' => 'MultipleValuesSetting', ], ], ], 'DescribeSlotTypeRequest' => [ 'type' => 'structure', 'required' => [ 'slotTypeId', 'botId', 'botVersion', 'localeId', ], 'members' => [ 'slotTypeId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'slotTypeId', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'BotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], ], ], 'DescribeSlotTypeResponse' => [ 'type' => 'structure', 'members' => [ 'slotTypeId' => [ 'shape' => 'Id', ], 'slotTypeName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'slotTypeValues' => [ 'shape' => 'SlotTypeValues', ], 'valueSelectionSetting' => [ 'shape' => 'SlotValueSelectionSetting', ], 'parentSlotTypeSignature' => [ 'shape' => 'SlotTypeSignature', ], 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'externalSourceSetting' => [ 'shape' => 'ExternalSourceSetting', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 200, 'min' => 0, ], 'DialogCodeHookSettings' => [ 'type' => 'structure', 'required' => [ 'enabled', ], 'members' => [ 'enabled' => [ 'shape' => 'Boolean', ], ], ], 'DraftBotVersion' => [ 'type' => 'string', 'max' => 5, 'min' => 5, 'pattern' => '^DRAFT$', ], 'Effect' => [ 'type' => 'string', 'enum' => [ 'Allow', 'Deny', ], ], 'EncryptionSetting' => [ 'type' => 'structure', 'members' => [ 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'botLocaleExportPassword' => [ 'shape' => 'FilePassword', ], 'associatedTranscriptsPassword' => [ 'shape' => 'FilePassword', ], ], ], 'ExceptionMessage' => [ 'type' => 'string', ], 'ExportFilter' => [ 'type' => 'structure', 'required' => [ 'name', 'values', 'operator', ], 'members' => [ 'name' => [ 'shape' => 'ExportFilterName', ], 'values' => [ 'shape' => 'FilterValues', ], 'operator' => [ 'shape' => 'ExportFilterOperator', ], ], ], 'ExportFilterName' => [ 'type' => 'string', 'enum' => [ 'ExportResourceType', ], ], 'ExportFilterOperator' => [ 'type' => 'string', 'enum' => [ 'CO', 'EQ', ], ], 'ExportFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExportFilter', ], 'max' => 1, 'min' => 1, ], 'ExportResourceSpecification' => [ 'type' => 'structure', 'members' => [ 'botExportSpecification' => [ 'shape' => 'BotExportSpecification', ], 'botLocaleExportSpecification' => [ 'shape' => 'BotLocaleExportSpecification', ], ], ], 'ExportSortAttribute' => [ 'type' => 'string', 'enum' => [ 'LastUpdatedDateTime', ], ], 'ExportSortBy' => [ 'type' => 'structure', 'required' => [ 'attribute', 'order', ], 'members' => [ 'attribute' => [ 'shape' => 'ExportSortAttribute', ], 'order' => [ 'shape' => 'SortOrder', ], ], ], 'ExportStatus' => [ 'type' => 'string', 'enum' => [ 'InProgress', 'Completed', 'Failed', 'Deleting', ], ], 'ExportSummary' => [ 'type' => 'structure', 'members' => [ 'exportId' => [ 'shape' => 'Id', ], 'resourceSpecification' => [ 'shape' => 'ExportResourceSpecification', ], 'fileFormat' => [ 'shape' => 'ImportExportFileFormat', ], 'exportStatus' => [ 'shape' => 'ExportStatus', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ExportSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExportSummary', ], ], 'ExternalSourceSetting' => [ 'type' => 'structure', 'members' => [ 'grammarSlotTypeSetting' => [ 'shape' => 'GrammarSlotTypeSetting', ], ], ], 'FailureReason' => [ 'type' => 'string', ], 'FailureReasons' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailureReason', ], ], 'FilePassword' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'sensitive' => true, ], 'FilterValue' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[0-9a-zA-Z_()\\s-]+$', ], 'FilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterValue', ], 'max' => 1, 'min' => 1, ], 'FulfillmentCodeHookSettings' => [ 'type' => 'structure', 'required' => [ 'enabled', ], 'members' => [ 'enabled' => [ 'shape' => 'Boolean', ], 'postFulfillmentStatusSpecification' => [ 'shape' => 'PostFulfillmentStatusSpecification', ], 'fulfillmentUpdatesSpecification' => [ 'shape' => 'FulfillmentUpdatesSpecification', ], ], ], 'FulfillmentStartResponseDelay' => [ 'type' => 'integer', 'max' => 900, 'min' => 1, ], 'FulfillmentStartResponseSpecification' => [ 'type' => 'structure', 'required' => [ 'delayInSeconds', 'messageGroups', ], 'members' => [ 'delayInSeconds' => [ 'shape' => 'FulfillmentStartResponseDelay', ], 'messageGroups' => [ 'shape' => 'MessageGroupsList', ], 'allowInterrupt' => [ 'shape' => 'BoxedBoolean', ], ], ], 'FulfillmentTimeout' => [ 'type' => 'integer', 'max' => 900, 'min' => 1, ], 'FulfillmentUpdateResponseFrequency' => [ 'type' => 'integer', 'max' => 900, 'min' => 1, ], 'FulfillmentUpdateResponseSpecification' => [ 'type' => 'structure', 'required' => [ 'frequencyInSeconds', 'messageGroups', ], 'members' => [ 'frequencyInSeconds' => [ 'shape' => 'FulfillmentUpdateResponseFrequency', ], 'messageGroups' => [ 'shape' => 'MessageGroupsList', ], 'allowInterrupt' => [ 'shape' => 'BoxedBoolean', ], ], ], 'FulfillmentUpdatesSpecification' => [ 'type' => 'structure', 'required' => [ 'active', ], 'members' => [ 'active' => [ 'shape' => 'BoxedBoolean', ], 'startResponse' => [ 'shape' => 'FulfillmentStartResponseSpecification', ], 'updateResponse' => [ 'shape' => 'FulfillmentUpdateResponseSpecification', ], 'timeoutInSeconds' => [ 'shape' => 'FulfillmentTimeout', ], ], ], 'GrammarSlotTypeSetting' => [ 'type' => 'structure', 'members' => [ 'source' => [ 'shape' => 'GrammarSlotTypeSource', ], ], ], 'GrammarSlotTypeSource' => [ 'type' => 'structure', 'required' => [ 's3BucketName', 's3ObjectKey', ], 'members' => [ 's3BucketName' => [ 'shape' => 'S3BucketName', ], 's3ObjectKey' => [ 'shape' => 'S3ObjectPath', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], ], ], 'HitCount' => [ 'type' => 'integer', ], 'Id' => [ 'type' => 'string', 'max' => 10, 'min' => 10, 'pattern' => '^[0-9a-zA-Z]+$', ], 'ImageResponseCard' => [ 'type' => 'structure', 'required' => [ 'title', ], 'members' => [ 'title' => [ 'shape' => 'AttachmentTitle', ], 'subtitle' => [ 'shape' => 'AttachmentTitle', ], 'imageUrl' => [ 'shape' => 'AttachmentUrl', ], 'buttons' => [ 'shape' => 'ButtonsList', ], ], ], 'ImportExportFileFormat' => [ 'type' => 'string', 'enum' => [ 'LexJson', ], ], 'ImportExportFilePassword' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'sensitive' => true, ], 'ImportFilter' => [ 'type' => 'structure', 'required' => [ 'name', 'values', 'operator', ], 'members' => [ 'name' => [ 'shape' => 'ImportFilterName', ], 'values' => [ 'shape' => 'FilterValues', ], 'operator' => [ 'shape' => 'ImportFilterOperator', ], ], ], 'ImportFilterName' => [ 'type' => 'string', 'enum' => [ 'ImportResourceType', ], ], 'ImportFilterOperator' => [ 'type' => 'string', 'enum' => [ 'CO', 'EQ', ], ], 'ImportFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImportFilter', ], 'max' => 1, 'min' => 1, ], 'ImportResourceSpecification' => [ 'type' => 'structure', 'members' => [ 'botImportSpecification' => [ 'shape' => 'BotImportSpecification', ], 'botLocaleImportSpecification' => [ 'shape' => 'BotLocaleImportSpecification', ], ], ], 'ImportSortAttribute' => [ 'type' => 'string', 'enum' => [ 'LastUpdatedDateTime', ], ], 'ImportSortBy' => [ 'type' => 'structure', 'required' => [ 'attribute', 'order', ], 'members' => [ 'attribute' => [ 'shape' => 'ImportSortAttribute', ], 'order' => [ 'shape' => 'SortOrder', ], ], ], 'ImportStatus' => [ 'type' => 'string', 'enum' => [ 'InProgress', 'Completed', 'Failed', 'Deleting', ], ], 'ImportSummary' => [ 'type' => 'structure', 'members' => [ 'importId' => [ 'shape' => 'Id', ], 'importedResourceId' => [ 'shape' => 'ImportedResourceId', ], 'importedResourceName' => [ 'shape' => 'Name', ], 'importStatus' => [ 'shape' => 'ImportStatus', ], 'mergeStrategy' => [ 'shape' => 'MergeStrategy', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ImportSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImportSummary', ], ], 'ImportedResourceId' => [ 'type' => 'string', 'max' => 10, 'min' => 5, 'pattern' => '^([0-9a-zA-Z_])+$', ], 'InputContext' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], ], ], 'InputContextsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputContext', ], 'max' => 5, 'min' => 0, ], 'IntentClosingSetting' => [ 'type' => 'structure', 'required' => [ 'closingResponse', ], 'members' => [ 'closingResponse' => [ 'shape' => 'ResponseSpecification', ], 'active' => [ 'shape' => 'BoxedBoolean', ], ], ], 'IntentConfirmationSetting' => [ 'type' => 'structure', 'required' => [ 'promptSpecification', 'declinationResponse', ], 'members' => [ 'promptSpecification' => [ 'shape' => 'PromptSpecification', ], 'declinationResponse' => [ 'shape' => 'ResponseSpecification', ], 'active' => [ 'shape' => 'BoxedBoolean', ], ], ], 'IntentFilter' => [ 'type' => 'structure', 'required' => [ 'name', 'values', 'operator', ], 'members' => [ 'name' => [ 'shape' => 'IntentFilterName', ], 'values' => [ 'shape' => 'FilterValues', ], 'operator' => [ 'shape' => 'IntentFilterOperator', ], ], ], 'IntentFilterName' => [ 'type' => 'string', 'enum' => [ 'IntentName', ], ], 'IntentFilterOperator' => [ 'type' => 'string', 'enum' => [ 'CO', 'EQ', ], ], 'IntentFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'IntentFilter', ], 'max' => 1, 'min' => 1, ], 'IntentSignature' => [ 'type' => 'string', ], 'IntentSortAttribute' => [ 'type' => 'string', 'enum' => [ 'IntentName', 'LastUpdatedDateTime', ], ], 'IntentSortBy' => [ 'type' => 'structure', 'required' => [ 'attribute', 'order', ], 'members' => [ 'attribute' => [ 'shape' => 'IntentSortAttribute', ], 'order' => [ 'shape' => 'SortOrder', ], ], ], 'IntentStatistics' => [ 'type' => 'structure', 'members' => [ 'discoveredIntentCount' => [ 'shape' => 'Count', ], ], ], 'IntentSummary' => [ 'type' => 'structure', 'members' => [ 'intentId' => [ 'shape' => 'Id', ], 'intentName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'parentIntentSignature' => [ 'shape' => 'IntentSignature', ], 'inputContexts' => [ 'shape' => 'InputContextsList', ], 'outputContexts' => [ 'shape' => 'OutputContextsList', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'IntentSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IntentSummary', ], ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'KendraConfiguration' => [ 'type' => 'structure', 'required' => [ 'kendraIndex', ], 'members' => [ 'kendraIndex' => [ 'shape' => 'KendraIndexArn', ], 'queryFilterStringEnabled' => [ 'shape' => 'Boolean', ], 'queryFilterString' => [ 'shape' => 'QueryFilterString', ], ], ], 'KendraIndexArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 32, 'pattern' => '^arn:aws:kendra:[a-z]+-[a-z]+-[0-9]:[0-9]{12}:index\\/[a-zA-Z0-9][a-zA-Z0-9_-]*$', ], 'KmsKeyArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:[\\w\\-]+:kms:[\\w\\-]+:[\\d]{12}:(?:key\\/[\\w\\-]+|alias\\/[a-zA-Z0-9:\\/_\\-]{1,256})$', ], 'LambdaARN' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws:lambda:[a-z]+-[a-z]+-[0-9]:[0-9]{12}:function:[a-zA-Z0-9-_]+(/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})?(:[a-zA-Z0-9-_]+)?', ], 'LambdaCodeHook' => [ 'type' => 'structure', 'required' => [ 'lambdaARN', 'codeHookInterfaceVersion', ], 'members' => [ 'lambdaARN' => [ 'shape' => 'LambdaARN', ], 'codeHookInterfaceVersion' => [ 'shape' => 'CodeHookInterfaceVersion', ], ], ], 'LexTranscriptFilter' => [ 'type' => 'structure', 'members' => [ 'dateRangeFilter' => [ 'shape' => 'DateRangeFilter', ], ], ], 'ListAggregatedUtterancesRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'localeId', 'aggregationDuration', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botAliasId' => [ 'shape' => 'BotAliasId', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'aggregationDuration' => [ 'shape' => 'UtteranceAggregationDuration', ], 'sortBy' => [ 'shape' => 'AggregatedUtterancesSortBy', ], 'filters' => [ 'shape' => 'AggregatedUtterancesFilters', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAggregatedUtterancesResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botAliasId' => [ 'shape' => 'BotAliasId', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'aggregationDuration' => [ 'shape' => 'UtteranceAggregationDuration', ], 'aggregationWindowStartTime' => [ 'shape' => 'Timestamp', ], 'aggregationWindowEndTime' => [ 'shape' => 'Timestamp', ], 'aggregationLastRefreshedDateTime' => [ 'shape' => 'Timestamp', ], 'aggregatedUtterancesSummaries' => [ 'shape' => 'AggregatedUtterancesSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBotAliasesRequest' => [ 'type' => 'structure', 'required' => [ 'botId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBotAliasesResponse' => [ 'type' => 'structure', 'members' => [ 'botAliasSummaries' => [ 'shape' => 'BotAliasSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'botId' => [ 'shape' => 'Id', ], ], ], 'ListBotLocalesRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'BotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'sortBy' => [ 'shape' => 'BotLocaleSortBy', ], 'filters' => [ 'shape' => 'BotLocaleFilters', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBotLocalesResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'botLocaleSummaries' => [ 'shape' => 'BotLocaleSummaryList', ], ], ], 'ListBotRecommendationsRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBotRecommendationsResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'botRecommendationSummaries' => [ 'shape' => 'BotRecommendationSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBotVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'botId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'sortBy' => [ 'shape' => 'BotVersionSortBy', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBotVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersionSummaries' => [ 'shape' => 'BotVersionSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBotsRequest' => [ 'type' => 'structure', 'members' => [ 'sortBy' => [ 'shape' => 'BotSortBy', ], 'filters' => [ 'shape' => 'BotFilters', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBotsResponse' => [ 'type' => 'structure', 'members' => [ 'botSummaries' => [ 'shape' => 'BotSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBuiltInIntentsRequest' => [ 'type' => 'structure', 'required' => [ 'localeId', ], 'members' => [ 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'sortBy' => [ 'shape' => 'BuiltInIntentSortBy', ], 'maxResults' => [ 'shape' => 'BuiltInsMaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBuiltInIntentsResponse' => [ 'type' => 'structure', 'members' => [ 'builtInIntentSummaries' => [ 'shape' => 'BuiltInIntentSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'localeId' => [ 'shape' => 'LocaleId', ], ], ], 'ListBuiltInSlotTypesRequest' => [ 'type' => 'structure', 'required' => [ 'localeId', ], 'members' => [ 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'sortBy' => [ 'shape' => 'BuiltInSlotTypeSortBy', ], 'maxResults' => [ 'shape' => 'BuiltInsMaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBuiltInSlotTypesResponse' => [ 'type' => 'structure', 'members' => [ 'builtInSlotTypeSummaries' => [ 'shape' => 'BuiltInSlotTypeSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'localeId' => [ 'shape' => 'LocaleId', ], ], ], 'ListExportsRequest' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'sortBy' => [ 'shape' => 'ExportSortBy', ], 'filters' => [ 'shape' => 'ExportFilters', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListExportsResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'exportSummaries' => [ 'shape' => 'ExportSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListImportsRequest' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'sortBy' => [ 'shape' => 'ImportSortBy', ], 'filters' => [ 'shape' => 'ImportFilters', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListImportsResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'importSummaries' => [ 'shape' => 'ImportSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListIntentsRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'BotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'sortBy' => [ 'shape' => 'IntentSortBy', ], 'filters' => [ 'shape' => 'IntentFilters', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListIntentsResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'intentSummaries' => [ 'shape' => 'IntentSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListRecommendedIntentsRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', 'botRecommendationId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'botRecommendationId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botRecommendationId', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListRecommendedIntentsResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'botRecommendationId' => [ 'shape' => 'Id', ], 'summaryList' => [ 'shape' => 'RecommendedIntentSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSlotTypesRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'BotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'sortBy' => [ 'shape' => 'SlotTypeSortBy', ], 'filters' => [ 'shape' => 'SlotTypeFilters', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSlotTypesResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'slotTypeSummaries' => [ 'shape' => 'SlotTypeSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSlotsRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', 'intentId', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'BotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'intentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'intentId', ], 'sortBy' => [ 'shape' => 'SlotSortBy', ], 'filters' => [ 'shape' => 'SlotFilters', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSlotsResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'intentId' => [ 'shape' => 'Id', ], 'slotSummaries' => [ 'shape' => 'SlotSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', ], 'members' => [ 'resourceARN' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceARN', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'LocaleId' => [ 'type' => 'string', ], 'LocaleName' => [ 'type' => 'string', ], 'LogPrefix' => [ 'type' => 'string', 'max' => 1024, ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'MergeStrategy' => [ 'type' => 'string', 'enum' => [ 'Overwrite', 'FailOnConflict', 'Append', ], ], 'Message' => [ 'type' => 'structure', 'members' => [ 'plainTextMessage' => [ 'shape' => 'PlainTextMessage', ], 'customPayload' => [ 'shape' => 'CustomPayload', ], 'ssmlMessage' => [ 'shape' => 'SSMLMessage', ], 'imageResponseCard' => [ 'shape' => 'ImageResponseCard', ], ], ], 'MessageGroup' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'Message', ], 'variations' => [ 'shape' => 'MessageVariationsList', ], ], ], 'MessageGroupsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MessageGroup', ], 'max' => 5, 'min' => 1, ], 'MessageVariationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Message', ], 'max' => 2, 'min' => 0, ], 'MissedCount' => [ 'type' => 'integer', ], 'MultipleValuesSetting' => [ 'type' => 'structure', 'members' => [ 'allowMultipleValues' => [ 'shape' => 'Boolean', ], ], ], 'Name' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^([0-9a-zA-Z][_-]?)+$', ], 'NextIndex' => [ 'type' => 'integer', 'box' => true, 'max' => 10000000, 'min' => 0, ], 'NextToken' => [ 'type' => 'string', ], 'NumericalBotVersion' => [ 'type' => 'string', 'max' => 5, 'min' => 1, 'pattern' => '^[0-9]+$', ], 'ObfuscationSetting' => [ 'type' => 'structure', 'required' => [ 'obfuscationSettingType', ], 'members' => [ 'obfuscationSettingType' => [ 'shape' => 'ObfuscationSettingType', ], ], ], 'ObfuscationSettingType' => [ 'type' => 'string', 'enum' => [ 'None', 'DefaultObfuscation', ], ], 'ObjectPrefix' => [ 'type' => 'string', 'min' => 1, 'pattern' => '^[\\/]?+[a-zA-Z0-9!_.*\'()-]+(\\/[a-zA-Z0-9!_.*\'()-]+)*$', ], 'ObjectPrefixes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ObjectPrefix', ], 'max' => 2, 'min' => 1, ], 'Operation' => [ 'type' => 'string', 'max' => 50, 'min' => 5, 'pattern' => 'lex:[a-zA-Z*]+$', ], 'OperationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Operation', ], ], 'OutputContext' => [ 'type' => 'structure', 'required' => [ 'name', 'timeToLiveInSeconds', 'turnsToLive', ], 'members' => [ 'name' => [ 'shape' => 'Name', ], 'timeToLiveInSeconds' => [ 'shape' => 'ContextTimeToLiveInSeconds', ], 'turnsToLive' => [ 'shape' => 'ContextTurnsToLive', ], ], ], 'OutputContextsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OutputContext', ], 'max' => 10, 'min' => 0, ], 'PathFormat' => [ 'type' => 'structure', 'members' => [ 'objectPrefixes' => [ 'shape' => 'ObjectPrefixes', ], ], ], 'PlainTextMessage' => [ 'type' => 'structure', 'required' => [ 'value', ], 'members' => [ 'value' => [ 'shape' => 'PlainTextMessageValue', ], ], ], 'PlainTextMessageValue' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'Policy' => [ 'type' => 'string', 'min' => 2, ], 'PostFulfillmentStatusSpecification' => [ 'type' => 'structure', 'members' => [ 'successResponse' => [ 'shape' => 'ResponseSpecification', ], 'failureResponse' => [ 'shape' => 'ResponseSpecification', ], 'timeoutResponse' => [ 'shape' => 'ResponseSpecification', ], ], ], 'PreconditionFailedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 412, ], 'exception' => true, ], 'PresignedS3Url' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'Principal' => [ 'type' => 'structure', 'members' => [ 'service' => [ 'shape' => 'ServicePrincipal', ], 'arn' => [ 'shape' => 'PrincipalArn', ], ], ], 'PrincipalArn' => [ 'type' => 'string', 'max' => 1024, 'min' => 30, 'pattern' => '^arn:aws:iam::[0-9]{12}:(root|(user|role)/.*)$', ], 'PrincipalList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Principal', ], ], 'PriorityValue' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'PromptMaxRetries' => [ 'type' => 'integer', 'max' => 5, 'min' => 0, ], 'PromptSpecification' => [ 'type' => 'structure', 'required' => [ 'messageGroups', 'maxRetries', ], 'members' => [ 'messageGroups' => [ 'shape' => 'MessageGroupsList', ], 'maxRetries' => [ 'shape' => 'PromptMaxRetries', ], 'allowInterrupt' => [ 'shape' => 'BoxedBoolean', ], ], ], 'QueryFilterString' => [ 'type' => 'string', 'max' => 5000, 'min' => 1, ], 'RecommendedAction' => [ 'type' => 'string', ], 'RecommendedActions' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendedAction', ], ], 'RecommendedIntentSummary' => [ 'type' => 'structure', 'members' => [ 'intentId' => [ 'shape' => 'Id', ], 'intentName' => [ 'shape' => 'Name', ], 'sampleUtterancesCount' => [ 'shape' => 'SampleUtterancesCount', ], ], ], 'RecommendedIntentSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendedIntentSummary', ], ], 'RegexPattern' => [ 'type' => 'string', 'max' => 300, 'min' => 1, ], 'RelativeAggregationDuration' => [ 'type' => 'structure', 'required' => [ 'timeDimension', 'timeValue', ], 'members' => [ 'timeDimension' => [ 'shape' => 'TimeDimension', ], 'timeValue' => [ 'shape' => 'TimeValue', ], ], ], 'ResourceCount' => [ 'type' => 'integer', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ResponseSpecification' => [ 'type' => 'structure', 'required' => [ 'messageGroups', ], 'members' => [ 'messageGroups' => [ 'shape' => 'MessageGroupsList', ], 'allowInterrupt' => [ 'shape' => 'BoxedBoolean', ], ], ], 'RetryAfterSeconds' => [ 'type' => 'integer', ], 'RevisionId' => [ 'type' => 'string', 'max' => 5, 'min' => 1, 'pattern' => '^[0-9]+$', ], 'RoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 32, 'pattern' => '^arn:aws:iam::[0-9]{12}:role/.*$', ], 'S3BucketArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^arn:[\\w\\-]+:s3:::[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9]$', ], 'S3BucketLogDestination' => [ 'type' => 'structure', 'required' => [ 's3BucketArn', 'logPrefix', ], 'members' => [ 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 's3BucketArn' => [ 'shape' => 'S3BucketArn', ], 'logPrefix' => [ 'shape' => 'LogPrefix', ], ], ], 'S3BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '^[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9]$', ], 'S3BucketTranscriptSource' => [ 'type' => 'structure', 'required' => [ 's3BucketName', 'transcriptFormat', ], 'members' => [ 's3BucketName' => [ 'shape' => 'S3BucketName', ], 'pathFormat' => [ 'shape' => 'PathFormat', ], 'transcriptFormat' => [ 'shape' => 'TranscriptFormat', ], 'transcriptFilter' => [ 'shape' => 'TranscriptFilter', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], ], ], 'S3ObjectPath' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[\\.\\-\\!\\*\\_\\\'\\(\\)a-zA-Z0-9][\\.\\-\\!\\*\\_\\\'\\(\\)\\/a-zA-Z0-9]*$', ], 'SSMLMessage' => [ 'type' => 'structure', 'required' => [ 'value', ], 'members' => [ 'value' => [ 'shape' => 'SSMLMessageValue', ], ], ], 'SSMLMessageValue' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'SampleUtterance' => [ 'type' => 'structure', 'required' => [ 'utterance', ], 'members' => [ 'utterance' => [ 'shape' => 'Utterance', ], ], ], 'SampleUtterancesCount' => [ 'type' => 'integer', ], 'SampleUtterancesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SampleUtterance', ], ], 'SampleValue' => [ 'type' => 'structure', 'required' => [ 'value', ], 'members' => [ 'value' => [ 'shape' => 'Value', ], ], ], 'SearchAssociatedTranscriptsRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', 'botRecommendationId', 'filters', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'BotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'botRecommendationId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botRecommendationId', ], 'searchOrder' => [ 'shape' => 'SearchOrder', ], 'filters' => [ 'shape' => 'AssociatedTranscriptFilters', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextIndex' => [ 'shape' => 'NextIndex', ], ], ], 'SearchAssociatedTranscriptsResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'botRecommendationId' => [ 'shape' => 'Id', ], 'nextIndex' => [ 'shape' => 'NextIndex', ], 'associatedTranscripts' => [ 'shape' => 'AssociatedTranscriptList', ], 'totalResults' => [ 'shape' => 'MaxResults', ], ], ], 'SearchOrder' => [ 'type' => 'string', 'enum' => [ 'Ascending', 'Descending', ], ], 'SentimentAnalysisSettings' => [ 'type' => 'structure', 'required' => [ 'detectSentiment', ], 'members' => [ 'detectSentiment' => [ 'shape' => 'Boolean', ], ], ], 'ServicePrincipal' => [ 'type' => 'string', 'max' => 1024, 'min' => 15, 'pattern' => '^[0-9a-zA-Z_.]+$', ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 402, ], 'exception' => true, ], 'SessionId' => [ 'type' => 'string', 'max' => 100, 'min' => 2, 'pattern' => '[0-9a-zA-Z._:-]+', ], 'SessionTTL' => [ 'type' => 'integer', 'max' => 86400, 'min' => 60, ], 'SkipResourceInUseCheck' => [ 'type' => 'boolean', ], 'SlotConstraint' => [ 'type' => 'string', 'enum' => [ 'Required', 'Optional', ], ], 'SlotDefaultValue' => [ 'type' => 'structure', 'required' => [ 'defaultValue', ], 'members' => [ 'defaultValue' => [ 'shape' => 'SlotDefaultValueString', ], ], ], 'SlotDefaultValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SlotDefaultValue', ], 'max' => 10, 'min' => 0, ], 'SlotDefaultValueSpecification' => [ 'type' => 'structure', 'required' => [ 'defaultValueList', ], 'members' => [ 'defaultValueList' => [ 'shape' => 'SlotDefaultValueList', ], ], ], 'SlotDefaultValueString' => [ 'type' => 'string', 'max' => 202, 'min' => 1, ], 'SlotFilter' => [ 'type' => 'structure', 'required' => [ 'name', 'values', 'operator', ], 'members' => [ 'name' => [ 'shape' => 'SlotFilterName', ], 'values' => [ 'shape' => 'FilterValues', ], 'operator' => [ 'shape' => 'SlotFilterOperator', ], ], ], 'SlotFilterName' => [ 'type' => 'string', 'enum' => [ 'SlotName', ], ], 'SlotFilterOperator' => [ 'type' => 'string', 'enum' => [ 'CO', 'EQ', ], ], 'SlotFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'SlotFilter', ], 'max' => 1, 'min' => 1, ], 'SlotPrioritiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SlotPriority', ], ], 'SlotPriority' => [ 'type' => 'structure', 'required' => [ 'priority', 'slotId', ], 'members' => [ 'priority' => [ 'shape' => 'PriorityValue', ], 'slotId' => [ 'shape' => 'Id', ], ], ], 'SlotSortAttribute' => [ 'type' => 'string', 'enum' => [ 'SlotName', 'LastUpdatedDateTime', ], ], 'SlotSortBy' => [ 'type' => 'structure', 'required' => [ 'attribute', 'order', ], 'members' => [ 'attribute' => [ 'shape' => 'SlotSortAttribute', ], 'order' => [ 'shape' => 'SortOrder', ], ], ], 'SlotSummary' => [ 'type' => 'structure', 'members' => [ 'slotId' => [ 'shape' => 'Id', ], 'slotName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'slotConstraint' => [ 'shape' => 'SlotConstraint', ], 'slotTypeId' => [ 'shape' => 'BuiltInOrCustomSlotTypeId', ], 'valueElicitationPromptSpecification' => [ 'shape' => 'PromptSpecification', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'SlotSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SlotSummary', ], ], 'SlotTypeCategory' => [ 'type' => 'string', 'enum' => [ 'Custom', 'Extended', 'ExternalGrammar', ], ], 'SlotTypeFilter' => [ 'type' => 'structure', 'required' => [ 'name', 'values', 'operator', ], 'members' => [ 'name' => [ 'shape' => 'SlotTypeFilterName', ], 'values' => [ 'shape' => 'FilterValues', ], 'operator' => [ 'shape' => 'SlotTypeFilterOperator', ], ], ], 'SlotTypeFilterName' => [ 'type' => 'string', 'enum' => [ 'SlotTypeName', 'ExternalSourceType', ], ], 'SlotTypeFilterOperator' => [ 'type' => 'string', 'enum' => [ 'CO', 'EQ', ], ], 'SlotTypeFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'SlotTypeFilter', ], 'max' => 1, 'min' => 1, ], 'SlotTypeSignature' => [ 'type' => 'string', ], 'SlotTypeSortAttribute' => [ 'type' => 'string', 'enum' => [ 'SlotTypeName', 'LastUpdatedDateTime', ], ], 'SlotTypeSortBy' => [ 'type' => 'structure', 'required' => [ 'attribute', 'order', ], 'members' => [ 'attribute' => [ 'shape' => 'SlotTypeSortAttribute', ], 'order' => [ 'shape' => 'SortOrder', ], ], ], 'SlotTypeStatistics' => [ 'type' => 'structure', 'members' => [ 'discoveredSlotTypeCount' => [ 'shape' => 'Count', ], ], ], 'SlotTypeSummary' => [ 'type' => 'structure', 'members' => [ 'slotTypeId' => [ 'shape' => 'Id', ], 'slotTypeName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'parentSlotTypeSignature' => [ 'shape' => 'SlotTypeSignature', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'slotTypeCategory' => [ 'shape' => 'SlotTypeCategory', ], ], ], 'SlotTypeSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SlotTypeSummary', ], ], 'SlotTypeValue' => [ 'type' => 'structure', 'members' => [ 'sampleValue' => [ 'shape' => 'SampleValue', ], 'synonyms' => [ 'shape' => 'SynonymList', ], ], ], 'SlotTypeValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'SlotTypeValue', ], 'max' => 10000, 'min' => 1, ], 'SlotValueElicitationSetting' => [ 'type' => 'structure', 'required' => [ 'slotConstraint', ], 'members' => [ 'defaultValueSpecification' => [ 'shape' => 'SlotDefaultValueSpecification', ], 'slotConstraint' => [ 'shape' => 'SlotConstraint', ], 'promptSpecification' => [ 'shape' => 'PromptSpecification', ], 'sampleUtterances' => [ 'shape' => 'SampleUtterancesList', ], 'waitAndContinueSpecification' => [ 'shape' => 'WaitAndContinueSpecification', ], ], ], 'SlotValueRegexFilter' => [ 'type' => 'structure', 'required' => [ 'pattern', ], 'members' => [ 'pattern' => [ 'shape' => 'RegexPattern', ], ], ], 'SlotValueResolutionStrategy' => [ 'type' => 'string', 'enum' => [ 'OriginalValue', 'TopResolution', ], ], 'SlotValueSelectionSetting' => [ 'type' => 'structure', 'required' => [ 'resolutionStrategy', ], 'members' => [ 'resolutionStrategy' => [ 'shape' => 'SlotValueResolutionStrategy', ], 'regexFilter' => [ 'shape' => 'SlotValueRegexFilter', ], ], ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'Ascending', 'Descending', ], ], 'StartBotRecommendationRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', 'transcriptSourceSetting', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'transcriptSourceSetting' => [ 'shape' => 'TranscriptSourceSetting', ], 'encryptionSetting' => [ 'shape' => 'EncryptionSetting', ], ], ], 'StartBotRecommendationResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'botRecommendationStatus' => [ 'shape' => 'BotRecommendationStatus', ], 'botRecommendationId' => [ 'shape' => 'Id', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'transcriptSourceSetting' => [ 'shape' => 'TranscriptSourceSetting', ], 'encryptionSetting' => [ 'shape' => 'EncryptionSetting', ], ], ], 'StartImportRequest' => [ 'type' => 'structure', 'required' => [ 'importId', 'resourceSpecification', 'mergeStrategy', ], 'members' => [ 'importId' => [ 'shape' => 'Id', ], 'resourceSpecification' => [ 'shape' => 'ImportResourceSpecification', ], 'mergeStrategy' => [ 'shape' => 'MergeStrategy', ], 'filePassword' => [ 'shape' => 'ImportExportFilePassword', ], ], ], 'StartImportResponse' => [ 'type' => 'structure', 'members' => [ 'importId' => [ 'shape' => 'Id', ], 'resourceSpecification' => [ 'shape' => 'ImportResourceSpecification', ], 'mergeStrategy' => [ 'shape' => 'MergeStrategy', ], 'importStatus' => [ 'shape' => 'ImportStatus', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'StillWaitingResponseFrequency' => [ 'type' => 'integer', 'max' => 300, 'min' => 1, ], 'StillWaitingResponseSpecification' => [ 'type' => 'structure', 'required' => [ 'messageGroups', 'frequencyInSeconds', 'timeoutInSeconds', ], 'members' => [ 'messageGroups' => [ 'shape' => 'MessageGroupsList', ], 'frequencyInSeconds' => [ 'shape' => 'StillWaitingResponseFrequency', ], 'timeoutInSeconds' => [ 'shape' => 'StillWaitingResponseTimeout', ], 'allowInterrupt' => [ 'shape' => 'BoxedBoolean', ], ], ], 'StillWaitingResponseTimeout' => [ 'type' => 'integer', 'max' => 900, 'min' => 1, ], 'SynonymList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SampleValue', ], 'max' => 10000, 'min' => 1, ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', 'tags', ], 'members' => [ 'resourceARN' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceARN', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TextLogDestination' => [ 'type' => 'structure', 'required' => [ 'cloudWatch', ], 'members' => [ 'cloudWatch' => [ 'shape' => 'CloudWatchLogGroupLogDestination', ], ], ], 'TextLogSetting' => [ 'type' => 'structure', 'required' => [ 'enabled', 'destination', ], 'members' => [ 'enabled' => [ 'shape' => 'Boolean', ], 'destination' => [ 'shape' => 'TextLogDestination', ], ], ], 'TextLogSettingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TextLogSetting', ], 'max' => 1, 'min' => 1, ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'retryAfterSeconds' => [ 'shape' => 'RetryAfterSeconds', 'location' => 'header', 'locationName' => 'Retry-After', ], 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'TimeDimension' => [ 'type' => 'string', 'enum' => [ 'Hours', 'Days', 'Weeks', ], ], 'TimeValue' => [ 'type' => 'integer', 'max' => 24, 'min' => 1, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'Transcript' => [ 'type' => 'string', 'max' => 6000000, 'min' => 1, 'pattern' => '.*', ], 'TranscriptFilter' => [ 'type' => 'structure', 'members' => [ 'lexTranscriptFilter' => [ 'shape' => 'LexTranscriptFilter', ], ], ], 'TranscriptFormat' => [ 'type' => 'string', 'enum' => [ 'Lex', ], ], 'TranscriptSourceSetting' => [ 'type' => 'structure', 'members' => [ 's3BucketTranscriptSource' => [ 'shape' => 'S3BucketTranscriptSource', ], ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', 'tagKeys', ], 'members' => [ 'resourceARN' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceARN', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateBotAliasRequest' => [ 'type' => 'structure', 'required' => [ 'botAliasId', 'botAliasName', 'botId', ], 'members' => [ 'botAliasId' => [ 'shape' => 'BotAliasId', 'location' => 'uri', 'locationName' => 'botAliasId', ], 'botAliasName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'botAliasLocaleSettings' => [ 'shape' => 'BotAliasLocaleSettingsMap', ], 'conversationLogSettings' => [ 'shape' => 'ConversationLogSettings', ], 'sentimentAnalysisSettings' => [ 'shape' => 'SentimentAnalysisSettings', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], ], ], 'UpdateBotAliasResponse' => [ 'type' => 'structure', 'members' => [ 'botAliasId' => [ 'shape' => 'BotAliasId', ], 'botAliasName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'botVersion' => [ 'shape' => 'BotVersion', ], 'botAliasLocaleSettings' => [ 'shape' => 'BotAliasLocaleSettingsMap', ], 'conversationLogSettings' => [ 'shape' => 'ConversationLogSettings', ], 'sentimentAnalysisSettings' => [ 'shape' => 'SentimentAnalysisSettings', ], 'botAliasStatus' => [ 'shape' => 'BotAliasStatus', ], 'botId' => [ 'shape' => 'Id', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateBotLocaleRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', 'nluIntentConfidenceThreshold', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'description' => [ 'shape' => 'Description', ], 'nluIntentConfidenceThreshold' => [ 'shape' => 'ConfidenceThreshold', ], 'voiceSettings' => [ 'shape' => 'VoiceSettings', ], ], ], 'UpdateBotLocaleResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'localeName' => [ 'shape' => 'LocaleName', ], 'description' => [ 'shape' => 'Description', ], 'nluIntentConfidenceThreshold' => [ 'shape' => 'ConfidenceThreshold', ], 'voiceSettings' => [ 'shape' => 'VoiceSettings', ], 'botLocaleStatus' => [ 'shape' => 'BotLocaleStatus', ], 'failureReasons' => [ 'shape' => 'FailureReasons', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'recommendedActions' => [ 'shape' => 'RecommendedActions', ], ], ], 'UpdateBotRecommendationRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botVersion', 'localeId', 'botRecommendationId', 'encryptionSetting', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'botRecommendationId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botRecommendationId', ], 'encryptionSetting' => [ 'shape' => 'EncryptionSetting', ], ], ], 'UpdateBotRecommendationResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'botRecommendationStatus' => [ 'shape' => 'BotRecommendationStatus', ], 'botRecommendationId' => [ 'shape' => 'Id', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'transcriptSourceSetting' => [ 'shape' => 'TranscriptSourceSetting', ], 'encryptionSetting' => [ 'shape' => 'EncryptionSetting', ], ], ], 'UpdateBotRequest' => [ 'type' => 'structure', 'required' => [ 'botId', 'botName', 'roleArn', 'dataPrivacy', 'idleSessionTTLInSeconds', ], 'members' => [ 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'dataPrivacy' => [ 'shape' => 'DataPrivacy', ], 'idleSessionTTLInSeconds' => [ 'shape' => 'SessionTTL', ], ], ], 'UpdateBotResponse' => [ 'type' => 'structure', 'members' => [ 'botId' => [ 'shape' => 'Id', ], 'botName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'dataPrivacy' => [ 'shape' => 'DataPrivacy', ], 'idleSessionTTLInSeconds' => [ 'shape' => 'SessionTTL', ], 'botStatus' => [ 'shape' => 'BotStatus', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateExportRequest' => [ 'type' => 'structure', 'required' => [ 'exportId', ], 'members' => [ 'exportId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'exportId', ], 'filePassword' => [ 'shape' => 'ImportExportFilePassword', ], ], ], 'UpdateExportResponse' => [ 'type' => 'structure', 'members' => [ 'exportId' => [ 'shape' => 'Id', ], 'resourceSpecification' => [ 'shape' => 'ExportResourceSpecification', ], 'fileFormat' => [ 'shape' => 'ImportExportFileFormat', ], 'exportStatus' => [ 'shape' => 'ExportStatus', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateIntentRequest' => [ 'type' => 'structure', 'required' => [ 'intentId', 'intentName', 'botId', 'botVersion', 'localeId', ], 'members' => [ 'intentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'intentId', ], 'intentName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'parentIntentSignature' => [ 'shape' => 'IntentSignature', ], 'sampleUtterances' => [ 'shape' => 'SampleUtterancesList', ], 'dialogCodeHook' => [ 'shape' => 'DialogCodeHookSettings', ], 'fulfillmentCodeHook' => [ 'shape' => 'FulfillmentCodeHookSettings', ], 'slotPriorities' => [ 'shape' => 'SlotPrioritiesList', ], 'intentConfirmationSetting' => [ 'shape' => 'IntentConfirmationSetting', ], 'intentClosingSetting' => [ 'shape' => 'IntentClosingSetting', ], 'inputContexts' => [ 'shape' => 'InputContextsList', ], 'outputContexts' => [ 'shape' => 'OutputContextsList', ], 'kendraConfiguration' => [ 'shape' => 'KendraConfiguration', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], ], ], 'UpdateIntentResponse' => [ 'type' => 'structure', 'members' => [ 'intentId' => [ 'shape' => 'Id', ], 'intentName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'parentIntentSignature' => [ 'shape' => 'IntentSignature', ], 'sampleUtterances' => [ 'shape' => 'SampleUtterancesList', ], 'dialogCodeHook' => [ 'shape' => 'DialogCodeHookSettings', ], 'fulfillmentCodeHook' => [ 'shape' => 'FulfillmentCodeHookSettings', ], 'slotPriorities' => [ 'shape' => 'SlotPrioritiesList', ], 'intentConfirmationSetting' => [ 'shape' => 'IntentConfirmationSetting', ], 'intentClosingSetting' => [ 'shape' => 'IntentClosingSetting', ], 'inputContexts' => [ 'shape' => 'InputContextsList', ], 'outputContexts' => [ 'shape' => 'OutputContextsList', ], 'kendraConfiguration' => [ 'shape' => 'KendraConfiguration', ], 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'policy', ], 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'policy' => [ 'shape' => 'Policy', ], 'expectedRevisionId' => [ 'shape' => 'RevisionId', 'location' => 'querystring', 'locationName' => 'expectedRevisionId', ], ], ], 'UpdateResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'resourceArn' => [ 'shape' => 'AmazonResourceName', ], 'revisionId' => [ 'shape' => 'RevisionId', ], ], ], 'UpdateSlotRequest' => [ 'type' => 'structure', 'required' => [ 'slotId', 'slotName', 'slotTypeId', 'valueElicitationSetting', 'botId', 'botVersion', 'localeId', 'intentId', ], 'members' => [ 'slotId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'slotId', ], 'slotName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'slotTypeId' => [ 'shape' => 'BuiltInOrCustomSlotTypeId', ], 'valueElicitationSetting' => [ 'shape' => 'SlotValueElicitationSetting', ], 'obfuscationSetting' => [ 'shape' => 'ObfuscationSetting', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'intentId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'intentId', ], 'multipleValuesSetting' => [ 'shape' => 'MultipleValuesSetting', ], ], ], 'UpdateSlotResponse' => [ 'type' => 'structure', 'members' => [ 'slotId' => [ 'shape' => 'Id', ], 'slotName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'slotTypeId' => [ 'shape' => 'BuiltInOrCustomSlotTypeId', ], 'valueElicitationSetting' => [ 'shape' => 'SlotValueElicitationSetting', ], 'obfuscationSetting' => [ 'shape' => 'ObfuscationSetting', ], 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'intentId' => [ 'shape' => 'Id', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'multipleValuesSetting' => [ 'shape' => 'MultipleValuesSetting', ], ], ], 'UpdateSlotTypeRequest' => [ 'type' => 'structure', 'required' => [ 'slotTypeId', 'slotTypeName', 'botId', 'botVersion', 'localeId', ], 'members' => [ 'slotTypeId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'slotTypeId', ], 'slotTypeName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'slotTypeValues' => [ 'shape' => 'SlotTypeValues', ], 'valueSelectionSetting' => [ 'shape' => 'SlotValueSelectionSetting', ], 'parentSlotTypeSignature' => [ 'shape' => 'SlotTypeSignature', ], 'botId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'botId', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', 'location' => 'uri', 'locationName' => 'botVersion', ], 'localeId' => [ 'shape' => 'LocaleId', 'location' => 'uri', 'locationName' => 'localeId', ], 'externalSourceSetting' => [ 'shape' => 'ExternalSourceSetting', ], ], ], 'UpdateSlotTypeResponse' => [ 'type' => 'structure', 'members' => [ 'slotTypeId' => [ 'shape' => 'Id', ], 'slotTypeName' => [ 'shape' => 'Name', ], 'description' => [ 'shape' => 'Description', ], 'slotTypeValues' => [ 'shape' => 'SlotTypeValues', ], 'valueSelectionSetting' => [ 'shape' => 'SlotValueSelectionSetting', ], 'parentSlotTypeSignature' => [ 'shape' => 'SlotTypeSignature', ], 'botId' => [ 'shape' => 'Id', ], 'botVersion' => [ 'shape' => 'DraftBotVersion', ], 'localeId' => [ 'shape' => 'LocaleId', ], 'creationDateTime' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDateTime' => [ 'shape' => 'Timestamp', ], 'externalSourceSetting' => [ 'shape' => 'ExternalSourceSetting', ], ], ], 'Utterance' => [ 'type' => 'string', ], 'UtteranceAggregationDuration' => [ 'type' => 'structure', 'required' => [ 'relativeAggregationDuration', ], 'members' => [ 'relativeAggregationDuration' => [ 'shape' => 'RelativeAggregationDuration', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'Value' => [ 'type' => 'string', 'max' => 140, 'min' => 1, ], 'VoiceEngine' => [ 'type' => 'string', 'enum' => [ 'standard', 'neural', ], ], 'VoiceId' => [ 'type' => 'string', ], 'VoiceSettings' => [ 'type' => 'structure', 'required' => [ 'voiceId', ], 'members' => [ 'voiceId' => [ 'shape' => 'VoiceId', ], 'engine' => [ 'shape' => 'VoiceEngine', ], ], ], 'WaitAndContinueSpecification' => [ 'type' => 'structure', 'required' => [ 'waitingResponse', 'continueResponse', ], 'members' => [ 'waitingResponse' => [ 'shape' => 'ResponseSpecification', ], 'continueResponse' => [ 'shape' => 'ResponseSpecification', ], 'stillWaitingResponse' => [ 'shape' => 'StillWaitingResponseSpecification', ], 'active' => [ 'shape' => 'BoxedBoolean', ], ], ], ],];
