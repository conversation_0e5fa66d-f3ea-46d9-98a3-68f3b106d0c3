<?php
// This file was auto-generated from sdk-root/src/data/route53resolver/2018-04-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-04-01', 'endpointPrefix' => 'route53resolver', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceAbbreviation' => 'Route53Resolver', 'serviceFullName' => 'Amazon Route 53 Resolver', 'serviceId' => 'Route53Resolver', 'signatureVersion' => 'v4', 'targetPrefix' => 'Route53Resolver', 'uid' => 'route53resolver-2018-04-01', ], 'operations' => [ 'AssociateFirewallRuleGroup' => [ 'name' => 'AssociateFirewallRuleGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateFirewallRuleGroupRequest', ], 'output' => [ 'shape' => 'AssociateFirewallRuleGroupResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'AssociateResolverEndpointIpAddress' => [ 'name' => 'AssociateResolverEndpointIpAddress', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateResolverEndpointIpAddressRequest', ], 'output' => [ 'shape' => 'AssociateResolverEndpointIpAddressResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'AssociateResolverQueryLogConfig' => [ 'name' => 'AssociateResolverQueryLogConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateResolverQueryLogConfigRequest', ], 'output' => [ 'shape' => 'AssociateResolverQueryLogConfigResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'AssociateResolverRule' => [ 'name' => 'AssociateResolverRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateResolverRuleRequest', ], 'output' => [ 'shape' => 'AssociateResolverRuleResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceUnavailableException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateFirewallDomainList' => [ 'name' => 'CreateFirewallDomainList', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateFirewallDomainListRequest', ], 'output' => [ 'shape' => 'CreateFirewallDomainListResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateFirewallRule' => [ 'name' => 'CreateFirewallRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateFirewallRuleRequest', ], 'output' => [ 'shape' => 'CreateFirewallRuleResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateFirewallRuleGroup' => [ 'name' => 'CreateFirewallRuleGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateFirewallRuleGroupRequest', ], 'output' => [ 'shape' => 'CreateFirewallRuleGroupResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateResolverEndpoint' => [ 'name' => 'CreateResolverEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateResolverEndpointRequest', ], 'output' => [ 'shape' => 'CreateResolverEndpointResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateResolverQueryLogConfig' => [ 'name' => 'CreateResolverQueryLogConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateResolverQueryLogConfigRequest', ], 'output' => [ 'shape' => 'CreateResolverQueryLogConfigResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateResolverRule' => [ 'name' => 'CreateResolverRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateResolverRuleRequest', ], 'output' => [ 'shape' => 'CreateResolverRuleResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'ResourceUnavailableException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteFirewallDomainList' => [ 'name' => 'DeleteFirewallDomainList', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteFirewallDomainListRequest', ], 'output' => [ 'shape' => 'DeleteFirewallDomainListResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteFirewallRule' => [ 'name' => 'DeleteFirewallRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteFirewallRuleRequest', ], 'output' => [ 'shape' => 'DeleteFirewallRuleResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteFirewallRuleGroup' => [ 'name' => 'DeleteFirewallRuleGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteFirewallRuleGroupRequest', ], 'output' => [ 'shape' => 'DeleteFirewallRuleGroupResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteResolverEndpoint' => [ 'name' => 'DeleteResolverEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteResolverEndpointRequest', ], 'output' => [ 'shape' => 'DeleteResolverEndpointResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteResolverQueryLogConfig' => [ 'name' => 'DeleteResolverQueryLogConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteResolverQueryLogConfigRequest', ], 'output' => [ 'shape' => 'DeleteResolverQueryLogConfigResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteResolverRule' => [ 'name' => 'DeleteResolverRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteResolverRuleRequest', ], 'output' => [ 'shape' => 'DeleteResolverRuleResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DisassociateFirewallRuleGroup' => [ 'name' => 'DisassociateFirewallRuleGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateFirewallRuleGroupRequest', ], 'output' => [ 'shape' => 'DisassociateFirewallRuleGroupResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DisassociateResolverEndpointIpAddress' => [ 'name' => 'DisassociateResolverEndpointIpAddress', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateResolverEndpointIpAddressRequest', ], 'output' => [ 'shape' => 'DisassociateResolverEndpointIpAddressResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceExistsException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DisassociateResolverQueryLogConfig' => [ 'name' => 'DisassociateResolverQueryLogConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateResolverQueryLogConfigRequest', ], 'output' => [ 'shape' => 'DisassociateResolverQueryLogConfigResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DisassociateResolverRule' => [ 'name' => 'DisassociateResolverRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateResolverRuleRequest', ], 'output' => [ 'shape' => 'DisassociateResolverRuleResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetFirewallConfig' => [ 'name' => 'GetFirewallConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetFirewallConfigRequest', ], 'output' => [ 'shape' => 'GetFirewallConfigResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetFirewallDomainList' => [ 'name' => 'GetFirewallDomainList', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetFirewallDomainListRequest', ], 'output' => [ 'shape' => 'GetFirewallDomainListResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetFirewallRuleGroup' => [ 'name' => 'GetFirewallRuleGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetFirewallRuleGroupRequest', ], 'output' => [ 'shape' => 'GetFirewallRuleGroupResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetFirewallRuleGroupAssociation' => [ 'name' => 'GetFirewallRuleGroupAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetFirewallRuleGroupAssociationRequest', ], 'output' => [ 'shape' => 'GetFirewallRuleGroupAssociationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetFirewallRuleGroupPolicy' => [ 'name' => 'GetFirewallRuleGroupPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetFirewallRuleGroupPolicyRequest', ], 'output' => [ 'shape' => 'GetFirewallRuleGroupPolicyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetResolverConfig' => [ 'name' => 'GetResolverConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResolverConfigRequest', ], 'output' => [ 'shape' => 'GetResolverConfigResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetResolverDnssecConfig' => [ 'name' => 'GetResolverDnssecConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResolverDnssecConfigRequest', ], 'output' => [ 'shape' => 'GetResolverDnssecConfigResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetResolverEndpoint' => [ 'name' => 'GetResolverEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResolverEndpointRequest', ], 'output' => [ 'shape' => 'GetResolverEndpointResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetResolverQueryLogConfig' => [ 'name' => 'GetResolverQueryLogConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResolverQueryLogConfigRequest', ], 'output' => [ 'shape' => 'GetResolverQueryLogConfigResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetResolverQueryLogConfigAssociation' => [ 'name' => 'GetResolverQueryLogConfigAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResolverQueryLogConfigAssociationRequest', ], 'output' => [ 'shape' => 'GetResolverQueryLogConfigAssociationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetResolverQueryLogConfigPolicy' => [ 'name' => 'GetResolverQueryLogConfigPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResolverQueryLogConfigPolicyRequest', ], 'output' => [ 'shape' => 'GetResolverQueryLogConfigPolicyResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnknownResourceException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetResolverRule' => [ 'name' => 'GetResolverRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResolverRuleRequest', ], 'output' => [ 'shape' => 'GetResolverRuleResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetResolverRuleAssociation' => [ 'name' => 'GetResolverRuleAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResolverRuleAssociationRequest', ], 'output' => [ 'shape' => 'GetResolverRuleAssociationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetResolverRulePolicy' => [ 'name' => 'GetResolverRulePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResolverRulePolicyRequest', ], 'output' => [ 'shape' => 'GetResolverRulePolicyResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnknownResourceException', ], [ 'shape' => 'InternalServiceErrorException', ], ], ], 'ImportFirewallDomains' => [ 'name' => 'ImportFirewallDomains', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ImportFirewallDomainsRequest', ], 'output' => [ 'shape' => 'ImportFirewallDomainsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListFirewallConfigs' => [ 'name' => 'ListFirewallConfigs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListFirewallConfigsRequest', ], 'output' => [ 'shape' => 'ListFirewallConfigsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListFirewallDomainLists' => [ 'name' => 'ListFirewallDomainLists', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListFirewallDomainListsRequest', ], 'output' => [ 'shape' => 'ListFirewallDomainListsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListFirewallDomains' => [ 'name' => 'ListFirewallDomains', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListFirewallDomainsRequest', ], 'output' => [ 'shape' => 'ListFirewallDomainsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListFirewallRuleGroupAssociations' => [ 'name' => 'ListFirewallRuleGroupAssociations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListFirewallRuleGroupAssociationsRequest', ], 'output' => [ 'shape' => 'ListFirewallRuleGroupAssociationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListFirewallRuleGroups' => [ 'name' => 'ListFirewallRuleGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListFirewallRuleGroupsRequest', ], 'output' => [ 'shape' => 'ListFirewallRuleGroupsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListFirewallRules' => [ 'name' => 'ListFirewallRules', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListFirewallRulesRequest', ], 'output' => [ 'shape' => 'ListFirewallRulesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListResolverConfigs' => [ 'name' => 'ListResolverConfigs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListResolverConfigsRequest', ], 'output' => [ 'shape' => 'ListResolverConfigsResponse', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListResolverDnssecConfigs' => [ 'name' => 'ListResolverDnssecConfigs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListResolverDnssecConfigsRequest', ], 'output' => [ 'shape' => 'ListResolverDnssecConfigsResponse', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListResolverEndpointIpAddresses' => [ 'name' => 'ListResolverEndpointIpAddresses', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListResolverEndpointIpAddressesRequest', ], 'output' => [ 'shape' => 'ListResolverEndpointIpAddressesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListResolverEndpoints' => [ 'name' => 'ListResolverEndpoints', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListResolverEndpointsRequest', ], 'output' => [ 'shape' => 'ListResolverEndpointsResponse', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListResolverQueryLogConfigAssociations' => [ 'name' => 'ListResolverQueryLogConfigAssociations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListResolverQueryLogConfigAssociationsRequest', ], 'output' => [ 'shape' => 'ListResolverQueryLogConfigAssociationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListResolverQueryLogConfigs' => [ 'name' => 'ListResolverQueryLogConfigs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListResolverQueryLogConfigsRequest', ], 'output' => [ 'shape' => 'ListResolverQueryLogConfigsResponse', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListResolverRuleAssociations' => [ 'name' => 'ListResolverRuleAssociations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListResolverRuleAssociationsRequest', ], 'output' => [ 'shape' => 'ListResolverRuleAssociationsResponse', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListResolverRules' => [ 'name' => 'ListResolverRules', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListResolverRulesRequest', ], 'output' => [ 'shape' => 'ListResolverRulesResponse', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'PutFirewallRuleGroupPolicy' => [ 'name' => 'PutFirewallRuleGroupPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutFirewallRuleGroupPolicyRequest', ], 'output' => [ 'shape' => 'PutFirewallRuleGroupPolicyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'PutResolverQueryLogConfigPolicy' => [ 'name' => 'PutResolverQueryLogConfigPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutResolverQueryLogConfigPolicyRequest', ], 'output' => [ 'shape' => 'PutResolverQueryLogConfigPolicyResponse', ], 'errors' => [ [ 'shape' => 'InvalidPolicyDocument', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UnknownResourceException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'PutResolverRulePolicy' => [ 'name' => 'PutResolverRulePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutResolverRulePolicyRequest', ], 'output' => [ 'shape' => 'PutResolverRulePolicyResponse', ], 'errors' => [ [ 'shape' => 'InvalidPolicyDocument', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnknownResourceException', ], [ 'shape' => 'InternalServiceErrorException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidTagException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateFirewallConfig' => [ 'name' => 'UpdateFirewallConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateFirewallConfigRequest', ], 'output' => [ 'shape' => 'UpdateFirewallConfigResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateFirewallDomains' => [ 'name' => 'UpdateFirewallDomains', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateFirewallDomainsRequest', ], 'output' => [ 'shape' => 'UpdateFirewallDomainsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateFirewallRule' => [ 'name' => 'UpdateFirewallRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateFirewallRuleRequest', ], 'output' => [ 'shape' => 'UpdateFirewallRuleResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateFirewallRuleGroupAssociation' => [ 'name' => 'UpdateFirewallRuleGroupAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateFirewallRuleGroupAssociationRequest', ], 'output' => [ 'shape' => 'UpdateFirewallRuleGroupAssociationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateResolverConfig' => [ 'name' => 'UpdateResolverConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateResolverConfigRequest', ], 'output' => [ 'shape' => 'UpdateResolverConfigResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceUnavailableException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateResolverDnssecConfig' => [ 'name' => 'UpdateResolverDnssecConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateResolverDnssecConfigRequest', ], 'output' => [ 'shape' => 'UpdateResolverDnssecConfigResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateResolverEndpoint' => [ 'name' => 'UpdateResolverEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateResolverEndpointRequest', ], 'output' => [ 'shape' => 'UpdateResolverEndpointResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateResolverRule' => [ 'name' => 'UpdateResolverRule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateResolverRuleRequest', ], 'output' => [ 'shape' => 'UpdateResolverRuleResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceUnavailableException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalServiceErrorException', ], [ 'shape' => 'ThrottlingException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'AccountId' => [ 'type' => 'string', 'max' => 32, 'min' => 12, ], 'Action' => [ 'type' => 'string', 'enum' => [ 'ALLOW', 'BLOCK', 'ALERT', ], ], 'Arn' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'AssociateFirewallRuleGroupRequest' => [ 'type' => 'structure', 'required' => [ 'CreatorRequestId', 'FirewallRuleGroupId', 'VpcId', 'Priority', 'Name', ], 'members' => [ 'CreatorRequestId' => [ 'shape' => 'CreatorRequestId', 'idempotencyToken' => true, ], 'FirewallRuleGroupId' => [ 'shape' => 'ResourceId', ], 'VpcId' => [ 'shape' => 'ResourceId', ], 'Priority' => [ 'shape' => 'Priority', ], 'Name' => [ 'shape' => 'Name', ], 'MutationProtection' => [ 'shape' => 'MutationProtectionStatus', 'box' => true, ], 'Tags' => [ 'shape' => 'TagList', 'box' => true, ], ], ], 'AssociateFirewallRuleGroupResponse' => [ 'type' => 'structure', 'members' => [ 'FirewallRuleGroupAssociation' => [ 'shape' => 'FirewallRuleGroupAssociation', ], ], ], 'AssociateResolverEndpointIpAddressRequest' => [ 'type' => 'structure', 'required' => [ 'ResolverEndpointId', 'IpAddress', ], 'members' => [ 'ResolverEndpointId' => [ 'shape' => 'ResourceId', ], 'IpAddress' => [ 'shape' => 'IpAddressUpdate', ], ], ], 'AssociateResolverEndpointIpAddressResponse' => [ 'type' => 'structure', 'members' => [ 'ResolverEndpoint' => [ 'shape' => 'ResolverEndpoint', ], ], ], 'AssociateResolverQueryLogConfigRequest' => [ 'type' => 'structure', 'required' => [ 'ResolverQueryLogConfigId', 'ResourceId', ], 'members' => [ 'ResolverQueryLogConfigId' => [ 'shape' => 'ResourceId', ], 'ResourceId' => [ 'shape' => 'ResourceId', ], ], ], 'AssociateResolverQueryLogConfigResponse' => [ 'type' => 'structure', 'members' => [ 'ResolverQueryLogConfigAssociation' => [ 'shape' => 'ResolverQueryLogConfigAssociation', ], ], ], 'AssociateResolverRuleRequest' => [ 'type' => 'structure', 'required' => [ 'ResolverRuleId', 'VPCId', ], 'members' => [ 'ResolverRuleId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'Name', ], 'VPCId' => [ 'shape' => 'ResourceId', ], ], ], 'AssociateResolverRuleResponse' => [ 'type' => 'structure', 'members' => [ 'ResolverRuleAssociation' => [ 'shape' => 'ResolverRuleAssociation', ], ], ], 'AutodefinedReverseFlag' => [ 'type' => 'string', 'enum' => [ 'ENABLE', 'DISABLE', ], ], 'BlockOverrideDnsType' => [ 'type' => 'string', 'enum' => [ 'CNAME', ], ], 'BlockOverrideDomain' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'BlockOverrideTtl' => [ 'type' => 'integer', 'max' => 604800, 'min' => 0, ], 'BlockResponse' => [ 'type' => 'string', 'enum' => [ 'NODATA', 'NXDOMAIN', 'OVERRIDE', ], ], 'Boolean' => [ 'type' => 'boolean', ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'Count' => [ 'type' => 'integer', ], 'CreateFirewallDomainListRequest' => [ 'type' => 'structure', 'required' => [ 'CreatorRequestId', 'Name', ], 'members' => [ 'CreatorRequestId' => [ 'shape' => 'CreatorRequestId', 'idempotencyToken' => true, ], 'Name' => [ 'shape' => 'Name', ], 'Tags' => [ 'shape' => 'TagList', 'box' => true, ], ], ], 'CreateFirewallDomainListResponse' => [ 'type' => 'structure', 'members' => [ 'FirewallDomainList' => [ 'shape' => 'FirewallDomainList', ], ], ], 'CreateFirewallRuleGroupRequest' => [ 'type' => 'structure', 'required' => [ 'CreatorRequestId', 'Name', ], 'members' => [ 'CreatorRequestId' => [ 'shape' => 'CreatorRequestId', 'idempotencyToken' => true, ], 'Name' => [ 'shape' => 'Name', ], 'Tags' => [ 'shape' => 'TagList', 'box' => true, ], ], ], 'CreateFirewallRuleGroupResponse' => [ 'type' => 'structure', 'members' => [ 'FirewallRuleGroup' => [ 'shape' => 'FirewallRuleGroup', ], ], ], 'CreateFirewallRuleRequest' => [ 'type' => 'structure', 'required' => [ 'CreatorRequestId', 'FirewallRuleGroupId', 'FirewallDomainListId', 'Priority', 'Action', 'Name', ], 'members' => [ 'CreatorRequestId' => [ 'shape' => 'CreatorRequestId', 'idempotencyToken' => true, ], 'FirewallRuleGroupId' => [ 'shape' => 'ResourceId', ], 'FirewallDomainListId' => [ 'shape' => 'ResourceId', ], 'Priority' => [ 'shape' => 'Priority', ], 'Action' => [ 'shape' => 'Action', ], 'BlockResponse' => [ 'shape' => 'BlockResponse', 'box' => true, ], 'BlockOverrideDomain' => [ 'shape' => 'BlockOverrideDomain', 'box' => true, ], 'BlockOverrideDnsType' => [ 'shape' => 'BlockOverrideDnsType', 'box' => true, ], 'BlockOverrideTtl' => [ 'shape' => 'BlockOverrideTtl', 'box' => true, ], 'Name' => [ 'shape' => 'Name', ], ], ], 'CreateFirewallRuleResponse' => [ 'type' => 'structure', 'members' => [ 'FirewallRule' => [ 'shape' => 'FirewallRule', ], ], ], 'CreateResolverEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'CreatorRequestId', 'SecurityGroupIds', 'Direction', 'IpAddresses', ], 'members' => [ 'CreatorRequestId' => [ 'shape' => 'CreatorRequestId', ], 'Name' => [ 'shape' => 'Name', ], 'SecurityGroupIds' => [ 'shape' => 'SecurityGroupIds', 'box' => true, ], 'Direction' => [ 'shape' => 'ResolverEndpointDirection', ], 'IpAddresses' => [ 'shape' => 'IpAddressesRequest', ], 'Tags' => [ 'shape' => 'TagList', 'box' => true, ], ], ], 'CreateResolverEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'ResolverEndpoint' => [ 'shape' => 'ResolverEndpoint', ], ], ], 'CreateResolverQueryLogConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'DestinationArn', 'CreatorRequestId', ], 'members' => [ 'Name' => [ 'shape' => 'ResolverQueryLogConfigName', ], 'DestinationArn' => [ 'shape' => 'DestinationArn', ], 'CreatorRequestId' => [ 'shape' => 'CreatorRequestId', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', 'box' => true, ], ], ], 'CreateResolverQueryLogConfigResponse' => [ 'type' => 'structure', 'members' => [ 'ResolverQueryLogConfig' => [ 'shape' => 'ResolverQueryLogConfig', ], ], ], 'CreateResolverRuleRequest' => [ 'type' => 'structure', 'required' => [ 'CreatorRequestId', 'RuleType', 'DomainName', ], 'members' => [ 'CreatorRequestId' => [ 'shape' => 'CreatorRequestId', ], 'Name' => [ 'shape' => 'Name', ], 'RuleType' => [ 'shape' => 'RuleTypeOption', ], 'DomainName' => [ 'shape' => 'DomainName', ], 'TargetIps' => [ 'shape' => 'TargetList', 'box' => true, ], 'ResolverEndpointId' => [ 'shape' => 'ResourceId', 'box' => true, ], 'Tags' => [ 'shape' => 'TagList', 'box' => true, ], ], ], 'CreateResolverRuleResponse' => [ 'type' => 'structure', 'members' => [ 'ResolverRule' => [ 'shape' => 'ResolverRule', ], ], ], 'CreatorRequestId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'DeleteFirewallDomainListRequest' => [ 'type' => 'structure', 'required' => [ 'FirewallDomainListId', ], 'members' => [ 'FirewallDomainListId' => [ 'shape' => 'ResourceId', ], ], ], 'DeleteFirewallDomainListResponse' => [ 'type' => 'structure', 'members' => [ 'FirewallDomainList' => [ 'shape' => 'FirewallDomainList', ], ], ], 'DeleteFirewallRuleGroupRequest' => [ 'type' => 'structure', 'required' => [ 'FirewallRuleGroupId', ], 'members' => [ 'FirewallRuleGroupId' => [ 'shape' => 'ResourceId', ], ], ], 'DeleteFirewallRuleGroupResponse' => [ 'type' => 'structure', 'members' => [ 'FirewallRuleGroup' => [ 'shape' => 'FirewallRuleGroup', ], ], ], 'DeleteFirewallRuleRequest' => [ 'type' => 'structure', 'required' => [ 'FirewallRuleGroupId', 'FirewallDomainListId', ], 'members' => [ 'FirewallRuleGroupId' => [ 'shape' => 'ResourceId', ], 'FirewallDomainListId' => [ 'shape' => 'ResourceId', ], ], ], 'DeleteFirewallRuleResponse' => [ 'type' => 'structure', 'members' => [ 'FirewallRule' => [ 'shape' => 'FirewallRule', ], ], ], 'DeleteResolverEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'ResolverEndpointId', ], 'members' => [ 'ResolverEndpointId' => [ 'shape' => 'ResourceId', ], ], ], 'DeleteResolverEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'ResolverEndpoint' => [ 'shape' => 'ResolverEndpoint', ], ], ], 'DeleteResolverQueryLogConfigRequest' => [ 'type' => 'structure', 'required' => [ 'ResolverQueryLogConfigId', ], 'members' => [ 'ResolverQueryLogConfigId' => [ 'shape' => 'ResourceId', ], ], ], 'DeleteResolverQueryLogConfigResponse' => [ 'type' => 'structure', 'members' => [ 'ResolverQueryLogConfig' => [ 'shape' => 'ResolverQueryLogConfig', ], ], ], 'DeleteResolverRuleRequest' => [ 'type' => 'structure', 'required' => [ 'ResolverRuleId', ], 'members' => [ 'ResolverRuleId' => [ 'shape' => 'ResourceId', ], ], ], 'DeleteResolverRuleResponse' => [ 'type' => 'structure', 'members' => [ 'ResolverRule' => [ 'shape' => 'ResolverRule', ], ], ], 'DestinationArn' => [ 'type' => 'string', 'max' => 600, 'min' => 1, ], 'DisassociateFirewallRuleGroupRequest' => [ 'type' => 'structure', 'required' => [ 'FirewallRuleGroupAssociationId', ], 'members' => [ 'FirewallRuleGroupAssociationId' => [ 'shape' => 'ResourceId', ], ], ], 'DisassociateFirewallRuleGroupResponse' => [ 'type' => 'structure', 'members' => [ 'FirewallRuleGroupAssociation' => [ 'shape' => 'FirewallRuleGroupAssociation', ], ], ], 'DisassociateResolverEndpointIpAddressRequest' => [ 'type' => 'structure', 'required' => [ 'ResolverEndpointId', 'IpAddress', ], 'members' => [ 'ResolverEndpointId' => [ 'shape' => 'ResourceId', ], 'IpAddress' => [ 'shape' => 'IpAddressUpdate', ], ], ], 'DisassociateResolverEndpointIpAddressResponse' => [ 'type' => 'structure', 'members' => [ 'ResolverEndpoint' => [ 'shape' => 'ResolverEndpoint', ], ], ], 'DisassociateResolverQueryLogConfigRequest' => [ 'type' => 'structure', 'required' => [ 'ResolverQueryLogConfigId', 'ResourceId', ], 'members' => [ 'ResolverQueryLogConfigId' => [ 'shape' => 'ResourceId', ], 'ResourceId' => [ 'shape' => 'ResourceId', ], ], ], 'DisassociateResolverQueryLogConfigResponse' => [ 'type' => 'structure', 'members' => [ 'ResolverQueryLogConfigAssociation' => [ 'shape' => 'ResolverQueryLogConfigAssociation', ], ], ], 'DisassociateResolverRuleRequest' => [ 'type' => 'structure', 'required' => [ 'VPCId', 'ResolverRuleId', ], 'members' => [ 'VPCId' => [ 'shape' => 'ResourceId', ], 'ResolverRuleId' => [ 'shape' => 'ResourceId', ], ], ], 'DisassociateResolverRuleResponse' => [ 'type' => 'structure', 'members' => [ 'ResolverRuleAssociation' => [ 'shape' => 'ResolverRuleAssociation', ], ], ], 'DomainListFileUrl' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'DomainName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'ExceptionMessage' => [ 'type' => 'string', ], 'Filter' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'FilterName', ], 'Values' => [ 'shape' => 'FilterValues', ], ], ], 'FilterName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'FilterValue' => [ 'type' => 'string', 'max' => 600, 'min' => 1, ], 'FilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterValue', ], ], 'Filters' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], ], 'FirewallConfig' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ResourceId', ], 'ResourceId' => [ 'shape' => 'ResourceId', ], 'OwnerId' => [ 'shape' => 'AccountId', ], 'FirewallFailOpen' => [ 'shape' => 'FirewallFailOpenStatus', ], ], ], 'FirewallConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FirewallConfig', ], ], 'FirewallDomainImportOperation' => [ 'type' => 'string', 'enum' => [ 'REPLACE', ], ], 'FirewallDomainList' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ResourceId', ], 'Arn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'Name', ], 'DomainCount' => [ 'shape' => 'Unsigned', ], 'Status' => [ 'shape' => 'FirewallDomainListStatus', ], 'StatusMessage' => [ 'shape' => 'StatusMessage', ], 'ManagedOwnerName' => [ 'shape' => 'ServicePrinciple', ], 'CreatorRequestId' => [ 'shape' => 'CreatorRequestId', ], 'CreationTime' => [ 'shape' => 'Rfc3339TimeString', ], 'ModificationTime' => [ 'shape' => 'Rfc3339TimeString', ], ], ], 'FirewallDomainListMetadata' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ResourceId', ], 'Arn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'Name', ], 'CreatorRequestId' => [ 'shape' => 'CreatorRequestId', ], 'ManagedOwnerName' => [ 'shape' => 'ServicePrinciple', ], ], ], 'FirewallDomainListMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FirewallDomainListMetadata', ], ], 'FirewallDomainListStatus' => [ 'type' => 'string', 'enum' => [ 'COMPLETE', 'COMPLETE_IMPORT_FAILED', 'IMPORTING', 'DELETING', 'UPDATING', ], ], 'FirewallDomainName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'FirewallDomainUpdateOperation' => [ 'type' => 'string', 'enum' => [ 'ADD', 'REMOVE', 'REPLACE', ], ], 'FirewallDomains' => [ 'type' => 'list', 'member' => [ 'shape' => 'FirewallDomainName', ], ], 'FirewallFailOpenStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'FirewallRule' => [ 'type' => 'structure', 'members' => [ 'FirewallRuleGroupId' => [ 'shape' => 'ResourceId', ], 'FirewallDomainListId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'Name', ], 'Priority' => [ 'shape' => 'Priority', ], 'Action' => [ 'shape' => 'Action', ], 'BlockResponse' => [ 'shape' => 'BlockResponse', ], 'BlockOverrideDomain' => [ 'shape' => 'BlockOverrideDomain', ], 'BlockOverrideDnsType' => [ 'shape' => 'BlockOverrideDnsType', ], 'BlockOverrideTtl' => [ 'shape' => 'Unsigned', ], 'CreatorRequestId' => [ 'shape' => 'CreatorRequestId', ], 'CreationTime' => [ 'shape' => 'Rfc3339TimeString', ], 'ModificationTime' => [ 'shape' => 'Rfc3339TimeString', ], ], ], 'FirewallRuleGroup' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ResourceId', ], 'Arn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'Name', ], 'RuleCount' => [ 'shape' => 'Unsigned', ], 'Status' => [ 'shape' => 'FirewallRuleGroupStatus', ], 'StatusMessage' => [ 'shape' => 'StatusMessage', ], 'OwnerId' => [ 'shape' => 'AccountId', ], 'CreatorRequestId' => [ 'shape' => 'CreatorRequestId', ], 'ShareStatus' => [ 'shape' => 'ShareStatus', ], 'CreationTime' => [ 'shape' => 'Rfc3339TimeString', ], 'ModificationTime' => [ 'shape' => 'Rfc3339TimeString', ], ], ], 'FirewallRuleGroupAssociation' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ResourceId', ], 'Arn' => [ 'shape' => 'Arn', ], 'FirewallRuleGroupId' => [ 'shape' => 'ResourceId', ], 'VpcId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'Name', ], 'Priority' => [ 'shape' => 'Priority', ], 'MutationProtection' => [ 'shape' => 'MutationProtectionStatus', ], 'ManagedOwnerName' => [ 'shape' => 'ServicePrinciple', ], 'Status' => [ 'shape' => 'FirewallRuleGroupAssociationStatus', ], 'StatusMessage' => [ 'shape' => 'StatusMessage', ], 'CreatorRequestId' => [ 'shape' => 'CreatorRequestId', ], 'CreationTime' => [ 'shape' => 'Rfc3339TimeString', ], 'ModificationTime' => [ 'shape' => 'Rfc3339TimeString', ], ], ], 'FirewallRuleGroupAssociationStatus' => [ 'type' => 'string', 'enum' => [ 'COMPLETE', 'DELETING', 'UPDATING', ], ], 'FirewallRuleGroupAssociations' => [ 'type' => 'list', 'member' => [ 'shape' => 'FirewallRuleGroupAssociation', ], ], 'FirewallRuleGroupMetadata' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ResourceId', ], 'Arn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'Name', ], 'OwnerId' => [ 'shape' => 'AccountId', ], 'CreatorRequestId' => [ 'shape' => 'CreatorRequestId', ], 'ShareStatus' => [ 'shape' => 'ShareStatus', ], ], ], 'FirewallRuleGroupMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FirewallRuleGroupMetadata', ], ], 'FirewallRuleGroupPolicy' => [ 'type' => 'string', 'max' => 30000, ], 'FirewallRuleGroupStatus' => [ 'type' => 'string', 'enum' => [ 'COMPLETE', 'DELETING', 'UPDATING', ], ], 'FirewallRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'FirewallRule', ], ], 'GetFirewallConfigRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', ], 'members' => [ 'ResourceId' => [ 'shape' => 'ResourceId', ], ], ], 'GetFirewallConfigResponse' => [ 'type' => 'structure', 'members' => [ 'FirewallConfig' => [ 'shape' => 'FirewallConfig', ], ], ], 'GetFirewallDomainListRequest' => [ 'type' => 'structure', 'required' => [ 'FirewallDomainListId', ], 'members' => [ 'FirewallDomainListId' => [ 'shape' => 'ResourceId', ], ], ], 'GetFirewallDomainListResponse' => [ 'type' => 'structure', 'members' => [ 'FirewallDomainList' => [ 'shape' => 'FirewallDomainList', ], ], ], 'GetFirewallRuleGroupAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'FirewallRuleGroupAssociationId', ], 'members' => [ 'FirewallRuleGroupAssociationId' => [ 'shape' => 'ResourceId', ], ], ], 'GetFirewallRuleGroupAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'FirewallRuleGroupAssociation' => [ 'shape' => 'FirewallRuleGroupAssociation', ], ], ], 'GetFirewallRuleGroupPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], ], ], 'GetFirewallRuleGroupPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'FirewallRuleGroupPolicy' => [ 'shape' => 'FirewallRuleGroupPolicy', ], ], ], 'GetFirewallRuleGroupRequest' => [ 'type' => 'structure', 'required' => [ 'FirewallRuleGroupId', ], 'members' => [ 'FirewallRuleGroupId' => [ 'shape' => 'ResourceId', ], ], ], 'GetFirewallRuleGroupResponse' => [ 'type' => 'structure', 'members' => [ 'FirewallRuleGroup' => [ 'shape' => 'FirewallRuleGroup', ], ], ], 'GetResolverConfigRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', ], 'members' => [ 'ResourceId' => [ 'shape' => 'ResourceId', ], ], ], 'GetResolverConfigResponse' => [ 'type' => 'structure', 'members' => [ 'ResolverConfig' => [ 'shape' => 'ResolverConfig', ], ], ], 'GetResolverDnssecConfigRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', ], 'members' => [ 'ResourceId' => [ 'shape' => 'ResourceId', ], ], ], 'GetResolverDnssecConfigResponse' => [ 'type' => 'structure', 'members' => [ 'ResolverDNSSECConfig' => [ 'shape' => 'ResolverDnssecConfig', ], ], ], 'GetResolverEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'ResolverEndpointId', ], 'members' => [ 'ResolverEndpointId' => [ 'shape' => 'ResourceId', ], ], ], 'GetResolverEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'ResolverEndpoint' => [ 'shape' => 'ResolverEndpoint', ], ], ], 'GetResolverQueryLogConfigAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'ResolverQueryLogConfigAssociationId', ], 'members' => [ 'ResolverQueryLogConfigAssociationId' => [ 'shape' => 'ResourceId', ], ], ], 'GetResolverQueryLogConfigAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'ResolverQueryLogConfigAssociation' => [ 'shape' => 'ResolverQueryLogConfigAssociation', ], ], ], 'GetResolverQueryLogConfigPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], ], ], 'GetResolverQueryLogConfigPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'ResolverQueryLogConfigPolicy' => [ 'shape' => 'ResolverQueryLogConfigPolicy', ], ], ], 'GetResolverQueryLogConfigRequest' => [ 'type' => 'structure', 'required' => [ 'ResolverQueryLogConfigId', ], 'members' => [ 'ResolverQueryLogConfigId' => [ 'shape' => 'ResourceId', ], ], ], 'GetResolverQueryLogConfigResponse' => [ 'type' => 'structure', 'members' => [ 'ResolverQueryLogConfig' => [ 'shape' => 'ResolverQueryLogConfig', ], ], ], 'GetResolverRuleAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'ResolverRuleAssociationId', ], 'members' => [ 'ResolverRuleAssociationId' => [ 'shape' => 'ResourceId', ], ], ], 'GetResolverRuleAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'ResolverRuleAssociation' => [ 'shape' => 'ResolverRuleAssociation', ], ], ], 'GetResolverRulePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], ], ], 'GetResolverRulePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'ResolverRulePolicy' => [ 'shape' => 'ResolverRulePolicy', ], ], ], 'GetResolverRuleRequest' => [ 'type' => 'structure', 'required' => [ 'ResolverRuleId', ], 'members' => [ 'ResolverRuleId' => [ 'shape' => 'ResourceId', ], ], ], 'GetResolverRuleResponse' => [ 'type' => 'structure', 'members' => [ 'ResolverRule' => [ 'shape' => 'ResolverRule', ], ], ], 'ImportFirewallDomainsRequest' => [ 'type' => 'structure', 'required' => [ 'FirewallDomainListId', 'Operation', 'DomainFileUrl', ], 'members' => [ 'FirewallDomainListId' => [ 'shape' => 'ResourceId', ], 'Operation' => [ 'shape' => 'FirewallDomainImportOperation', ], 'DomainFileUrl' => [ 'shape' => 'DomainListFileUrl', ], ], ], 'ImportFirewallDomainsResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'Name', ], 'Status' => [ 'shape' => 'FirewallDomainListStatus', ], 'StatusMessage' => [ 'shape' => 'StatusMessage', ], ], ], 'InternalServiceErrorException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'InvalidNextTokenException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidParameterException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'FieldName' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'InvalidPolicyDocument' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'InvalidRequestException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'InvalidTagException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'Ip' => [ 'type' => 'string', 'max' => 36, 'min' => 7, ], 'IpAddressCount' => [ 'type' => 'integer', ], 'IpAddressRequest' => [ 'type' => 'structure', 'required' => [ 'SubnetId', ], 'members' => [ 'SubnetId' => [ 'shape' => 'SubnetId', ], 'Ip' => [ 'shape' => 'Ip', 'box' => true, ], ], ], 'IpAddressResponse' => [ 'type' => 'structure', 'members' => [ 'IpId' => [ 'shape' => 'ResourceId', ], 'SubnetId' => [ 'shape' => 'SubnetId', ], 'Ip' => [ 'shape' => 'Ip', ], 'Status' => [ 'shape' => 'IpAddressStatus', ], 'StatusMessage' => [ 'shape' => 'StatusMessage', ], 'CreationTime' => [ 'shape' => 'Rfc3339TimeString', ], 'ModificationTime' => [ 'shape' => 'Rfc3339TimeString', ], ], ], 'IpAddressStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'FAILED_CREATION', 'ATTACHING', 'ATTACHED', 'REMAP_DETACHING', 'REMAP_ATTACHING', 'DETACHING', 'FAILED_RESOURCE_GONE', 'DELETING', 'DELETE_FAILED_FAS_EXPIRED', ], ], 'IpAddressUpdate' => [ 'type' => 'structure', 'members' => [ 'IpId' => [ 'shape' => 'ResourceId', 'box' => true, ], 'SubnetId' => [ 'shape' => 'SubnetId', 'box' => true, ], 'Ip' => [ 'shape' => 'Ip', 'box' => true, ], ], ], 'IpAddressesRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpAddressRequest', ], 'max' => 10, 'min' => 1, ], 'IpAddressesResponse' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpAddressResponse', ], ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ListDomainMaxResults' => [ 'type' => 'integer', 'max' => 5000, 'min' => 1, ], 'ListFirewallConfigsMaxResult' => [ 'type' => 'integer', 'max' => 10, 'min' => 5, ], 'ListFirewallConfigsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'ListFirewallConfigsMaxResult', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', 'box' => true, ], ], ], 'ListFirewallConfigsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'FirewallConfigs' => [ 'shape' => 'FirewallConfigList', ], ], ], 'ListFirewallDomainListsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', 'box' => true, ], ], ], 'ListFirewallDomainListsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'FirewallDomainLists' => [ 'shape' => 'FirewallDomainListMetadataList', ], ], ], 'ListFirewallDomainsRequest' => [ 'type' => 'structure', 'required' => [ 'FirewallDomainListId', ], 'members' => [ 'FirewallDomainListId' => [ 'shape' => 'ResourceId', ], 'MaxResults' => [ 'shape' => 'ListDomainMaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', 'box' => true, ], ], ], 'ListFirewallDomainsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'Domains' => [ 'shape' => 'FirewallDomains', ], ], ], 'ListFirewallRuleGroupAssociationsRequest' => [ 'type' => 'structure', 'members' => [ 'FirewallRuleGroupId' => [ 'shape' => 'ResourceId', 'box' => true, ], 'VpcId' => [ 'shape' => 'ResourceId', 'box' => true, ], 'Priority' => [ 'shape' => 'Priority', 'box' => true, ], 'Status' => [ 'shape' => 'FirewallRuleGroupAssociationStatus', 'box' => true, ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', 'box' => true, ], ], ], 'ListFirewallRuleGroupAssociationsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'FirewallRuleGroupAssociations' => [ 'shape' => 'FirewallRuleGroupAssociations', ], ], ], 'ListFirewallRuleGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', 'box' => true, ], ], ], 'ListFirewallRuleGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'FirewallRuleGroups' => [ 'shape' => 'FirewallRuleGroupMetadataList', ], ], ], 'ListFirewallRulesRequest' => [ 'type' => 'structure', 'required' => [ 'FirewallRuleGroupId', ], 'members' => [ 'FirewallRuleGroupId' => [ 'shape' => 'ResourceId', ], 'Priority' => [ 'shape' => 'Priority', 'box' => true, ], 'Action' => [ 'shape' => 'Action', 'box' => true, ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', 'box' => true, ], ], ], 'ListFirewallRulesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'FirewallRules' => [ 'shape' => 'FirewallRules', ], ], ], 'ListResolverConfigsMaxResult' => [ 'type' => 'integer', 'max' => 100, 'min' => 5, ], 'ListResolverConfigsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'ListResolverConfigsMaxResult', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', 'box' => true, ], ], ], 'ListResolverConfigsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'ResolverConfigs' => [ 'shape' => 'ResolverConfigList', ], ], ], 'ListResolverDnssecConfigsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', 'box' => true, ], 'Filters' => [ 'shape' => 'Filters', 'box' => true, ], ], ], 'ListResolverDnssecConfigsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'ResolverDnssecConfigs' => [ 'shape' => 'ResolverDnssecConfigList', ], ], ], 'ListResolverEndpointIpAddressesRequest' => [ 'type' => 'structure', 'required' => [ 'ResolverEndpointId', ], 'members' => [ 'ResolverEndpointId' => [ 'shape' => 'ResourceId', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', 'box' => true, ], ], ], 'ListResolverEndpointIpAddressesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'IpAddresses' => [ 'shape' => 'IpAddressesResponse', ], ], ], 'ListResolverEndpointsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', 'box' => true, ], 'Filters' => [ 'shape' => 'Filters', 'box' => true, ], ], ], 'ListResolverEndpointsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'ResolverEndpoints' => [ 'shape' => 'ResolverEndpoints', ], ], ], 'ListResolverQueryLogConfigAssociationsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Filters' => [ 'shape' => 'Filters', ], 'SortBy' => [ 'shape' => 'SortByKey', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'ListResolverQueryLogConfigAssociationsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'TotalCount' => [ 'shape' => 'Count', ], 'TotalFilteredCount' => [ 'shape' => 'Count', ], 'ResolverQueryLogConfigAssociations' => [ 'shape' => 'ResolverQueryLogConfigAssociationList', ], ], ], 'ListResolverQueryLogConfigsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', 'box' => true, ], 'Filters' => [ 'shape' => 'Filters', 'box' => true, ], 'SortBy' => [ 'shape' => 'SortByKey', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'ListResolverQueryLogConfigsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'TotalCount' => [ 'shape' => 'Count', ], 'TotalFilteredCount' => [ 'shape' => 'Count', ], 'ResolverQueryLogConfigs' => [ 'shape' => 'ResolverQueryLogConfigList', ], ], ], 'ListResolverRuleAssociationsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', 'box' => true, ], 'Filters' => [ 'shape' => 'Filters', 'box' => true, ], ], ], 'ListResolverRuleAssociationsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'ResolverRuleAssociations' => [ 'shape' => 'ResolverRuleAssociations', ], ], ], 'ListResolverRulesRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', 'box' => true, ], 'Filters' => [ 'shape' => 'Filters', 'box' => true, ], ], ], 'ListResolverRulesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'ResolverRules' => [ 'shape' => 'ResolverRules', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'box' => true, ], 'NextToken' => [ 'shape' => 'NextToken', 'box' => true, ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'MutationProtectionStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'Name' => [ 'type' => 'string', 'max' => 64, 'pattern' => '(?!^[0-9]+$)([a-zA-Z0-9\\-_\' \']+)', ], 'NextToken' => [ 'type' => 'string', ], 'Port' => [ 'type' => 'integer', 'max' => 65535, 'min' => 0, ], 'Priority' => [ 'type' => 'integer', ], 'PutFirewallRuleGroupPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Arn', 'FirewallRuleGroupPolicy', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'FirewallRuleGroupPolicy' => [ 'shape' => 'FirewallRuleGroupPolicy', ], ], ], 'PutFirewallRuleGroupPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'ReturnValue' => [ 'shape' => 'Boolean', ], ], ], 'PutResolverQueryLogConfigPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Arn', 'ResolverQueryLogConfigPolicy', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'ResolverQueryLogConfigPolicy' => [ 'shape' => 'ResolverQueryLogConfigPolicy', ], ], ], 'PutResolverQueryLogConfigPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'ReturnValue' => [ 'shape' => 'Boolean', ], ], ], 'PutResolverRulePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Arn', 'ResolverRulePolicy', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'ResolverRulePolicy' => [ 'shape' => 'ResolverRulePolicy', ], ], ], 'PutResolverRulePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'ReturnValue' => [ 'shape' => 'Boolean', ], ], ], 'ResolverAutodefinedReverseStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLING', 'ENABLED', 'DISABLING', 'DISABLED', ], ], 'ResolverConfig' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ResourceId', ], 'ResourceId' => [ 'shape' => 'ResourceId', ], 'OwnerId' => [ 'shape' => 'AccountId', ], 'AutodefinedReverse' => [ 'shape' => 'ResolverAutodefinedReverseStatus', ], ], ], 'ResolverConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResolverConfig', ], ], 'ResolverDNSSECValidationStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLING', 'ENABLED', 'DISABLING', 'DISABLED', ], ], 'ResolverDnssecConfig' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ResourceId', ], 'OwnerId' => [ 'shape' => 'AccountId', ], 'ResourceId' => [ 'shape' => 'ResourceId', ], 'ValidationStatus' => [ 'shape' => 'ResolverDNSSECValidationStatus', ], ], ], 'ResolverDnssecConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResolverDnssecConfig', ], ], 'ResolverEndpoint' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ResourceId', ], 'CreatorRequestId' => [ 'shape' => 'CreatorRequestId', ], 'Arn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'Name', ], 'SecurityGroupIds' => [ 'shape' => 'SecurityGroupIds', ], 'Direction' => [ 'shape' => 'ResolverEndpointDirection', ], 'IpAddressCount' => [ 'shape' => 'IpAddressCount', ], 'HostVPCId' => [ 'shape' => 'ResourceId', ], 'Status' => [ 'shape' => 'ResolverEndpointStatus', ], 'StatusMessage' => [ 'shape' => 'StatusMessage', ], 'CreationTime' => [ 'shape' => 'Rfc3339TimeString', ], 'ModificationTime' => [ 'shape' => 'Rfc3339TimeString', ], ], ], 'ResolverEndpointDirection' => [ 'type' => 'string', 'enum' => [ 'INBOUND', 'OUTBOUND', ], ], 'ResolverEndpointStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'OPERATIONAL', 'UPDATING', 'AUTO_RECOVERING', 'ACTION_NEEDED', 'DELETING', ], ], 'ResolverEndpoints' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResolverEndpoint', ], ], 'ResolverQueryLogConfig' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ResourceId', ], 'OwnerId' => [ 'shape' => 'AccountId', ], 'Status' => [ 'shape' => 'ResolverQueryLogConfigStatus', ], 'ShareStatus' => [ 'shape' => 'ShareStatus', ], 'AssociationCount' => [ 'shape' => 'Count', ], 'Arn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'ResolverQueryLogConfigName', ], 'DestinationArn' => [ 'shape' => 'DestinationArn', ], 'CreatorRequestId' => [ 'shape' => 'CreatorRequestId', ], 'CreationTime' => [ 'shape' => 'Rfc3339TimeString', ], ], ], 'ResolverQueryLogConfigAssociation' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ResourceId', ], 'ResolverQueryLogConfigId' => [ 'shape' => 'ResourceId', ], 'ResourceId' => [ 'shape' => 'ResourceId', ], 'Status' => [ 'shape' => 'ResolverQueryLogConfigAssociationStatus', ], 'Error' => [ 'shape' => 'ResolverQueryLogConfigAssociationError', ], 'ErrorMessage' => [ 'shape' => 'ResolverQueryLogConfigAssociationErrorMessage', ], 'CreationTime' => [ 'shape' => 'Rfc3339TimeString', ], ], ], 'ResolverQueryLogConfigAssociationError' => [ 'type' => 'string', 'enum' => [ 'NONE', 'DESTINATION_NOT_FOUND', 'ACCESS_DENIED', 'INTERNAL_SERVICE_ERROR', ], ], 'ResolverQueryLogConfigAssociationErrorMessage' => [ 'type' => 'string', ], 'ResolverQueryLogConfigAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResolverQueryLogConfigAssociation', ], ], 'ResolverQueryLogConfigAssociationStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'ACTION_NEEDED', 'DELETING', 'FAILED', ], ], 'ResolverQueryLogConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResolverQueryLogConfig', ], ], 'ResolverQueryLogConfigName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '(?!^[0-9]+$)([a-zA-Z0-9\\-_\' \']+)', ], 'ResolverQueryLogConfigPolicy' => [ 'type' => 'string', 'max' => 30000, ], 'ResolverQueryLogConfigStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'CREATED', 'DELETING', 'FAILED', ], ], 'ResolverRule' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ResourceId', ], 'CreatorRequestId' => [ 'shape' => 'CreatorRequestId', ], 'Arn' => [ 'shape' => 'Arn', ], 'DomainName' => [ 'shape' => 'DomainName', ], 'Status' => [ 'shape' => 'ResolverRuleStatus', ], 'StatusMessage' => [ 'shape' => 'StatusMessage', ], 'RuleType' => [ 'shape' => 'RuleTypeOption', ], 'Name' => [ 'shape' => 'Name', ], 'TargetIps' => [ 'shape' => 'TargetList', ], 'ResolverEndpointId' => [ 'shape' => 'ResourceId', ], 'OwnerId' => [ 'shape' => 'AccountId', ], 'ShareStatus' => [ 'shape' => 'ShareStatus', ], 'CreationTime' => [ 'shape' => 'Rfc3339TimeString', ], 'ModificationTime' => [ 'shape' => 'Rfc3339TimeString', ], ], ], 'ResolverRuleAssociation' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ResourceId', ], 'ResolverRuleId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'Name', ], 'VPCId' => [ 'shape' => 'ResourceId', ], 'Status' => [ 'shape' => 'ResolverRuleAssociationStatus', ], 'StatusMessage' => [ 'shape' => 'StatusMessage', ], ], ], 'ResolverRuleAssociationStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'COMPLETE', 'DELETING', 'FAILED', 'OVERRIDDEN', ], ], 'ResolverRuleAssociations' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResolverRuleAssociation', ], ], 'ResolverRuleConfig' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'Name', ], 'TargetIps' => [ 'shape' => 'TargetList', ], 'ResolverEndpointId' => [ 'shape' => 'ResourceId', ], ], ], 'ResolverRulePolicy' => [ 'type' => 'string', 'max' => 5000, ], 'ResolverRuleStatus' => [ 'type' => 'string', 'enum' => [ 'COMPLETE', 'DELETING', 'UPDATING', 'FAILED', ], ], 'ResolverRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResolverRule', ], ], 'ResourceExistsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ResourceId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'ResourceInUseException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ResourceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], 'ResourceType' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'Rfc3339TimeString' => [ 'type' => 'string', 'max' => 40, 'min' => 20, ], 'RuleTypeOption' => [ 'type' => 'string', 'enum' => [ 'FORWARD', 'SYSTEM', 'RECURSIVE', ], ], 'SecurityGroupIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceId', ], ], 'ServicePrinciple' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'ShareStatus' => [ 'type' => 'string', 'enum' => [ 'NOT_SHARED', 'SHARED_WITH_ME', 'SHARED_BY_ME', ], ], 'SortByKey' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'StatusMessage' => [ 'type' => 'string', 'max' => 255, ], 'String' => [ 'type' => 'string', ], 'SubnetId' => [ 'type' => 'string', 'max' => 32, 'min' => 1, ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TargetAddress' => [ 'type' => 'structure', 'required' => [ 'Ip', ], 'members' => [ 'Ip' => [ 'shape' => 'Ip', ], 'Port' => [ 'shape' => 'Port', 'box' => true, ], ], ], 'TargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetAddress', ], 'min' => 1, ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'UnknownResourceException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'Unsigned' => [ 'type' => 'integer', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateFirewallConfigRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', 'FirewallFailOpen', ], 'members' => [ 'ResourceId' => [ 'shape' => 'ResourceId', ], 'FirewallFailOpen' => [ 'shape' => 'FirewallFailOpenStatus', ], ], ], 'UpdateFirewallConfigResponse' => [ 'type' => 'structure', 'members' => [ 'FirewallConfig' => [ 'shape' => 'FirewallConfig', ], ], ], 'UpdateFirewallDomainsRequest' => [ 'type' => 'structure', 'required' => [ 'FirewallDomainListId', 'Operation', 'Domains', ], 'members' => [ 'FirewallDomainListId' => [ 'shape' => 'ResourceId', ], 'Operation' => [ 'shape' => 'FirewallDomainUpdateOperation', ], 'Domains' => [ 'shape' => 'FirewallDomains', ], ], ], 'UpdateFirewallDomainsResponse' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'Name', ], 'Status' => [ 'shape' => 'FirewallDomainListStatus', ], 'StatusMessage' => [ 'shape' => 'StatusMessage', ], ], ], 'UpdateFirewallRuleGroupAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'FirewallRuleGroupAssociationId', ], 'members' => [ 'FirewallRuleGroupAssociationId' => [ 'shape' => 'ResourceId', ], 'Priority' => [ 'shape' => 'Priority', 'box' => true, ], 'MutationProtection' => [ 'shape' => 'MutationProtectionStatus', 'box' => true, ], 'Name' => [ 'shape' => 'Name', 'box' => true, ], ], ], 'UpdateFirewallRuleGroupAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'FirewallRuleGroupAssociation' => [ 'shape' => 'FirewallRuleGroupAssociation', ], ], ], 'UpdateFirewallRuleRequest' => [ 'type' => 'structure', 'required' => [ 'FirewallRuleGroupId', 'FirewallDomainListId', ], 'members' => [ 'FirewallRuleGroupId' => [ 'shape' => 'ResourceId', ], 'FirewallDomainListId' => [ 'shape' => 'ResourceId', ], 'Priority' => [ 'shape' => 'Priority', 'box' => true, ], 'Action' => [ 'shape' => 'Action', 'box' => true, ], 'BlockResponse' => [ 'shape' => 'BlockResponse', 'box' => true, ], 'BlockOverrideDomain' => [ 'shape' => 'BlockOverrideDomain', 'box' => true, ], 'BlockOverrideDnsType' => [ 'shape' => 'BlockOverrideDnsType', 'box' => true, ], 'BlockOverrideTtl' => [ 'shape' => 'BlockOverrideTtl', 'box' => true, ], 'Name' => [ 'shape' => 'Name', 'box' => true, ], ], ], 'UpdateFirewallRuleResponse' => [ 'type' => 'structure', 'members' => [ 'FirewallRule' => [ 'shape' => 'FirewallRule', ], ], ], 'UpdateResolverConfigRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', 'AutodefinedReverseFlag', ], 'members' => [ 'ResourceId' => [ 'shape' => 'ResourceId', ], 'AutodefinedReverseFlag' => [ 'shape' => 'AutodefinedReverseFlag', ], ], ], 'UpdateResolverConfigResponse' => [ 'type' => 'structure', 'members' => [ 'ResolverConfig' => [ 'shape' => 'ResolverConfig', ], ], ], 'UpdateResolverDnssecConfigRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', 'Validation', ], 'members' => [ 'ResourceId' => [ 'shape' => 'ResourceId', ], 'Validation' => [ 'shape' => 'Validation', ], ], ], 'UpdateResolverDnssecConfigResponse' => [ 'type' => 'structure', 'members' => [ 'ResolverDNSSECConfig' => [ 'shape' => 'ResolverDnssecConfig', ], ], ], 'UpdateResolverEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'ResolverEndpointId', ], 'members' => [ 'ResolverEndpointId' => [ 'shape' => 'ResourceId', ], 'Name' => [ 'shape' => 'Name', 'box' => true, ], ], ], 'UpdateResolverEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'ResolverEndpoint' => [ 'shape' => 'ResolverEndpoint', ], ], ], 'UpdateResolverRuleRequest' => [ 'type' => 'structure', 'required' => [ 'ResolverRuleId', 'Config', ], 'members' => [ 'ResolverRuleId' => [ 'shape' => 'ResourceId', ], 'Config' => [ 'shape' => 'ResolverRuleConfig', ], ], ], 'UpdateResolverRuleResponse' => [ 'type' => 'structure', 'members' => [ 'ResolverRule' => [ 'shape' => 'ResolverRule', ], ], ], 'Validation' => [ 'type' => 'string', 'enum' => [ 'ENABLE', 'DISABLE', ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], ],];
