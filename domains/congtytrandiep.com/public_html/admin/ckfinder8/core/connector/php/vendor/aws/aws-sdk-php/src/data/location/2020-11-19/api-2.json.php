<?php
// This file was auto-generated from sdk-root/src/data/location/2020-11-19/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-11-19', 'endpointPrefix' => 'geo', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'Amazon Location Service', 'serviceId' => 'Location', 'signatureVersion' => 'v4', 'signingName' => 'geo', 'uid' => 'location-2020-11-19', ], 'operations' => [ 'AssociateTrackerConsumer' => [ 'name' => 'AssociateTrackerConsumer', 'http' => [ 'method' => 'POST', 'requestUri' => '/tracking/v0/trackers/{TrackerName}/consumers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateTrackerConsumerRequest', ], 'output' => [ 'shape' => 'AssociateTrackerConsumerResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'tracking.', ], ], 'BatchDeleteDevicePositionHistory' => [ 'name' => 'BatchDeleteDevicePositionHistory', 'http' => [ 'method' => 'POST', 'requestUri' => '/tracking/v0/trackers/{TrackerName}/delete-positions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchDeleteDevicePositionHistoryRequest', ], 'output' => [ 'shape' => 'BatchDeleteDevicePositionHistoryResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'tracking.', ], ], 'BatchDeleteGeofence' => [ 'name' => 'BatchDeleteGeofence', 'http' => [ 'method' => 'POST', 'requestUri' => '/geofencing/v0/collections/{CollectionName}/delete-geofences', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchDeleteGeofenceRequest', ], 'output' => [ 'shape' => 'BatchDeleteGeofenceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'geofencing.', ], ], 'BatchEvaluateGeofences' => [ 'name' => 'BatchEvaluateGeofences', 'http' => [ 'method' => 'POST', 'requestUri' => '/geofencing/v0/collections/{CollectionName}/positions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchEvaluateGeofencesRequest', ], 'output' => [ 'shape' => 'BatchEvaluateGeofencesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'geofencing.', ], ], 'BatchGetDevicePosition' => [ 'name' => 'BatchGetDevicePosition', 'http' => [ 'method' => 'POST', 'requestUri' => '/tracking/v0/trackers/{TrackerName}/get-positions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchGetDevicePositionRequest', ], 'output' => [ 'shape' => 'BatchGetDevicePositionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'tracking.', ], ], 'BatchPutGeofence' => [ 'name' => 'BatchPutGeofence', 'http' => [ 'method' => 'POST', 'requestUri' => '/geofencing/v0/collections/{CollectionName}/put-geofences', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchPutGeofenceRequest', ], 'output' => [ 'shape' => 'BatchPutGeofenceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'geofencing.', ], ], 'BatchUpdateDevicePosition' => [ 'name' => 'BatchUpdateDevicePosition', 'http' => [ 'method' => 'POST', 'requestUri' => '/tracking/v0/trackers/{TrackerName}/positions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchUpdateDevicePositionRequest', ], 'output' => [ 'shape' => 'BatchUpdateDevicePositionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'tracking.', ], ], 'CalculateRoute' => [ 'name' => 'CalculateRoute', 'http' => [ 'method' => 'POST', 'requestUri' => '/routes/v0/calculators/{CalculatorName}/calculate/route', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CalculateRouteRequest', ], 'output' => [ 'shape' => 'CalculateRouteResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'routes.', ], ], 'CreateGeofenceCollection' => [ 'name' => 'CreateGeofenceCollection', 'http' => [ 'method' => 'POST', 'requestUri' => '/geofencing/v0/collections', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateGeofenceCollectionRequest', ], 'output' => [ 'shape' => 'CreateGeofenceCollectionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'geofencing.', ], 'idempotent' => true, ], 'CreateMap' => [ 'name' => 'CreateMap', 'http' => [ 'method' => 'POST', 'requestUri' => '/maps/v0/maps', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateMapRequest', ], 'output' => [ 'shape' => 'CreateMapResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'maps.', ], 'idempotent' => true, ], 'CreatePlaceIndex' => [ 'name' => 'CreatePlaceIndex', 'http' => [ 'method' => 'POST', 'requestUri' => '/places/v0/indexes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreatePlaceIndexRequest', ], 'output' => [ 'shape' => 'CreatePlaceIndexResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'places.', ], 'idempotent' => true, ], 'CreateRouteCalculator' => [ 'name' => 'CreateRouteCalculator', 'http' => [ 'method' => 'POST', 'requestUri' => '/routes/v0/calculators', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateRouteCalculatorRequest', ], 'output' => [ 'shape' => 'CreateRouteCalculatorResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'routes.', ], 'idempotent' => true, ], 'CreateTracker' => [ 'name' => 'CreateTracker', 'http' => [ 'method' => 'POST', 'requestUri' => '/tracking/v0/trackers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateTrackerRequest', ], 'output' => [ 'shape' => 'CreateTrackerResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'tracking.', ], 'idempotent' => true, ], 'DeleteGeofenceCollection' => [ 'name' => 'DeleteGeofenceCollection', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/geofencing/v0/collections/{CollectionName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteGeofenceCollectionRequest', ], 'output' => [ 'shape' => 'DeleteGeofenceCollectionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'geofencing.', ], 'idempotent' => true, ], 'DeleteMap' => [ 'name' => 'DeleteMap', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/maps/v0/maps/{MapName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteMapRequest', ], 'output' => [ 'shape' => 'DeleteMapResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'maps.', ], 'idempotent' => true, ], 'DeletePlaceIndex' => [ 'name' => 'DeletePlaceIndex', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/places/v0/indexes/{IndexName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeletePlaceIndexRequest', ], 'output' => [ 'shape' => 'DeletePlaceIndexResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'places.', ], 'idempotent' => true, ], 'DeleteRouteCalculator' => [ 'name' => 'DeleteRouteCalculator', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/routes/v0/calculators/{CalculatorName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteRouteCalculatorRequest', ], 'output' => [ 'shape' => 'DeleteRouteCalculatorResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'routes.', ], 'idempotent' => true, ], 'DeleteTracker' => [ 'name' => 'DeleteTracker', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tracking/v0/trackers/{TrackerName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteTrackerRequest', ], 'output' => [ 'shape' => 'DeleteTrackerResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'tracking.', ], 'idempotent' => true, ], 'DescribeGeofenceCollection' => [ 'name' => 'DescribeGeofenceCollection', 'http' => [ 'method' => 'GET', 'requestUri' => '/geofencing/v0/collections/{CollectionName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeGeofenceCollectionRequest', ], 'output' => [ 'shape' => 'DescribeGeofenceCollectionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'geofencing.', ], ], 'DescribeMap' => [ 'name' => 'DescribeMap', 'http' => [ 'method' => 'GET', 'requestUri' => '/maps/v0/maps/{MapName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeMapRequest', ], 'output' => [ 'shape' => 'DescribeMapResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'maps.', ], ], 'DescribePlaceIndex' => [ 'name' => 'DescribePlaceIndex', 'http' => [ 'method' => 'GET', 'requestUri' => '/places/v0/indexes/{IndexName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribePlaceIndexRequest', ], 'output' => [ 'shape' => 'DescribePlaceIndexResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'places.', ], ], 'DescribeRouteCalculator' => [ 'name' => 'DescribeRouteCalculator', 'http' => [ 'method' => 'GET', 'requestUri' => '/routes/v0/calculators/{CalculatorName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeRouteCalculatorRequest', ], 'output' => [ 'shape' => 'DescribeRouteCalculatorResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'routes.', ], ], 'DescribeTracker' => [ 'name' => 'DescribeTracker', 'http' => [ 'method' => 'GET', 'requestUri' => '/tracking/v0/trackers/{TrackerName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeTrackerRequest', ], 'output' => [ 'shape' => 'DescribeTrackerResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'tracking.', ], ], 'DisassociateTrackerConsumer' => [ 'name' => 'DisassociateTrackerConsumer', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tracking/v0/trackers/{TrackerName}/consumers/{ConsumerArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateTrackerConsumerRequest', ], 'output' => [ 'shape' => 'DisassociateTrackerConsumerResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'tracking.', ], ], 'GetDevicePosition' => [ 'name' => 'GetDevicePosition', 'http' => [ 'method' => 'GET', 'requestUri' => '/tracking/v0/trackers/{TrackerName}/devices/{DeviceId}/positions/latest', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDevicePositionRequest', ], 'output' => [ 'shape' => 'GetDevicePositionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'tracking.', ], ], 'GetDevicePositionHistory' => [ 'name' => 'GetDevicePositionHistory', 'http' => [ 'method' => 'POST', 'requestUri' => '/tracking/v0/trackers/{TrackerName}/devices/{DeviceId}/list-positions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDevicePositionHistoryRequest', ], 'output' => [ 'shape' => 'GetDevicePositionHistoryResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'tracking.', ], ], 'GetGeofence' => [ 'name' => 'GetGeofence', 'http' => [ 'method' => 'GET', 'requestUri' => '/geofencing/v0/collections/{CollectionName}/geofences/{GeofenceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetGeofenceRequest', ], 'output' => [ 'shape' => 'GetGeofenceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'geofencing.', ], ], 'GetMapGlyphs' => [ 'name' => 'GetMapGlyphs', 'http' => [ 'method' => 'GET', 'requestUri' => '/maps/v0/maps/{MapName}/glyphs/{FontStack}/{FontUnicodeRange}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMapGlyphsRequest', ], 'output' => [ 'shape' => 'GetMapGlyphsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'maps.', ], ], 'GetMapSprites' => [ 'name' => 'GetMapSprites', 'http' => [ 'method' => 'GET', 'requestUri' => '/maps/v0/maps/{MapName}/sprites/{FileName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMapSpritesRequest', ], 'output' => [ 'shape' => 'GetMapSpritesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'maps.', ], ], 'GetMapStyleDescriptor' => [ 'name' => 'GetMapStyleDescriptor', 'http' => [ 'method' => 'GET', 'requestUri' => '/maps/v0/maps/{MapName}/style-descriptor', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMapStyleDescriptorRequest', ], 'output' => [ 'shape' => 'GetMapStyleDescriptorResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'maps.', ], ], 'GetMapTile' => [ 'name' => 'GetMapTile', 'http' => [ 'method' => 'GET', 'requestUri' => '/maps/v0/maps/{MapName}/tiles/{Z}/{X}/{Y}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMapTileRequest', ], 'output' => [ 'shape' => 'GetMapTileResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'maps.', ], ], 'ListDevicePositions' => [ 'name' => 'ListDevicePositions', 'http' => [ 'method' => 'POST', 'requestUri' => '/tracking/v0/trackers/{TrackerName}/list-positions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDevicePositionsRequest', ], 'output' => [ 'shape' => 'ListDevicePositionsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'tracking.', ], ], 'ListGeofenceCollections' => [ 'name' => 'ListGeofenceCollections', 'http' => [ 'method' => 'POST', 'requestUri' => '/geofencing/v0/list-collections', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListGeofenceCollectionsRequest', ], 'output' => [ 'shape' => 'ListGeofenceCollectionsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'geofencing.', ], ], 'ListGeofences' => [ 'name' => 'ListGeofences', 'http' => [ 'method' => 'POST', 'requestUri' => '/geofencing/v0/collections/{CollectionName}/list-geofences', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListGeofencesRequest', ], 'output' => [ 'shape' => 'ListGeofencesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'geofencing.', ], ], 'ListMaps' => [ 'name' => 'ListMaps', 'http' => [ 'method' => 'POST', 'requestUri' => '/maps/v0/list-maps', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMapsRequest', ], 'output' => [ 'shape' => 'ListMapsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'maps.', ], ], 'ListPlaceIndexes' => [ 'name' => 'ListPlaceIndexes', 'http' => [ 'method' => 'POST', 'requestUri' => '/places/v0/list-indexes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPlaceIndexesRequest', ], 'output' => [ 'shape' => 'ListPlaceIndexesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'places.', ], ], 'ListRouteCalculators' => [ 'name' => 'ListRouteCalculators', 'http' => [ 'method' => 'POST', 'requestUri' => '/routes/v0/list-calculators', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRouteCalculatorsRequest', ], 'output' => [ 'shape' => 'ListRouteCalculatorsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'routes.', ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'metadata.', ], ], 'ListTrackerConsumers' => [ 'name' => 'ListTrackerConsumers', 'http' => [ 'method' => 'POST', 'requestUri' => '/tracking/v0/trackers/{TrackerName}/list-consumers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTrackerConsumersRequest', ], 'output' => [ 'shape' => 'ListTrackerConsumersResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'tracking.', ], ], 'ListTrackers' => [ 'name' => 'ListTrackers', 'http' => [ 'method' => 'POST', 'requestUri' => '/tracking/v0/list-trackers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTrackersRequest', ], 'output' => [ 'shape' => 'ListTrackersResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'tracking.', ], ], 'PutGeofence' => [ 'name' => 'PutGeofence', 'http' => [ 'method' => 'PUT', 'requestUri' => '/geofencing/v0/collections/{CollectionName}/geofences/{GeofenceId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutGeofenceRequest', ], 'output' => [ 'shape' => 'PutGeofenceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'geofencing.', ], ], 'SearchPlaceIndexForPosition' => [ 'name' => 'SearchPlaceIndexForPosition', 'http' => [ 'method' => 'POST', 'requestUri' => '/places/v0/indexes/{IndexName}/search/position', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchPlaceIndexForPositionRequest', ], 'output' => [ 'shape' => 'SearchPlaceIndexForPositionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'places.', ], ], 'SearchPlaceIndexForSuggestions' => [ 'name' => 'SearchPlaceIndexForSuggestions', 'http' => [ 'method' => 'POST', 'requestUri' => '/places/v0/indexes/{IndexName}/search/suggestions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchPlaceIndexForSuggestionsRequest', ], 'output' => [ 'shape' => 'SearchPlaceIndexForSuggestionsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'places.', ], ], 'SearchPlaceIndexForText' => [ 'name' => 'SearchPlaceIndexForText', 'http' => [ 'method' => 'POST', 'requestUri' => '/places/v0/indexes/{IndexName}/search/text', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchPlaceIndexForTextRequest', ], 'output' => [ 'shape' => 'SearchPlaceIndexForTextResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'places.', ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'metadata.', ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{ResourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'metadata.', ], 'idempotent' => true, ], 'UpdateGeofenceCollection' => [ 'name' => 'UpdateGeofenceCollection', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/geofencing/v0/collections/{CollectionName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateGeofenceCollectionRequest', ], 'output' => [ 'shape' => 'UpdateGeofenceCollectionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'geofencing.', ], 'idempotent' => true, ], 'UpdateMap' => [ 'name' => 'UpdateMap', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/maps/v0/maps/{MapName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateMapRequest', ], 'output' => [ 'shape' => 'UpdateMapResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'maps.', ], 'idempotent' => true, ], 'UpdatePlaceIndex' => [ 'name' => 'UpdatePlaceIndex', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/places/v0/indexes/{IndexName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdatePlaceIndexRequest', ], 'output' => [ 'shape' => 'UpdatePlaceIndexResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'places.', ], 'idempotent' => true, ], 'UpdateRouteCalculator' => [ 'name' => 'UpdateRouteCalculator', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/routes/v0/calculators/{CalculatorName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateRouteCalculatorRequest', ], 'output' => [ 'shape' => 'UpdateRouteCalculatorResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'routes.', ], 'idempotent' => true, ], 'UpdateTracker' => [ 'name' => 'UpdateTracker', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/tracking/v0/trackers/{TrackerName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateTrackerRequest', ], 'output' => [ 'shape' => 'UpdateTrackerResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], ], 'endpoint' => [ 'hostPrefix' => 'tracking.', ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'Arn' => [ 'type' => 'string', 'max' => 1600, 'min' => 0, 'pattern' => '^arn(:[a-z0-9]+([.-][a-z0-9]+)*){2}(:([a-z0-9]+([.-][a-z0-9]+)*)?){2}:([^/].*)?$', ], 'ArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], ], 'AssociateTrackerConsumerRequest' => [ 'type' => 'structure', 'required' => [ 'ConsumerArn', 'TrackerName', ], 'members' => [ 'ConsumerArn' => [ 'shape' => 'Arn', ], 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], ], ], 'AssociateTrackerConsumerResponse' => [ 'type' => 'structure', 'members' => [], ], 'BatchDeleteDevicePositionHistoryError' => [ 'type' => 'structure', 'required' => [ 'DeviceId', 'Error', ], 'members' => [ 'DeviceId' => [ 'shape' => 'Id', ], 'Error' => [ 'shape' => 'BatchItemError', ], ], ], 'BatchDeleteDevicePositionHistoryErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchDeleteDevicePositionHistoryError', ], ], 'BatchDeleteDevicePositionHistoryRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceIds', 'TrackerName', ], 'members' => [ 'DeviceIds' => [ 'shape' => 'BatchDeleteDevicePositionHistoryRequestDeviceIdsList', ], 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], ], ], 'BatchDeleteDevicePositionHistoryRequestDeviceIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Id', ], 'max' => 100, 'min' => 1, ], 'BatchDeleteDevicePositionHistoryResponse' => [ 'type' => 'structure', 'required' => [ 'Errors', ], 'members' => [ 'Errors' => [ 'shape' => 'BatchDeleteDevicePositionHistoryErrorList', ], ], ], 'BatchDeleteGeofenceError' => [ 'type' => 'structure', 'required' => [ 'Error', 'GeofenceId', ], 'members' => [ 'Error' => [ 'shape' => 'BatchItemError', ], 'GeofenceId' => [ 'shape' => 'Id', ], ], ], 'BatchDeleteGeofenceErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchDeleteGeofenceError', ], ], 'BatchDeleteGeofenceRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', 'GeofenceIds', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CollectionName', ], 'GeofenceIds' => [ 'shape' => 'BatchDeleteGeofenceRequestGeofenceIdsList', ], ], ], 'BatchDeleteGeofenceRequestGeofenceIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Id', ], 'max' => 10, 'min' => 1, ], 'BatchDeleteGeofenceResponse' => [ 'type' => 'structure', 'required' => [ 'Errors', ], 'members' => [ 'Errors' => [ 'shape' => 'BatchDeleteGeofenceErrorList', ], ], ], 'BatchEvaluateGeofencesError' => [ 'type' => 'structure', 'required' => [ 'DeviceId', 'Error', 'SampleTime', ], 'members' => [ 'DeviceId' => [ 'shape' => 'Id', ], 'Error' => [ 'shape' => 'BatchItemError', ], 'SampleTime' => [ 'shape' => 'Timestamp', ], ], ], 'BatchEvaluateGeofencesErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchEvaluateGeofencesError', ], ], 'BatchEvaluateGeofencesRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', 'DevicePositionUpdates', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CollectionName', ], 'DevicePositionUpdates' => [ 'shape' => 'BatchEvaluateGeofencesRequestDevicePositionUpdatesList', ], ], ], 'BatchEvaluateGeofencesRequestDevicePositionUpdatesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DevicePositionUpdate', ], 'max' => 10, 'min' => 1, ], 'BatchEvaluateGeofencesResponse' => [ 'type' => 'structure', 'required' => [ 'Errors', ], 'members' => [ 'Errors' => [ 'shape' => 'BatchEvaluateGeofencesErrorList', ], ], ], 'BatchGetDevicePositionError' => [ 'type' => 'structure', 'required' => [ 'DeviceId', 'Error', ], 'members' => [ 'DeviceId' => [ 'shape' => 'Id', ], 'Error' => [ 'shape' => 'BatchItemError', ], ], ], 'BatchGetDevicePositionErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchGetDevicePositionError', ], ], 'BatchGetDevicePositionRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceIds', 'TrackerName', ], 'members' => [ 'DeviceIds' => [ 'shape' => 'BatchGetDevicePositionRequestDeviceIdsList', ], 'TrackerName' => [ 'shape' => 'BatchGetDevicePositionRequestTrackerNameString', 'location' => 'uri', 'locationName' => 'TrackerName', ], ], ], 'BatchGetDevicePositionRequestDeviceIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Id', ], 'max' => 10, 'min' => 1, ], 'BatchGetDevicePositionRequestTrackerNameString' => [ 'type' => 'string', 'min' => 1, 'pattern' => '^[-._\\w]+$', ], 'BatchGetDevicePositionResponse' => [ 'type' => 'structure', 'required' => [ 'DevicePositions', 'Errors', ], 'members' => [ 'DevicePositions' => [ 'shape' => 'DevicePositionList', ], 'Errors' => [ 'shape' => 'BatchGetDevicePositionErrorList', ], ], ], 'BatchItemError' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'BatchItemErrorCode', ], 'Message' => [ 'shape' => 'String', ], ], ], 'BatchItemErrorCode' => [ 'type' => 'string', 'enum' => [ 'AccessDeniedError', 'ConflictError', 'InternalServerError', 'ResourceNotFoundError', 'ThrottlingError', 'ValidationError', ], ], 'BatchPutGeofenceError' => [ 'type' => 'structure', 'required' => [ 'Error', 'GeofenceId', ], 'members' => [ 'Error' => [ 'shape' => 'BatchItemError', ], 'GeofenceId' => [ 'shape' => 'Id', ], ], ], 'BatchPutGeofenceErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchPutGeofenceError', ], ], 'BatchPutGeofenceRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', 'Entries', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CollectionName', ], 'Entries' => [ 'shape' => 'BatchPutGeofenceRequestEntriesList', ], ], ], 'BatchPutGeofenceRequestEntriesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchPutGeofenceRequestEntry', ], 'max' => 10, 'min' => 1, ], 'BatchPutGeofenceRequestEntry' => [ 'type' => 'structure', 'required' => [ 'GeofenceId', 'Geometry', ], 'members' => [ 'GeofenceId' => [ 'shape' => 'Id', ], 'Geometry' => [ 'shape' => 'GeofenceGeometry', ], ], ], 'BatchPutGeofenceResponse' => [ 'type' => 'structure', 'required' => [ 'Errors', 'Successes', ], 'members' => [ 'Errors' => [ 'shape' => 'BatchPutGeofenceErrorList', ], 'Successes' => [ 'shape' => 'BatchPutGeofenceSuccessList', ], ], ], 'BatchPutGeofenceSuccess' => [ 'type' => 'structure', 'required' => [ 'CreateTime', 'GeofenceId', 'UpdateTime', ], 'members' => [ 'CreateTime' => [ 'shape' => 'Timestamp', ], 'GeofenceId' => [ 'shape' => 'Id', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'BatchPutGeofenceSuccessList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchPutGeofenceSuccess', ], ], 'BatchUpdateDevicePositionError' => [ 'type' => 'structure', 'required' => [ 'DeviceId', 'Error', 'SampleTime', ], 'members' => [ 'DeviceId' => [ 'shape' => 'Id', ], 'Error' => [ 'shape' => 'BatchItemError', ], 'SampleTime' => [ 'shape' => 'Timestamp', ], ], ], 'BatchUpdateDevicePositionErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchUpdateDevicePositionError', ], ], 'BatchUpdateDevicePositionRequest' => [ 'type' => 'structure', 'required' => [ 'TrackerName', 'Updates', ], 'members' => [ 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], 'Updates' => [ 'shape' => 'BatchUpdateDevicePositionRequestUpdatesList', ], ], ], 'BatchUpdateDevicePositionRequestUpdatesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DevicePositionUpdate', ], 'max' => 10, 'min' => 1, ], 'BatchUpdateDevicePositionResponse' => [ 'type' => 'structure', 'required' => [ 'Errors', ], 'members' => [ 'Errors' => [ 'shape' => 'BatchUpdateDevicePositionErrorList', ], ], ], 'Blob' => [ 'type' => 'blob', ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'BoundingBox' => [ 'type' => 'list', 'member' => [ 'shape' => 'Double', ], 'max' => 4, 'min' => 4, 'sensitive' => true, ], 'CalculateRouteCarModeOptions' => [ 'type' => 'structure', 'members' => [ 'AvoidFerries' => [ 'shape' => 'Boolean', ], 'AvoidTolls' => [ 'shape' => 'Boolean', ], ], ], 'CalculateRouteRequest' => [ 'type' => 'structure', 'required' => [ 'CalculatorName', 'DeparturePosition', 'DestinationPosition', ], 'members' => [ 'CalculatorName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CalculatorName', ], 'CarModeOptions' => [ 'shape' => 'CalculateRouteCarModeOptions', ], 'DepartNow' => [ 'shape' => 'Boolean', ], 'DeparturePosition' => [ 'shape' => 'Position', ], 'DepartureTime' => [ 'shape' => 'Timestamp', ], 'DestinationPosition' => [ 'shape' => 'Position', ], 'DistanceUnit' => [ 'shape' => 'DistanceUnit', ], 'IncludeLegGeometry' => [ 'shape' => 'Boolean', ], 'TravelMode' => [ 'shape' => 'TravelMode', ], 'TruckModeOptions' => [ 'shape' => 'CalculateRouteTruckModeOptions', ], 'WaypointPositions' => [ 'shape' => 'CalculateRouteRequestWaypointPositionsList', ], ], ], 'CalculateRouteRequestWaypointPositionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Position', ], 'max' => 23, 'min' => 0, ], 'CalculateRouteResponse' => [ 'type' => 'structure', 'required' => [ 'Legs', 'Summary', ], 'members' => [ 'Legs' => [ 'shape' => 'LegList', ], 'Summary' => [ 'shape' => 'CalculateRouteSummary', ], ], ], 'CalculateRouteSummary' => [ 'type' => 'structure', 'required' => [ 'DataSource', 'Distance', 'DistanceUnit', 'DurationSeconds', 'RouteBBox', ], 'members' => [ 'DataSource' => [ 'shape' => 'String', ], 'Distance' => [ 'shape' => 'CalculateRouteSummaryDistanceDouble', ], 'DistanceUnit' => [ 'shape' => 'DistanceUnit', ], 'DurationSeconds' => [ 'shape' => 'CalculateRouteSummaryDurationSecondsDouble', ], 'RouteBBox' => [ 'shape' => 'BoundingBox', ], ], ], 'CalculateRouteSummaryDistanceDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'CalculateRouteSummaryDurationSecondsDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'CalculateRouteTruckModeOptions' => [ 'type' => 'structure', 'members' => [ 'AvoidFerries' => [ 'shape' => 'Boolean', ], 'AvoidTolls' => [ 'shape' => 'Boolean', ], 'Dimensions' => [ 'shape' => 'TruckDimensions', ], 'Weight' => [ 'shape' => 'TruckWeight', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CountryCode' => [ 'type' => 'string', 'pattern' => '^[A-Z]{3}$', ], 'CountryCodeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CountryCode', ], 'max' => 100, 'min' => 1, ], 'CreateGeofenceCollectionRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', 'PricingPlan', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', ], 'PricingPlanDataSource' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateGeofenceCollectionResponse' => [ 'type' => 'structure', 'required' => [ 'CollectionArn', 'CollectionName', 'CreateTime', ], 'members' => [ 'CollectionArn' => [ 'shape' => 'Arn', ], 'CollectionName' => [ 'shape' => 'ResourceName', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], ], ], 'CreateMapRequest' => [ 'type' => 'structure', 'required' => [ 'Configuration', 'MapName', 'PricingPlan', ], 'members' => [ 'Configuration' => [ 'shape' => 'MapConfiguration', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'MapName' => [ 'shape' => 'ResourceName', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateMapResponse' => [ 'type' => 'structure', 'required' => [ 'CreateTime', 'MapArn', 'MapName', ], 'members' => [ 'CreateTime' => [ 'shape' => 'Timestamp', ], 'MapArn' => [ 'shape' => 'Arn', ], 'MapName' => [ 'shape' => 'ResourceName', ], ], ], 'CreatePlaceIndexRequest' => [ 'type' => 'structure', 'required' => [ 'DataSource', 'IndexName', 'PricingPlan', ], 'members' => [ 'DataSource' => [ 'shape' => 'String', ], 'DataSourceConfiguration' => [ 'shape' => 'DataSourceConfiguration', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'IndexName' => [ 'shape' => 'ResourceName', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreatePlaceIndexResponse' => [ 'type' => 'structure', 'required' => [ 'CreateTime', 'IndexArn', 'IndexName', ], 'members' => [ 'CreateTime' => [ 'shape' => 'Timestamp', ], 'IndexArn' => [ 'shape' => 'Arn', ], 'IndexName' => [ 'shape' => 'ResourceName', ], ], ], 'CreateRouteCalculatorRequest' => [ 'type' => 'structure', 'required' => [ 'CalculatorName', 'DataSource', 'PricingPlan', ], 'members' => [ 'CalculatorName' => [ 'shape' => 'ResourceName', ], 'DataSource' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateRouteCalculatorResponse' => [ 'type' => 'structure', 'required' => [ 'CalculatorArn', 'CalculatorName', 'CreateTime', ], 'members' => [ 'CalculatorArn' => [ 'shape' => 'Arn', ], 'CalculatorName' => [ 'shape' => 'ResourceName', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], ], ], 'CreateTrackerRequest' => [ 'type' => 'structure', 'required' => [ 'PricingPlan', 'TrackerName', ], 'members' => [ 'Description' => [ 'shape' => 'ResourceDescription', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'PositionFiltering' => [ 'shape' => 'PositionFiltering', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', ], 'PricingPlanDataSource' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagMap', ], 'TrackerName' => [ 'shape' => 'ResourceName', ], ], ], 'CreateTrackerResponse' => [ 'type' => 'structure', 'required' => [ 'CreateTime', 'TrackerArn', 'TrackerName', ], 'members' => [ 'CreateTime' => [ 'shape' => 'Timestamp', ], 'TrackerArn' => [ 'shape' => 'Arn', ], 'TrackerName' => [ 'shape' => 'ResourceName', ], ], ], 'DataSourceConfiguration' => [ 'type' => 'structure', 'members' => [ 'IntendedUse' => [ 'shape' => 'IntendedUse', ], ], ], 'DeleteGeofenceCollectionRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CollectionName', ], ], ], 'DeleteGeofenceCollectionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteMapRequest' => [ 'type' => 'structure', 'required' => [ 'MapName', ], 'members' => [ 'MapName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'MapName', ], ], ], 'DeleteMapResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeletePlaceIndexRequest' => [ 'type' => 'structure', 'required' => [ 'IndexName', ], 'members' => [ 'IndexName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'IndexName', ], ], ], 'DeletePlaceIndexResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRouteCalculatorRequest' => [ 'type' => 'structure', 'required' => [ 'CalculatorName', ], 'members' => [ 'CalculatorName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CalculatorName', ], ], ], 'DeleteRouteCalculatorResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTrackerRequest' => [ 'type' => 'structure', 'required' => [ 'TrackerName', ], 'members' => [ 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], ], ], 'DeleteTrackerResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeGeofenceCollectionRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CollectionName', ], ], ], 'DescribeGeofenceCollectionResponse' => [ 'type' => 'structure', 'required' => [ 'CollectionArn', 'CollectionName', 'CreateTime', 'Description', 'PricingPlan', 'UpdateTime', ], 'members' => [ 'CollectionArn' => [ 'shape' => 'Arn', ], 'CollectionName' => [ 'shape' => 'ResourceName', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', ], 'PricingPlanDataSource' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagMap', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeMapRequest' => [ 'type' => 'structure', 'required' => [ 'MapName', ], 'members' => [ 'MapName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'MapName', ], ], ], 'DescribeMapResponse' => [ 'type' => 'structure', 'required' => [ 'Configuration', 'CreateTime', 'DataSource', 'Description', 'MapArn', 'MapName', 'PricingPlan', 'UpdateTime', ], 'members' => [ 'Configuration' => [ 'shape' => 'MapConfiguration', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'DataSource' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'MapArn' => [ 'shape' => 'Arn', ], 'MapName' => [ 'shape' => 'ResourceName', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', ], 'Tags' => [ 'shape' => 'TagMap', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribePlaceIndexRequest' => [ 'type' => 'structure', 'required' => [ 'IndexName', ], 'members' => [ 'IndexName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'IndexName', ], ], ], 'DescribePlaceIndexResponse' => [ 'type' => 'structure', 'required' => [ 'CreateTime', 'DataSource', 'DataSourceConfiguration', 'Description', 'IndexArn', 'IndexName', 'PricingPlan', 'UpdateTime', ], 'members' => [ 'CreateTime' => [ 'shape' => 'Timestamp', ], 'DataSource' => [ 'shape' => 'String', ], 'DataSourceConfiguration' => [ 'shape' => 'DataSourceConfiguration', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'IndexArn' => [ 'shape' => 'Arn', ], 'IndexName' => [ 'shape' => 'ResourceName', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', ], 'Tags' => [ 'shape' => 'TagMap', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeRouteCalculatorRequest' => [ 'type' => 'structure', 'required' => [ 'CalculatorName', ], 'members' => [ 'CalculatorName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CalculatorName', ], ], ], 'DescribeRouteCalculatorResponse' => [ 'type' => 'structure', 'required' => [ 'CalculatorArn', 'CalculatorName', 'CreateTime', 'DataSource', 'Description', 'PricingPlan', 'UpdateTime', ], 'members' => [ 'CalculatorArn' => [ 'shape' => 'Arn', ], 'CalculatorName' => [ 'shape' => 'ResourceName', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'DataSource' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', ], 'Tags' => [ 'shape' => 'TagMap', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeTrackerRequest' => [ 'type' => 'structure', 'required' => [ 'TrackerName', ], 'members' => [ 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], ], ], 'DescribeTrackerResponse' => [ 'type' => 'structure', 'required' => [ 'CreateTime', 'Description', 'PricingPlan', 'TrackerArn', 'TrackerName', 'UpdateTime', ], 'members' => [ 'CreateTime' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'KmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'PositionFiltering' => [ 'shape' => 'PositionFiltering', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', ], 'PricingPlanDataSource' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagMap', ], 'TrackerArn' => [ 'shape' => 'Arn', ], 'TrackerName' => [ 'shape' => 'ResourceName', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'DevicePosition' => [ 'type' => 'structure', 'required' => [ 'Position', 'ReceivedTime', 'SampleTime', ], 'members' => [ 'Accuracy' => [ 'shape' => 'PositionalAccuracy', ], 'DeviceId' => [ 'shape' => 'Id', ], 'Position' => [ 'shape' => 'Position', ], 'PositionProperties' => [ 'shape' => 'PropertyMap', ], 'ReceivedTime' => [ 'shape' => 'Timestamp', ], 'SampleTime' => [ 'shape' => 'Timestamp', ], ], ], 'DevicePositionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DevicePosition', ], ], 'DevicePositionUpdate' => [ 'type' => 'structure', 'required' => [ 'DeviceId', 'Position', 'SampleTime', ], 'members' => [ 'Accuracy' => [ 'shape' => 'PositionalAccuracy', ], 'DeviceId' => [ 'shape' => 'Id', ], 'Position' => [ 'shape' => 'Position', ], 'PositionProperties' => [ 'shape' => 'PropertyMap', ], 'SampleTime' => [ 'shape' => 'Timestamp', ], ], ], 'DimensionUnit' => [ 'type' => 'string', 'enum' => [ 'Meters', 'Feet', ], ], 'DisassociateTrackerConsumerRequest' => [ 'type' => 'structure', 'required' => [ 'ConsumerArn', 'TrackerName', ], 'members' => [ 'ConsumerArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ConsumerArn', ], 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], ], ], 'DisassociateTrackerConsumerResponse' => [ 'type' => 'structure', 'members' => [], ], 'DistanceUnit' => [ 'type' => 'string', 'enum' => [ 'Kilometers', 'Miles', ], ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'GeofenceGeometry' => [ 'type' => 'structure', 'members' => [ 'Polygon' => [ 'shape' => 'LinearRings', ], ], ], 'GetDevicePositionHistoryRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceId', 'TrackerName', ], 'members' => [ 'DeviceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'DeviceId', ], 'EndTimeExclusive' => [ 'shape' => 'Timestamp', ], 'NextToken' => [ 'shape' => 'Token', ], 'StartTimeInclusive' => [ 'shape' => 'Timestamp', ], 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], ], ], 'GetDevicePositionHistoryResponse' => [ 'type' => 'structure', 'required' => [ 'DevicePositions', ], 'members' => [ 'DevicePositions' => [ 'shape' => 'DevicePositionList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetDevicePositionRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceId', 'TrackerName', ], 'members' => [ 'DeviceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'DeviceId', ], 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], ], ], 'GetDevicePositionResponse' => [ 'type' => 'structure', 'required' => [ 'Position', 'ReceivedTime', 'SampleTime', ], 'members' => [ 'Accuracy' => [ 'shape' => 'PositionalAccuracy', ], 'DeviceId' => [ 'shape' => 'Id', ], 'Position' => [ 'shape' => 'Position', ], 'PositionProperties' => [ 'shape' => 'PropertyMap', ], 'ReceivedTime' => [ 'shape' => 'Timestamp', ], 'SampleTime' => [ 'shape' => 'Timestamp', ], ], ], 'GetGeofenceRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', 'GeofenceId', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CollectionName', ], 'GeofenceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'GeofenceId', ], ], ], 'GetGeofenceResponse' => [ 'type' => 'structure', 'required' => [ 'CreateTime', 'GeofenceId', 'Geometry', 'Status', 'UpdateTime', ], 'members' => [ 'CreateTime' => [ 'shape' => 'Timestamp', ], 'GeofenceId' => [ 'shape' => 'Id', ], 'Geometry' => [ 'shape' => 'GeofenceGeometry', ], 'Status' => [ 'shape' => 'String', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'GetMapGlyphsRequest' => [ 'type' => 'structure', 'required' => [ 'FontStack', 'FontUnicodeRange', 'MapName', ], 'members' => [ 'FontStack' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'FontStack', ], 'FontUnicodeRange' => [ 'shape' => 'GetMapGlyphsRequestFontUnicodeRangeString', 'location' => 'uri', 'locationName' => 'FontUnicodeRange', ], 'MapName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'MapName', ], ], ], 'GetMapGlyphsRequestFontUnicodeRangeString' => [ 'type' => 'string', 'pattern' => '^[0-9]+-[0-9]+\\.pbf$', ], 'GetMapGlyphsResponse' => [ 'type' => 'structure', 'members' => [ 'Blob' => [ 'shape' => 'Blob', ], 'ContentType' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Content-Type', ], ], 'payload' => 'Blob', ], 'GetMapSpritesRequest' => [ 'type' => 'structure', 'required' => [ 'FileName', 'MapName', ], 'members' => [ 'FileName' => [ 'shape' => 'GetMapSpritesRequestFileNameString', 'location' => 'uri', 'locationName' => 'FileName', ], 'MapName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'MapName', ], ], ], 'GetMapSpritesRequestFileNameString' => [ 'type' => 'string', 'pattern' => '^sprites(@2x)?\\.(png|json)$', ], 'GetMapSpritesResponse' => [ 'type' => 'structure', 'members' => [ 'Blob' => [ 'shape' => 'Blob', ], 'ContentType' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Content-Type', ], ], 'payload' => 'Blob', ], 'GetMapStyleDescriptorRequest' => [ 'type' => 'structure', 'required' => [ 'MapName', ], 'members' => [ 'MapName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'MapName', ], ], ], 'GetMapStyleDescriptorResponse' => [ 'type' => 'structure', 'members' => [ 'Blob' => [ 'shape' => 'Blob', ], 'ContentType' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Content-Type', ], ], 'payload' => 'Blob', ], 'GetMapTileRequest' => [ 'type' => 'structure', 'required' => [ 'MapName', 'X', 'Y', 'Z', ], 'members' => [ 'MapName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'MapName', ], 'X' => [ 'shape' => 'GetMapTileRequestXString', 'location' => 'uri', 'locationName' => 'X', ], 'Y' => [ 'shape' => 'GetMapTileRequestYString', 'location' => 'uri', 'locationName' => 'Y', ], 'Z' => [ 'shape' => 'GetMapTileRequestZString', 'location' => 'uri', 'locationName' => 'Z', ], ], ], 'GetMapTileRequestXString' => [ 'type' => 'string', 'pattern' => '\\d+', ], 'GetMapTileRequestYString' => [ 'type' => 'string', 'pattern' => '\\d+', ], 'GetMapTileRequestZString' => [ 'type' => 'string', 'pattern' => '\\d+', ], 'GetMapTileResponse' => [ 'type' => 'structure', 'members' => [ 'Blob' => [ 'shape' => 'Blob', ], 'ContentType' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Content-Type', ], ], 'payload' => 'Blob', ], 'Id' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[-._\\p{L}\\p{N}]+$', ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'IntendedUse' => [ 'type' => 'string', 'enum' => [ 'SingleUse', 'Storage', ], ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'KmsKeyId' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'LanguageTag' => [ 'type' => 'string', 'max' => 35, 'min' => 2, ], 'Leg' => [ 'type' => 'structure', 'required' => [ 'Distance', 'DurationSeconds', 'EndPosition', 'StartPosition', 'Steps', ], 'members' => [ 'Distance' => [ 'shape' => 'LegDistanceDouble', ], 'DurationSeconds' => [ 'shape' => 'LegDurationSecondsDouble', ], 'EndPosition' => [ 'shape' => 'Position', ], 'Geometry' => [ 'shape' => 'LegGeometry', ], 'StartPosition' => [ 'shape' => 'Position', ], 'Steps' => [ 'shape' => 'StepList', ], ], ], 'LegDistanceDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'LegDurationSecondsDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'LegGeometry' => [ 'type' => 'structure', 'members' => [ 'LineString' => [ 'shape' => 'LineString', ], ], ], 'LegList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Leg', ], ], 'LineString' => [ 'type' => 'list', 'member' => [ 'shape' => 'Position', ], 'min' => 2, ], 'LinearRing' => [ 'type' => 'list', 'member' => [ 'shape' => 'Position', ], 'min' => 4, ], 'LinearRings' => [ 'type' => 'list', 'member' => [ 'shape' => 'LinearRing', ], 'min' => 1, ], 'ListDevicePositionsRequest' => [ 'type' => 'structure', 'required' => [ 'TrackerName', ], 'members' => [ 'MaxResults' => [ 'shape' => 'ListDevicePositionsRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'Token', ], 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], ], ], 'ListDevicePositionsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListDevicePositionsResponse' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'Entries' => [ 'shape' => 'ListDevicePositionsResponseEntryList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListDevicePositionsResponseEntry' => [ 'type' => 'structure', 'required' => [ 'DeviceId', 'Position', 'SampleTime', ], 'members' => [ 'Accuracy' => [ 'shape' => 'PositionalAccuracy', ], 'DeviceId' => [ 'shape' => 'Id', ], 'Position' => [ 'shape' => 'Position', ], 'PositionProperties' => [ 'shape' => 'PropertyMap', ], 'SampleTime' => [ 'shape' => 'Timestamp', ], ], ], 'ListDevicePositionsResponseEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListDevicePositionsResponseEntry', ], ], 'ListGeofenceCollectionsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'ListGeofenceCollectionsRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListGeofenceCollectionsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListGeofenceCollectionsResponse' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'Entries' => [ 'shape' => 'ListGeofenceCollectionsResponseEntryList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListGeofenceCollectionsResponseEntry' => [ 'type' => 'structure', 'required' => [ 'CollectionName', 'CreateTime', 'Description', 'PricingPlan', 'UpdateTime', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', ], 'PricingPlanDataSource' => [ 'shape' => 'String', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ListGeofenceCollectionsResponseEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListGeofenceCollectionsResponseEntry', ], ], 'ListGeofenceResponseEntry' => [ 'type' => 'structure', 'required' => [ 'CreateTime', 'GeofenceId', 'Geometry', 'Status', 'UpdateTime', ], 'members' => [ 'CreateTime' => [ 'shape' => 'Timestamp', ], 'GeofenceId' => [ 'shape' => 'Id', ], 'Geometry' => [ 'shape' => 'GeofenceGeometry', ], 'Status' => [ 'shape' => 'String', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ListGeofenceResponseEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListGeofenceResponseEntry', ], ], 'ListGeofencesRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CollectionName', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListGeofencesResponse' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'Entries' => [ 'shape' => 'ListGeofenceResponseEntryList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListMapsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'ListMapsRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListMapsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListMapsResponse' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'Entries' => [ 'shape' => 'ListMapsResponseEntryList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListMapsResponseEntry' => [ 'type' => 'structure', 'required' => [ 'CreateTime', 'DataSource', 'Description', 'MapName', 'PricingPlan', 'UpdateTime', ], 'members' => [ 'CreateTime' => [ 'shape' => 'Timestamp', ], 'DataSource' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'MapName' => [ 'shape' => 'ResourceName', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ListMapsResponseEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListMapsResponseEntry', ], ], 'ListPlaceIndexesRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'ListPlaceIndexesRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListPlaceIndexesRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListPlaceIndexesResponse' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'Entries' => [ 'shape' => 'ListPlaceIndexesResponseEntryList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListPlaceIndexesResponseEntry' => [ 'type' => 'structure', 'required' => [ 'CreateTime', 'DataSource', 'Description', 'IndexName', 'PricingPlan', 'UpdateTime', ], 'members' => [ 'CreateTime' => [ 'shape' => 'Timestamp', ], 'DataSource' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'IndexName' => [ 'shape' => 'ResourceName', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ListPlaceIndexesResponseEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListPlaceIndexesResponseEntry', ], ], 'ListRouteCalculatorsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'ListRouteCalculatorsRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListRouteCalculatorsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListRouteCalculatorsResponse' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'Entries' => [ 'shape' => 'ListRouteCalculatorsResponseEntryList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListRouteCalculatorsResponseEntry' => [ 'type' => 'structure', 'required' => [ 'CalculatorName', 'CreateTime', 'DataSource', 'Description', 'PricingPlan', 'UpdateTime', ], 'members' => [ 'CalculatorName' => [ 'shape' => 'ResourceName', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'DataSource' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ListRouteCalculatorsResponseEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListRouteCalculatorsResponseEntry', ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'ListTrackerConsumersRequest' => [ 'type' => 'structure', 'required' => [ 'TrackerName', ], 'members' => [ 'MaxResults' => [ 'shape' => 'ListTrackerConsumersRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'Token', ], 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], ], ], 'ListTrackerConsumersRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListTrackerConsumersResponse' => [ 'type' => 'structure', 'required' => [ 'ConsumerArns', ], 'members' => [ 'ConsumerArns' => [ 'shape' => 'ArnList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListTrackersRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'ListTrackersRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListTrackersRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListTrackersResponse' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'Entries' => [ 'shape' => 'ListTrackersResponseEntryList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListTrackersResponseEntry' => [ 'type' => 'structure', 'required' => [ 'CreateTime', 'Description', 'PricingPlan', 'TrackerName', 'UpdateTime', ], 'members' => [ 'CreateTime' => [ 'shape' => 'Timestamp', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', ], 'PricingPlanDataSource' => [ 'shape' => 'String', ], 'TrackerName' => [ 'shape' => 'ResourceName', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ListTrackersResponseEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListTrackersResponseEntry', ], ], 'MapConfiguration' => [ 'type' => 'structure', 'required' => [ 'Style', ], 'members' => [ 'Style' => [ 'shape' => 'MapStyle', ], ], ], 'MapStyle' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[-._\\w]+$', ], 'Place' => [ 'type' => 'structure', 'required' => [ 'Geometry', ], 'members' => [ 'AddressNumber' => [ 'shape' => 'String', ], 'Country' => [ 'shape' => 'String', ], 'Geometry' => [ 'shape' => 'PlaceGeometry', ], 'Interpolated' => [ 'shape' => 'Boolean', ], 'Label' => [ 'shape' => 'String', ], 'Municipality' => [ 'shape' => 'String', ], 'Neighborhood' => [ 'shape' => 'String', ], 'PostalCode' => [ 'shape' => 'String', ], 'Region' => [ 'shape' => 'String', ], 'Street' => [ 'shape' => 'String', ], 'SubRegion' => [ 'shape' => 'String', ], 'TimeZone' => [ 'shape' => 'TimeZone', ], ], ], 'PlaceGeometry' => [ 'type' => 'structure', 'members' => [ 'Point' => [ 'shape' => 'Position', ], ], ], 'PlaceIndexSearchResultLimit' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'Position' => [ 'type' => 'list', 'member' => [ 'shape' => 'Double', ], 'max' => 2, 'min' => 2, 'sensitive' => true, ], 'PositionFiltering' => [ 'type' => 'string', 'enum' => [ 'TimeBased', 'DistanceBased', 'AccuracyBased', ], ], 'PositionalAccuracy' => [ 'type' => 'structure', 'required' => [ 'Horizontal', ], 'members' => [ 'Horizontal' => [ 'shape' => 'PositionalAccuracyHorizontalDouble', ], ], ], 'PositionalAccuracyHorizontalDouble' => [ 'type' => 'double', 'box' => true, 'max' => 10000, 'min' => 0, ], 'PricingPlan' => [ 'type' => 'string', 'enum' => [ 'RequestBasedUsage', 'MobileAssetTracking', 'MobileAssetManagement', ], ], 'PropertyMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'PropertyMapKeyString', ], 'value' => [ 'shape' => 'PropertyMapValueString', ], 'max' => 3, 'min' => 0, 'sensitive' => true, ], 'PropertyMapKeyString' => [ 'type' => 'string', 'max' => 20, 'min' => 1, ], 'PropertyMapValueString' => [ 'type' => 'string', 'max' => 40, 'min' => 1, ], 'PutGeofenceRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', 'GeofenceId', 'Geometry', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CollectionName', ], 'GeofenceId' => [ 'shape' => 'Id', 'location' => 'uri', 'locationName' => 'GeofenceId', ], 'Geometry' => [ 'shape' => 'GeofenceGeometry', ], ], ], 'PutGeofenceResponse' => [ 'type' => 'structure', 'required' => [ 'CreateTime', 'GeofenceId', 'UpdateTime', ], 'members' => [ 'CreateTime' => [ 'shape' => 'Timestamp', ], 'GeofenceId' => [ 'shape' => 'Id', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ResourceDescription' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, ], 'ResourceName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[-._\\w]+$', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'SearchForPositionResult' => [ 'type' => 'structure', 'required' => [ 'Distance', 'Place', ], 'members' => [ 'Distance' => [ 'shape' => 'SearchForPositionResultDistanceDouble', ], 'Place' => [ 'shape' => 'Place', ], ], ], 'SearchForPositionResultDistanceDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'SearchForPositionResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchForPositionResult', ], ], 'SearchForSuggestionsResult' => [ 'type' => 'structure', 'required' => [ 'Text', ], 'members' => [ 'Text' => [ 'shape' => 'String', ], ], ], 'SearchForSuggestionsResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchForSuggestionsResult', ], ], 'SearchForTextResult' => [ 'type' => 'structure', 'required' => [ 'Place', ], 'members' => [ 'Distance' => [ 'shape' => 'SearchForTextResultDistanceDouble', ], 'Place' => [ 'shape' => 'Place', ], 'Relevance' => [ 'shape' => 'SearchForTextResultRelevanceDouble', ], ], ], 'SearchForTextResultDistanceDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'SearchForTextResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SearchForTextResult', ], ], 'SearchForTextResultRelevanceDouble' => [ 'type' => 'double', 'box' => true, 'max' => 1, 'min' => 0, ], 'SearchPlaceIndexForPositionRequest' => [ 'type' => 'structure', 'required' => [ 'IndexName', 'Position', ], 'members' => [ 'IndexName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'IndexName', ], 'Language' => [ 'shape' => 'LanguageTag', ], 'MaxResults' => [ 'shape' => 'PlaceIndexSearchResultLimit', ], 'Position' => [ 'shape' => 'Position', ], ], ], 'SearchPlaceIndexForPositionResponse' => [ 'type' => 'structure', 'required' => [ 'Results', 'Summary', ], 'members' => [ 'Results' => [ 'shape' => 'SearchForPositionResultList', ], 'Summary' => [ 'shape' => 'SearchPlaceIndexForPositionSummary', ], ], ], 'SearchPlaceIndexForPositionSummary' => [ 'type' => 'structure', 'required' => [ 'DataSource', 'Position', ], 'members' => [ 'DataSource' => [ 'shape' => 'String', ], 'Language' => [ 'shape' => 'LanguageTag', ], 'MaxResults' => [ 'shape' => 'PlaceIndexSearchResultLimit', ], 'Position' => [ 'shape' => 'Position', ], ], ], 'SearchPlaceIndexForSuggestionsRequest' => [ 'type' => 'structure', 'required' => [ 'IndexName', 'Text', ], 'members' => [ 'BiasPosition' => [ 'shape' => 'Position', ], 'FilterBBox' => [ 'shape' => 'BoundingBox', ], 'FilterCountries' => [ 'shape' => 'CountryCodeList', ], 'IndexName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'IndexName', ], 'Language' => [ 'shape' => 'LanguageTag', ], 'MaxResults' => [ 'shape' => 'SearchPlaceIndexForSuggestionsRequestMaxResultsInteger', ], 'Text' => [ 'shape' => 'SyntheticSearchPlaceIndexForSuggestionsRequestString', ], ], ], 'SearchPlaceIndexForSuggestionsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 15, 'min' => 1, ], 'SearchPlaceIndexForSuggestionsResponse' => [ 'type' => 'structure', 'required' => [ 'Results', 'Summary', ], 'members' => [ 'Results' => [ 'shape' => 'SearchForSuggestionsResultList', ], 'Summary' => [ 'shape' => 'SearchPlaceIndexForSuggestionsSummary', ], ], ], 'SearchPlaceIndexForSuggestionsSummary' => [ 'type' => 'structure', 'required' => [ 'DataSource', 'Text', ], 'members' => [ 'BiasPosition' => [ 'shape' => 'Position', ], 'DataSource' => [ 'shape' => 'String', ], 'FilterBBox' => [ 'shape' => 'BoundingBox', ], 'FilterCountries' => [ 'shape' => 'CountryCodeList', ], 'Language' => [ 'shape' => 'LanguageTag', ], 'MaxResults' => [ 'shape' => 'Integer', ], 'Text' => [ 'shape' => 'SyntheticSearchPlaceIndexForSuggestionsSummaryString', ], ], ], 'SearchPlaceIndexForTextRequest' => [ 'type' => 'structure', 'required' => [ 'IndexName', 'Text', ], 'members' => [ 'BiasPosition' => [ 'shape' => 'Position', ], 'FilterBBox' => [ 'shape' => 'BoundingBox', ], 'FilterCountries' => [ 'shape' => 'CountryCodeList', ], 'IndexName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'IndexName', ], 'Language' => [ 'shape' => 'LanguageTag', ], 'MaxResults' => [ 'shape' => 'PlaceIndexSearchResultLimit', ], 'Text' => [ 'shape' => 'SyntheticSearchPlaceIndexForTextRequestString', ], ], ], 'SearchPlaceIndexForTextResponse' => [ 'type' => 'structure', 'required' => [ 'Results', 'Summary', ], 'members' => [ 'Results' => [ 'shape' => 'SearchForTextResultList', ], 'Summary' => [ 'shape' => 'SearchPlaceIndexForTextSummary', ], ], ], 'SearchPlaceIndexForTextSummary' => [ 'type' => 'structure', 'required' => [ 'DataSource', 'Text', ], 'members' => [ 'BiasPosition' => [ 'shape' => 'Position', ], 'DataSource' => [ 'shape' => 'String', ], 'FilterBBox' => [ 'shape' => 'BoundingBox', ], 'FilterCountries' => [ 'shape' => 'CountryCodeList', ], 'Language' => [ 'shape' => 'LanguageTag', ], 'MaxResults' => [ 'shape' => 'PlaceIndexSearchResultLimit', ], 'ResultBBox' => [ 'shape' => 'BoundingBox', ], 'Text' => [ 'shape' => 'SyntheticSearchPlaceIndexForTextSummaryString', ], ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'Step' => [ 'type' => 'structure', 'required' => [ 'Distance', 'DurationSeconds', 'EndPosition', 'StartPosition', ], 'members' => [ 'Distance' => [ 'shape' => 'StepDistanceDouble', ], 'DurationSeconds' => [ 'shape' => 'StepDurationSecondsDouble', ], 'EndPosition' => [ 'shape' => 'Position', ], 'GeometryOffset' => [ 'shape' => 'StepGeometryOffsetInteger', ], 'StartPosition' => [ 'shape' => 'Position', ], ], ], 'StepDistanceDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'StepDurationSecondsDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'StepGeometryOffsetInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'StepList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Step', ], ], 'String' => [ 'type' => 'string', ], 'SyntheticSearchPlaceIndexForSuggestionsRequestString' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'sensitive' => true, ], 'SyntheticSearchPlaceIndexForSuggestionsSummaryString' => [ 'type' => 'string', 'sensitive' => true, ], 'SyntheticSearchPlaceIndexForTextRequestString' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'sensitive' => true, ], 'SyntheticSearchPlaceIndexForTextSummaryString' => [ 'type' => 'string', 'sensitive' => true, ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z+-=._:/]+$', ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 50, 'min' => 1, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^[A-Za-z0-9 _=@:.+-/]*$', ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'TimeZone' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Offset' => [ 'shape' => 'Integer', ], ], ], 'Timestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'Token' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, ], 'TravelMode' => [ 'type' => 'string', 'enum' => [ 'Car', 'Truck', 'Walking', ], ], 'TruckDimensions' => [ 'type' => 'structure', 'members' => [ 'Height' => [ 'shape' => 'TruckDimensionsHeightDouble', ], 'Length' => [ 'shape' => 'TruckDimensionsLengthDouble', ], 'Unit' => [ 'shape' => 'DimensionUnit', ], 'Width' => [ 'shape' => 'TruckDimensionsWidthDouble', ], ], ], 'TruckDimensionsHeightDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'TruckDimensionsLengthDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'TruckDimensionsWidthDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'TruckWeight' => [ 'type' => 'structure', 'members' => [ 'Total' => [ 'shape' => 'TruckWeightTotalDouble', ], 'Unit' => [ 'shape' => 'VehicleWeightUnit', ], ], ], 'TruckWeightTotalDouble' => [ 'type' => 'double', 'box' => true, 'min' => 0, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeys', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateGeofenceCollectionRequest' => [ 'type' => 'structure', 'required' => [ 'CollectionName', ], 'members' => [ 'CollectionName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CollectionName', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', ], 'PricingPlanDataSource' => [ 'shape' => 'String', ], ], ], 'UpdateGeofenceCollectionResponse' => [ 'type' => 'structure', 'required' => [ 'CollectionArn', 'CollectionName', 'UpdateTime', ], 'members' => [ 'CollectionArn' => [ 'shape' => 'Arn', ], 'CollectionName' => [ 'shape' => 'ResourceName', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateMapRequest' => [ 'type' => 'structure', 'required' => [ 'MapName', ], 'members' => [ 'Description' => [ 'shape' => 'ResourceDescription', ], 'MapName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'MapName', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', ], ], ], 'UpdateMapResponse' => [ 'type' => 'structure', 'required' => [ 'MapArn', 'MapName', 'UpdateTime', ], 'members' => [ 'MapArn' => [ 'shape' => 'Arn', ], 'MapName' => [ 'shape' => 'ResourceName', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdatePlaceIndexRequest' => [ 'type' => 'structure', 'required' => [ 'IndexName', ], 'members' => [ 'DataSourceConfiguration' => [ 'shape' => 'DataSourceConfiguration', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'IndexName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'IndexName', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', ], ], ], 'UpdatePlaceIndexResponse' => [ 'type' => 'structure', 'required' => [ 'IndexArn', 'IndexName', 'UpdateTime', ], 'members' => [ 'IndexArn' => [ 'shape' => 'Arn', ], 'IndexName' => [ 'shape' => 'ResourceName', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateRouteCalculatorRequest' => [ 'type' => 'structure', 'required' => [ 'CalculatorName', ], 'members' => [ 'CalculatorName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'CalculatorName', ], 'Description' => [ 'shape' => 'ResourceDescription', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', ], ], ], 'UpdateRouteCalculatorResponse' => [ 'type' => 'structure', 'required' => [ 'CalculatorArn', 'CalculatorName', 'UpdateTime', ], 'members' => [ 'CalculatorArn' => [ 'shape' => 'Arn', ], 'CalculatorName' => [ 'shape' => 'ResourceName', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateTrackerRequest' => [ 'type' => 'structure', 'required' => [ 'TrackerName', ], 'members' => [ 'Description' => [ 'shape' => 'ResourceDescription', ], 'PositionFiltering' => [ 'shape' => 'PositionFiltering', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', ], 'PricingPlanDataSource' => [ 'shape' => 'String', ], 'TrackerName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'TrackerName', ], ], ], 'UpdateTrackerResponse' => [ 'type' => 'structure', 'required' => [ 'TrackerArn', 'TrackerName', 'UpdateTime', ], 'members' => [ 'TrackerArn' => [ 'shape' => 'Arn', ], 'TrackerName' => [ 'shape' => 'ResourceName', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'FieldList', 'Message', 'Reason', ], 'members' => [ 'FieldList' => [ 'shape' => 'ValidationExceptionFieldList', 'locationName' => 'fieldList', ], 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], 'Reason' => [ 'shape' => 'ValidationExceptionReason', 'locationName' => 'reason', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'Message', 'Name', ], 'members' => [ 'Message' => [ 'shape' => 'String', 'locationName' => 'message', ], 'Name' => [ 'shape' => 'String', 'locationName' => 'name', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'UnknownOperation', 'Missing', 'CannotParse', 'FieldValidationFailed', 'Other', ], ], 'VehicleWeightUnit' => [ 'type' => 'string', 'enum' => [ 'Kilograms', 'Pounds', ], ], ],];
