<?php
// This file was auto-generated from sdk-root/src/data/fis/2020-12-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-12-01', 'endpointPrefix' => 'fis', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'FIS', 'serviceFullName' => 'AWS Fault Injection Simulator', 'serviceId' => 'fis', 'signatureVersion' => 'v4', 'signingName' => 'fis', 'uid' => 'fis-2020-12-01', ], 'operations' => [ 'CreateExperimentTemplate' => [ 'name' => 'CreateExperimentTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/experimentTemplates', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateExperimentTemplateRequest', ], 'output' => [ 'shape' => 'CreateExperimentTemplateResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'DeleteExperimentTemplate' => [ 'name' => 'DeleteExperimentTemplate', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/experimentTemplates/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteExperimentTemplateRequest', ], 'output' => [ 'shape' => 'DeleteExperimentTemplateResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetAction' => [ 'name' => 'GetAction', 'http' => [ 'method' => 'GET', 'requestUri' => '/actions/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetActionRequest', ], 'output' => [ 'shape' => 'GetActionResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetExperiment' => [ 'name' => 'GetExperiment', 'http' => [ 'method' => 'GET', 'requestUri' => '/experiments/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetExperimentRequest', ], 'output' => [ 'shape' => 'GetExperimentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetExperimentTemplate' => [ 'name' => 'GetExperimentTemplate', 'http' => [ 'method' => 'GET', 'requestUri' => '/experimentTemplates/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetExperimentTemplateRequest', ], 'output' => [ 'shape' => 'GetExperimentTemplateResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListActions' => [ 'name' => 'ListActions', 'http' => [ 'method' => 'GET', 'requestUri' => '/actions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListActionsRequest', ], 'output' => [ 'shape' => 'ListActionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], ], ], 'ListExperimentTemplates' => [ 'name' => 'ListExperimentTemplates', 'http' => [ 'method' => 'GET', 'requestUri' => '/experimentTemplates', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListExperimentTemplatesRequest', ], 'output' => [ 'shape' => 'ListExperimentTemplatesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], ], ], 'ListExperiments' => [ 'name' => 'ListExperiments', 'http' => [ 'method' => 'GET', 'requestUri' => '/experiments', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListExperimentsRequest', ], 'output' => [ 'shape' => 'ListExperimentsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], ], 'StartExperiment' => [ 'name' => 'StartExperiment', 'http' => [ 'method' => 'POST', 'requestUri' => '/experiments', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartExperimentRequest', ], 'output' => [ 'shape' => 'StartExperimentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'StopExperiment' => [ 'name' => 'StopExperiment', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/experiments/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopExperimentRequest', ], 'output' => [ 'shape' => 'StopExperimentResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], ], 'UpdateExperimentTemplate' => [ 'name' => 'UpdateExperimentTemplate', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/experimentTemplates/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateExperimentTemplateRequest', ], 'output' => [ 'shape' => 'UpdateExperimentTemplateResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], ], 'shapes' => [ 'Action' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ActionId', ], 'description' => [ 'shape' => 'ActionDescription', ], 'parameters' => [ 'shape' => 'ActionParameterMap', ], 'targets' => [ 'shape' => 'ActionTargetMap', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ActionDescription' => [ 'type' => 'string', 'max' => 512, 'pattern' => '[\\s\\S]+', ], 'ActionId' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[\\S]+', ], 'ActionParameter' => [ 'type' => 'structure', 'members' => [ 'description' => [ 'shape' => 'ActionParameterDescription', ], 'required' => [ 'shape' => 'ActionParameterRequired', 'box' => true, ], ], ], 'ActionParameterDescription' => [ 'type' => 'string', 'max' => 512, 'pattern' => '[\\s\\S]+', ], 'ActionParameterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ActionParameterName', ], 'value' => [ 'shape' => 'ActionParameter', ], ], 'ActionParameterName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ActionParameterRequired' => [ 'type' => 'boolean', ], 'ActionSummary' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ActionId', ], 'description' => [ 'shape' => 'ActionDescription', ], 'targets' => [ 'shape' => 'ActionTargetMap', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ActionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ActionSummary', ], ], 'ActionTarget' => [ 'type' => 'structure', 'members' => [ 'resourceType' => [ 'shape' => 'TargetResourceType', ], ], ], 'ActionTargetMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ActionTargetName', ], 'value' => [ 'shape' => 'ActionTarget', ], ], 'ActionTargetName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ClientToken' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[\\S]+', ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'CreateExperimentTemplateActionInput' => [ 'type' => 'structure', 'required' => [ 'actionId', ], 'members' => [ 'actionId' => [ 'shape' => 'ActionId', ], 'description' => [ 'shape' => 'ExperimentTemplateActionDescription', ], 'parameters' => [ 'shape' => 'ExperimentTemplateActionParameterMap', ], 'targets' => [ 'shape' => 'ExperimentTemplateActionTargetMap', ], 'startAfter' => [ 'shape' => 'ExperimentTemplateActionStartAfterList', ], ], ], 'CreateExperimentTemplateActionInputMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExperimentTemplateActionName', ], 'value' => [ 'shape' => 'CreateExperimentTemplateActionInput', ], ], 'CreateExperimentTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'clientToken', 'description', 'stopConditions', 'actions', 'roleArn', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'description' => [ 'shape' => 'ExperimentTemplateDescription', ], 'stopConditions' => [ 'shape' => 'CreateExperimentTemplateStopConditionInputList', ], 'targets' => [ 'shape' => 'CreateExperimentTemplateTargetInputMap', ], 'actions' => [ 'shape' => 'CreateExperimentTemplateActionInputMap', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateExperimentTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'experimentTemplate' => [ 'shape' => 'ExperimentTemplate', ], ], ], 'CreateExperimentTemplateStopConditionInput' => [ 'type' => 'structure', 'required' => [ 'source', ], 'members' => [ 'source' => [ 'shape' => 'StopConditionSource', ], 'value' => [ 'shape' => 'StopConditionValue', ], ], ], 'CreateExperimentTemplateStopConditionInputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CreateExperimentTemplateStopConditionInput', ], ], 'CreateExperimentTemplateTargetInput' => [ 'type' => 'structure', 'required' => [ 'resourceType', 'selectionMode', ], 'members' => [ 'resourceType' => [ 'shape' => 'ResourceType', ], 'resourceArns' => [ 'shape' => 'ResourceArnList', ], 'resourceTags' => [ 'shape' => 'TagMap', ], 'filters' => [ 'shape' => 'ExperimentTemplateTargetFilterInputList', ], 'selectionMode' => [ 'shape' => 'ExperimentTemplateTargetSelectionMode', ], ], ], 'CreateExperimentTemplateTargetInputMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExperimentTemplateTargetName', ], 'value' => [ 'shape' => 'CreateExperimentTemplateTargetInput', ], ], 'CreationTime' => [ 'type' => 'timestamp', ], 'DeleteExperimentTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ExperimentTemplateId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'DeleteExperimentTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'experimentTemplate' => [ 'shape' => 'ExperimentTemplate', ], ], ], 'ExceptionMessage' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '[\\s\\S]+', ], 'Experiment' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ExperimentId', ], 'experimentTemplateId' => [ 'shape' => 'ExperimentTemplateId', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'state' => [ 'shape' => 'ExperimentState', ], 'targets' => [ 'shape' => 'ExperimentTargetMap', ], 'actions' => [ 'shape' => 'ExperimentActionMap', ], 'stopConditions' => [ 'shape' => 'ExperimentStopConditionList', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'startTime' => [ 'shape' => 'ExperimentStartTime', ], 'endTime' => [ 'shape' => 'ExperimentEndTime', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ExperimentAction' => [ 'type' => 'structure', 'members' => [ 'actionId' => [ 'shape' => 'ActionId', ], 'description' => [ 'shape' => 'ExperimentActionDescription', ], 'parameters' => [ 'shape' => 'ExperimentActionParameterMap', ], 'targets' => [ 'shape' => 'ExperimentActionTargetMap', ], 'startAfter' => [ 'shape' => 'ExperimentActionStartAfterList', ], 'state' => [ 'shape' => 'ExperimentActionState', ], ], ], 'ExperimentActionDescription' => [ 'type' => 'string', 'max' => 512, 'pattern' => '[\\s\\S]+', ], 'ExperimentActionMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExperimentActionName', ], 'value' => [ 'shape' => 'ExperimentAction', ], ], 'ExperimentActionName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ExperimentActionParameter' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '[\\S]+', ], 'ExperimentActionParameterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExperimentActionParameterName', ], 'value' => [ 'shape' => 'ExperimentActionParameter', ], ], 'ExperimentActionParameterName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ExperimentActionStartAfter' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ExperimentActionStartAfterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperimentActionStartAfter', ], ], 'ExperimentActionState' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'ExperimentActionStatus', ], 'reason' => [ 'shape' => 'ExperimentActionStatusReason', ], ], ], 'ExperimentActionStatus' => [ 'type' => 'string', 'enum' => [ 'pending', 'initiating', 'running', 'completed', 'cancelled', 'stopping', 'stopped', 'failed', ], ], 'ExperimentActionStatusReason' => [ 'type' => 'string', 'max' => 512, 'pattern' => '[\\s\\S]+', ], 'ExperimentActionTargetMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExperimentActionTargetName', ], 'value' => [ 'shape' => 'ExperimentTargetName', ], ], 'ExperimentActionTargetName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ExperimentEndTime' => [ 'type' => 'timestamp', ], 'ExperimentId' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ExperimentStartTime' => [ 'type' => 'timestamp', ], 'ExperimentState' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'ExperimentStatus', ], 'reason' => [ 'shape' => 'ExperimentStatusReason', ], ], ], 'ExperimentStatus' => [ 'type' => 'string', 'enum' => [ 'pending', 'initiating', 'running', 'completed', 'stopping', 'stopped', 'failed', ], ], 'ExperimentStatusReason' => [ 'type' => 'string', 'max' => 512, 'pattern' => '[\\s\\S]+', ], 'ExperimentStopCondition' => [ 'type' => 'structure', 'members' => [ 'source' => [ 'shape' => 'StopConditionSource', ], 'value' => [ 'shape' => 'StopConditionValue', ], ], ], 'ExperimentStopConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperimentStopCondition', ], ], 'ExperimentSummary' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ExperimentId', ], 'experimentTemplateId' => [ 'shape' => 'ExperimentTemplateId', ], 'state' => [ 'shape' => 'ExperimentState', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ExperimentSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperimentSummary', ], ], 'ExperimentTarget' => [ 'type' => 'structure', 'members' => [ 'resourceType' => [ 'shape' => 'ResourceType', ], 'resourceArns' => [ 'shape' => 'ResourceArnList', ], 'resourceTags' => [ 'shape' => 'TagMap', ], 'filters' => [ 'shape' => 'ExperimentTargetFilterList', ], 'selectionMode' => [ 'shape' => 'ExperimentTargetSelectionMode', ], ], ], 'ExperimentTargetFilter' => [ 'type' => 'structure', 'members' => [ 'path' => [ 'shape' => 'ExperimentTargetFilterPath', ], 'values' => [ 'shape' => 'ExperimentTargetFilterValues', ], ], ], 'ExperimentTargetFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperimentTargetFilter', ], ], 'ExperimentTargetFilterPath' => [ 'type' => 'string', 'max' => 256, 'pattern' => '[\\S]+', ], 'ExperimentTargetFilterValue' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[\\S]+', ], 'ExperimentTargetFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperimentTargetFilterValue', ], ], 'ExperimentTargetMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExperimentTargetName', ], 'value' => [ 'shape' => 'ExperimentTarget', ], ], 'ExperimentTargetName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ExperimentTargetSelectionMode' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ExperimentTemplate' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ExperimentTemplateId', ], 'description' => [ 'shape' => 'ExperimentTemplateDescription', ], 'targets' => [ 'shape' => 'ExperimentTemplateTargetMap', ], 'actions' => [ 'shape' => 'ExperimentTemplateActionMap', ], 'stopConditions' => [ 'shape' => 'ExperimentTemplateStopConditionList', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'lastUpdateTime' => [ 'shape' => 'LastUpdateTime', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ExperimentTemplateAction' => [ 'type' => 'structure', 'members' => [ 'actionId' => [ 'shape' => 'ActionId', ], 'description' => [ 'shape' => 'ExperimentTemplateActionDescription', ], 'parameters' => [ 'shape' => 'ExperimentTemplateActionParameterMap', ], 'targets' => [ 'shape' => 'ExperimentTemplateActionTargetMap', ], 'startAfter' => [ 'shape' => 'ExperimentTemplateActionStartAfterList', ], ], ], 'ExperimentTemplateActionDescription' => [ 'type' => 'string', 'max' => 512, 'pattern' => '[\\s\\S]+', ], 'ExperimentTemplateActionMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExperimentTemplateActionName', ], 'value' => [ 'shape' => 'ExperimentTemplateAction', ], ], 'ExperimentTemplateActionName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ExperimentTemplateActionParameter' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '[\\S]+', ], 'ExperimentTemplateActionParameterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExperimentTemplateActionParameterName', ], 'value' => [ 'shape' => 'ExperimentTemplateActionParameter', ], ], 'ExperimentTemplateActionParameterName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ExperimentTemplateActionStartAfter' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ExperimentTemplateActionStartAfterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperimentTemplateActionStartAfter', ], ], 'ExperimentTemplateActionTargetMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExperimentTemplateActionTargetName', ], 'value' => [ 'shape' => 'ExperimentTemplateTargetName', ], ], 'ExperimentTemplateActionTargetName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ExperimentTemplateDescription' => [ 'type' => 'string', 'max' => 512, 'pattern' => '[\\s\\S]+', ], 'ExperimentTemplateId' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ExperimentTemplateStopCondition' => [ 'type' => 'structure', 'members' => [ 'source' => [ 'shape' => 'StopConditionSource', ], 'value' => [ 'shape' => 'StopConditionValue', ], ], ], 'ExperimentTemplateStopConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperimentTemplateStopCondition', ], ], 'ExperimentTemplateSummary' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'ExperimentTemplateId', ], 'description' => [ 'shape' => 'ExperimentTemplateDescription', ], 'creationTime' => [ 'shape' => 'CreationTime', ], 'lastUpdateTime' => [ 'shape' => 'LastUpdateTime', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ExperimentTemplateSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperimentTemplateSummary', ], ], 'ExperimentTemplateTarget' => [ 'type' => 'structure', 'members' => [ 'resourceType' => [ 'shape' => 'ResourceType', ], 'resourceArns' => [ 'shape' => 'ResourceArnList', ], 'resourceTags' => [ 'shape' => 'TagMap', ], 'filters' => [ 'shape' => 'ExperimentTemplateTargetFilterList', ], 'selectionMode' => [ 'shape' => 'ExperimentTemplateTargetSelectionMode', ], ], ], 'ExperimentTemplateTargetFilter' => [ 'type' => 'structure', 'members' => [ 'path' => [ 'shape' => 'ExperimentTemplateTargetFilterPath', ], 'values' => [ 'shape' => 'ExperimentTemplateTargetFilterValues', ], ], ], 'ExperimentTemplateTargetFilterInputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperimentTemplateTargetInputFilter', ], ], 'ExperimentTemplateTargetFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperimentTemplateTargetFilter', ], ], 'ExperimentTemplateTargetFilterPath' => [ 'type' => 'string', 'max' => 256, 'pattern' => '[\\S]+', ], 'ExperimentTemplateTargetFilterValue' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[\\S]+', ], 'ExperimentTemplateTargetFilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExperimentTemplateTargetFilterValue', ], ], 'ExperimentTemplateTargetInputFilter' => [ 'type' => 'structure', 'required' => [ 'path', 'values', ], 'members' => [ 'path' => [ 'shape' => 'ExperimentTemplateTargetFilterPath', ], 'values' => [ 'shape' => 'ExperimentTemplateTargetFilterValues', ], ], ], 'ExperimentTemplateTargetMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExperimentTemplateTargetName', ], 'value' => [ 'shape' => 'ExperimentTemplateTarget', ], ], 'ExperimentTemplateTargetName' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'ExperimentTemplateTargetSelectionMode' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'GetActionRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ActionId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'GetActionResponse' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'Action', ], ], ], 'GetExperimentRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ExperimentId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'GetExperimentResponse' => [ 'type' => 'structure', 'members' => [ 'experiment' => [ 'shape' => 'Experiment', ], ], ], 'GetExperimentTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ExperimentTemplateId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'GetExperimentTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'experimentTemplate' => [ 'shape' => 'ExperimentTemplate', ], ], ], 'LastUpdateTime' => [ 'type' => 'timestamp', ], 'ListActionsMaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'ListActionsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListActionsMaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListActionsResponse' => [ 'type' => 'structure', 'members' => [ 'actions' => [ 'shape' => 'ActionSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListExperimentTemplatesMaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'ListExperimentTemplatesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListExperimentTemplatesMaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListExperimentTemplatesResponse' => [ 'type' => 'structure', 'members' => [ 'experimentTemplates' => [ 'shape' => 'ExperimentTemplateSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListExperimentsMaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'ListExperimentsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListExperimentsMaxResults', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListExperimentsResponse' => [ 'type' => 'structure', 'members' => [ 'experiments' => [ 'shape' => 'ExperimentSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'NextToken' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[\\S]+', ], 'ResourceArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '[\\S]+', ], 'ResourceArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceArn', ], 'max' => 5, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ResourceType' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'RoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '[\\S]+', ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 402, ], 'exception' => true, ], 'StartExperimentRequest' => [ 'type' => 'structure', 'required' => [ 'clientToken', 'experimentTemplateId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'experimentTemplateId' => [ 'shape' => 'ExperimentTemplateId', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'StartExperimentResponse' => [ 'type' => 'structure', 'members' => [ 'experiment' => [ 'shape' => 'Experiment', ], ], ], 'StopConditionSource' => [ 'type' => 'string', 'max' => 64, 'pattern' => '[\\S]+', ], 'StopConditionValue' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '[\\s\\S]+', ], 'StopExperimentRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ExperimentId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'StopExperimentResponse' => [ 'type' => 'structure', 'members' => [ 'experiment' => [ 'shape' => 'Experiment', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[\\s\\S]+', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'pattern' => '[\\s\\S]*', ], 'TargetResourceType' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[\\S]+', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateExperimentTemplateActionInputItem' => [ 'type' => 'structure', 'members' => [ 'actionId' => [ 'shape' => 'ActionId', ], 'description' => [ 'shape' => 'ExperimentTemplateActionDescription', ], 'parameters' => [ 'shape' => 'ExperimentTemplateActionParameterMap', ], 'targets' => [ 'shape' => 'ExperimentTemplateActionTargetMap', ], 'startAfter' => [ 'shape' => 'ExperimentTemplateActionStartAfterList', ], ], ], 'UpdateExperimentTemplateActionInputMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExperimentTemplateActionName', ], 'value' => [ 'shape' => 'UpdateExperimentTemplateActionInputItem', ], ], 'UpdateExperimentTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'ExperimentTemplateId', 'location' => 'uri', 'locationName' => 'id', ], 'description' => [ 'shape' => 'ExperimentTemplateDescription', ], 'stopConditions' => [ 'shape' => 'UpdateExperimentTemplateStopConditionInputList', ], 'targets' => [ 'shape' => 'UpdateExperimentTemplateTargetInputMap', ], 'actions' => [ 'shape' => 'UpdateExperimentTemplateActionInputMap', ], 'roleArn' => [ 'shape' => 'RoleArn', ], ], ], 'UpdateExperimentTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'experimentTemplate' => [ 'shape' => 'ExperimentTemplate', ], ], ], 'UpdateExperimentTemplateStopConditionInput' => [ 'type' => 'structure', 'required' => [ 'source', ], 'members' => [ 'source' => [ 'shape' => 'StopConditionSource', ], 'value' => [ 'shape' => 'StopConditionValue', ], ], ], 'UpdateExperimentTemplateStopConditionInputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpdateExperimentTemplateStopConditionInput', ], ], 'UpdateExperimentTemplateTargetInput' => [ 'type' => 'structure', 'required' => [ 'resourceType', 'selectionMode', ], 'members' => [ 'resourceType' => [ 'shape' => 'ResourceType', ], 'resourceArns' => [ 'shape' => 'ResourceArnList', ], 'resourceTags' => [ 'shape' => 'TagMap', ], 'filters' => [ 'shape' => 'ExperimentTemplateTargetFilterInputList', ], 'selectionMode' => [ 'shape' => 'ExperimentTemplateTargetSelectionMode', ], ], ], 'UpdateExperimentTemplateTargetInputMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ExperimentTemplateTargetName', ], 'value' => [ 'shape' => 'UpdateExperimentTemplateTargetInput', ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], ],];
