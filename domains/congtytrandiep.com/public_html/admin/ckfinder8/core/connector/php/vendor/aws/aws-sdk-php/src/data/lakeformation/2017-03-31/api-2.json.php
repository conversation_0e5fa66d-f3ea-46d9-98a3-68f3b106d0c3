<?php
// This file was auto-generated from sdk-root/src/data/lakeformation/2017-03-31/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-03-31', 'endpointPrefix' => 'lakeformation', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'AWS Lake Formation', 'serviceId' => 'LakeFormation', 'signatureVersion' => 'v4', 'signingName' => 'lakeformation', 'uid' => 'lakeformation-2017-03-31', ], 'operations' => [ 'AddLFTagsToResource' => [ 'name' => 'AddLFTagsToResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/AddLFTagsToResource', ], 'input' => [ 'shape' => 'AddLFTagsToResourceRequest', ], 'output' => [ 'shape' => 'AddLFTagsToResourceResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'BatchGrantPermissions' => [ 'name' => 'BatchGrantPermissions', 'http' => [ 'method' => 'POST', 'requestUri' => '/BatchGrantPermissions', ], 'input' => [ 'shape' => 'BatchGrantPermissionsRequest', ], 'output' => [ 'shape' => 'BatchGrantPermissionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'BatchRevokePermissions' => [ 'name' => 'BatchRevokePermissions', 'http' => [ 'method' => 'POST', 'requestUri' => '/BatchRevokePermissions', ], 'input' => [ 'shape' => 'BatchRevokePermissionsRequest', ], 'output' => [ 'shape' => 'BatchRevokePermissionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'CancelTransaction' => [ 'name' => 'CancelTransaction', 'http' => [ 'method' => 'POST', 'requestUri' => '/CancelTransaction', ], 'input' => [ 'shape' => 'CancelTransactionRequest', ], 'output' => [ 'shape' => 'CancelTransactionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'TransactionCommittedException', ], [ 'shape' => 'TransactionCommitInProgressException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'CommitTransaction' => [ 'name' => 'CommitTransaction', 'http' => [ 'method' => 'POST', 'requestUri' => '/CommitTransaction', ], 'input' => [ 'shape' => 'CommitTransactionRequest', ], 'output' => [ 'shape' => 'CommitTransactionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'TransactionCanceledException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'CreateDataCellsFilter' => [ 'name' => 'CreateDataCellsFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateDataCellsFilter', ], 'input' => [ 'shape' => 'CreateDataCellsFilterRequest', ], 'output' => [ 'shape' => 'CreateDataCellsFilterResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateLFTag' => [ 'name' => 'CreateLFTag', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateLFTag', ], 'input' => [ 'shape' => 'CreateLFTagRequest', ], 'output' => [ 'shape' => 'CreateLFTagResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteDataCellsFilter' => [ 'name' => 'DeleteDataCellsFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteDataCellsFilter', ], 'input' => [ 'shape' => 'DeleteDataCellsFilterRequest', ], 'output' => [ 'shape' => 'DeleteDataCellsFilterResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteLFTag' => [ 'name' => 'DeleteLFTag', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteLFTag', ], 'input' => [ 'shape' => 'DeleteLFTagRequest', ], 'output' => [ 'shape' => 'DeleteLFTagResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteObjectsOnCancel' => [ 'name' => 'DeleteObjectsOnCancel', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteObjectsOnCancel', ], 'input' => [ 'shape' => 'DeleteObjectsOnCancelRequest', ], 'output' => [ 'shape' => 'DeleteObjectsOnCancelResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'TransactionCommittedException', ], [ 'shape' => 'TransactionCanceledException', ], [ 'shape' => 'ResourceNotReadyException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DeregisterResource' => [ 'name' => 'DeregisterResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeregisterResource', ], 'input' => [ 'shape' => 'DeregisterResourceRequest', ], 'output' => [ 'shape' => 'DeregisterResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'EntityNotFoundException', ], ], ], 'DescribeResource' => [ 'name' => 'DescribeResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/DescribeResource', ], 'input' => [ 'shape' => 'DescribeResourceRequest', ], 'output' => [ 'shape' => 'DescribeResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'EntityNotFoundException', ], ], ], 'DescribeTransaction' => [ 'name' => 'DescribeTransaction', 'http' => [ 'method' => 'POST', 'requestUri' => '/DescribeTransaction', ], 'input' => [ 'shape' => 'DescribeTransactionRequest', ], 'output' => [ 'shape' => 'DescribeTransactionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'ExtendTransaction' => [ 'name' => 'ExtendTransaction', 'http' => [ 'method' => 'POST', 'requestUri' => '/ExtendTransaction', ], 'input' => [ 'shape' => 'ExtendTransactionRequest', ], 'output' => [ 'shape' => 'ExtendTransactionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'TransactionCommittedException', ], [ 'shape' => 'TransactionCanceledException', ], [ 'shape' => 'TransactionCommitInProgressException', ], ], ], 'GetDataLakeSettings' => [ 'name' => 'GetDataLakeSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetDataLakeSettings', ], 'input' => [ 'shape' => 'GetDataLakeSettingsRequest', ], 'output' => [ 'shape' => 'GetDataLakeSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], ], ], 'GetEffectivePermissionsForPath' => [ 'name' => 'GetEffectivePermissionsForPath', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetEffectivePermissionsForPath', ], 'input' => [ 'shape' => 'GetEffectivePermissionsForPathRequest', ], 'output' => [ 'shape' => 'GetEffectivePermissionsForPathResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetLFTag' => [ 'name' => 'GetLFTag', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetLFTag', ], 'input' => [ 'shape' => 'GetLFTagRequest', ], 'output' => [ 'shape' => 'GetLFTagResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetQueryState' => [ 'name' => 'GetQueryState', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetQueryState', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetQueryStateRequest', ], 'output' => [ 'shape' => 'GetQueryStateResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], ], 'endpoint' => [ 'hostPrefix' => 'query-', ], ], 'GetQueryStatistics' => [ 'name' => 'GetQueryStatistics', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetQueryStatistics', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetQueryStatisticsRequest', ], 'output' => [ 'shape' => 'GetQueryStatisticsResponse', ], 'errors' => [ [ 'shape' => 'StatisticsNotReadyYetException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ExpiredException', ], [ 'shape' => 'ThrottledException', ], ], 'endpoint' => [ 'hostPrefix' => 'query-', ], ], 'GetResourceLFTags' => [ 'name' => 'GetResourceLFTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetResourceLFTags', ], 'input' => [ 'shape' => 'GetResourceLFTagsRequest', ], 'output' => [ 'shape' => 'GetResourceLFTagsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetTableObjects' => [ 'name' => 'GetTableObjects', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetTableObjects', ], 'input' => [ 'shape' => 'GetTableObjectsRequest', ], 'output' => [ 'shape' => 'GetTableObjectsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'TransactionCommittedException', ], [ 'shape' => 'TransactionCanceledException', ], [ 'shape' => 'ResourceNotReadyException', ], ], ], 'GetWorkUnitResults' => [ 'name' => 'GetWorkUnitResults', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetWorkUnitResults', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetWorkUnitResultsRequest', ], 'output' => [ 'shape' => 'GetWorkUnitResultsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ExpiredException', ], [ 'shape' => 'ThrottledException', ], ], 'endpoint' => [ 'hostPrefix' => 'data-', ], ], 'GetWorkUnits' => [ 'name' => 'GetWorkUnits', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetWorkUnits', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetWorkUnitsRequest', ], 'output' => [ 'shape' => 'GetWorkUnitsResponse', ], 'errors' => [ [ 'shape' => 'WorkUnitsNotReadyYetException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ExpiredException', ], ], 'endpoint' => [ 'hostPrefix' => 'query-', ], ], 'GrantPermissions' => [ 'name' => 'GrantPermissions', 'http' => [ 'method' => 'POST', 'requestUri' => '/GrantPermissions', ], 'input' => [ 'shape' => 'GrantPermissionsRequest', ], 'output' => [ 'shape' => 'GrantPermissionsResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'ListDataCellsFilter' => [ 'name' => 'ListDataCellsFilter', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListDataCellsFilter', ], 'input' => [ 'shape' => 'ListDataCellsFilterRequest', ], 'output' => [ 'shape' => 'ListDataCellsFilterResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListLFTags' => [ 'name' => 'ListLFTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListLFTags', ], 'input' => [ 'shape' => 'ListLFTagsRequest', ], 'output' => [ 'shape' => 'ListLFTagsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListPermissions' => [ 'name' => 'ListPermissions', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListPermissions', ], 'input' => [ 'shape' => 'ListPermissionsRequest', ], 'output' => [ 'shape' => 'ListPermissionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListResources' => [ 'name' => 'ListResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListResources', ], 'input' => [ 'shape' => 'ListResourcesRequest', ], 'output' => [ 'shape' => 'ListResourcesResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'ListTableStorageOptimizers' => [ 'name' => 'ListTableStorageOptimizers', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListTableStorageOptimizers', ], 'input' => [ 'shape' => 'ListTableStorageOptimizersRequest', ], 'output' => [ 'shape' => 'ListTableStorageOptimizersResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListTransactions' => [ 'name' => 'ListTransactions', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListTransactions', ], 'input' => [ 'shape' => 'ListTransactionsRequest', ], 'output' => [ 'shape' => 'ListTransactionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'PutDataLakeSettings' => [ 'name' => 'PutDataLakeSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/PutDataLakeSettings', ], 'input' => [ 'shape' => 'PutDataLakeSettingsRequest', ], 'output' => [ 'shape' => 'PutDataLakeSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'RegisterResource' => [ 'name' => 'RegisterResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/RegisterResource', ], 'input' => [ 'shape' => 'RegisterResourceRequest', ], 'output' => [ 'shape' => 'RegisterResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'RemoveLFTagsFromResource' => [ 'name' => 'RemoveLFTagsFromResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/RemoveLFTagsFromResource', ], 'input' => [ 'shape' => 'RemoveLFTagsFromResourceRequest', ], 'output' => [ 'shape' => 'RemoveLFTagsFromResourceResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'RevokePermissions' => [ 'name' => 'RevokePermissions', 'http' => [ 'method' => 'POST', 'requestUri' => '/RevokePermissions', ], 'input' => [ 'shape' => 'RevokePermissionsRequest', ], 'output' => [ 'shape' => 'RevokePermissionsResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'SearchDatabasesByLFTags' => [ 'name' => 'SearchDatabasesByLFTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/SearchDatabasesByLFTags', ], 'input' => [ 'shape' => 'SearchDatabasesByLFTagsRequest', ], 'output' => [ 'shape' => 'SearchDatabasesByLFTagsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'SearchTablesByLFTags' => [ 'name' => 'SearchTablesByLFTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/SearchTablesByLFTags', ], 'input' => [ 'shape' => 'SearchTablesByLFTagsRequest', ], 'output' => [ 'shape' => 'SearchTablesByLFTagsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'StartQueryPlanning' => [ 'name' => 'StartQueryPlanning', 'http' => [ 'method' => 'POST', 'requestUri' => '/StartQueryPlanning', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartQueryPlanningRequest', ], 'output' => [ 'shape' => 'StartQueryPlanningResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottledException', ], ], 'endpoint' => [ 'hostPrefix' => 'query-', ], ], 'StartTransaction' => [ 'name' => 'StartTransaction', 'http' => [ 'method' => 'POST', 'requestUri' => '/StartTransaction', ], 'input' => [ 'shape' => 'StartTransactionRequest', ], 'output' => [ 'shape' => 'StartTransactionResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'UpdateLFTag' => [ 'name' => 'UpdateLFTag', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateLFTag', ], 'input' => [ 'shape' => 'UpdateLFTagRequest', ], 'output' => [ 'shape' => 'UpdateLFTagResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateResource' => [ 'name' => 'UpdateResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateResource', ], 'input' => [ 'shape' => 'UpdateResourceRequest', ], 'output' => [ 'shape' => 'UpdateResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'EntityNotFoundException', ], ], ], 'UpdateTableObjects' => [ 'name' => 'UpdateTableObjects', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateTableObjects', ], 'input' => [ 'shape' => 'UpdateTableObjectsRequest', ], 'output' => [ 'shape' => 'UpdateTableObjectsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'TransactionCommittedException', ], [ 'shape' => 'TransactionCanceledException', ], [ 'shape' => 'TransactionCommitInProgressException', ], [ 'shape' => 'ResourceNotReadyException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'UpdateTableStorageOptimizer' => [ 'name' => 'UpdateTableStorageOptimizer', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateTableStorageOptimizer', ], 'input' => [ 'shape' => 'UpdateTableStorageOptimizerRequest', ], 'output' => [ 'shape' => 'UpdateTableStorageOptimizerResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AddLFTagsToResourceRequest' => [ 'type' => 'structure', 'required' => [ 'Resource', 'LFTags', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Resource' => [ 'shape' => 'Resource', ], 'LFTags' => [ 'shape' => 'LFTagsList', ], ], ], 'AddLFTagsToResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Failures' => [ 'shape' => 'LFTagErrors', ], ], ], 'AddObjectInput' => [ 'type' => 'structure', 'required' => [ 'Uri', 'ETag', 'Size', ], 'members' => [ 'Uri' => [ 'shape' => 'URI', ], 'ETag' => [ 'shape' => 'ETagString', ], 'Size' => [ 'shape' => 'ObjectSize', ], 'PartitionValues' => [ 'shape' => 'PartitionValuesList', ], ], ], 'AllRowsWildcard' => [ 'type' => 'structure', 'members' => [], ], 'AlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'BatchGrantPermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Entries' => [ 'shape' => 'BatchPermissionsRequestEntryList', ], ], ], 'BatchGrantPermissionsResponse' => [ 'type' => 'structure', 'members' => [ 'Failures' => [ 'shape' => 'BatchPermissionsFailureList', ], ], ], 'BatchPermissionsFailureEntry' => [ 'type' => 'structure', 'members' => [ 'RequestEntry' => [ 'shape' => 'BatchPermissionsRequestEntry', ], 'Error' => [ 'shape' => 'ErrorDetail', ], ], ], 'BatchPermissionsFailureList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchPermissionsFailureEntry', ], ], 'BatchPermissionsRequestEntry' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'Identifier', ], 'Principal' => [ 'shape' => 'DataLakePrincipal', ], 'Resource' => [ 'shape' => 'Resource', ], 'Permissions' => [ 'shape' => 'PermissionList', ], 'PermissionsWithGrantOption' => [ 'shape' => 'PermissionList', ], ], ], 'BatchPermissionsRequestEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchPermissionsRequestEntry', ], ], 'BatchRevokePermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'Entries', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Entries' => [ 'shape' => 'BatchPermissionsRequestEntryList', ], ], ], 'BatchRevokePermissionsResponse' => [ 'type' => 'structure', 'members' => [ 'Failures' => [ 'shape' => 'BatchPermissionsFailureList', ], ], ], 'BooleanNullable' => [ 'type' => 'boolean', ], 'CancelTransactionRequest' => [ 'type' => 'structure', 'required' => [ 'TransactionId', ], 'members' => [ 'TransactionId' => [ 'shape' => 'TransactionIdString', ], ], ], 'CancelTransactionResponse' => [ 'type' => 'structure', 'members' => [], ], 'CatalogIdString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'CatalogResource' => [ 'type' => 'structure', 'members' => [], ], 'ColumnLFTag' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'LFTags' => [ 'shape' => 'LFTagsList', ], ], ], 'ColumnLFTagsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnLFTag', ], ], 'ColumnNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], ], 'ColumnWildcard' => [ 'type' => 'structure', 'members' => [ 'ExcludedColumnNames' => [ 'shape' => 'ColumnNames', ], ], ], 'CommitTransactionRequest' => [ 'type' => 'structure', 'required' => [ 'TransactionId', ], 'members' => [ 'TransactionId' => [ 'shape' => 'TransactionIdString', ], ], ], 'CommitTransactionResponse' => [ 'type' => 'structure', 'members' => [ 'TransactionStatus' => [ 'shape' => 'TransactionStatus', ], ], ], 'ComparisonOperator' => [ 'type' => 'string', 'enum' => [ 'EQ', 'NE', 'LE', 'LT', 'GE', 'GT', 'CONTAINS', 'NOT_CONTAINS', 'BEGINS_WITH', 'IN', 'BETWEEN', ], ], 'ConcurrentModificationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'CreateDataCellsFilterRequest' => [ 'type' => 'structure', 'required' => [ 'TableData', ], 'members' => [ 'TableData' => [ 'shape' => 'DataCellsFilter', ], ], ], 'CreateDataCellsFilterResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateLFTagRequest' => [ 'type' => 'structure', 'required' => [ 'TagKey', 'TagValues', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'TagKey' => [ 'shape' => 'LFTagKey', ], 'TagValues' => [ 'shape' => 'TagValueList', ], ], ], 'CreateLFTagResponse' => [ 'type' => 'structure', 'members' => [], ], 'DataCellsFilter' => [ 'type' => 'structure', 'required' => [ 'TableCatalogId', 'DatabaseName', 'TableName', 'Name', ], 'members' => [ 'TableCatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'Name' => [ 'shape' => 'NameString', ], 'RowFilter' => [ 'shape' => 'RowFilter', ], 'ColumnNames' => [ 'shape' => 'ColumnNames', ], 'ColumnWildcard' => [ 'shape' => 'ColumnWildcard', ], ], ], 'DataCellsFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataCellsFilter', ], ], 'DataCellsFilterResource' => [ 'type' => 'structure', 'members' => [ 'TableCatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'Name' => [ 'shape' => 'NameString', ], ], ], 'DataLakePrincipal' => [ 'type' => 'structure', 'members' => [ 'DataLakePrincipalIdentifier' => [ 'shape' => 'DataLakePrincipalString', ], ], ], 'DataLakePrincipalList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataLakePrincipal', ], 'max' => 10, 'min' => 0, ], 'DataLakePrincipalString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'DataLakeResourceType' => [ 'type' => 'string', 'enum' => [ 'CATALOG', 'DATABASE', 'TABLE', 'DATA_LOCATION', 'LF_TAG', 'LF_TAG_POLICY', 'LF_TAG_POLICY_DATABASE', 'LF_TAG_POLICY_TABLE', ], ], 'DataLakeSettings' => [ 'type' => 'structure', 'members' => [ 'DataLakeAdmins' => [ 'shape' => 'DataLakePrincipalList', ], 'CreateDatabaseDefaultPermissions' => [ 'shape' => 'PrincipalPermissionsList', ], 'CreateTableDefaultPermissions' => [ 'shape' => 'PrincipalPermissionsList', ], 'TrustedResourceOwners' => [ 'shape' => 'TrustedResourceOwners', ], ], ], 'DataLocationResource' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'ResourceArn' => [ 'shape' => 'ResourceArnString', ], ], ], 'DatabaseLFTagsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TaggedDatabase', ], ], 'DatabaseResource' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Name' => [ 'shape' => 'NameString', ], ], ], 'DateTime' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'DeleteDataCellsFilterRequest' => [ 'type' => 'structure', 'members' => [ 'TableCatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteDataCellsFilterResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteLFTagRequest' => [ 'type' => 'structure', 'required' => [ 'TagKey', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'TagKey' => [ 'shape' => 'LFTagKey', ], ], ], 'DeleteLFTagResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteObjectInput' => [ 'type' => 'structure', 'required' => [ 'Uri', ], 'members' => [ 'Uri' => [ 'shape' => 'URI', ], 'ETag' => [ 'shape' => 'ETagString', ], 'PartitionValues' => [ 'shape' => 'PartitionValuesList', ], ], ], 'DeleteObjectsOnCancelRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'TransactionId', 'Objects', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'TransactionId' => [ 'shape' => 'TransactionIdString', ], 'Objects' => [ 'shape' => 'VirtualObjectList', ], ], ], 'DeleteObjectsOnCancelResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeregisterResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArnString', ], ], ], 'DeregisterResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArnString', ], ], ], 'DescribeResourceResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceInfo' => [ 'shape' => 'ResourceInfo', ], ], ], 'DescribeTransactionRequest' => [ 'type' => 'structure', 'required' => [ 'TransactionId', ], 'members' => [ 'TransactionId' => [ 'shape' => 'TransactionIdString', ], ], ], 'DescribeTransactionResponse' => [ 'type' => 'structure', 'members' => [ 'TransactionDescription' => [ 'shape' => 'TransactionDescription', ], ], ], 'DescriptionString' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'DetailsMap' => [ 'type' => 'structure', 'members' => [ 'ResourceShare' => [ 'shape' => 'ResourceShareList', ], ], ], 'ETagString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\p{L}\\p{N}\\p{P}]*', ], 'EntityNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'ErrorDetail' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'NameString', ], 'ErrorMessage' => [ 'shape' => 'DescriptionString', ], ], ], 'ErrorMessageString' => [ 'type' => 'string', ], 'ExecutionStatistics' => [ 'type' => 'structure', 'members' => [ 'AverageExecutionTimeMillis' => [ 'shape' => 'NumberOfMilliseconds', ], 'DataScannedBytes' => [ 'shape' => 'NumberOfBytes', ], 'WorkUnitsExecutedCount' => [ 'shape' => 'NumberOfItems', ], ], ], 'ExpiredException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'error' => [ 'httpStatusCode' => 410, 'senderFault' => true, ], 'exception' => true, ], 'Expression' => [ 'type' => 'list', 'member' => [ 'shape' => 'LFTag', ], 'max' => 5, 'min' => 1, ], 'ExtendTransactionRequest' => [ 'type' => 'structure', 'members' => [ 'TransactionId' => [ 'shape' => 'TransactionIdString', ], ], ], 'ExtendTransactionResponse' => [ 'type' => 'structure', 'members' => [], ], 'FieldNameString' => [ 'type' => 'string', 'enum' => [ 'RESOURCE_ARN', 'ROLE_ARN', 'LAST_MODIFIED', ], ], 'FilterCondition' => [ 'type' => 'structure', 'members' => [ 'Field' => [ 'shape' => 'FieldNameString', ], 'ComparisonOperator' => [ 'shape' => 'ComparisonOperator', ], 'StringValueList' => [ 'shape' => 'StringValueList', ], ], ], 'FilterConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterCondition', ], 'max' => 20, 'min' => 1, ], 'GetDataLakeSettingsRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], ], ], 'GetDataLakeSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'DataLakeSettings' => [ 'shape' => 'DataLakeSettings', ], ], ], 'GetEffectivePermissionsForPathRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'ResourceArn' => [ 'shape' => 'ResourceArnString', ], 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'GetEffectivePermissionsForPathResponse' => [ 'type' => 'structure', 'members' => [ 'Permissions' => [ 'shape' => 'PrincipalResourcePermissionsList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetLFTagRequest' => [ 'type' => 'structure', 'required' => [ 'TagKey', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'TagKey' => [ 'shape' => 'LFTagKey', ], ], ], 'GetLFTagResponse' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'TagKey' => [ 'shape' => 'LFTagKey', ], 'TagValues' => [ 'shape' => 'TagValueList', ], ], ], 'GetQueryStateRequest' => [ 'type' => 'structure', 'required' => [ 'QueryId', ], 'members' => [ 'QueryId' => [ 'shape' => 'GetQueryStateRequestQueryIdString', ], ], ], 'GetQueryStateRequestQueryIdString' => [ 'type' => 'string', 'max' => 36, 'min' => 36, ], 'GetQueryStateResponse' => [ 'type' => 'structure', 'required' => [ 'State', ], 'members' => [ 'Error' => [ 'shape' => 'ErrorMessageString', ], 'State' => [ 'shape' => 'QueryStateString', ], ], ], 'GetQueryStatisticsRequest' => [ 'type' => 'structure', 'required' => [ 'QueryId', ], 'members' => [ 'QueryId' => [ 'shape' => 'GetQueryStatisticsRequestQueryIdString', ], ], ], 'GetQueryStatisticsRequestQueryIdString' => [ 'type' => 'string', 'max' => 36, 'min' => 36, ], 'GetQueryStatisticsResponse' => [ 'type' => 'structure', 'members' => [ 'ExecutionStatistics' => [ 'shape' => 'ExecutionStatistics', ], 'PlanningStatistics' => [ 'shape' => 'PlanningStatistics', ], 'QuerySubmissionTime' => [ 'shape' => 'DateTime', ], ], ], 'GetResourceLFTagsRequest' => [ 'type' => 'structure', 'required' => [ 'Resource', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Resource' => [ 'shape' => 'Resource', ], 'ShowAssignedLFTags' => [ 'shape' => 'BooleanNullable', ], ], ], 'GetResourceLFTagsResponse' => [ 'type' => 'structure', 'members' => [ 'LFTagOnDatabase' => [ 'shape' => 'LFTagsList', ], 'LFTagsOnTable' => [ 'shape' => 'LFTagsList', ], 'LFTagsOnColumns' => [ 'shape' => 'ColumnLFTagsList', ], ], ], 'GetTableObjectsRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'TransactionId' => [ 'shape' => 'TransactionIdString', ], 'QueryAsOfTime' => [ 'shape' => 'Timestamp', ], 'PartitionPredicate' => [ 'shape' => 'PredicateString', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'TokenString', ], ], ], 'GetTableObjectsResponse' => [ 'type' => 'structure', 'members' => [ 'Objects' => [ 'shape' => 'PartitionedTableObjectsList', ], 'NextToken' => [ 'shape' => 'TokenString', ], ], ], 'GetWorkUnitResultsRequest' => [ 'type' => 'structure', 'required' => [ 'QueryId', 'WorkUnitId', 'WorkUnitToken', ], 'members' => [ 'QueryId' => [ 'shape' => 'GetWorkUnitResultsRequestQueryIdString', ], 'WorkUnitId' => [ 'shape' => 'GetWorkUnitResultsRequestWorkUnitIdLong', ], 'WorkUnitToken' => [ 'shape' => 'SyntheticGetWorkUnitResultsRequestWorkUnitTokenString', ], ], ], 'GetWorkUnitResultsRequestQueryIdString' => [ 'type' => 'string', 'max' => 36, 'min' => 36, ], 'GetWorkUnitResultsRequestWorkUnitIdLong' => [ 'type' => 'long', 'min' => 0, ], 'GetWorkUnitResultsResponse' => [ 'type' => 'structure', 'members' => [ 'ResultStream' => [ 'shape' => 'ResultStream', ], ], 'payload' => 'ResultStream', ], 'GetWorkUnitsRequest' => [ 'type' => 'structure', 'required' => [ 'QueryId', ], 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'PageSize' => [ 'shape' => 'Integer', ], 'QueryId' => [ 'shape' => 'GetWorkUnitsRequestQueryIdString', ], ], ], 'GetWorkUnitsRequestQueryIdString' => [ 'type' => 'string', 'max' => 36, 'min' => 36, ], 'GetWorkUnitsResponse' => [ 'type' => 'structure', 'required' => [ 'QueryId', 'WorkUnitRanges', ], 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'QueryId' => [ 'shape' => 'QueryIdString', ], 'WorkUnitRanges' => [ 'shape' => 'WorkUnitRangeList', ], ], ], 'GlueEncryptionException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'GrantPermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'Principal', 'Resource', 'Permissions', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Principal' => [ 'shape' => 'DataLakePrincipal', ], 'Resource' => [ 'shape' => 'Resource', ], 'Permissions' => [ 'shape' => 'PermissionList', ], 'PermissionsWithGrantOption' => [ 'shape' => 'PermissionList', ], ], ], 'GrantPermissionsResponse' => [ 'type' => 'structure', 'members' => [], ], 'IAMRoleArn' => [ 'type' => 'string', 'pattern' => 'arn:aws:iam::[0-9]*:role/.*', ], 'Identifier' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServiceException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InvalidInputException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'LFTag' => [ 'type' => 'structure', 'required' => [ 'TagKey', 'TagValues', ], 'members' => [ 'TagKey' => [ 'shape' => 'LFTagKey', ], 'TagValues' => [ 'shape' => 'TagValueList', ], ], ], 'LFTagError' => [ 'type' => 'structure', 'members' => [ 'LFTag' => [ 'shape' => 'LFTagPair', ], 'Error' => [ 'shape' => 'ErrorDetail', ], ], ], 'LFTagErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'LFTagError', ], ], 'LFTagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:\\/=+\\-@%]*)$', ], 'LFTagKeyResource' => [ 'type' => 'structure', 'required' => [ 'TagKey', 'TagValues', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'TagKey' => [ 'shape' => 'NameString', ], 'TagValues' => [ 'shape' => 'TagValueList', ], ], ], 'LFTagPair' => [ 'type' => 'structure', 'required' => [ 'TagKey', 'TagValues', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'TagKey' => [ 'shape' => 'LFTagKey', ], 'TagValues' => [ 'shape' => 'TagValueList', ], ], ], 'LFTagPolicyResource' => [ 'type' => 'structure', 'required' => [ 'ResourceType', 'Expression', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'Expression' => [ 'shape' => 'Expression', ], ], ], 'LFTagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:\\*\\/=+\\-@%]*)$', ], 'LFTagsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LFTagPair', ], 'max' => 50, 'min' => 1, ], 'LastModifiedTimestamp' => [ 'type' => 'timestamp', ], 'ListDataCellsFilterRequest' => [ 'type' => 'structure', 'members' => [ 'Table' => [ 'shape' => 'TableResource', ], 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'ListDataCellsFilterResponse' => [ 'type' => 'structure', 'members' => [ 'DataCellsFilters' => [ 'shape' => 'DataCellsFilterList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListLFTagsRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'ResourceShareType' => [ 'shape' => 'ResourceShareType', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListLFTagsResponse' => [ 'type' => 'structure', 'members' => [ 'LFTags' => [ 'shape' => 'LFTagsList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListPermissionsRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Principal' => [ 'shape' => 'DataLakePrincipal', ], 'ResourceType' => [ 'shape' => 'DataLakeResourceType', ], 'Resource' => [ 'shape' => 'Resource', ], 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'IncludeRelated' => [ 'shape' => 'TrueFalseString', ], ], ], 'ListPermissionsResponse' => [ 'type' => 'structure', 'members' => [ 'PrincipalResourcePermissions' => [ 'shape' => 'PrincipalResourcePermissionsList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListResourcesRequest' => [ 'type' => 'structure', 'members' => [ 'FilterConditionList' => [ 'shape' => 'FilterConditionList', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListResourcesResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceInfoList' => [ 'shape' => 'ResourceInfoList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListTableStorageOptimizersRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'StorageOptimizerType' => [ 'shape' => 'OptimizerType', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListTableStorageOptimizersResponse' => [ 'type' => 'structure', 'members' => [ 'StorageOptimizerList' => [ 'shape' => 'StorageOptimizerList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListTransactionsRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'StatusFilter' => [ 'shape' => 'TransactionStatusFilter', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'TokenString', ], ], ], 'ListTransactionsResponse' => [ 'type' => 'structure', 'members' => [ 'Transactions' => [ 'shape' => 'TransactionDescriptionList', ], 'NextToken' => [ 'shape' => 'TokenString', ], ], ], 'MessageString' => [ 'type' => 'string', ], 'NameString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'NullableBoolean' => [ 'type' => 'boolean', 'box' => true, ], 'NumberOfBytes' => [ 'type' => 'long', ], 'NumberOfItems' => [ 'type' => 'long', ], 'NumberOfMilliseconds' => [ 'type' => 'long', ], 'ObjectSize' => [ 'type' => 'long', ], 'OperationTimeoutException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'OptimizerType' => [ 'type' => 'string', 'enum' => [ 'COMPACTION', 'GARBAGE_COLLECTION', 'ALL', ], ], 'PageSize' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'PartitionObjects' => [ 'type' => 'structure', 'members' => [ 'PartitionValues' => [ 'shape' => 'PartitionValuesList', ], 'Objects' => [ 'shape' => 'TableObjectList', ], ], ], 'PartitionValueString' => [ 'type' => 'string', 'max' => 1024, ], 'PartitionValuesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PartitionValueString', ], 'max' => 100, 'min' => 1, ], 'PartitionedTableObjectsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PartitionObjects', ], ], 'Permission' => [ 'type' => 'string', 'enum' => [ 'ALL', 'SELECT', 'ALTER', 'DROP', 'DELETE', 'INSERT', 'DESCRIBE', 'CREATE_DATABASE', 'CREATE_TABLE', 'DATA_LOCATION_ACCESS', 'CREATE_TAG', 'ALTER_TAG', 'DELETE_TAG', 'DESCRIBE_TAG', 'ASSOCIATE_TAG', ], ], 'PermissionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Permission', ], ], 'PlanningStatistics' => [ 'type' => 'structure', 'members' => [ 'EstimatedDataToScanBytes' => [ 'shape' => 'NumberOfBytes', ], 'PlanningTimeMillis' => [ 'shape' => 'NumberOfMilliseconds', ], 'QueueTimeMillis' => [ 'shape' => 'NumberOfMilliseconds', ], 'WorkUnitsGeneratedCount' => [ 'shape' => 'NumberOfItems', ], ], ], 'PredicateString' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'PrincipalPermissions' => [ 'type' => 'structure', 'members' => [ 'Principal' => [ 'shape' => 'DataLakePrincipal', ], 'Permissions' => [ 'shape' => 'PermissionList', ], ], ], 'PrincipalPermissionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PrincipalPermissions', ], ], 'PrincipalResourcePermissions' => [ 'type' => 'structure', 'members' => [ 'Principal' => [ 'shape' => 'DataLakePrincipal', ], 'Resource' => [ 'shape' => 'Resource', ], 'Permissions' => [ 'shape' => 'PermissionList', ], 'PermissionsWithGrantOption' => [ 'shape' => 'PermissionList', ], 'AdditionalDetails' => [ 'shape' => 'DetailsMap', ], ], ], 'PrincipalResourcePermissionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PrincipalResourcePermissions', ], ], 'PutDataLakeSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'DataLakeSettings', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DataLakeSettings' => [ 'shape' => 'DataLakeSettings', ], ], ], 'PutDataLakeSettingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'QueryIdString' => [ 'type' => 'string', ], 'QueryParameterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'QueryPlanningContext' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'QueryPlanningContextDatabaseNameString', ], 'QueryAsOfTime' => [ 'shape' => 'Timestamp', ], 'QueryParameters' => [ 'shape' => 'QueryParameterMap', ], 'TransactionId' => [ 'shape' => 'TransactionIdString', ], ], ], 'QueryPlanningContextDatabaseNameString' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'QueryStateString' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'WORKUNITS_AVAILABLE', 'ERROR', 'FINISHED', 'EXPIRED', ], ], 'RAMResourceShareArn' => [ 'type' => 'string', ], 'RegisterResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArnString', ], 'UseServiceLinkedRole' => [ 'shape' => 'NullableBoolean', ], 'RoleArn' => [ 'shape' => 'IAMRoleArn', ], ], ], 'RegisterResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'RemoveLFTagsFromResourceRequest' => [ 'type' => 'structure', 'required' => [ 'Resource', 'LFTags', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Resource' => [ 'shape' => 'Resource', ], 'LFTags' => [ 'shape' => 'LFTagsList', ], ], ], 'RemoveLFTagsFromResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Failures' => [ 'shape' => 'LFTagErrors', ], ], ], 'Resource' => [ 'type' => 'structure', 'members' => [ 'Catalog' => [ 'shape' => 'CatalogResource', ], 'Database' => [ 'shape' => 'DatabaseResource', ], 'Table' => [ 'shape' => 'TableResource', ], 'TableWithColumns' => [ 'shape' => 'TableWithColumnsResource', ], 'DataLocation' => [ 'shape' => 'DataLocationResource', ], 'DataCellsFilter' => [ 'shape' => 'DataCellsFilterResource', ], 'LFTag' => [ 'shape' => 'LFTagKeyResource', ], 'LFTagPolicy' => [ 'shape' => 'LFTagPolicyResource', ], ], ], 'ResourceArnString' => [ 'type' => 'string', ], 'ResourceInfo' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArnString', ], 'RoleArn' => [ 'shape' => 'IAMRoleArn', ], 'LastModified' => [ 'shape' => 'LastModifiedTimestamp', ], ], ], 'ResourceInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceInfo', ], ], 'ResourceNotReadyException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ResourceNumberLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'ResourceShareList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RAMResourceShareArn', ], ], 'ResourceShareType' => [ 'type' => 'string', 'enum' => [ 'FOREIGN', 'ALL', ], ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'DATABASE', 'TABLE', ], ], 'Result' => [ 'type' => 'string', ], 'ResultStream' => [ 'type' => 'blob', 'streaming' => true, ], 'RevokePermissionsRequest' => [ 'type' => 'structure', 'required' => [ 'Principal', 'Resource', 'Permissions', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Principal' => [ 'shape' => 'DataLakePrincipal', ], 'Resource' => [ 'shape' => 'Resource', ], 'Permissions' => [ 'shape' => 'PermissionList', ], 'PermissionsWithGrantOption' => [ 'shape' => 'PermissionList', ], ], ], 'RevokePermissionsResponse' => [ 'type' => 'structure', 'members' => [], ], 'RowFilter' => [ 'type' => 'structure', 'members' => [ 'FilterExpression' => [ 'shape' => 'PredicateString', ], 'AllRowsWildcard' => [ 'shape' => 'AllRowsWildcard', ], ], ], 'SearchDatabasesByLFTagsRequest' => [ 'type' => 'structure', 'required' => [ 'Expression', ], 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Expression' => [ 'shape' => 'Expression', ], ], ], 'SearchDatabasesByLFTagsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'DatabaseList' => [ 'shape' => 'DatabaseLFTagsList', ], ], ], 'SearchTablesByLFTagsRequest' => [ 'type' => 'structure', 'required' => [ 'Expression', ], 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Expression' => [ 'shape' => 'Expression', ], ], ], 'SearchTablesByLFTagsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'TableList' => [ 'shape' => 'TableLFTagsList', ], ], ], 'StartQueryPlanningRequest' => [ 'type' => 'structure', 'required' => [ 'QueryPlanningContext', 'QueryString', ], 'members' => [ 'QueryPlanningContext' => [ 'shape' => 'QueryPlanningContext', ], 'QueryString' => [ 'shape' => 'SyntheticStartQueryPlanningRequestQueryString', ], ], ], 'StartQueryPlanningResponse' => [ 'type' => 'structure', 'required' => [ 'QueryId', ], 'members' => [ 'QueryId' => [ 'shape' => 'QueryIdString', ], ], ], 'StartTransactionRequest' => [ 'type' => 'structure', 'members' => [ 'TransactionType' => [ 'shape' => 'TransactionType', ], ], ], 'StartTransactionResponse' => [ 'type' => 'structure', 'members' => [ 'TransactionId' => [ 'shape' => 'TransactionIdString', ], ], ], 'StatisticsNotReadyYetException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'error' => [ 'httpStatusCode' => 420, 'senderFault' => true, ], 'exception' => true, ], 'StorageOptimizer' => [ 'type' => 'structure', 'members' => [ 'StorageOptimizerType' => [ 'shape' => 'OptimizerType', ], 'Config' => [ 'shape' => 'StorageOptimizerConfig', ], 'ErrorMessage' => [ 'shape' => 'MessageString', ], 'Warnings' => [ 'shape' => 'MessageString', ], 'LastRunDetails' => [ 'shape' => 'MessageString', ], ], ], 'StorageOptimizerConfig' => [ 'type' => 'map', 'key' => [ 'shape' => 'StorageOptimizerConfigKey', ], 'value' => [ 'shape' => 'StorageOptimizerConfigValue', ], ], 'StorageOptimizerConfigKey' => [ 'type' => 'string', ], 'StorageOptimizerConfigMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'OptimizerType', ], 'value' => [ 'shape' => 'StorageOptimizerConfig', ], ], 'StorageOptimizerConfigValue' => [ 'type' => 'string', ], 'StorageOptimizerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StorageOptimizer', ], ], 'String' => [ 'type' => 'string', ], 'StringValue' => [ 'type' => 'string', ], 'StringValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringValue', ], ], 'SyntheticGetWorkUnitResultsRequestWorkUnitTokenString' => [ 'type' => 'string', 'min' => 1, 'sensitive' => true, ], 'SyntheticStartQueryPlanningRequestQueryString' => [ 'type' => 'string', 'min' => 1, 'sensitive' => true, ], 'TableLFTagsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TaggedTable', ], ], 'TableObject' => [ 'type' => 'structure', 'members' => [ 'Uri' => [ 'shape' => 'URI', ], 'ETag' => [ 'shape' => 'ETagString', ], 'Size' => [ 'shape' => 'ObjectSize', ], ], ], 'TableObjectList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TableObject', ], ], 'TableResource' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'Name' => [ 'shape' => 'NameString', ], 'TableWildcard' => [ 'shape' => 'TableWildcard', ], ], ], 'TableWildcard' => [ 'type' => 'structure', 'members' => [], ], 'TableWithColumnsResource' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'Name', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'Name' => [ 'shape' => 'NameString', ], 'ColumnNames' => [ 'shape' => 'ColumnNames', ], 'ColumnWildcard' => [ 'shape' => 'ColumnWildcard', ], ], ], 'TagValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LFTagValue', ], 'max' => 50, 'min' => 1, ], 'TaggedDatabase' => [ 'type' => 'structure', 'members' => [ 'Database' => [ 'shape' => 'DatabaseResource', ], 'LFTags' => [ 'shape' => 'LFTagsList', ], ], ], 'TaggedTable' => [ 'type' => 'structure', 'members' => [ 'Table' => [ 'shape' => 'TableResource', ], 'LFTagOnDatabase' => [ 'shape' => 'LFTagsList', ], 'LFTagsOnTable' => [ 'shape' => 'LFTagsList', ], 'LFTagsOnColumns' => [ 'shape' => 'ColumnLFTagsList', ], ], ], 'ThrottledException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'Token' => [ 'type' => 'string', ], 'TokenString' => [ 'type' => 'string', 'max' => 4096, ], 'TransactionCanceledException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TransactionCommitInProgressException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TransactionCommittedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'TransactionDescription' => [ 'type' => 'structure', 'members' => [ 'TransactionId' => [ 'shape' => 'TransactionIdString', ], 'TransactionStatus' => [ 'shape' => 'TransactionStatus', ], 'TransactionStartTime' => [ 'shape' => 'Timestamp', ], 'TransactionEndTime' => [ 'shape' => 'Timestamp', ], ], ], 'TransactionDescriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TransactionDescription', ], ], 'TransactionIdString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\p{L}\\p{N}\\p{P}]*', ], 'TransactionStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'COMMITTED', 'ABORTED', 'COMMIT_IN_PROGRESS', ], ], 'TransactionStatusFilter' => [ 'type' => 'string', 'enum' => [ 'ALL', 'COMPLETED', 'ACTIVE', 'COMMITTED', 'ABORTED', ], ], 'TransactionType' => [ 'type' => 'string', 'enum' => [ 'READ_AND_WRITE', 'READ_ONLY', ], ], 'TrueFalseString' => [ 'type' => 'string', 'max' => 5, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'TrustedResourceOwners' => [ 'type' => 'list', 'member' => [ 'shape' => 'CatalogIdString', ], ], 'URI' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'UpdateLFTagRequest' => [ 'type' => 'structure', 'required' => [ 'TagKey', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'TagKey' => [ 'shape' => 'LFTagKey', ], 'TagValuesToDelete' => [ 'shape' => 'TagValueList', ], 'TagValuesToAdd' => [ 'shape' => 'TagValueList', ], ], ], 'UpdateLFTagResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateResourceRequest' => [ 'type' => 'structure', 'required' => [ 'RoleArn', 'ResourceArn', ], 'members' => [ 'RoleArn' => [ 'shape' => 'IAMRoleArn', ], 'ResourceArn' => [ 'shape' => 'ResourceArnString', ], ], ], 'UpdateResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateTableObjectsRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'TransactionId', 'WriteOperations', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'TransactionId' => [ 'shape' => 'TransactionIdString', ], 'WriteOperations' => [ 'shape' => 'WriteOperationList', ], ], ], 'UpdateTableObjectsResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateTableStorageOptimizerRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'StorageOptimizerConfig', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'StorageOptimizerConfig' => [ 'shape' => 'StorageOptimizerConfigMap', ], ], ], 'UpdateTableStorageOptimizerResponse' => [ 'type' => 'structure', 'members' => [ 'Result' => [ 'shape' => 'Result', ], ], ], 'VirtualObject' => [ 'type' => 'structure', 'required' => [ 'Uri', ], 'members' => [ 'Uri' => [ 'shape' => 'URI', ], 'ETag' => [ 'shape' => 'ETagString', ], ], ], 'VirtualObjectList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VirtualObject', ], 'max' => 100, 'min' => 1, ], 'WorkUnitIdLong' => [ 'type' => 'long', ], 'WorkUnitRange' => [ 'type' => 'structure', 'required' => [ 'WorkUnitIdMax', 'WorkUnitIdMin', 'WorkUnitToken', ], 'members' => [ 'WorkUnitIdMax' => [ 'shape' => 'WorkUnitIdLong', ], 'WorkUnitIdMin' => [ 'shape' => 'WorkUnitIdLong', ], 'WorkUnitToken' => [ 'shape' => 'WorkUnitTokenString', ], ], ], 'WorkUnitRangeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkUnitRange', ], ], 'WorkUnitTokenString' => [ 'type' => 'string', ], 'WorkUnitsNotReadyYetException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'error' => [ 'httpStatusCode' => 420, 'senderFault' => true, ], 'exception' => true, ], 'WriteOperation' => [ 'type' => 'structure', 'members' => [ 'AddObject' => [ 'shape' => 'AddObjectInput', ], 'DeleteObject' => [ 'shape' => 'DeleteObjectInput', ], ], ], 'WriteOperationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WriteOperation', ], 'max' => 100, 'min' => 1, ], ],];
