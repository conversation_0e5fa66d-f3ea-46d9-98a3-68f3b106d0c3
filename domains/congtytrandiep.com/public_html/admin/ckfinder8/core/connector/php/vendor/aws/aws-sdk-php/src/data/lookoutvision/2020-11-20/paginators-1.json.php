<?php
// This file was auto-generated from sdk-root/src/data/lookoutvision/2020-11-20/paginators-1.json
return [ 'pagination' => [ 'ListDatasetEntries' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'DatasetEntries', ], 'ListModelPackagingJobs' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'ModelPackagingJobs', ], 'ListModels' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'Models', ], 'ListProjects' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', 'result_key' => 'Projects', ], ],];
