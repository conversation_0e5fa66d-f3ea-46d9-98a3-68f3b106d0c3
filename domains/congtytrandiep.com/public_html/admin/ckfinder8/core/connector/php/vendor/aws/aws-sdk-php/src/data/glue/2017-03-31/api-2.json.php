<?php
// This file was auto-generated from sdk-root/src/data/glue/2017-03-31/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-03-31', 'endpointPrefix' => 'glue', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceFullName' => 'AWS Glue', 'serviceId' => 'Glue', 'signatureVersion' => 'v4', 'targetPrefix' => 'AWSGlue', 'uid' => 'glue-2017-03-31', ], 'operations' => [ 'BatchCreatePartition' => [ 'name' => 'BatchCreatePartition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchCreatePartitionRequest', ], 'output' => [ 'shape' => 'BatchCreatePartitionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'BatchDeleteConnection' => [ 'name' => 'BatchDeleteConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDeleteConnectionRequest', ], 'output' => [ 'shape' => 'BatchDeleteConnectionResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'BatchDeletePartition' => [ 'name' => 'BatchDeletePartition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDeletePartitionRequest', ], 'output' => [ 'shape' => 'BatchDeletePartitionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'BatchDeleteTable' => [ 'name' => 'BatchDeleteTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDeleteTableRequest', ], 'output' => [ 'shape' => 'BatchDeleteTableResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'ResourceNotReadyException', ], ], ], 'BatchDeleteTableVersion' => [ 'name' => 'BatchDeleteTableVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDeleteTableVersionRequest', ], 'output' => [ 'shape' => 'BatchDeleteTableVersionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'BatchGetBlueprints' => [ 'name' => 'BatchGetBlueprints', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetBlueprintsRequest', ], 'output' => [ 'shape' => 'BatchGetBlueprintsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'BatchGetCrawlers' => [ 'name' => 'BatchGetCrawlers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetCrawlersRequest', ], 'output' => [ 'shape' => 'BatchGetCrawlersResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'BatchGetDevEndpoints' => [ 'name' => 'BatchGetDevEndpoints', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetDevEndpointsRequest', ], 'output' => [ 'shape' => 'BatchGetDevEndpointsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'BatchGetJobs' => [ 'name' => 'BatchGetJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetJobsRequest', ], 'output' => [ 'shape' => 'BatchGetJobsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'BatchGetPartition' => [ 'name' => 'BatchGetPartition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetPartitionRequest', ], 'output' => [ 'shape' => 'BatchGetPartitionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'InvalidStateException', ], ], ], 'BatchGetTriggers' => [ 'name' => 'BatchGetTriggers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetTriggersRequest', ], 'output' => [ 'shape' => 'BatchGetTriggersResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'BatchGetWorkflows' => [ 'name' => 'BatchGetWorkflows', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetWorkflowsRequest', ], 'output' => [ 'shape' => 'BatchGetWorkflowsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'BatchStopJobRun' => [ 'name' => 'BatchStopJobRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchStopJobRunRequest', ], 'output' => [ 'shape' => 'BatchStopJobRunResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'BatchUpdatePartition' => [ 'name' => 'BatchUpdatePartition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchUpdatePartitionRequest', ], 'output' => [ 'shape' => 'BatchUpdatePartitionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'CancelMLTaskRun' => [ 'name' => 'CancelMLTaskRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelMLTaskRunRequest', ], 'output' => [ 'shape' => 'CancelMLTaskRunResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CheckSchemaVersionValidity' => [ 'name' => 'CheckSchemaVersionValidity', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CheckSchemaVersionValidityInput', ], 'output' => [ 'shape' => 'CheckSchemaVersionValidityResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateBlueprint' => [ 'name' => 'CreateBlueprint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateBlueprintRequest', ], 'output' => [ 'shape' => 'CreateBlueprintResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], ], ], 'CreateClassifier' => [ 'name' => 'CreateClassifier', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateClassifierRequest', ], 'output' => [ 'shape' => 'CreateClassifierResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'CreateConnection' => [ 'name' => 'CreateConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateConnectionRequest', ], 'output' => [ 'shape' => 'CreateConnectionResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'CreateCrawler' => [ 'name' => 'CreateCrawler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateCrawlerRequest', ], 'output' => [ 'shape' => 'CreateCrawlerResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], ], ], 'CreateDatabase' => [ 'name' => 'CreateDatabase', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDatabaseRequest', ], 'output' => [ 'shape' => 'CreateDatabaseResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'CreateDevEndpoint' => [ 'name' => 'CreateDevEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDevEndpointRequest', ], 'output' => [ 'shape' => 'CreateDevEndpointResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], ], ], 'CreateJob' => [ 'name' => 'CreateJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateJobRequest', ], 'output' => [ 'shape' => 'CreateJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'CreateMLTransform' => [ 'name' => 'CreateMLTransform', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateMLTransformRequest', ], 'output' => [ 'shape' => 'CreateMLTransformResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], ], ], 'CreatePartition' => [ 'name' => 'CreatePartition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePartitionRequest', ], 'output' => [ 'shape' => 'CreatePartitionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'CreatePartitionIndex' => [ 'name' => 'CreatePartitionIndex', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePartitionIndexRequest', ], 'output' => [ 'shape' => 'CreatePartitionIndexResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'CreateRegistry' => [ 'name' => 'CreateRegistry', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRegistryInput', ], 'output' => [ 'shape' => 'CreateRegistryResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateSchema' => [ 'name' => 'CreateSchema', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSchemaInput', ], 'output' => [ 'shape' => 'CreateSchemaResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'CreateScript' => [ 'name' => 'CreateScript', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateScriptRequest', ], 'output' => [ 'shape' => 'CreateScriptResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'CreateSecurityConfiguration' => [ 'name' => 'CreateSecurityConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSecurityConfigurationRequest', ], 'output' => [ 'shape' => 'CreateSecurityConfigurationResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], ], ], 'CreateTable' => [ 'name' => 'CreateTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTableRequest', ], 'output' => [ 'shape' => 'CreateTableResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ResourceNotReadyException', ], ], ], 'CreateTrigger' => [ 'name' => 'CreateTrigger', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTriggerRequest', ], 'output' => [ 'shape' => 'CreateTriggerResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'CreateUserDefinedFunction' => [ 'name' => 'CreateUserDefinedFunction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateUserDefinedFunctionRequest', ], 'output' => [ 'shape' => 'CreateUserDefinedFunctionResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'CreateWorkflow' => [ 'name' => 'CreateWorkflow', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateWorkflowRequest', ], 'output' => [ 'shape' => 'CreateWorkflowResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DeleteBlueprint' => [ 'name' => 'DeleteBlueprint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteBlueprintRequest', ], 'output' => [ 'shape' => 'DeleteBlueprintResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeleteClassifier' => [ 'name' => 'DeleteClassifier', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteClassifierRequest', ], 'output' => [ 'shape' => 'DeleteClassifierResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'DeleteColumnStatisticsForPartition' => [ 'name' => 'DeleteColumnStatisticsForPartition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteColumnStatisticsForPartitionRequest', ], 'output' => [ 'shape' => 'DeleteColumnStatisticsForPartitionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'DeleteColumnStatisticsForTable' => [ 'name' => 'DeleteColumnStatisticsForTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteColumnStatisticsForTableRequest', ], 'output' => [ 'shape' => 'DeleteColumnStatisticsForTableResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'DeleteConnection' => [ 'name' => 'DeleteConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteConnectionRequest', ], 'output' => [ 'shape' => 'DeleteConnectionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'DeleteCrawler' => [ 'name' => 'DeleteCrawler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCrawlerRequest', ], 'output' => [ 'shape' => 'DeleteCrawlerResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'CrawlerRunningException', ], [ 'shape' => 'SchedulerTransitioningException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'DeleteDatabase' => [ 'name' => 'DeleteDatabase', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDatabaseRequest', ], 'output' => [ 'shape' => 'DeleteDatabaseResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DeleteDevEndpoint' => [ 'name' => 'DeleteDevEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDevEndpointRequest', ], 'output' => [ 'shape' => 'DeleteDevEndpointResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'DeleteJob' => [ 'name' => 'DeleteJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteJobRequest', ], 'output' => [ 'shape' => 'DeleteJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'DeleteMLTransform' => [ 'name' => 'DeleteMLTransform', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteMLTransformRequest', ], 'output' => [ 'shape' => 'DeleteMLTransformResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'DeletePartition' => [ 'name' => 'DeletePartition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePartitionRequest', ], 'output' => [ 'shape' => 'DeletePartitionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'DeletePartitionIndex' => [ 'name' => 'DeletePartitionIndex', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePartitionIndexRequest', ], 'output' => [ 'shape' => 'DeletePartitionIndexResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'DeleteRegistry' => [ 'name' => 'DeleteRegistry', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRegistryInput', ], 'output' => [ 'shape' => 'DeleteRegistryResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DeleteResourcePolicy' => [ 'name' => 'DeleteResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteResourcePolicyRequest', ], 'output' => [ 'shape' => 'DeleteResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ConditionCheckFailureException', ], ], ], 'DeleteSchema' => [ 'name' => 'DeleteSchema', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSchemaInput', ], 'output' => [ 'shape' => 'DeleteSchemaResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DeleteSchemaVersions' => [ 'name' => 'DeleteSchemaVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSchemaVersionsInput', ], 'output' => [ 'shape' => 'DeleteSchemaVersionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DeleteSecurityConfiguration' => [ 'name' => 'DeleteSecurityConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSecurityConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteSecurityConfigurationResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'DeleteTable' => [ 'name' => 'DeleteTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTableRequest', ], 'output' => [ 'shape' => 'DeleteTableResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ResourceNotReadyException', ], ], ], 'DeleteTableVersion' => [ 'name' => 'DeleteTableVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTableVersionRequest', ], 'output' => [ 'shape' => 'DeleteTableVersionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'DeleteTrigger' => [ 'name' => 'DeleteTrigger', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTriggerRequest', ], 'output' => [ 'shape' => 'DeleteTriggerResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DeleteUserDefinedFunction' => [ 'name' => 'DeleteUserDefinedFunction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteUserDefinedFunctionRequest', ], 'output' => [ 'shape' => 'DeleteUserDefinedFunctionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'DeleteWorkflow' => [ 'name' => 'DeleteWorkflow', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteWorkflowRequest', ], 'output' => [ 'shape' => 'DeleteWorkflowResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'GetBlueprint' => [ 'name' => 'GetBlueprint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetBlueprintRequest', ], 'output' => [ 'shape' => 'GetBlueprintResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetBlueprintRun' => [ 'name' => 'GetBlueprintRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetBlueprintRunRequest', ], 'output' => [ 'shape' => 'GetBlueprintRunResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetBlueprintRuns' => [ 'name' => 'GetBlueprintRuns', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetBlueprintRunsRequest', ], 'output' => [ 'shape' => 'GetBlueprintRunsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'GetCatalogImportStatus' => [ 'name' => 'GetCatalogImportStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCatalogImportStatusRequest', ], 'output' => [ 'shape' => 'GetCatalogImportStatusResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetClassifier' => [ 'name' => 'GetClassifier', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetClassifierRequest', ], 'output' => [ 'shape' => 'GetClassifierResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetClassifiers' => [ 'name' => 'GetClassifiers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetClassifiersRequest', ], 'output' => [ 'shape' => 'GetClassifiersResponse', ], 'errors' => [ [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetColumnStatisticsForPartition' => [ 'name' => 'GetColumnStatisticsForPartition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetColumnStatisticsForPartitionRequest', ], 'output' => [ 'shape' => 'GetColumnStatisticsForPartitionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'GetColumnStatisticsForTable' => [ 'name' => 'GetColumnStatisticsForTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetColumnStatisticsForTableRequest', ], 'output' => [ 'shape' => 'GetColumnStatisticsForTableResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'GetConnection' => [ 'name' => 'GetConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetConnectionRequest', ], 'output' => [ 'shape' => 'GetConnectionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'GetConnections' => [ 'name' => 'GetConnections', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetConnectionsRequest', ], 'output' => [ 'shape' => 'GetConnectionsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'GetCrawler' => [ 'name' => 'GetCrawler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCrawlerRequest', ], 'output' => [ 'shape' => 'GetCrawlerResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetCrawlerMetrics' => [ 'name' => 'GetCrawlerMetrics', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCrawlerMetricsRequest', ], 'output' => [ 'shape' => 'GetCrawlerMetricsResponse', ], 'errors' => [ [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetCrawlers' => [ 'name' => 'GetCrawlers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCrawlersRequest', ], 'output' => [ 'shape' => 'GetCrawlersResponse', ], 'errors' => [ [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetDataCatalogEncryptionSettings' => [ 'name' => 'GetDataCatalogEncryptionSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDataCatalogEncryptionSettingsRequest', ], 'output' => [ 'shape' => 'GetDataCatalogEncryptionSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetDatabase' => [ 'name' => 'GetDatabase', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDatabaseRequest', ], 'output' => [ 'shape' => 'GetDatabaseResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'GetDatabases' => [ 'name' => 'GetDatabases', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDatabasesRequest', ], 'output' => [ 'shape' => 'GetDatabasesResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'GetDataflowGraph' => [ 'name' => 'GetDataflowGraph', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDataflowGraphRequest', ], 'output' => [ 'shape' => 'GetDataflowGraphResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetDevEndpoint' => [ 'name' => 'GetDevEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDevEndpointRequest', ], 'output' => [ 'shape' => 'GetDevEndpointResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'GetDevEndpoints' => [ 'name' => 'GetDevEndpoints', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDevEndpointsRequest', ], 'output' => [ 'shape' => 'GetDevEndpointsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'GetJob' => [ 'name' => 'GetJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetJobRequest', ], 'output' => [ 'shape' => 'GetJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetJobBookmark' => [ 'name' => 'GetJobBookmark', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetJobBookmarkRequest', ], 'output' => [ 'shape' => 'GetJobBookmarkResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetJobRun' => [ 'name' => 'GetJobRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetJobRunRequest', ], 'output' => [ 'shape' => 'GetJobRunResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetJobRuns' => [ 'name' => 'GetJobRuns', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetJobRunsRequest', ], 'output' => [ 'shape' => 'GetJobRunsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetJobs' => [ 'name' => 'GetJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetJobsRequest', ], 'output' => [ 'shape' => 'GetJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetMLTaskRun' => [ 'name' => 'GetMLTaskRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMLTaskRunRequest', ], 'output' => [ 'shape' => 'GetMLTaskRunResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetMLTaskRuns' => [ 'name' => 'GetMLTaskRuns', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMLTaskRunsRequest', ], 'output' => [ 'shape' => 'GetMLTaskRunsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetMLTransform' => [ 'name' => 'GetMLTransform', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMLTransformRequest', ], 'output' => [ 'shape' => 'GetMLTransformResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetMLTransforms' => [ 'name' => 'GetMLTransforms', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMLTransformsRequest', ], 'output' => [ 'shape' => 'GetMLTransformsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetMapping' => [ 'name' => 'GetMapping', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMappingRequest', ], 'output' => [ 'shape' => 'GetMappingResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'EntityNotFoundException', ], ], ], 'GetPartition' => [ 'name' => 'GetPartition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPartitionRequest', ], 'output' => [ 'shape' => 'GetPartitionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'GetPartitionIndexes' => [ 'name' => 'GetPartitionIndexes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPartitionIndexesRequest', ], 'output' => [ 'shape' => 'GetPartitionIndexesResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetPartitions' => [ 'name' => 'GetPartitions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPartitionsRequest', ], 'output' => [ 'shape' => 'GetPartitionsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'InvalidStateException', ], [ 'shape' => 'ResourceNotReadyException', ], ], ], 'GetPlan' => [ 'name' => 'GetPlan', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPlanRequest', ], 'output' => [ 'shape' => 'GetPlanResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetRegistry' => [ 'name' => 'GetRegistry', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRegistryInput', ], 'output' => [ 'shape' => 'GetRegistryResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetResourcePolicies' => [ 'name' => 'GetResourcePolicies', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResourcePoliciesRequest', ], 'output' => [ 'shape' => 'GetResourcePoliciesResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'GetResourcePolicy' => [ 'name' => 'GetResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResourcePolicyRequest', ], 'output' => [ 'shape' => 'GetResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'GetSchema' => [ 'name' => 'GetSchema', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSchemaInput', ], 'output' => [ 'shape' => 'GetSchemaResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetSchemaByDefinition' => [ 'name' => 'GetSchemaByDefinition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSchemaByDefinitionInput', ], 'output' => [ 'shape' => 'GetSchemaByDefinitionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetSchemaVersion' => [ 'name' => 'GetSchemaVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSchemaVersionInput', ], 'output' => [ 'shape' => 'GetSchemaVersionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetSchemaVersionsDiff' => [ 'name' => 'GetSchemaVersionsDiff', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSchemaVersionsDiffInput', ], 'output' => [ 'shape' => 'GetSchemaVersionsDiffResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetSecurityConfiguration' => [ 'name' => 'GetSecurityConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSecurityConfigurationRequest', ], 'output' => [ 'shape' => 'GetSecurityConfigurationResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetSecurityConfigurations' => [ 'name' => 'GetSecurityConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSecurityConfigurationsRequest', ], 'output' => [ 'shape' => 'GetSecurityConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetTable' => [ 'name' => 'GetTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTableRequest', ], 'output' => [ 'shape' => 'GetTableResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'ResourceNotReadyException', ], ], ], 'GetTableVersion' => [ 'name' => 'GetTableVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTableVersionRequest', ], 'output' => [ 'shape' => 'GetTableVersionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'GetTableVersions' => [ 'name' => 'GetTableVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTableVersionsRequest', ], 'output' => [ 'shape' => 'GetTableVersionsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'GetTables' => [ 'name' => 'GetTables', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTablesRequest', ], 'output' => [ 'shape' => 'GetTablesResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'GetTags' => [ 'name' => 'GetTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTagsRequest', ], 'output' => [ 'shape' => 'GetTagsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'EntityNotFoundException', ], ], ], 'GetTrigger' => [ 'name' => 'GetTrigger', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTriggerRequest', ], 'output' => [ 'shape' => 'GetTriggerResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetTriggers' => [ 'name' => 'GetTriggers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTriggersRequest', ], 'output' => [ 'shape' => 'GetTriggersResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetUserDefinedFunction' => [ 'name' => 'GetUserDefinedFunction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetUserDefinedFunctionRequest', ], 'output' => [ 'shape' => 'GetUserDefinedFunctionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'GetUserDefinedFunctions' => [ 'name' => 'GetUserDefinedFunctions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetUserDefinedFunctionsRequest', ], 'output' => [ 'shape' => 'GetUserDefinedFunctionsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'GetWorkflow' => [ 'name' => 'GetWorkflow', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetWorkflowRequest', ], 'output' => [ 'shape' => 'GetWorkflowResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetWorkflowRun' => [ 'name' => 'GetWorkflowRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetWorkflowRunRequest', ], 'output' => [ 'shape' => 'GetWorkflowRunResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetWorkflowRunProperties' => [ 'name' => 'GetWorkflowRunProperties', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetWorkflowRunPropertiesRequest', ], 'output' => [ 'shape' => 'GetWorkflowRunPropertiesResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetWorkflowRuns' => [ 'name' => 'GetWorkflowRuns', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetWorkflowRunsRequest', ], 'output' => [ 'shape' => 'GetWorkflowRunsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'ImportCatalogToGlue' => [ 'name' => 'ImportCatalogToGlue', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ImportCatalogToGlueRequest', ], 'output' => [ 'shape' => 'ImportCatalogToGlueResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'ListBlueprints' => [ 'name' => 'ListBlueprints', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListBlueprintsRequest', ], 'output' => [ 'shape' => 'ListBlueprintsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'ListCrawlers' => [ 'name' => 'ListCrawlers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCrawlersRequest', ], 'output' => [ 'shape' => 'ListCrawlersResponse', ], 'errors' => [ [ 'shape' => 'OperationTimeoutException', ], ], ], 'ListDevEndpoints' => [ 'name' => 'ListDevEndpoints', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDevEndpointsRequest', ], 'output' => [ 'shape' => 'ListDevEndpointsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'ListJobs' => [ 'name' => 'ListJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListJobsRequest', ], 'output' => [ 'shape' => 'ListJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'ListMLTransforms' => [ 'name' => 'ListMLTransforms', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListMLTransformsRequest', ], 'output' => [ 'shape' => 'ListMLTransformsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListRegistries' => [ 'name' => 'ListRegistries', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRegistriesInput', ], 'output' => [ 'shape' => 'ListRegistriesResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListSchemaVersions' => [ 'name' => 'ListSchemaVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSchemaVersionsInput', ], 'output' => [ 'shape' => 'ListSchemaVersionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListSchemas' => [ 'name' => 'ListSchemas', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSchemasInput', ], 'output' => [ 'shape' => 'ListSchemasResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ListTriggers' => [ 'name' => 'ListTriggers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTriggersRequest', ], 'output' => [ 'shape' => 'ListTriggersResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'ListWorkflows' => [ 'name' => 'ListWorkflows', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListWorkflowsRequest', ], 'output' => [ 'shape' => 'ListWorkflowsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'PutDataCatalogEncryptionSettings' => [ 'name' => 'PutDataCatalogEncryptionSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutDataCatalogEncryptionSettingsRequest', ], 'output' => [ 'shape' => 'PutDataCatalogEncryptionSettingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'PutResourcePolicy' => [ 'name' => 'PutResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutResourcePolicyRequest', ], 'output' => [ 'shape' => 'PutResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ConditionCheckFailureException', ], ], ], 'PutSchemaVersionMetadata' => [ 'name' => 'PutSchemaVersionMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutSchemaVersionMetadataInput', ], 'output' => [ 'shape' => 'PutSchemaVersionMetadataResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], ], ], 'PutWorkflowRunProperties' => [ 'name' => 'PutWorkflowRunProperties', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutWorkflowRunPropertiesRequest', ], 'output' => [ 'shape' => 'PutWorkflowRunPropertiesResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'QuerySchemaVersionMetadata' => [ 'name' => 'QuerySchemaVersionMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'QuerySchemaVersionMetadataInput', ], 'output' => [ 'shape' => 'QuerySchemaVersionMetadataResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], ], ], 'RegisterSchemaVersion' => [ 'name' => 'RegisterSchemaVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterSchemaVersionInput', ], 'output' => [ 'shape' => 'RegisterSchemaVersionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'RemoveSchemaVersionMetadata' => [ 'name' => 'RemoveSchemaVersionMetadata', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveSchemaVersionMetadataInput', ], 'output' => [ 'shape' => 'RemoveSchemaVersionMetadataResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], ], ], 'ResetJobBookmark' => [ 'name' => 'ResetJobBookmark', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ResetJobBookmarkRequest', ], 'output' => [ 'shape' => 'ResetJobBookmarkResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'ResumeWorkflowRun' => [ 'name' => 'ResumeWorkflowRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ResumeWorkflowRunRequest', ], 'output' => [ 'shape' => 'ResumeWorkflowRunResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ConcurrentRunsExceededException', ], [ 'shape' => 'IllegalWorkflowStateException', ], ], ], 'SearchTables' => [ 'name' => 'SearchTables', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SearchTablesRequest', ], 'output' => [ 'shape' => 'SearchTablesResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'StartBlueprintRun' => [ 'name' => 'StartBlueprintRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartBlueprintRunRequest', ], 'output' => [ 'shape' => 'StartBlueprintRunResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'IllegalBlueprintStateException', ], ], ], 'StartCrawler' => [ 'name' => 'StartCrawler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartCrawlerRequest', ], 'output' => [ 'shape' => 'StartCrawlerResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'CrawlerRunningException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'StartCrawlerSchedule' => [ 'name' => 'StartCrawlerSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartCrawlerScheduleRequest', ], 'output' => [ 'shape' => 'StartCrawlerScheduleResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'SchedulerRunningException', ], [ 'shape' => 'SchedulerTransitioningException', ], [ 'shape' => 'NoScheduleException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'StartExportLabelsTaskRun' => [ 'name' => 'StartExportLabelsTaskRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartExportLabelsTaskRunRequest', ], 'output' => [ 'shape' => 'StartExportLabelsTaskRunResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'StartImportLabelsTaskRun' => [ 'name' => 'StartImportLabelsTaskRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartImportLabelsTaskRunRequest', ], 'output' => [ 'shape' => 'StartImportLabelsTaskRunResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'StartJobRun' => [ 'name' => 'StartJobRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartJobRunRequest', ], 'output' => [ 'shape' => 'StartJobRunResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'ConcurrentRunsExceededException', ], ], ], 'StartMLEvaluationTaskRun' => [ 'name' => 'StartMLEvaluationTaskRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartMLEvaluationTaskRunRequest', ], 'output' => [ 'shape' => 'StartMLEvaluationTaskRunResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ConcurrentRunsExceededException', ], [ 'shape' => 'MLTransformNotReadyException', ], ], ], 'StartMLLabelingSetGenerationTaskRun' => [ 'name' => 'StartMLLabelingSetGenerationTaskRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartMLLabelingSetGenerationTaskRunRequest', ], 'output' => [ 'shape' => 'StartMLLabelingSetGenerationTaskRunResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'ConcurrentRunsExceededException', ], ], ], 'StartTrigger' => [ 'name' => 'StartTrigger', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartTriggerRequest', ], 'output' => [ 'shape' => 'StartTriggerResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'ConcurrentRunsExceededException', ], ], ], 'StartWorkflowRun' => [ 'name' => 'StartWorkflowRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartWorkflowRunRequest', ], 'output' => [ 'shape' => 'StartWorkflowRunResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'ConcurrentRunsExceededException', ], ], ], 'StopCrawler' => [ 'name' => 'StopCrawler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopCrawlerRequest', ], 'output' => [ 'shape' => 'StopCrawlerResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'CrawlerNotRunningException', ], [ 'shape' => 'CrawlerStoppingException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'StopCrawlerSchedule' => [ 'name' => 'StopCrawlerSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopCrawlerScheduleRequest', ], 'output' => [ 'shape' => 'StopCrawlerScheduleResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'SchedulerNotRunningException', ], [ 'shape' => 'SchedulerTransitioningException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'StopTrigger' => [ 'name' => 'StopTrigger', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopTriggerRequest', ], 'output' => [ 'shape' => 'StopTriggerResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'StopWorkflowRun' => [ 'name' => 'StopWorkflowRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopWorkflowRunRequest', ], 'output' => [ 'shape' => 'StopWorkflowRunResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'IllegalWorkflowStateException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'EntityNotFoundException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'EntityNotFoundException', ], ], ], 'UpdateBlueprint' => [ 'name' => 'UpdateBlueprint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateBlueprintRequest', ], 'output' => [ 'shape' => 'UpdateBlueprintResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'IllegalBlueprintStateException', ], ], ], 'UpdateClassifier' => [ 'name' => 'UpdateClassifier', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateClassifierRequest', ], 'output' => [ 'shape' => 'UpdateClassifierResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'VersionMismatchException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'UpdateColumnStatisticsForPartition' => [ 'name' => 'UpdateColumnStatisticsForPartition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateColumnStatisticsForPartitionRequest', ], 'output' => [ 'shape' => 'UpdateColumnStatisticsForPartitionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'UpdateColumnStatisticsForTable' => [ 'name' => 'UpdateColumnStatisticsForTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateColumnStatisticsForTableRequest', ], 'output' => [ 'shape' => 'UpdateColumnStatisticsForTableResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'UpdateConnection' => [ 'name' => 'UpdateConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateConnectionRequest', ], 'output' => [ 'shape' => 'UpdateConnectionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'UpdateCrawler' => [ 'name' => 'UpdateCrawler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateCrawlerRequest', ], 'output' => [ 'shape' => 'UpdateCrawlerResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'VersionMismatchException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'CrawlerRunningException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'UpdateCrawlerSchedule' => [ 'name' => 'UpdateCrawlerSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateCrawlerScheduleRequest', ], 'output' => [ 'shape' => 'UpdateCrawlerScheduleResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'VersionMismatchException', ], [ 'shape' => 'SchedulerTransitioningException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'UpdateDatabase' => [ 'name' => 'UpdateDatabase', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDatabaseRequest', ], 'output' => [ 'shape' => 'UpdateDatabaseResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'UpdateDevEndpoint' => [ 'name' => 'UpdateDevEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDevEndpointRequest', ], 'output' => [ 'shape' => 'UpdateDevEndpointResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateJob' => [ 'name' => 'UpdateJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateJobRequest', ], 'output' => [ 'shape' => 'UpdateJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'UpdateMLTransform' => [ 'name' => 'UpdateMLTransform', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateMLTransformRequest', ], 'output' => [ 'shape' => 'UpdateMLTransformResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdatePartition' => [ 'name' => 'UpdatePartition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdatePartitionRequest', ], 'output' => [ 'shape' => 'UpdatePartitionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'UpdateRegistry' => [ 'name' => 'UpdateRegistry', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRegistryInput', ], 'output' => [ 'shape' => 'UpdateRegistryResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateSchema' => [ 'name' => 'UpdateSchema', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateSchemaInput', ], 'output' => [ 'shape' => 'UpdateSchemaResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'UpdateTable' => [ 'name' => 'UpdateTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateTableRequest', ], 'output' => [ 'shape' => 'UpdateTableResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'GlueEncryptionException', ], [ 'shape' => 'ResourceNotReadyException', ], ], ], 'UpdateTrigger' => [ 'name' => 'UpdateTrigger', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateTriggerRequest', ], 'output' => [ 'shape' => 'UpdateTriggerResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'UpdateUserDefinedFunction' => [ 'name' => 'UpdateUserDefinedFunction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateUserDefinedFunctionRequest', ], 'output' => [ 'shape' => 'UpdateUserDefinedFunctionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'GlueEncryptionException', ], ], ], 'UpdateWorkflow' => [ 'name' => 'UpdateWorkflow', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateWorkflowRequest', ], 'output' => [ 'shape' => 'UpdateWorkflowResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'Action' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], 'Arguments' => [ 'shape' => 'GenericMap', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'SecurityConfiguration' => [ 'shape' => 'NameString', ], 'NotificationProperty' => [ 'shape' => 'NotificationProperty', ], 'CrawlerName' => [ 'shape' => 'NameString', ], ], ], 'ActionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Action', ], ], 'AdditionalPlanOptionsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'GenericString', ], 'value' => [ 'shape' => 'GenericString', ], ], 'AlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'AttemptCount' => [ 'type' => 'integer', ], 'BackfillError' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'BackfillErrorCode', ], 'Partitions' => [ 'shape' => 'BackfillErroredPartitionsList', ], ], ], 'BackfillErrorCode' => [ 'type' => 'string', 'enum' => [ 'ENCRYPTED_PARTITION_ERROR', 'INTERNAL_ERROR', 'INVALID_PARTITION_TYPE_DATA_ERROR', 'MISSING_PARTITION_VALUE_ERROR', 'UNSUPPORTED_PARTITION_CHARACTER_ERROR', ], ], 'BackfillErroredPartitionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PartitionValueList', ], ], 'BackfillErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'BackfillError', ], ], 'BatchCreatePartitionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'PartitionInputList', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionInputList' => [ 'shape' => 'PartitionInputList', ], ], ], 'BatchCreatePartitionResponse' => [ 'type' => 'structure', 'members' => [ 'Errors' => [ 'shape' => 'PartitionErrors', ], ], ], 'BatchDeleteConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectionNameList', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'ConnectionNameList' => [ 'shape' => 'DeleteConnectionNameList', ], ], ], 'BatchDeleteConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'Succeeded' => [ 'shape' => 'NameStringList', ], 'Errors' => [ 'shape' => 'ErrorByName', ], ], ], 'BatchDeletePartitionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'PartitionsToDelete', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionsToDelete' => [ 'shape' => 'BatchDeletePartitionValueList', ], ], ], 'BatchDeletePartitionResponse' => [ 'type' => 'structure', 'members' => [ 'Errors' => [ 'shape' => 'PartitionErrors', ], ], ], 'BatchDeletePartitionValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PartitionValueList', ], 'max' => 25, 'min' => 0, ], 'BatchDeleteTableNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], 'max' => 100, 'min' => 0, ], 'BatchDeleteTableRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TablesToDelete', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TablesToDelete' => [ 'shape' => 'BatchDeleteTableNameList', ], 'TransactionId' => [ 'shape' => 'TransactionIdString', ], ], ], 'BatchDeleteTableResponse' => [ 'type' => 'structure', 'members' => [ 'Errors' => [ 'shape' => 'TableErrors', ], ], ], 'BatchDeleteTableVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VersionString', ], 'max' => 100, 'min' => 0, ], 'BatchDeleteTableVersionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'VersionIds', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'VersionIds' => [ 'shape' => 'BatchDeleteTableVersionList', ], ], ], 'BatchDeleteTableVersionResponse' => [ 'type' => 'structure', 'members' => [ 'Errors' => [ 'shape' => 'TableVersionErrors', ], ], ], 'BatchGetBlueprintNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrchestrationNameString', ], 'max' => 25, 'min' => 1, ], 'BatchGetBlueprintsRequest' => [ 'type' => 'structure', 'required' => [ 'Names', ], 'members' => [ 'Names' => [ 'shape' => 'BatchGetBlueprintNames', ], 'IncludeBlueprint' => [ 'shape' => 'NullableBoolean', ], 'IncludeParameterSpec' => [ 'shape' => 'NullableBoolean', ], ], ], 'BatchGetBlueprintsResponse' => [ 'type' => 'structure', 'members' => [ 'Blueprints' => [ 'shape' => 'Blueprints', ], 'MissingBlueprints' => [ 'shape' => 'BlueprintNames', ], ], ], 'BatchGetCrawlersRequest' => [ 'type' => 'structure', 'required' => [ 'CrawlerNames', ], 'members' => [ 'CrawlerNames' => [ 'shape' => 'CrawlerNameList', ], ], ], 'BatchGetCrawlersResponse' => [ 'type' => 'structure', 'members' => [ 'Crawlers' => [ 'shape' => 'CrawlerList', ], 'CrawlersNotFound' => [ 'shape' => 'CrawlerNameList', ], ], ], 'BatchGetDevEndpointsRequest' => [ 'type' => 'structure', 'required' => [ 'DevEndpointNames', ], 'members' => [ 'DevEndpointNames' => [ 'shape' => 'DevEndpointNames', ], ], ], 'BatchGetDevEndpointsResponse' => [ 'type' => 'structure', 'members' => [ 'DevEndpoints' => [ 'shape' => 'DevEndpointList', ], 'DevEndpointsNotFound' => [ 'shape' => 'DevEndpointNames', ], ], ], 'BatchGetJobsRequest' => [ 'type' => 'structure', 'required' => [ 'JobNames', ], 'members' => [ 'JobNames' => [ 'shape' => 'JobNameList', ], ], ], 'BatchGetJobsResponse' => [ 'type' => 'structure', 'members' => [ 'Jobs' => [ 'shape' => 'JobList', ], 'JobsNotFound' => [ 'shape' => 'JobNameList', ], ], ], 'BatchGetPartitionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'PartitionsToGet', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionsToGet' => [ 'shape' => 'BatchGetPartitionValueList', ], ], ], 'BatchGetPartitionResponse' => [ 'type' => 'structure', 'members' => [ 'Partitions' => [ 'shape' => 'PartitionList', ], 'UnprocessedKeys' => [ 'shape' => 'BatchGetPartitionValueList', ], ], ], 'BatchGetPartitionValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PartitionValueList', ], 'max' => 1000, 'min' => 0, ], 'BatchGetTriggersRequest' => [ 'type' => 'structure', 'required' => [ 'TriggerNames', ], 'members' => [ 'TriggerNames' => [ 'shape' => 'TriggerNameList', ], ], ], 'BatchGetTriggersResponse' => [ 'type' => 'structure', 'members' => [ 'Triggers' => [ 'shape' => 'TriggerList', ], 'TriggersNotFound' => [ 'shape' => 'TriggerNameList', ], ], ], 'BatchGetWorkflowsRequest' => [ 'type' => 'structure', 'required' => [ 'Names', ], 'members' => [ 'Names' => [ 'shape' => 'WorkflowNames', ], 'IncludeGraph' => [ 'shape' => 'NullableBoolean', ], ], ], 'BatchGetWorkflowsResponse' => [ 'type' => 'structure', 'members' => [ 'Workflows' => [ 'shape' => 'Workflows', ], 'MissingWorkflows' => [ 'shape' => 'WorkflowNames', ], ], ], 'BatchSize' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'BatchStopJobRunError' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], 'JobRunId' => [ 'shape' => 'IdString', ], 'ErrorDetail' => [ 'shape' => 'ErrorDetail', ], ], ], 'BatchStopJobRunErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchStopJobRunError', ], ], 'BatchStopJobRunJobRunIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdString', ], 'max' => 25, 'min' => 1, ], 'BatchStopJobRunRequest' => [ 'type' => 'structure', 'required' => [ 'JobName', 'JobRunIds', ], 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], 'JobRunIds' => [ 'shape' => 'BatchStopJobRunJobRunIdList', ], ], ], 'BatchStopJobRunResponse' => [ 'type' => 'structure', 'members' => [ 'SuccessfulSubmissions' => [ 'shape' => 'BatchStopJobRunSuccessfulSubmissionList', ], 'Errors' => [ 'shape' => 'BatchStopJobRunErrorList', ], ], ], 'BatchStopJobRunSuccessfulSubmission' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], 'JobRunId' => [ 'shape' => 'IdString', ], ], ], 'BatchStopJobRunSuccessfulSubmissionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchStopJobRunSuccessfulSubmission', ], ], 'BatchUpdatePartitionFailureEntry' => [ 'type' => 'structure', 'members' => [ 'PartitionValueList' => [ 'shape' => 'BoundedPartitionValueList', ], 'ErrorDetail' => [ 'shape' => 'ErrorDetail', ], ], ], 'BatchUpdatePartitionFailureList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchUpdatePartitionFailureEntry', ], ], 'BatchUpdatePartitionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'Entries', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'Entries' => [ 'shape' => 'BatchUpdatePartitionRequestEntryList', ], ], ], 'BatchUpdatePartitionRequestEntry' => [ 'type' => 'structure', 'required' => [ 'PartitionValueList', 'PartitionInput', ], 'members' => [ 'PartitionValueList' => [ 'shape' => 'BoundedPartitionValueList', ], 'PartitionInput' => [ 'shape' => 'PartitionInput', ], ], ], 'BatchUpdatePartitionRequestEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchUpdatePartitionRequestEntry', ], 'max' => 100, 'min' => 1, ], 'BatchUpdatePartitionResponse' => [ 'type' => 'structure', 'members' => [ 'Errors' => [ 'shape' => 'BatchUpdatePartitionFailureList', ], ], ], 'BatchWindow' => [ 'type' => 'integer', 'box' => true, 'max' => 900, 'min' => 1, ], 'BinaryColumnStatisticsData' => [ 'type' => 'structure', 'required' => [ 'MaximumLength', 'AverageLength', 'NumberOfNulls', ], 'members' => [ 'MaximumLength' => [ 'shape' => 'NonNegativeLong', ], 'AverageLength' => [ 'shape' => 'NonNegativeDouble', ], 'NumberOfNulls' => [ 'shape' => 'NonNegativeLong', ], ], ], 'Blob' => [ 'type' => 'blob', ], 'Blueprint' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'OrchestrationNameString', ], 'Description' => [ 'shape' => 'Generic512CharString', ], 'CreatedOn' => [ 'shape' => 'TimestampValue', ], 'LastModifiedOn' => [ 'shape' => 'TimestampValue', ], 'ParameterSpec' => [ 'shape' => 'BlueprintParameterSpec', ], 'BlueprintLocation' => [ 'shape' => 'GenericString', ], 'BlueprintServiceLocation' => [ 'shape' => 'GenericString', ], 'Status' => [ 'shape' => 'BlueprintStatus', ], 'ErrorMessage' => [ 'shape' => 'ErrorString', ], 'LastActiveDefinition' => [ 'shape' => 'LastActiveDefinition', ], ], ], 'BlueprintDetails' => [ 'type' => 'structure', 'members' => [ 'BlueprintName' => [ 'shape' => 'OrchestrationNameString', ], 'RunId' => [ 'shape' => 'IdString', ], ], ], 'BlueprintNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrchestrationNameString', ], ], 'BlueprintParameterSpec' => [ 'type' => 'string', 'max' => 131072, 'min' => 1, ], 'BlueprintParameters' => [ 'type' => 'string', 'max' => 131072, 'min' => 1, ], 'BlueprintRun' => [ 'type' => 'structure', 'members' => [ 'BlueprintName' => [ 'shape' => 'OrchestrationNameString', ], 'RunId' => [ 'shape' => 'IdString', ], 'WorkflowName' => [ 'shape' => 'NameString', ], 'State' => [ 'shape' => 'BlueprintRunState', ], 'StartedOn' => [ 'shape' => 'TimestampValue', ], 'CompletedOn' => [ 'shape' => 'TimestampValue', ], 'ErrorMessage' => [ 'shape' => 'MessageString', ], 'RollbackErrorMessage' => [ 'shape' => 'MessageString', ], 'Parameters' => [ 'shape' => 'BlueprintParameters', ], 'RoleArn' => [ 'shape' => 'OrchestrationIAMRoleArn', ], ], ], 'BlueprintRunState' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'SUCCEEDED', 'FAILED', 'ROLLING_BACK', ], ], 'BlueprintRuns' => [ 'type' => 'list', 'member' => [ 'shape' => 'BlueprintRun', ], ], 'BlueprintStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'UPDATING', 'FAILED', ], ], 'Blueprints' => [ 'type' => 'list', 'member' => [ 'shape' => 'Blueprint', ], ], 'Boolean' => [ 'type' => 'boolean', ], 'BooleanColumnStatisticsData' => [ 'type' => 'structure', 'required' => [ 'NumberOfTrues', 'NumberOfFalses', 'NumberOfNulls', ], 'members' => [ 'NumberOfTrues' => [ 'shape' => 'NonNegativeLong', ], 'NumberOfFalses' => [ 'shape' => 'NonNegativeLong', ], 'NumberOfNulls' => [ 'shape' => 'NonNegativeLong', ], ], ], 'BooleanNullable' => [ 'type' => 'boolean', ], 'BooleanValue' => [ 'type' => 'boolean', ], 'BoundedPartitionValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValueString', ], 'max' => 100, 'min' => 0, ], 'CancelMLTaskRunRequest' => [ 'type' => 'structure', 'required' => [ 'TransformId', 'TaskRunId', ], 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], 'TaskRunId' => [ 'shape' => 'HashString', ], ], ], 'CancelMLTaskRunResponse' => [ 'type' => 'structure', 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], 'TaskRunId' => [ 'shape' => 'HashString', ], 'Status' => [ 'shape' => 'TaskStatusType', ], ], ], 'CatalogEncryptionMode' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'SSE-KMS', ], ], 'CatalogEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'CatalogEntry', ], ], 'CatalogEntry' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', ], 'members' => [ 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], ], ], 'CatalogGetterPageSize' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'CatalogIdString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'CatalogImportStatus' => [ 'type' => 'structure', 'members' => [ 'ImportCompleted' => [ 'shape' => 'Boolean', ], 'ImportTime' => [ 'shape' => 'Timestamp', ], 'ImportedBy' => [ 'shape' => 'NameString', ], ], ], 'CatalogTablesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], 'min' => 1, ], 'CatalogTarget' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'Tables', ], 'members' => [ 'DatabaseName' => [ 'shape' => 'NameString', ], 'Tables' => [ 'shape' => 'CatalogTablesList', ], ], ], 'CatalogTargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CatalogTarget', ], ], 'CheckSchemaVersionValidityInput' => [ 'type' => 'structure', 'required' => [ 'DataFormat', 'SchemaDefinition', ], 'members' => [ 'DataFormat' => [ 'shape' => 'DataFormat', ], 'SchemaDefinition' => [ 'shape' => 'SchemaDefinitionString', ], ], ], 'CheckSchemaVersionValidityResponse' => [ 'type' => 'structure', 'members' => [ 'Valid' => [ 'shape' => 'IsVersionValid', ], 'Error' => [ 'shape' => 'SchemaValidationError', ], ], ], 'Classification' => [ 'type' => 'string', ], 'Classifier' => [ 'type' => 'structure', 'members' => [ 'GrokClassifier' => [ 'shape' => 'GrokClassifier', ], 'XMLClassifier' => [ 'shape' => 'XMLClassifier', ], 'JsonClassifier' => [ 'shape' => 'JsonClassifier', ], 'CsvClassifier' => [ 'shape' => 'CsvClassifier', ], ], ], 'ClassifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Classifier', ], ], 'ClassifierNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], ], 'CloudWatchEncryption' => [ 'type' => 'structure', 'members' => [ 'CloudWatchEncryptionMode' => [ 'shape' => 'CloudWatchEncryptionMode', ], 'KmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], ], ], 'CloudWatchEncryptionMode' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'SSE-KMS', ], ], 'CodeGenArgName' => [ 'type' => 'string', ], 'CodeGenArgValue' => [ 'type' => 'string', ], 'CodeGenEdge' => [ 'type' => 'structure', 'required' => [ 'Source', 'Target', ], 'members' => [ 'Source' => [ 'shape' => 'CodeGenIdentifier', ], 'Target' => [ 'shape' => 'CodeGenIdentifier', ], 'TargetParameter' => [ 'shape' => 'CodeGenArgName', ], ], ], 'CodeGenIdentifier' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[A-Za-z_][A-Za-z0-9_]*', ], 'CodeGenNode' => [ 'type' => 'structure', 'required' => [ 'Id', 'NodeType', 'Args', ], 'members' => [ 'Id' => [ 'shape' => 'CodeGenIdentifier', ], 'NodeType' => [ 'shape' => 'CodeGenNodeType', ], 'Args' => [ 'shape' => 'CodeGenNodeArgs', ], 'LineNumber' => [ 'shape' => 'Integer', ], ], ], 'CodeGenNodeArg' => [ 'type' => 'structure', 'required' => [ 'Name', 'Value', ], 'members' => [ 'Name' => [ 'shape' => 'CodeGenArgName', ], 'Value' => [ 'shape' => 'CodeGenArgValue', ], 'Param' => [ 'shape' => 'Boolean', ], ], ], 'CodeGenNodeArgs' => [ 'type' => 'list', 'member' => [ 'shape' => 'CodeGenNodeArg', ], 'max' => 50, 'min' => 0, ], 'CodeGenNodeType' => [ 'type' => 'string', ], 'Column' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Type' => [ 'shape' => 'ColumnTypeString', ], 'Comment' => [ 'shape' => 'CommentString', ], 'Parameters' => [ 'shape' => 'ParametersMap', ], ], ], 'ColumnError' => [ 'type' => 'structure', 'members' => [ 'ColumnName' => [ 'shape' => 'NameString', ], 'Error' => [ 'shape' => 'ErrorDetail', ], ], ], 'ColumnErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnError', ], ], 'ColumnImportance' => [ 'type' => 'structure', 'members' => [ 'ColumnName' => [ 'shape' => 'NameString', ], 'Importance' => [ 'shape' => 'GenericBoundedDouble', ], ], ], 'ColumnImportanceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnImportance', ], 'max' => 100, 'min' => 0, ], 'ColumnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Column', ], ], 'ColumnNameString' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'ColumnStatistics' => [ 'type' => 'structure', 'required' => [ 'ColumnName', 'ColumnType', 'AnalyzedTime', 'StatisticsData', ], 'members' => [ 'ColumnName' => [ 'shape' => 'NameString', ], 'ColumnType' => [ 'shape' => 'TypeString', ], 'AnalyzedTime' => [ 'shape' => 'Timestamp', ], 'StatisticsData' => [ 'shape' => 'ColumnStatisticsData', ], ], ], 'ColumnStatisticsData' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'Type' => [ 'shape' => 'ColumnStatisticsType', ], 'BooleanColumnStatisticsData' => [ 'shape' => 'BooleanColumnStatisticsData', ], 'DateColumnStatisticsData' => [ 'shape' => 'DateColumnStatisticsData', ], 'DecimalColumnStatisticsData' => [ 'shape' => 'DecimalColumnStatisticsData', ], 'DoubleColumnStatisticsData' => [ 'shape' => 'DoubleColumnStatisticsData', ], 'LongColumnStatisticsData' => [ 'shape' => 'LongColumnStatisticsData', ], 'StringColumnStatisticsData' => [ 'shape' => 'StringColumnStatisticsData', ], 'BinaryColumnStatisticsData' => [ 'shape' => 'BinaryColumnStatisticsData', ], ], ], 'ColumnStatisticsError' => [ 'type' => 'structure', 'members' => [ 'ColumnStatistics' => [ 'shape' => 'ColumnStatistics', ], 'Error' => [ 'shape' => 'ErrorDetail', ], ], ], 'ColumnStatisticsErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnStatisticsError', ], ], 'ColumnStatisticsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnStatistics', ], ], 'ColumnStatisticsType' => [ 'type' => 'string', 'enum' => [ 'BOOLEAN', 'DATE', 'DECIMAL', 'DOUBLE', 'LONG', 'STRING', 'BINARY', ], ], 'ColumnTypeString' => [ 'type' => 'string', 'max' => 131072, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'ColumnValueStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnValuesString', ], ], 'ColumnValuesString' => [ 'type' => 'string', ], 'CommentString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'Comparator' => [ 'type' => 'string', 'enum' => [ 'EQUALS', 'GREATER_THAN', 'LESS_THAN', 'GREATER_THAN_EQUALS', 'LESS_THAN_EQUALS', ], ], 'Compatibility' => [ 'type' => 'string', 'enum' => [ 'NONE', 'DISABLED', 'BACKWARD', 'BACKWARD_ALL', 'FORWARD', 'FORWARD_ALL', 'FULL', 'FULL_ALL', ], ], 'ConcurrentModificationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'ConcurrentRunsExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'Condition' => [ 'type' => 'structure', 'members' => [ 'LogicalOperator' => [ 'shape' => 'LogicalOperator', ], 'JobName' => [ 'shape' => 'NameString', ], 'State' => [ 'shape' => 'JobRunState', ], 'CrawlerName' => [ 'shape' => 'NameString', ], 'CrawlState' => [ 'shape' => 'CrawlState', ], ], ], 'ConditionCheckFailureException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'ConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Condition', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'ConfusionMatrix' => [ 'type' => 'structure', 'members' => [ 'NumTruePositives' => [ 'shape' => 'RecordsCount', ], 'NumFalsePositives' => [ 'shape' => 'RecordsCount', ], 'NumTrueNegatives' => [ 'shape' => 'RecordsCount', ], 'NumFalseNegatives' => [ 'shape' => 'RecordsCount', ], ], ], 'Connection' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'ConnectionType' => [ 'shape' => 'ConnectionType', ], 'MatchCriteria' => [ 'shape' => 'MatchCriteria', ], 'ConnectionProperties' => [ 'shape' => 'ConnectionProperties', ], 'PhysicalConnectionRequirements' => [ 'shape' => 'PhysicalConnectionRequirements', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'LastUpdatedBy' => [ 'shape' => 'NameString', ], ], ], 'ConnectionInput' => [ 'type' => 'structure', 'required' => [ 'Name', 'ConnectionType', 'ConnectionProperties', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'ConnectionType' => [ 'shape' => 'ConnectionType', ], 'MatchCriteria' => [ 'shape' => 'MatchCriteria', ], 'ConnectionProperties' => [ 'shape' => 'ConnectionProperties', ], 'PhysicalConnectionRequirements' => [ 'shape' => 'PhysicalConnectionRequirements', ], ], ], 'ConnectionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Connection', ], ], 'ConnectionName' => [ 'type' => 'string', ], 'ConnectionPasswordEncryption' => [ 'type' => 'structure', 'required' => [ 'ReturnConnectionPasswordEncrypted', ], 'members' => [ 'ReturnConnectionPasswordEncrypted' => [ 'shape' => 'Boolean', ], 'AwsKmsKeyId' => [ 'shape' => 'NameString', ], ], ], 'ConnectionProperties' => [ 'type' => 'map', 'key' => [ 'shape' => 'ConnectionPropertyKey', ], 'value' => [ 'shape' => 'ValueString', ], 'max' => 100, 'min' => 0, ], 'ConnectionPropertyKey' => [ 'type' => 'string', 'enum' => [ 'HOST', 'PORT', 'USERNAME', 'PASSWORD', 'ENCRYPTED_PASSWORD', 'JDBC_DRIVER_JAR_URI', 'JDBC_DRIVER_CLASS_NAME', 'JDBC_ENGINE', 'JDBC_ENGINE_VERSION', 'CONFIG_FILES', 'INSTANCE_ID', 'JDBC_CONNECTION_URL', 'JDBC_ENFORCE_SSL', 'CUSTOM_JDBC_CERT', 'SKIP_CUSTOM_JDBC_CERT_VALIDATION', 'CUSTOM_JDBC_CERT_STRING', 'CONNECTION_URL', 'KAFKA_BOOTSTRAP_SERVERS', 'KAFKA_SSL_ENABLED', 'KAFKA_CUSTOM_CERT', 'KAFKA_SKIP_CUSTOM_CERT_VALIDATION', 'KAFKA_CLIENT_KEYSTORE', 'KAFKA_CLIENT_KEYSTORE_PASSWORD', 'KAFKA_CLIENT_KEY_PASSWORD', 'ENCRYPTED_KAFKA_CLIENT_KEYSTORE_PASSWORD', 'ENCRYPTED_KAFKA_CLIENT_KEY_PASSWORD', 'SECRET_ID', 'CONNECTOR_URL', 'CONNECTOR_TYPE', 'CONNECTOR_CLASS_NAME', ], ], 'ConnectionType' => [ 'type' => 'string', 'enum' => [ 'JDBC', 'SFTP', 'MONGODB', 'KAFKA', 'NETWORK', 'MARKETPLACE', 'CUSTOM', ], ], 'ConnectionsList' => [ 'type' => 'structure', 'members' => [ 'Connections' => [ 'shape' => 'OrchestrationStringList', ], ], ], 'Crawl' => [ 'type' => 'structure', 'members' => [ 'State' => [ 'shape' => 'CrawlState', ], 'StartedOn' => [ 'shape' => 'TimestampValue', ], 'CompletedOn' => [ 'shape' => 'TimestampValue', ], 'ErrorMessage' => [ 'shape' => 'DescriptionString', ], 'LogGroup' => [ 'shape' => 'LogGroup', ], 'LogStream' => [ 'shape' => 'LogStream', ], ], ], 'CrawlList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Crawl', ], ], 'CrawlState' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'CANCELLING', 'CANCELLED', 'SUCCEEDED', 'FAILED', ], ], 'Crawler' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Role' => [ 'shape' => 'Role', ], 'Targets' => [ 'shape' => 'CrawlerTargets', ], 'DatabaseName' => [ 'shape' => 'DatabaseName', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Classifiers' => [ 'shape' => 'ClassifierNameList', ], 'RecrawlPolicy' => [ 'shape' => 'RecrawlPolicy', ], 'SchemaChangePolicy' => [ 'shape' => 'SchemaChangePolicy', ], 'LineageConfiguration' => [ 'shape' => 'LineageConfiguration', ], 'State' => [ 'shape' => 'CrawlerState', ], 'TablePrefix' => [ 'shape' => 'TablePrefix', ], 'Schedule' => [ 'shape' => 'Schedule', ], 'CrawlElapsedTime' => [ 'shape' => 'MillisecondsCount', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastUpdated' => [ 'shape' => 'Timestamp', ], 'LastCrawl' => [ 'shape' => 'LastCrawlInfo', ], 'Version' => [ 'shape' => 'VersionId', ], 'Configuration' => [ 'shape' => 'CrawlerConfiguration', ], 'CrawlerSecurityConfiguration' => [ 'shape' => 'CrawlerSecurityConfiguration', ], ], ], 'CrawlerConfiguration' => [ 'type' => 'string', ], 'CrawlerLineageSettings' => [ 'type' => 'string', 'enum' => [ 'ENABLE', 'DISABLE', ], ], 'CrawlerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Crawler', ], ], 'CrawlerMetrics' => [ 'type' => 'structure', 'members' => [ 'CrawlerName' => [ 'shape' => 'NameString', ], 'TimeLeftSeconds' => [ 'shape' => 'NonNegativeDouble', ], 'StillEstimating' => [ 'shape' => 'Boolean', ], 'LastRuntimeSeconds' => [ 'shape' => 'NonNegativeDouble', ], 'MedianRuntimeSeconds' => [ 'shape' => 'NonNegativeDouble', ], 'TablesCreated' => [ 'shape' => 'NonNegativeInteger', ], 'TablesUpdated' => [ 'shape' => 'NonNegativeInteger', ], 'TablesDeleted' => [ 'shape' => 'NonNegativeInteger', ], ], ], 'CrawlerMetricsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CrawlerMetrics', ], ], 'CrawlerNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], 'max' => 100, 'min' => 0, ], 'CrawlerNodeDetails' => [ 'type' => 'structure', 'members' => [ 'Crawls' => [ 'shape' => 'CrawlList', ], ], ], 'CrawlerNotRunningException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'CrawlerRunningException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'CrawlerSecurityConfiguration' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'CrawlerState' => [ 'type' => 'string', 'enum' => [ 'READY', 'RUNNING', 'STOPPING', ], ], 'CrawlerStoppingException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'CrawlerTargets' => [ 'type' => 'structure', 'members' => [ 'S3Targets' => [ 'shape' => 'S3TargetList', ], 'JdbcTargets' => [ 'shape' => 'JdbcTargetList', ], 'MongoDBTargets' => [ 'shape' => 'MongoDBTargetList', ], 'DynamoDBTargets' => [ 'shape' => 'DynamoDBTargetList', ], 'CatalogTargets' => [ 'shape' => 'CatalogTargetList', ], ], ], 'CreateBlueprintRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'BlueprintLocation', ], 'members' => [ 'Name' => [ 'shape' => 'OrchestrationNameString', ], 'Description' => [ 'shape' => 'Generic512CharString', ], 'BlueprintLocation' => [ 'shape' => 'OrchestrationS3Location', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateBlueprintResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'CreateClassifierRequest' => [ 'type' => 'structure', 'members' => [ 'GrokClassifier' => [ 'shape' => 'CreateGrokClassifierRequest', ], 'XMLClassifier' => [ 'shape' => 'CreateXMLClassifierRequest', ], 'JsonClassifier' => [ 'shape' => 'CreateJsonClassifierRequest', ], 'CsvClassifier' => [ 'shape' => 'CreateCsvClassifierRequest', ], ], ], 'CreateClassifierResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectionInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'ConnectionInput' => [ 'shape' => 'ConnectionInput', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateConnectionResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateCrawlerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Role', 'Targets', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Role' => [ 'shape' => 'Role', ], 'DatabaseName' => [ 'shape' => 'DatabaseName', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Targets' => [ 'shape' => 'CrawlerTargets', ], 'Schedule' => [ 'shape' => 'CronExpression', ], 'Classifiers' => [ 'shape' => 'ClassifierNameList', ], 'TablePrefix' => [ 'shape' => 'TablePrefix', ], 'SchemaChangePolicy' => [ 'shape' => 'SchemaChangePolicy', ], 'RecrawlPolicy' => [ 'shape' => 'RecrawlPolicy', ], 'LineageConfiguration' => [ 'shape' => 'LineageConfiguration', ], 'Configuration' => [ 'shape' => 'CrawlerConfiguration', ], 'CrawlerSecurityConfiguration' => [ 'shape' => 'CrawlerSecurityConfiguration', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateCrawlerResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateCsvClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Delimiter' => [ 'shape' => 'CsvColumnDelimiter', ], 'QuoteSymbol' => [ 'shape' => 'CsvQuoteSymbol', ], 'ContainsHeader' => [ 'shape' => 'CsvHeaderOption', ], 'Header' => [ 'shape' => 'CsvHeader', ], 'DisableValueTrimming' => [ 'shape' => 'NullableBoolean', ], 'AllowSingleColumn' => [ 'shape' => 'NullableBoolean', ], ], ], 'CreateDatabaseRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseInput' => [ 'shape' => 'DatabaseInput', ], ], ], 'CreateDatabaseResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateDevEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'EndpointName', 'RoleArn', ], 'members' => [ 'EndpointName' => [ 'shape' => 'GenericString', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'SecurityGroupIds' => [ 'shape' => 'StringList', ], 'SubnetId' => [ 'shape' => 'GenericString', ], 'PublicKey' => [ 'shape' => 'GenericString', ], 'PublicKeys' => [ 'shape' => 'PublicKeysList', ], 'NumberOfNodes' => [ 'shape' => 'IntegerValue', ], 'WorkerType' => [ 'shape' => 'WorkerType', ], 'GlueVersion' => [ 'shape' => 'GlueVersionString', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], 'ExtraPythonLibsS3Path' => [ 'shape' => 'GenericString', ], 'ExtraJarsS3Path' => [ 'shape' => 'GenericString', ], 'SecurityConfiguration' => [ 'shape' => 'NameString', ], 'Tags' => [ 'shape' => 'TagsMap', ], 'Arguments' => [ 'shape' => 'MapValue', ], ], ], 'CreateDevEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'EndpointName' => [ 'shape' => 'GenericString', ], 'Status' => [ 'shape' => 'GenericString', ], 'SecurityGroupIds' => [ 'shape' => 'StringList', ], 'SubnetId' => [ 'shape' => 'GenericString', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'YarnEndpointAddress' => [ 'shape' => 'GenericString', ], 'ZeppelinRemoteSparkInterpreterPort' => [ 'shape' => 'IntegerValue', ], 'NumberOfNodes' => [ 'shape' => 'IntegerValue', ], 'WorkerType' => [ 'shape' => 'WorkerType', ], 'GlueVersion' => [ 'shape' => 'GlueVersionString', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], 'AvailabilityZone' => [ 'shape' => 'GenericString', ], 'VpcId' => [ 'shape' => 'GenericString', ], 'ExtraPythonLibsS3Path' => [ 'shape' => 'GenericString', ], 'ExtraJarsS3Path' => [ 'shape' => 'GenericString', ], 'FailureReason' => [ 'shape' => 'GenericString', ], 'SecurityConfiguration' => [ 'shape' => 'NameString', ], 'CreatedTimestamp' => [ 'shape' => 'TimestampValue', ], 'Arguments' => [ 'shape' => 'MapValue', ], ], ], 'CreateGrokClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'Classification', 'Name', 'GrokPattern', ], 'members' => [ 'Classification' => [ 'shape' => 'Classification', ], 'Name' => [ 'shape' => 'NameString', ], 'GrokPattern' => [ 'shape' => 'GrokPattern', ], 'CustomPatterns' => [ 'shape' => 'CustomPatterns', ], ], ], 'CreateJobRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Role', 'Command', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'LogUri' => [ 'shape' => 'UriString', ], 'Role' => [ 'shape' => 'RoleString', ], 'ExecutionProperty' => [ 'shape' => 'ExecutionProperty', ], 'Command' => [ 'shape' => 'JobCommand', ], 'DefaultArguments' => [ 'shape' => 'GenericMap', ], 'NonOverridableArguments' => [ 'shape' => 'GenericMap', ], 'Connections' => [ 'shape' => 'ConnectionsList', ], 'MaxRetries' => [ 'shape' => 'MaxRetries', ], 'AllocatedCapacity' => [ 'shape' => 'IntegerValue', 'deprecated' => true, 'deprecatedMessage' => 'This property is deprecated, use MaxCapacity instead.', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'MaxCapacity' => [ 'shape' => 'NullableDouble', ], 'SecurityConfiguration' => [ 'shape' => 'NameString', ], 'Tags' => [ 'shape' => 'TagsMap', ], 'NotificationProperty' => [ 'shape' => 'NotificationProperty', ], 'GlueVersion' => [ 'shape' => 'GlueVersionString', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], 'WorkerType' => [ 'shape' => 'WorkerType', ], ], ], 'CreateJobResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'CreateJsonClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'JsonPath', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'JsonPath' => [ 'shape' => 'JsonPath', ], ], ], 'CreateMLTransformRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'InputRecordTables', 'Parameters', 'Role', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'InputRecordTables' => [ 'shape' => 'GlueTables', ], 'Parameters' => [ 'shape' => 'TransformParameters', ], 'Role' => [ 'shape' => 'RoleString', ], 'GlueVersion' => [ 'shape' => 'GlueVersionString', ], 'MaxCapacity' => [ 'shape' => 'NullableDouble', ], 'WorkerType' => [ 'shape' => 'WorkerType', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'MaxRetries' => [ 'shape' => 'NullableInteger', ], 'Tags' => [ 'shape' => 'TagsMap', ], 'TransformEncryption' => [ 'shape' => 'TransformEncryption', ], ], ], 'CreateMLTransformResponse' => [ 'type' => 'structure', 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], ], ], 'CreatePartitionIndexRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'PartitionIndex', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionIndex' => [ 'shape' => 'PartitionIndex', ], ], ], 'CreatePartitionIndexResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreatePartitionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'PartitionInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionInput' => [ 'shape' => 'PartitionInput', ], ], ], 'CreatePartitionResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateRegistryInput' => [ 'type' => 'structure', 'required' => [ 'RegistryName', ], 'members' => [ 'RegistryName' => [ 'shape' => 'SchemaRegistryNameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateRegistryResponse' => [ 'type' => 'structure', 'members' => [ 'RegistryArn' => [ 'shape' => 'GlueResourceArn', ], 'RegistryName' => [ 'shape' => 'SchemaRegistryNameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateSchemaInput' => [ 'type' => 'structure', 'required' => [ 'SchemaName', 'DataFormat', ], 'members' => [ 'RegistryId' => [ 'shape' => 'RegistryId', ], 'SchemaName' => [ 'shape' => 'SchemaRegistryNameString', ], 'DataFormat' => [ 'shape' => 'DataFormat', ], 'Compatibility' => [ 'shape' => 'Compatibility', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Tags' => [ 'shape' => 'TagsMap', ], 'SchemaDefinition' => [ 'shape' => 'SchemaDefinitionString', ], ], ], 'CreateSchemaResponse' => [ 'type' => 'structure', 'members' => [ 'RegistryName' => [ 'shape' => 'SchemaRegistryNameString', ], 'RegistryArn' => [ 'shape' => 'GlueResourceArn', ], 'SchemaName' => [ 'shape' => 'SchemaRegistryNameString', ], 'SchemaArn' => [ 'shape' => 'GlueResourceArn', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'DataFormat' => [ 'shape' => 'DataFormat', ], 'Compatibility' => [ 'shape' => 'Compatibility', ], 'SchemaCheckpoint' => [ 'shape' => 'SchemaCheckpointNumber', ], 'LatestSchemaVersion' => [ 'shape' => 'VersionLongNumber', ], 'NextSchemaVersion' => [ 'shape' => 'VersionLongNumber', ], 'SchemaStatus' => [ 'shape' => 'SchemaStatus', ], 'Tags' => [ 'shape' => 'TagsMap', ], 'SchemaVersionId' => [ 'shape' => 'SchemaVersionIdString', ], 'SchemaVersionStatus' => [ 'shape' => 'SchemaVersionStatus', ], ], ], 'CreateScriptRequest' => [ 'type' => 'structure', 'members' => [ 'DagNodes' => [ 'shape' => 'DagNodes', ], 'DagEdges' => [ 'shape' => 'DagEdges', ], 'Language' => [ 'shape' => 'Language', ], ], ], 'CreateScriptResponse' => [ 'type' => 'structure', 'members' => [ 'PythonScript' => [ 'shape' => 'PythonScript', ], 'ScalaCode' => [ 'shape' => 'ScalaCode', ], ], ], 'CreateSecurityConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'EncryptionConfiguration', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'EncryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], ], ], 'CreateSecurityConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'CreatedTimestamp' => [ 'shape' => 'TimestampValue', ], ], ], 'CreateTableRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableInput' => [ 'shape' => 'TableInput', ], 'PartitionIndexes' => [ 'shape' => 'PartitionIndexList', ], 'TransactionId' => [ 'shape' => 'TransactionIdString', ], ], ], 'CreateTableResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateTriggerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Type', 'Actions', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'WorkflowName' => [ 'shape' => 'NameString', ], 'Type' => [ 'shape' => 'TriggerType', ], 'Schedule' => [ 'shape' => 'GenericString', ], 'Predicate' => [ 'shape' => 'Predicate', ], 'Actions' => [ 'shape' => 'ActionList', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'StartOnCreation' => [ 'shape' => 'BooleanValue', ], 'Tags' => [ 'shape' => 'TagsMap', ], 'EventBatchingCondition' => [ 'shape' => 'EventBatchingCondition', ], ], ], 'CreateTriggerResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'CreateUserDefinedFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'FunctionInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'FunctionInput' => [ 'shape' => 'UserDefinedFunctionInput', ], ], ], 'CreateUserDefinedFunctionResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateWorkflowRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'GenericString', ], 'DefaultRunProperties' => [ 'shape' => 'WorkflowRunProperties', ], 'Tags' => [ 'shape' => 'TagsMap', ], 'MaxConcurrentRuns' => [ 'shape' => 'NullableInteger', ], ], ], 'CreateWorkflowResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'CreateXMLClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'Classification', 'Name', ], 'members' => [ 'Classification' => [ 'shape' => 'Classification', ], 'Name' => [ 'shape' => 'NameString', ], 'RowTag' => [ 'shape' => 'RowTag', ], ], ], 'CreatedTimestamp' => [ 'type' => 'string', ], 'CronExpression' => [ 'type' => 'string', ], 'CsvClassifier' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastUpdated' => [ 'shape' => 'Timestamp', ], 'Version' => [ 'shape' => 'VersionId', ], 'Delimiter' => [ 'shape' => 'CsvColumnDelimiter', ], 'QuoteSymbol' => [ 'shape' => 'CsvQuoteSymbol', ], 'ContainsHeader' => [ 'shape' => 'CsvHeaderOption', ], 'Header' => [ 'shape' => 'CsvHeader', ], 'DisableValueTrimming' => [ 'shape' => 'NullableBoolean', ], 'AllowSingleColumn' => [ 'shape' => 'NullableBoolean', ], ], ], 'CsvColumnDelimiter' => [ 'type' => 'string', 'max' => 1, 'min' => 1, 'pattern' => '[^\\r\\n]', ], 'CsvHeader' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], ], 'CsvHeaderOption' => [ 'type' => 'string', 'enum' => [ 'UNKNOWN', 'PRESENT', 'ABSENT', ], ], 'CsvQuoteSymbol' => [ 'type' => 'string', 'max' => 1, 'min' => 1, 'pattern' => '[^\\r\\n]', ], 'CustomPatterns' => [ 'type' => 'string', 'max' => 16000, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'DagEdges' => [ 'type' => 'list', 'member' => [ 'shape' => 'CodeGenEdge', ], ], 'DagNodes' => [ 'type' => 'list', 'member' => [ 'shape' => 'CodeGenNode', ], ], 'DataCatalogEncryptionSettings' => [ 'type' => 'structure', 'members' => [ 'EncryptionAtRest' => [ 'shape' => 'EncryptionAtRest', ], 'ConnectionPasswordEncryption' => [ 'shape' => 'ConnectionPasswordEncryption', ], ], ], 'DataFormat' => [ 'type' => 'string', 'enum' => [ 'AVRO', 'JSON', ], ], 'DataLakePrincipal' => [ 'type' => 'structure', 'members' => [ 'DataLakePrincipalIdentifier' => [ 'shape' => 'DataLakePrincipalString', ], ], ], 'DataLakePrincipalString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'Database' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'LocationUri' => [ 'shape' => 'URI', ], 'Parameters' => [ 'shape' => 'ParametersMap', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'CreateTableDefaultPermissions' => [ 'shape' => 'PrincipalPermissionsList', ], 'TargetDatabase' => [ 'shape' => 'DatabaseIdentifier', ], 'CatalogId' => [ 'shape' => 'CatalogIdString', ], ], ], 'DatabaseIdentifier' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], ], ], 'DatabaseInput' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'LocationUri' => [ 'shape' => 'URI', ], 'Parameters' => [ 'shape' => 'ParametersMap', ], 'CreateTableDefaultPermissions' => [ 'shape' => 'PrincipalPermissionsList', ], 'TargetDatabase' => [ 'shape' => 'DatabaseIdentifier', ], ], ], 'DatabaseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Database', ], ], 'DatabaseName' => [ 'type' => 'string', ], 'DateColumnStatisticsData' => [ 'type' => 'structure', 'required' => [ 'NumberOfNulls', 'NumberOfDistinctValues', ], 'members' => [ 'MinimumValue' => [ 'shape' => 'Timestamp', ], 'MaximumValue' => [ 'shape' => 'Timestamp', ], 'NumberOfNulls' => [ 'shape' => 'NonNegativeLong', ], 'NumberOfDistinctValues' => [ 'shape' => 'NonNegativeLong', ], ], ], 'DecimalColumnStatisticsData' => [ 'type' => 'structure', 'required' => [ 'NumberOfNulls', 'NumberOfDistinctValues', ], 'members' => [ 'MinimumValue' => [ 'shape' => 'DecimalNumber', ], 'MaximumValue' => [ 'shape' => 'DecimalNumber', ], 'NumberOfNulls' => [ 'shape' => 'NonNegativeLong', ], 'NumberOfDistinctValues' => [ 'shape' => 'NonNegativeLong', ], ], ], 'DecimalNumber' => [ 'type' => 'structure', 'required' => [ 'UnscaledValue', 'Scale', ], 'members' => [ 'UnscaledValue' => [ 'shape' => 'Blob', ], 'Scale' => [ 'shape' => 'Integer', ], ], ], 'DeleteBehavior' => [ 'type' => 'string', 'enum' => [ 'LOG', 'DELETE_FROM_DATABASE', 'DEPRECATE_IN_DATABASE', ], ], 'DeleteBlueprintRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteBlueprintResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteClassifierResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteColumnStatisticsForPartitionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'PartitionValues', 'ColumnName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionValues' => [ 'shape' => 'ValueStringList', ], 'ColumnName' => [ 'shape' => 'NameString', ], ], ], 'DeleteColumnStatisticsForPartitionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteColumnStatisticsForTableRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'ColumnName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'ColumnName' => [ 'shape' => 'NameString', ], ], ], 'DeleteColumnStatisticsForTableResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteConnectionNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], 'max' => 25, 'min' => 0, ], 'DeleteConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectionName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'ConnectionName' => [ 'shape' => 'NameString', ], ], ], 'DeleteConnectionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteCrawlerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteCrawlerResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDatabaseRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteDatabaseResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDevEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'EndpointName', ], 'members' => [ 'EndpointName' => [ 'shape' => 'GenericString', ], ], ], 'DeleteDevEndpointResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobName', ], 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], ], ], 'DeleteJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], ], ], 'DeleteMLTransformRequest' => [ 'type' => 'structure', 'required' => [ 'TransformId', ], 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], ], ], 'DeleteMLTransformResponse' => [ 'type' => 'structure', 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], ], ], 'DeletePartitionIndexRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'IndexName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'IndexName' => [ 'shape' => 'NameString', ], ], ], 'DeletePartitionIndexResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeletePartitionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'PartitionValues', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionValues' => [ 'shape' => 'ValueStringList', ], ], ], 'DeletePartitionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRegistryInput' => [ 'type' => 'structure', 'required' => [ 'RegistryId', ], 'members' => [ 'RegistryId' => [ 'shape' => 'RegistryId', ], ], ], 'DeleteRegistryResponse' => [ 'type' => 'structure', 'members' => [ 'RegistryName' => [ 'shape' => 'SchemaRegistryNameString', ], 'RegistryArn' => [ 'shape' => 'GlueResourceArn', ], 'Status' => [ 'shape' => 'RegistryStatus', ], ], ], 'DeleteResourcePolicyRequest' => [ 'type' => 'structure', 'members' => [ 'PolicyHashCondition' => [ 'shape' => 'HashString', ], 'ResourceArn' => [ 'shape' => 'GlueResourceArn', ], ], ], 'DeleteResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSchemaInput' => [ 'type' => 'structure', 'required' => [ 'SchemaId', ], 'members' => [ 'SchemaId' => [ 'shape' => 'SchemaId', ], ], ], 'DeleteSchemaResponse' => [ 'type' => 'structure', 'members' => [ 'SchemaArn' => [ 'shape' => 'GlueResourceArn', ], 'SchemaName' => [ 'shape' => 'SchemaRegistryNameString', ], 'Status' => [ 'shape' => 'SchemaStatus', ], ], ], 'DeleteSchemaVersionsInput' => [ 'type' => 'structure', 'required' => [ 'SchemaId', 'Versions', ], 'members' => [ 'SchemaId' => [ 'shape' => 'SchemaId', ], 'Versions' => [ 'shape' => 'VersionsString', ], ], ], 'DeleteSchemaVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'SchemaVersionErrors' => [ 'shape' => 'SchemaVersionErrorList', ], ], ], 'DeleteSecurityConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteSecurityConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTableRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'Name', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'Name' => [ 'shape' => 'NameString', ], 'TransactionId' => [ 'shape' => 'TransactionIdString', ], ], ], 'DeleteTableResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTableVersionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'VersionId', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'VersionId' => [ 'shape' => 'VersionString', ], ], ], 'DeleteTableVersionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTriggerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteTriggerResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteUserDefinedFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'FunctionName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'FunctionName' => [ 'shape' => 'NameString', ], ], ], 'DeleteUserDefinedFunctionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteWorkflowRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteWorkflowResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'DescriptionString' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'DescriptionStringRemovable' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'DevEndpoint' => [ 'type' => 'structure', 'members' => [ 'EndpointName' => [ 'shape' => 'GenericString', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'SecurityGroupIds' => [ 'shape' => 'StringList', ], 'SubnetId' => [ 'shape' => 'GenericString', ], 'YarnEndpointAddress' => [ 'shape' => 'GenericString', ], 'PrivateAddress' => [ 'shape' => 'GenericString', ], 'ZeppelinRemoteSparkInterpreterPort' => [ 'shape' => 'IntegerValue', ], 'PublicAddress' => [ 'shape' => 'GenericString', ], 'Status' => [ 'shape' => 'GenericString', ], 'WorkerType' => [ 'shape' => 'WorkerType', ], 'GlueVersion' => [ 'shape' => 'GlueVersionString', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], 'NumberOfNodes' => [ 'shape' => 'IntegerValue', ], 'AvailabilityZone' => [ 'shape' => 'GenericString', ], 'VpcId' => [ 'shape' => 'GenericString', ], 'ExtraPythonLibsS3Path' => [ 'shape' => 'GenericString', ], 'ExtraJarsS3Path' => [ 'shape' => 'GenericString', ], 'FailureReason' => [ 'shape' => 'GenericString', ], 'LastUpdateStatus' => [ 'shape' => 'GenericString', ], 'CreatedTimestamp' => [ 'shape' => 'TimestampValue', ], 'LastModifiedTimestamp' => [ 'shape' => 'TimestampValue', ], 'PublicKey' => [ 'shape' => 'GenericString', ], 'PublicKeys' => [ 'shape' => 'PublicKeysList', ], 'SecurityConfiguration' => [ 'shape' => 'NameString', ], 'Arguments' => [ 'shape' => 'MapValue', ], ], ], 'DevEndpointCustomLibraries' => [ 'type' => 'structure', 'members' => [ 'ExtraPythonLibsS3Path' => [ 'shape' => 'GenericString', ], 'ExtraJarsS3Path' => [ 'shape' => 'GenericString', ], ], ], 'DevEndpointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DevEndpoint', ], ], 'DevEndpointNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], ], 'DevEndpointNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'GenericString', ], 'max' => 25, 'min' => 1, ], 'Double' => [ 'type' => 'double', ], 'DoubleColumnStatisticsData' => [ 'type' => 'structure', 'required' => [ 'NumberOfNulls', 'NumberOfDistinctValues', ], 'members' => [ 'MinimumValue' => [ 'shape' => 'Double', ], 'MaximumValue' => [ 'shape' => 'Double', ], 'NumberOfNulls' => [ 'shape' => 'NonNegativeLong', ], 'NumberOfDistinctValues' => [ 'shape' => 'NonNegativeLong', ], ], ], 'DynamoDBTarget' => [ 'type' => 'structure', 'members' => [ 'Path' => [ 'shape' => 'Path', ], 'scanAll' => [ 'shape' => 'NullableBoolean', ], 'scanRate' => [ 'shape' => 'NullableDouble', ], ], ], 'DynamoDBTargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DynamoDBTarget', ], ], 'Edge' => [ 'type' => 'structure', 'members' => [ 'SourceId' => [ 'shape' => 'NameString', ], 'DestinationId' => [ 'shape' => 'NameString', ], ], ], 'EdgeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Edge', ], ], 'EnableHybridValues' => [ 'type' => 'string', 'enum' => [ 'TRUE', 'FALSE', ], ], 'EncryptionAtRest' => [ 'type' => 'structure', 'required' => [ 'CatalogEncryptionMode', ], 'members' => [ 'CatalogEncryptionMode' => [ 'shape' => 'CatalogEncryptionMode', ], 'SseAwsKmsKeyId' => [ 'shape' => 'NameString', ], ], ], 'EncryptionConfiguration' => [ 'type' => 'structure', 'members' => [ 'S3Encryption' => [ 'shape' => 'S3EncryptionList', ], 'CloudWatchEncryption' => [ 'shape' => 'CloudWatchEncryption', ], 'JobBookmarksEncryption' => [ 'shape' => 'JobBookmarksEncryption', ], ], ], 'EntityNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'ErrorByName' => [ 'type' => 'map', 'key' => [ 'shape' => 'NameString', ], 'value' => [ 'shape' => 'ErrorDetail', ], ], 'ErrorCodeString' => [ 'type' => 'string', ], 'ErrorDetail' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'NameString', ], 'ErrorMessage' => [ 'shape' => 'DescriptionString', ], ], ], 'ErrorDetails' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'ErrorCodeString', ], 'ErrorMessage' => [ 'shape' => 'ErrorMessageString', ], ], ], 'ErrorMessageString' => [ 'type' => 'string', ], 'ErrorString' => [ 'type' => 'string', ], 'EvaluationMetrics' => [ 'type' => 'structure', 'required' => [ 'TransformType', ], 'members' => [ 'TransformType' => [ 'shape' => 'TransformType', ], 'FindMatchesMetrics' => [ 'shape' => 'FindMatchesMetrics', ], ], ], 'EventBatchingCondition' => [ 'type' => 'structure', 'required' => [ 'BatchSize', ], 'members' => [ 'BatchSize' => [ 'shape' => 'BatchSize', ], 'BatchWindow' => [ 'shape' => 'BatchWindow', ], ], ], 'EventQueueArn' => [ 'type' => 'string', ], 'ExecutionProperty' => [ 'type' => 'structure', 'members' => [ 'MaxConcurrentRuns' => [ 'shape' => 'MaxConcurrentRuns', ], ], ], 'ExecutionTime' => [ 'type' => 'integer', ], 'ExistCondition' => [ 'type' => 'string', 'enum' => [ 'MUST_EXIST', 'NOT_EXIST', 'NONE', ], ], 'ExportLabelsTaskRunProperties' => [ 'type' => 'structure', 'members' => [ 'OutputS3Path' => [ 'shape' => 'UriString', ], ], ], 'FieldType' => [ 'type' => 'string', ], 'FilterString' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'FindMatchesMetrics' => [ 'type' => 'structure', 'members' => [ 'AreaUnderPRCurve' => [ 'shape' => 'GenericBoundedDouble', ], 'Precision' => [ 'shape' => 'GenericBoundedDouble', ], 'Recall' => [ 'shape' => 'GenericBoundedDouble', ], 'F1' => [ 'shape' => 'GenericBoundedDouble', ], 'ConfusionMatrix' => [ 'shape' => 'ConfusionMatrix', ], 'ColumnImportances' => [ 'shape' => 'ColumnImportanceList', ], ], ], 'FindMatchesParameters' => [ 'type' => 'structure', 'members' => [ 'PrimaryKeyColumnName' => [ 'shape' => 'ColumnNameString', ], 'PrecisionRecallTradeoff' => [ 'shape' => 'GenericBoundedDouble', ], 'AccuracyCostTradeoff' => [ 'shape' => 'GenericBoundedDouble', ], 'EnforceProvidedLabels' => [ 'shape' => 'NullableBoolean', ], ], ], 'FindMatchesTaskRunProperties' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => 'HashString', ], 'JobName' => [ 'shape' => 'NameString', ], 'JobRunId' => [ 'shape' => 'HashString', ], ], ], 'FormatString' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'Generic512CharString' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'GenericBoundedDouble' => [ 'type' => 'double', 'box' => true, 'max' => 1, 'min' => 0, ], 'GenericMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'GenericString', ], 'value' => [ 'shape' => 'GenericString', ], ], 'GenericString' => [ 'type' => 'string', ], 'GetBlueprintRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'IncludeBlueprint' => [ 'shape' => 'NullableBoolean', ], 'IncludeParameterSpec' => [ 'shape' => 'NullableBoolean', ], ], ], 'GetBlueprintResponse' => [ 'type' => 'structure', 'members' => [ 'Blueprint' => [ 'shape' => 'Blueprint', ], ], ], 'GetBlueprintRunRequest' => [ 'type' => 'structure', 'required' => [ 'BlueprintName', 'RunId', ], 'members' => [ 'BlueprintName' => [ 'shape' => 'OrchestrationNameString', ], 'RunId' => [ 'shape' => 'IdString', ], ], ], 'GetBlueprintRunResponse' => [ 'type' => 'structure', 'members' => [ 'BlueprintRun' => [ 'shape' => 'BlueprintRun', ], ], ], 'GetBlueprintRunsRequest' => [ 'type' => 'structure', 'required' => [ 'BlueprintName', ], 'members' => [ 'BlueprintName' => [ 'shape' => 'NameString', ], 'NextToken' => [ 'shape' => 'GenericString', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'GetBlueprintRunsResponse' => [ 'type' => 'structure', 'members' => [ 'BlueprintRuns' => [ 'shape' => 'BlueprintRuns', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'GetCatalogImportStatusRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], ], ], 'GetCatalogImportStatusResponse' => [ 'type' => 'structure', 'members' => [ 'ImportStatus' => [ 'shape' => 'CatalogImportStatus', ], ], ], 'GetClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'GetClassifierResponse' => [ 'type' => 'structure', 'members' => [ 'Classifier' => [ 'shape' => 'Classifier', ], ], ], 'GetClassifiersRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetClassifiersResponse' => [ 'type' => 'structure', 'members' => [ 'Classifiers' => [ 'shape' => 'ClassifierList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetColumnNamesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], 'max' => 100, 'min' => 0, ], 'GetColumnStatisticsForPartitionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'PartitionValues', 'ColumnNames', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionValues' => [ 'shape' => 'ValueStringList', ], 'ColumnNames' => [ 'shape' => 'GetColumnNamesList', ], ], ], 'GetColumnStatisticsForPartitionResponse' => [ 'type' => 'structure', 'members' => [ 'ColumnStatisticsList' => [ 'shape' => 'ColumnStatisticsList', ], 'Errors' => [ 'shape' => 'ColumnErrors', ], ], ], 'GetColumnStatisticsForTableRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'ColumnNames', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'ColumnNames' => [ 'shape' => 'GetColumnNamesList', ], ], ], 'GetColumnStatisticsForTableResponse' => [ 'type' => 'structure', 'members' => [ 'ColumnStatisticsList' => [ 'shape' => 'ColumnStatisticsList', ], 'Errors' => [ 'shape' => 'ColumnErrors', ], ], ], 'GetConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Name' => [ 'shape' => 'NameString', ], 'HidePassword' => [ 'shape' => 'Boolean', ], ], ], 'GetConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'Connection' => [ 'shape' => 'Connection', ], ], ], 'GetConnectionsFilter' => [ 'type' => 'structure', 'members' => [ 'MatchCriteria' => [ 'shape' => 'MatchCriteria', ], 'ConnectionType' => [ 'shape' => 'ConnectionType', ], ], ], 'GetConnectionsRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Filter' => [ 'shape' => 'GetConnectionsFilter', ], 'HidePassword' => [ 'shape' => 'Boolean', ], 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'GetConnectionsResponse' => [ 'type' => 'structure', 'members' => [ 'ConnectionList' => [ 'shape' => 'ConnectionList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetCrawlerMetricsRequest' => [ 'type' => 'structure', 'members' => [ 'CrawlerNameList' => [ 'shape' => 'CrawlerNameList', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetCrawlerMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'CrawlerMetricsList' => [ 'shape' => 'CrawlerMetricsList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetCrawlerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'GetCrawlerResponse' => [ 'type' => 'structure', 'members' => [ 'Crawler' => [ 'shape' => 'Crawler', ], ], ], 'GetCrawlersRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetCrawlersResponse' => [ 'type' => 'structure', 'members' => [ 'Crawlers' => [ 'shape' => 'CrawlerList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetDataCatalogEncryptionSettingsRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], ], ], 'GetDataCatalogEncryptionSettingsResponse' => [ 'type' => 'structure', 'members' => [ 'DataCatalogEncryptionSettings' => [ 'shape' => 'DataCatalogEncryptionSettings', ], ], ], 'GetDatabaseRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Name' => [ 'shape' => 'NameString', ], ], ], 'GetDatabaseResponse' => [ 'type' => 'structure', 'members' => [ 'Database' => [ 'shape' => 'Database', ], ], ], 'GetDatabasesRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'CatalogGetterPageSize', ], 'ResourceShareType' => [ 'shape' => 'ResourceShareType', ], ], ], 'GetDatabasesResponse' => [ 'type' => 'structure', 'required' => [ 'DatabaseList', ], 'members' => [ 'DatabaseList' => [ 'shape' => 'DatabaseList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetDataflowGraphRequest' => [ 'type' => 'structure', 'members' => [ 'PythonScript' => [ 'shape' => 'PythonScript', ], ], ], 'GetDataflowGraphResponse' => [ 'type' => 'structure', 'members' => [ 'DagNodes' => [ 'shape' => 'DagNodes', ], 'DagEdges' => [ 'shape' => 'DagEdges', ], ], ], 'GetDevEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'EndpointName', ], 'members' => [ 'EndpointName' => [ 'shape' => 'GenericString', ], ], ], 'GetDevEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'DevEndpoint' => [ 'shape' => 'DevEndpoint', ], ], ], 'GetDevEndpointsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'GetDevEndpointsResponse' => [ 'type' => 'structure', 'members' => [ 'DevEndpoints' => [ 'shape' => 'DevEndpointList', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'GetJobBookmarkRequest' => [ 'type' => 'structure', 'required' => [ 'JobName', ], 'members' => [ 'JobName' => [ 'shape' => 'JobName', ], 'RunId' => [ 'shape' => 'RunId', ], ], ], 'GetJobBookmarkResponse' => [ 'type' => 'structure', 'members' => [ 'JobBookmarkEntry' => [ 'shape' => 'JobBookmarkEntry', ], ], ], 'GetJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobName', ], 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], ], ], 'GetJobResponse' => [ 'type' => 'structure', 'members' => [ 'Job' => [ 'shape' => 'Job', ], ], ], 'GetJobRunRequest' => [ 'type' => 'structure', 'required' => [ 'JobName', 'RunId', ], 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], 'RunId' => [ 'shape' => 'IdString', ], 'PredecessorsIncluded' => [ 'shape' => 'BooleanValue', ], ], ], 'GetJobRunResponse' => [ 'type' => 'structure', 'members' => [ 'JobRun' => [ 'shape' => 'JobRun', ], ], ], 'GetJobRunsRequest' => [ 'type' => 'structure', 'required' => [ 'JobName', ], 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], 'NextToken' => [ 'shape' => 'GenericString', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'GetJobRunsResponse' => [ 'type' => 'structure', 'members' => [ 'JobRuns' => [ 'shape' => 'JobRunList', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'GetJobsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'GenericString', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'GetJobsResponse' => [ 'type' => 'structure', 'members' => [ 'Jobs' => [ 'shape' => 'JobList', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'GetMLTaskRunRequest' => [ 'type' => 'structure', 'required' => [ 'TransformId', 'TaskRunId', ], 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], 'TaskRunId' => [ 'shape' => 'HashString', ], ], ], 'GetMLTaskRunResponse' => [ 'type' => 'structure', 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], 'TaskRunId' => [ 'shape' => 'HashString', ], 'Status' => [ 'shape' => 'TaskStatusType', ], 'LogGroupName' => [ 'shape' => 'GenericString', ], 'Properties' => [ 'shape' => 'TaskRunProperties', ], 'ErrorString' => [ 'shape' => 'GenericString', ], 'StartedOn' => [ 'shape' => 'Timestamp', ], 'LastModifiedOn' => [ 'shape' => 'Timestamp', ], 'CompletedOn' => [ 'shape' => 'Timestamp', ], 'ExecutionTime' => [ 'shape' => 'ExecutionTime', ], ], ], 'GetMLTaskRunsRequest' => [ 'type' => 'structure', 'required' => [ 'TransformId', ], 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'Filter' => [ 'shape' => 'TaskRunFilterCriteria', ], 'Sort' => [ 'shape' => 'TaskRunSortCriteria', ], ], ], 'GetMLTaskRunsResponse' => [ 'type' => 'structure', 'members' => [ 'TaskRuns' => [ 'shape' => 'TaskRunList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'GetMLTransformRequest' => [ 'type' => 'structure', 'required' => [ 'TransformId', ], 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], ], ], 'GetMLTransformResponse' => [ 'type' => 'structure', 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Status' => [ 'shape' => 'TransformStatusType', ], 'CreatedOn' => [ 'shape' => 'Timestamp', ], 'LastModifiedOn' => [ 'shape' => 'Timestamp', ], 'InputRecordTables' => [ 'shape' => 'GlueTables', ], 'Parameters' => [ 'shape' => 'TransformParameters', ], 'EvaluationMetrics' => [ 'shape' => 'EvaluationMetrics', ], 'LabelCount' => [ 'shape' => 'LabelCount', ], 'Schema' => [ 'shape' => 'TransformSchema', ], 'Role' => [ 'shape' => 'RoleString', ], 'GlueVersion' => [ 'shape' => 'GlueVersionString', ], 'MaxCapacity' => [ 'shape' => 'NullableDouble', ], 'WorkerType' => [ 'shape' => 'WorkerType', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'MaxRetries' => [ 'shape' => 'NullableInteger', ], 'TransformEncryption' => [ 'shape' => 'TransformEncryption', ], ], ], 'GetMLTransformsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'Filter' => [ 'shape' => 'TransformFilterCriteria', ], 'Sort' => [ 'shape' => 'TransformSortCriteria', ], ], ], 'GetMLTransformsResponse' => [ 'type' => 'structure', 'required' => [ 'Transforms', ], 'members' => [ 'Transforms' => [ 'shape' => 'TransformList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'GetMappingRequest' => [ 'type' => 'structure', 'required' => [ 'Source', ], 'members' => [ 'Source' => [ 'shape' => 'CatalogEntry', ], 'Sinks' => [ 'shape' => 'CatalogEntries', ], 'Location' => [ 'shape' => 'Location', ], ], ], 'GetMappingResponse' => [ 'type' => 'structure', 'required' => [ 'Mapping', ], 'members' => [ 'Mapping' => [ 'shape' => 'MappingList', ], ], ], 'GetPartitionIndexesRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetPartitionIndexesResponse' => [ 'type' => 'structure', 'members' => [ 'PartitionIndexDescriptorList' => [ 'shape' => 'PartitionIndexDescriptorList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetPartitionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'PartitionValues', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionValues' => [ 'shape' => 'ValueStringList', ], ], ], 'GetPartitionResponse' => [ 'type' => 'structure', 'members' => [ 'Partition' => [ 'shape' => 'Partition', ], ], ], 'GetPartitionsRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'Expression' => [ 'shape' => 'PredicateString', ], 'NextToken' => [ 'shape' => 'Token', ], 'Segment' => [ 'shape' => 'Segment', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'ExcludeColumnSchema' => [ 'shape' => 'BooleanNullable', ], 'TransactionId' => [ 'shape' => 'TransactionIdString', ], 'QueryAsOfTime' => [ 'shape' => 'Timestamp', ], ], ], 'GetPartitionsResponse' => [ 'type' => 'structure', 'members' => [ 'Partitions' => [ 'shape' => 'PartitionList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetPlanRequest' => [ 'type' => 'structure', 'required' => [ 'Mapping', 'Source', ], 'members' => [ 'Mapping' => [ 'shape' => 'MappingList', ], 'Source' => [ 'shape' => 'CatalogEntry', ], 'Sinks' => [ 'shape' => 'CatalogEntries', ], 'Location' => [ 'shape' => 'Location', ], 'Language' => [ 'shape' => 'Language', ], 'AdditionalPlanOptionsMap' => [ 'shape' => 'AdditionalPlanOptionsMap', ], ], ], 'GetPlanResponse' => [ 'type' => 'structure', 'members' => [ 'PythonScript' => [ 'shape' => 'PythonScript', ], 'ScalaCode' => [ 'shape' => 'ScalaCode', ], ], ], 'GetRegistryInput' => [ 'type' => 'structure', 'required' => [ 'RegistryId', ], 'members' => [ 'RegistryId' => [ 'shape' => 'RegistryId', ], ], ], 'GetRegistryResponse' => [ 'type' => 'structure', 'members' => [ 'RegistryName' => [ 'shape' => 'SchemaRegistryNameString', ], 'RegistryArn' => [ 'shape' => 'GlueResourceArn', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Status' => [ 'shape' => 'RegistryStatus', ], 'CreatedTime' => [ 'shape' => 'CreatedTimestamp', ], 'UpdatedTime' => [ 'shape' => 'UpdatedTimestamp', ], ], ], 'GetResourcePoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'GetResourcePoliciesResponse' => [ 'type' => 'structure', 'members' => [ 'GetResourcePoliciesResponseList' => [ 'shape' => 'GetResourcePoliciesResponseList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetResourcePoliciesResponseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GluePolicy', ], ], 'GetResourcePolicyRequest' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'GlueResourceArn', ], ], ], 'GetResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'PolicyInJson' => [ 'shape' => 'PolicyJsonString', ], 'PolicyHash' => [ 'shape' => 'HashString', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'GetSchemaByDefinitionInput' => [ 'type' => 'structure', 'required' => [ 'SchemaId', 'SchemaDefinition', ], 'members' => [ 'SchemaId' => [ 'shape' => 'SchemaId', ], 'SchemaDefinition' => [ 'shape' => 'SchemaDefinitionString', ], ], ], 'GetSchemaByDefinitionResponse' => [ 'type' => 'structure', 'members' => [ 'SchemaVersionId' => [ 'shape' => 'SchemaVersionIdString', ], 'SchemaArn' => [ 'shape' => 'GlueResourceArn', ], 'DataFormat' => [ 'shape' => 'DataFormat', ], 'Status' => [ 'shape' => 'SchemaVersionStatus', ], 'CreatedTime' => [ 'shape' => 'CreatedTimestamp', ], ], ], 'GetSchemaInput' => [ 'type' => 'structure', 'required' => [ 'SchemaId', ], 'members' => [ 'SchemaId' => [ 'shape' => 'SchemaId', ], ], ], 'GetSchemaResponse' => [ 'type' => 'structure', 'members' => [ 'RegistryName' => [ 'shape' => 'SchemaRegistryNameString', ], 'RegistryArn' => [ 'shape' => 'GlueResourceArn', ], 'SchemaName' => [ 'shape' => 'SchemaRegistryNameString', ], 'SchemaArn' => [ 'shape' => 'GlueResourceArn', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'DataFormat' => [ 'shape' => 'DataFormat', ], 'Compatibility' => [ 'shape' => 'Compatibility', ], 'SchemaCheckpoint' => [ 'shape' => 'SchemaCheckpointNumber', ], 'LatestSchemaVersion' => [ 'shape' => 'VersionLongNumber', ], 'NextSchemaVersion' => [ 'shape' => 'VersionLongNumber', ], 'SchemaStatus' => [ 'shape' => 'SchemaStatus', ], 'CreatedTime' => [ 'shape' => 'CreatedTimestamp', ], 'UpdatedTime' => [ 'shape' => 'UpdatedTimestamp', ], ], ], 'GetSchemaVersionInput' => [ 'type' => 'structure', 'members' => [ 'SchemaId' => [ 'shape' => 'SchemaId', ], 'SchemaVersionId' => [ 'shape' => 'SchemaVersionIdString', ], 'SchemaVersionNumber' => [ 'shape' => 'SchemaVersionNumber', ], ], ], 'GetSchemaVersionResponse' => [ 'type' => 'structure', 'members' => [ 'SchemaVersionId' => [ 'shape' => 'SchemaVersionIdString', ], 'SchemaDefinition' => [ 'shape' => 'SchemaDefinitionString', ], 'DataFormat' => [ 'shape' => 'DataFormat', ], 'SchemaArn' => [ 'shape' => 'GlueResourceArn', ], 'VersionNumber' => [ 'shape' => 'VersionLongNumber', ], 'Status' => [ 'shape' => 'SchemaVersionStatus', ], 'CreatedTime' => [ 'shape' => 'CreatedTimestamp', ], ], ], 'GetSchemaVersionsDiffInput' => [ 'type' => 'structure', 'required' => [ 'SchemaId', 'FirstSchemaVersionNumber', 'SecondSchemaVersionNumber', 'SchemaDiffType', ], 'members' => [ 'SchemaId' => [ 'shape' => 'SchemaId', ], 'FirstSchemaVersionNumber' => [ 'shape' => 'SchemaVersionNumber', ], 'SecondSchemaVersionNumber' => [ 'shape' => 'SchemaVersionNumber', ], 'SchemaDiffType' => [ 'shape' => 'SchemaDiffType', ], ], ], 'GetSchemaVersionsDiffResponse' => [ 'type' => 'structure', 'members' => [ 'Diff' => [ 'shape' => 'SchemaDefinitionDiff', ], ], ], 'GetSecurityConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'GetSecurityConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'SecurityConfiguration' => [ 'shape' => 'SecurityConfiguration', ], ], ], 'GetSecurityConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'GetSecurityConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'SecurityConfigurations' => [ 'shape' => 'SecurityConfigurationList', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'GetTableRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'Name', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'Name' => [ 'shape' => 'NameString', ], 'TransactionId' => [ 'shape' => 'TransactionIdString', ], 'QueryAsOfTime' => [ 'shape' => 'Timestamp', ], ], ], 'GetTableResponse' => [ 'type' => 'structure', 'members' => [ 'Table' => [ 'shape' => 'Table', ], ], ], 'GetTableVersionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'VersionId' => [ 'shape' => 'VersionString', ], ], ], 'GetTableVersionResponse' => [ 'type' => 'structure', 'members' => [ 'TableVersion' => [ 'shape' => 'TableVersion', ], ], ], 'GetTableVersionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TableVersion', ], ], 'GetTableVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'CatalogGetterPageSize', ], ], ], 'GetTableVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'TableVersions' => [ 'shape' => 'GetTableVersionsList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetTablesRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'Expression' => [ 'shape' => 'FilterString', ], 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'CatalogGetterPageSize', ], 'TransactionId' => [ 'shape' => 'TransactionIdString', ], 'QueryAsOfTime' => [ 'shape' => 'Timestamp', ], ], ], 'GetTablesResponse' => [ 'type' => 'structure', 'members' => [ 'TableList' => [ 'shape' => 'TableList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetTagsRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'GlueResourceArn', ], ], ], 'GetTagsResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'GetTriggerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'GetTriggerResponse' => [ 'type' => 'structure', 'members' => [ 'Trigger' => [ 'shape' => 'Trigger', ], ], ], 'GetTriggersRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'GenericString', ], 'DependentJobName' => [ 'shape' => 'NameString', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'GetTriggersResponse' => [ 'type' => 'structure', 'members' => [ 'Triggers' => [ 'shape' => 'TriggerList', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'GetUserDefinedFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'FunctionName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'FunctionName' => [ 'shape' => 'NameString', ], ], ], 'GetUserDefinedFunctionResponse' => [ 'type' => 'structure', 'members' => [ 'UserDefinedFunction' => [ 'shape' => 'UserDefinedFunction', ], ], ], 'GetUserDefinedFunctionsRequest' => [ 'type' => 'structure', 'required' => [ 'Pattern', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'Pattern' => [ 'shape' => 'NameString', ], 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'CatalogGetterPageSize', ], ], ], 'GetUserDefinedFunctionsResponse' => [ 'type' => 'structure', 'members' => [ 'UserDefinedFunctions' => [ 'shape' => 'UserDefinedFunctionList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetWorkflowRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'IncludeGraph' => [ 'shape' => 'NullableBoolean', ], ], ], 'GetWorkflowResponse' => [ 'type' => 'structure', 'members' => [ 'Workflow' => [ 'shape' => 'Workflow', ], ], ], 'GetWorkflowRunPropertiesRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'RunId', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'RunId' => [ 'shape' => 'IdString', ], ], ], 'GetWorkflowRunPropertiesResponse' => [ 'type' => 'structure', 'members' => [ 'RunProperties' => [ 'shape' => 'WorkflowRunProperties', ], ], ], 'GetWorkflowRunRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'RunId', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'RunId' => [ 'shape' => 'IdString', ], 'IncludeGraph' => [ 'shape' => 'NullableBoolean', ], ], ], 'GetWorkflowRunResponse' => [ 'type' => 'structure', 'members' => [ 'Run' => [ 'shape' => 'WorkflowRun', ], ], ], 'GetWorkflowRunsRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'IncludeGraph' => [ 'shape' => 'NullableBoolean', ], 'NextToken' => [ 'shape' => 'GenericString', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'GetWorkflowRunsResponse' => [ 'type' => 'structure', 'members' => [ 'Runs' => [ 'shape' => 'WorkflowRuns', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'GlueEncryptionException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'GluePolicy' => [ 'type' => 'structure', 'members' => [ 'PolicyInJson' => [ 'shape' => 'PolicyJsonString', ], 'PolicyHash' => [ 'shape' => 'HashString', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'GlueResourceArn' => [ 'type' => 'string', 'max' => 10240, 'min' => 1, 'pattern' => 'arn:aws:glue:.*', ], 'GlueTable' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', ], 'members' => [ 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'CatalogId' => [ 'shape' => 'NameString', ], 'ConnectionName' => [ 'shape' => 'NameString', ], ], ], 'GlueTables' => [ 'type' => 'list', 'member' => [ 'shape' => 'GlueTable', ], 'max' => 10, 'min' => 0, ], 'GlueVersionString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^\\w+\\.\\w+$', ], 'GrokClassifier' => [ 'type' => 'structure', 'required' => [ 'Name', 'Classification', 'GrokPattern', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Classification' => [ 'shape' => 'Classification', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastUpdated' => [ 'shape' => 'Timestamp', ], 'Version' => [ 'shape' => 'VersionId', ], 'GrokPattern' => [ 'shape' => 'GrokPattern', ], 'CustomPatterns' => [ 'shape' => 'CustomPatterns', ], ], ], 'GrokPattern' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\t]*', ], 'HashString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'IdString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'IdempotentParameterMismatchException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'IllegalBlueprintStateException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'IllegalWorkflowStateException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'ImportCatalogToGlueRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], ], ], 'ImportCatalogToGlueResponse' => [ 'type' => 'structure', 'members' => [], ], 'ImportLabelsTaskRunProperties' => [ 'type' => 'structure', 'members' => [ 'InputS3Path' => [ 'shape' => 'UriString', ], 'Replace' => [ 'shape' => 'ReplaceBoolean', ], ], ], 'Integer' => [ 'type' => 'integer', ], 'IntegerFlag' => [ 'type' => 'integer', 'max' => 1, 'min' => 0, ], 'IntegerValue' => [ 'type' => 'integer', ], 'InternalServiceException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, 'fault' => true, ], 'InvalidInputException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'InvalidStateException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'IsVersionValid' => [ 'type' => 'boolean', ], 'JdbcTarget' => [ 'type' => 'structure', 'members' => [ 'ConnectionName' => [ 'shape' => 'ConnectionName', ], 'Path' => [ 'shape' => 'Path', ], 'Exclusions' => [ 'shape' => 'PathList', ], ], ], 'JdbcTargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'JdbcTarget', ], ], 'Job' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'LogUri' => [ 'shape' => 'UriString', ], 'Role' => [ 'shape' => 'RoleString', ], 'CreatedOn' => [ 'shape' => 'TimestampValue', ], 'LastModifiedOn' => [ 'shape' => 'TimestampValue', ], 'ExecutionProperty' => [ 'shape' => 'ExecutionProperty', ], 'Command' => [ 'shape' => 'JobCommand', ], 'DefaultArguments' => [ 'shape' => 'GenericMap', ], 'NonOverridableArguments' => [ 'shape' => 'GenericMap', ], 'Connections' => [ 'shape' => 'ConnectionsList', ], 'MaxRetries' => [ 'shape' => 'MaxRetries', ], 'AllocatedCapacity' => [ 'shape' => 'IntegerValue', 'deprecated' => true, 'deprecatedMessage' => 'This property is deprecated, use MaxCapacity instead.', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'MaxCapacity' => [ 'shape' => 'NullableDouble', ], 'WorkerType' => [ 'shape' => 'WorkerType', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], 'SecurityConfiguration' => [ 'shape' => 'NameString', ], 'NotificationProperty' => [ 'shape' => 'NotificationProperty', ], 'GlueVersion' => [ 'shape' => 'GlueVersionString', ], ], ], 'JobBookmarkEntry' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'JobName', ], 'Version' => [ 'shape' => 'IntegerValue', ], 'Run' => [ 'shape' => 'IntegerValue', ], 'Attempt' => [ 'shape' => 'IntegerValue', ], 'PreviousRunId' => [ 'shape' => 'RunId', ], 'RunId' => [ 'shape' => 'RunId', ], 'JobBookmark' => [ 'shape' => 'JsonValue', ], ], ], 'JobBookmarksEncryption' => [ 'type' => 'structure', 'members' => [ 'JobBookmarksEncryptionMode' => [ 'shape' => 'JobBookmarksEncryptionMode', ], 'KmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], ], ], 'JobBookmarksEncryptionMode' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'CSE-KMS', ], ], 'JobCommand' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'GenericString', ], 'ScriptLocation' => [ 'shape' => 'ScriptLocationString', ], 'PythonVersion' => [ 'shape' => 'PythonVersionString', ], ], ], 'JobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Job', ], ], 'JobName' => [ 'type' => 'string', ], 'JobNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], ], 'JobNodeDetails' => [ 'type' => 'structure', 'members' => [ 'JobRuns' => [ 'shape' => 'JobRunList', ], ], ], 'JobRun' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'IdString', ], 'Attempt' => [ 'shape' => 'AttemptCount', ], 'PreviousRunId' => [ 'shape' => 'IdString', ], 'TriggerName' => [ 'shape' => 'NameString', ], 'JobName' => [ 'shape' => 'NameString', ], 'StartedOn' => [ 'shape' => 'TimestampValue', ], 'LastModifiedOn' => [ 'shape' => 'TimestampValue', ], 'CompletedOn' => [ 'shape' => 'TimestampValue', ], 'JobRunState' => [ 'shape' => 'JobRunState', ], 'Arguments' => [ 'shape' => 'GenericMap', ], 'ErrorMessage' => [ 'shape' => 'ErrorString', ], 'PredecessorRuns' => [ 'shape' => 'PredecessorList', ], 'AllocatedCapacity' => [ 'shape' => 'IntegerValue', 'deprecated' => true, 'deprecatedMessage' => 'This property is deprecated, use MaxCapacity instead.', ], 'ExecutionTime' => [ 'shape' => 'ExecutionTime', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'MaxCapacity' => [ 'shape' => 'NullableDouble', ], 'WorkerType' => [ 'shape' => 'WorkerType', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], 'SecurityConfiguration' => [ 'shape' => 'NameString', ], 'LogGroupName' => [ 'shape' => 'GenericString', ], 'NotificationProperty' => [ 'shape' => 'NotificationProperty', ], 'GlueVersion' => [ 'shape' => 'GlueVersionString', ], ], ], 'JobRunList' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobRun', ], ], 'JobRunState' => [ 'type' => 'string', 'enum' => [ 'STARTING', 'RUNNING', 'STOPPING', 'STOPPED', 'SUCCEEDED', 'FAILED', 'TIMEOUT', ], ], 'JobUpdate' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'DescriptionString', ], 'LogUri' => [ 'shape' => 'UriString', ], 'Role' => [ 'shape' => 'RoleString', ], 'ExecutionProperty' => [ 'shape' => 'ExecutionProperty', ], 'Command' => [ 'shape' => 'JobCommand', ], 'DefaultArguments' => [ 'shape' => 'GenericMap', ], 'NonOverridableArguments' => [ 'shape' => 'GenericMap', ], 'Connections' => [ 'shape' => 'ConnectionsList', ], 'MaxRetries' => [ 'shape' => 'MaxRetries', ], 'AllocatedCapacity' => [ 'shape' => 'IntegerValue', 'deprecated' => true, 'deprecatedMessage' => 'This property is deprecated, use MaxCapacity instead.', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'MaxCapacity' => [ 'shape' => 'NullableDouble', ], 'WorkerType' => [ 'shape' => 'WorkerType', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], 'SecurityConfiguration' => [ 'shape' => 'NameString', ], 'NotificationProperty' => [ 'shape' => 'NotificationProperty', ], 'GlueVersion' => [ 'shape' => 'GlueVersionString', ], ], ], 'JsonClassifier' => [ 'type' => 'structure', 'required' => [ 'Name', 'JsonPath', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastUpdated' => [ 'shape' => 'Timestamp', ], 'Version' => [ 'shape' => 'VersionId', ], 'JsonPath' => [ 'shape' => 'JsonPath', ], ], ], 'JsonPath' => [ 'type' => 'string', ], 'JsonValue' => [ 'type' => 'string', ], 'KeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], 'min' => 1, ], 'KeySchemaElement' => [ 'type' => 'structure', 'required' => [ 'Name', 'Type', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Type' => [ 'shape' => 'ColumnTypeString', ], ], ], 'KeySchemaElementList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KeySchemaElement', ], 'min' => 1, ], 'KeyString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'KmsKeyArn' => [ 'type' => 'string', 'pattern' => 'arn:aws:kms:.*', ], 'LabelCount' => [ 'type' => 'integer', ], 'LabelingSetGenerationTaskRunProperties' => [ 'type' => 'structure', 'members' => [ 'OutputS3Path' => [ 'shape' => 'UriString', ], ], ], 'Language' => [ 'type' => 'string', 'enum' => [ 'PYTHON', 'SCALA', ], ], 'LastActiveDefinition' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'Generic512CharString', ], 'LastModifiedOn' => [ 'shape' => 'TimestampValue', ], 'ParameterSpec' => [ 'shape' => 'BlueprintParameterSpec', ], 'BlueprintLocation' => [ 'shape' => 'GenericString', ], 'BlueprintServiceLocation' => [ 'shape' => 'GenericString', ], ], ], 'LastCrawlInfo' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'LastCrawlStatus', ], 'ErrorMessage' => [ 'shape' => 'DescriptionString', ], 'LogGroup' => [ 'shape' => 'LogGroup', ], 'LogStream' => [ 'shape' => 'LogStream', ], 'MessagePrefix' => [ 'shape' => 'MessagePrefix', ], 'StartTime' => [ 'shape' => 'Timestamp', ], ], ], 'LastCrawlStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCEEDED', 'CANCELLED', 'FAILED', ], ], 'LatestSchemaVersionBoolean' => [ 'type' => 'boolean', ], 'LineageConfiguration' => [ 'type' => 'structure', 'members' => [ 'CrawlerLineageSettings' => [ 'shape' => 'CrawlerLineageSettings', ], ], ], 'ListBlueprintsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'GenericString', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'ListBlueprintsResponse' => [ 'type' => 'structure', 'members' => [ 'Blueprints' => [ 'shape' => 'BlueprintNames', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'ListCrawlersRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'Token', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'ListCrawlersResponse' => [ 'type' => 'structure', 'members' => [ 'CrawlerNames' => [ 'shape' => 'CrawlerNameList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListDevEndpointsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'GenericString', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'ListDevEndpointsResponse' => [ 'type' => 'structure', 'members' => [ 'DevEndpointNames' => [ 'shape' => 'DevEndpointNameList', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'ListJobsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'GenericString', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'ListJobsResponse' => [ 'type' => 'structure', 'members' => [ 'JobNames' => [ 'shape' => 'JobNameList', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'ListMLTransformsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'Filter' => [ 'shape' => 'TransformFilterCriteria', ], 'Sort' => [ 'shape' => 'TransformSortCriteria', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'ListMLTransformsResponse' => [ 'type' => 'structure', 'required' => [ 'TransformIds', ], 'members' => [ 'TransformIds' => [ 'shape' => 'TransformIdList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListRegistriesInput' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResultsNumber', ], 'NextToken' => [ 'shape' => 'SchemaRegistryTokenString', ], ], ], 'ListRegistriesResponse' => [ 'type' => 'structure', 'members' => [ 'Registries' => [ 'shape' => 'RegistryListDefinition', ], 'NextToken' => [ 'shape' => 'SchemaRegistryTokenString', ], ], ], 'ListSchemaVersionsInput' => [ 'type' => 'structure', 'required' => [ 'SchemaId', ], 'members' => [ 'SchemaId' => [ 'shape' => 'SchemaId', ], 'MaxResults' => [ 'shape' => 'MaxResultsNumber', ], 'NextToken' => [ 'shape' => 'SchemaRegistryTokenString', ], ], ], 'ListSchemaVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'Schemas' => [ 'shape' => 'SchemaVersionList', ], 'NextToken' => [ 'shape' => 'SchemaRegistryTokenString', ], ], ], 'ListSchemasInput' => [ 'type' => 'structure', 'members' => [ 'RegistryId' => [ 'shape' => 'RegistryId', ], 'MaxResults' => [ 'shape' => 'MaxResultsNumber', ], 'NextToken' => [ 'shape' => 'SchemaRegistryTokenString', ], ], ], 'ListSchemasResponse' => [ 'type' => 'structure', 'members' => [ 'Schemas' => [ 'shape' => 'SchemaListDefinition', ], 'NextToken' => [ 'shape' => 'SchemaRegistryTokenString', ], ], ], 'ListTriggersRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'GenericString', ], 'DependentJobName' => [ 'shape' => 'NameString', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'Tags' => [ 'shape' => 'TagsMap', ], ], ], 'ListTriggersResponse' => [ 'type' => 'structure', 'members' => [ 'TriggerNames' => [ 'shape' => 'TriggerNameList', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'ListWorkflowsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'GenericString', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'ListWorkflowsResponse' => [ 'type' => 'structure', 'members' => [ 'Workflows' => [ 'shape' => 'WorkflowNames', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'Location' => [ 'type' => 'structure', 'members' => [ 'Jdbc' => [ 'shape' => 'CodeGenNodeArgs', ], 'S3' => [ 'shape' => 'CodeGenNodeArgs', ], 'DynamoDB' => [ 'shape' => 'CodeGenNodeArgs', ], ], ], 'LocationMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ColumnValuesString', ], 'value' => [ 'shape' => 'ColumnValuesString', ], ], 'LocationString' => [ 'type' => 'string', 'max' => 2056, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'LogGroup' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[\\.\\-_/#A-Za-z0-9]+', ], 'LogStream' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[^:*]*', ], 'Logical' => [ 'type' => 'string', 'enum' => [ 'AND', 'ANY', ], ], 'LogicalOperator' => [ 'type' => 'string', 'enum' => [ 'EQUALS', ], ], 'Long' => [ 'type' => 'long', ], 'LongColumnStatisticsData' => [ 'type' => 'structure', 'required' => [ 'NumberOfNulls', 'NumberOfDistinctValues', ], 'members' => [ 'MinimumValue' => [ 'shape' => 'Long', ], 'MaximumValue' => [ 'shape' => 'Long', ], 'NumberOfNulls' => [ 'shape' => 'NonNegativeLong', ], 'NumberOfDistinctValues' => [ 'shape' => 'NonNegativeLong', ], ], ], 'MLTransform' => [ 'type' => 'structure', 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Status' => [ 'shape' => 'TransformStatusType', ], 'CreatedOn' => [ 'shape' => 'Timestamp', ], 'LastModifiedOn' => [ 'shape' => 'Timestamp', ], 'InputRecordTables' => [ 'shape' => 'GlueTables', ], 'Parameters' => [ 'shape' => 'TransformParameters', ], 'EvaluationMetrics' => [ 'shape' => 'EvaluationMetrics', ], 'LabelCount' => [ 'shape' => 'LabelCount', ], 'Schema' => [ 'shape' => 'TransformSchema', ], 'Role' => [ 'shape' => 'RoleString', ], 'GlueVersion' => [ 'shape' => 'GlueVersionString', ], 'MaxCapacity' => [ 'shape' => 'NullableDouble', ], 'WorkerType' => [ 'shape' => 'WorkerType', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'MaxRetries' => [ 'shape' => 'NullableInteger', ], 'TransformEncryption' => [ 'shape' => 'TransformEncryption', ], ], ], 'MLTransformNotReadyException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'MLUserDataEncryption' => [ 'type' => 'structure', 'required' => [ 'MlUserDataEncryptionMode', ], 'members' => [ 'MlUserDataEncryptionMode' => [ 'shape' => 'MLUserDataEncryptionModeString', ], 'KmsKeyId' => [ 'shape' => 'NameString', ], ], ], 'MLUserDataEncryptionModeString' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'SSE-KMS', ], ], 'MapValue' => [ 'type' => 'map', 'key' => [ 'shape' => 'GenericString', ], 'value' => [ 'shape' => 'GenericString', ], 'max' => 100, 'min' => 0, ], 'MappingEntry' => [ 'type' => 'structure', 'members' => [ 'SourceTable' => [ 'shape' => 'TableName', ], 'SourcePath' => [ 'shape' => 'SchemaPathString', ], 'SourceType' => [ 'shape' => 'FieldType', ], 'TargetTable' => [ 'shape' => 'TableName', ], 'TargetPath' => [ 'shape' => 'SchemaPathString', ], 'TargetType' => [ 'shape' => 'FieldType', ], ], ], 'MappingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MappingEntry', ], ], 'MatchCriteria' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], 'max' => 10, 'min' => 0, ], 'MaxConcurrentRuns' => [ 'type' => 'integer', ], 'MaxResultsNumber' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxRetries' => [ 'type' => 'integer', ], 'MessagePrefix' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'MessageString' => [ 'type' => 'string', ], 'MetadataInfo' => [ 'type' => 'structure', 'members' => [ 'MetadataValue' => [ 'shape' => 'MetadataValueString', ], 'CreatedTime' => [ 'shape' => 'CreatedTimestamp', ], 'OtherMetadataValueList' => [ 'shape' => 'OtherMetadataValueList', ], ], ], 'MetadataInfoMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'MetadataKeyString', ], 'value' => [ 'shape' => 'MetadataInfo', ], ], 'MetadataKeyString' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9+-=._./@]+', ], 'MetadataKeyValuePair' => [ 'type' => 'structure', 'members' => [ 'MetadataKey' => [ 'shape' => 'MetadataKeyString', ], 'MetadataValue' => [ 'shape' => 'MetadataValueString', ], ], ], 'MetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetadataKeyValuePair', ], ], 'MetadataValueString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9+-=._./@]+', ], 'MillisecondsCount' => [ 'type' => 'long', ], 'MongoDBTarget' => [ 'type' => 'structure', 'members' => [ 'ConnectionName' => [ 'shape' => 'ConnectionName', ], 'Path' => [ 'shape' => 'Path', ], 'ScanAll' => [ 'shape' => 'NullableBoolean', ], ], ], 'MongoDBTargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MongoDBTarget', ], ], 'NameString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'NameStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], ], 'NoScheduleException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'Node' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'NodeType', ], 'Name' => [ 'shape' => 'NameString', ], 'UniqueId' => [ 'shape' => 'NameString', ], 'TriggerDetails' => [ 'shape' => 'TriggerNodeDetails', ], 'JobDetails' => [ 'shape' => 'JobNodeDetails', ], 'CrawlerDetails' => [ 'shape' => 'CrawlerNodeDetails', ], ], ], 'NodeIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], ], 'NodeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Node', ], ], 'NodeType' => [ 'type' => 'string', 'enum' => [ 'CRAWLER', 'JOB', 'TRIGGER', ], ], 'NonNegativeDouble' => [ 'type' => 'double', 'min' => 0, ], 'NonNegativeInteger' => [ 'type' => 'integer', 'min' => 0, ], 'NonNegativeLong' => [ 'type' => 'long', 'min' => 0, ], 'NotificationProperty' => [ 'type' => 'structure', 'members' => [ 'NotifyDelayAfter' => [ 'shape' => 'NotifyDelayAfter', ], ], ], 'NotifyDelayAfter' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'NullableBoolean' => [ 'type' => 'boolean', 'box' => true, ], 'NullableDouble' => [ 'type' => 'double', 'box' => true, ], 'NullableInteger' => [ 'type' => 'integer', 'box' => true, ], 'OperationTimeoutException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'OrchestrationIAMRoleArn' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => 'arn:aws[^:]*:iam::[0-9]*:role/.+', ], 'OrchestrationNameString' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\.\\-_A-Za-z0-9]+', ], 'OrchestrationS3Location' => [ 'type' => 'string', 'max' => 8192, 'min' => 1, 'pattern' => '^s3://([^/]+)/([^/]+/)*([^/]+)$', ], 'OrchestrationStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GenericString', ], ], 'Order' => [ 'type' => 'structure', 'required' => [ 'Column', 'SortOrder', ], 'members' => [ 'Column' => [ 'shape' => 'NameString', ], 'SortOrder' => [ 'shape' => 'IntegerFlag', ], ], ], 'OrderList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Order', ], ], 'OtherMetadataValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OtherMetadataValueListItem', ], ], 'OtherMetadataValueListItem' => [ 'type' => 'structure', 'members' => [ 'MetadataValue' => [ 'shape' => 'MetadataValueString', ], 'CreatedTime' => [ 'shape' => 'CreatedTimestamp', ], ], ], 'PageSize' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'PaginationToken' => [ 'type' => 'string', ], 'ParametersMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'KeyString', ], 'value' => [ 'shape' => 'ParametersMapValue', ], ], 'ParametersMapValue' => [ 'type' => 'string', 'max' => 512000, ], 'Partition' => [ 'type' => 'structure', 'members' => [ 'Values' => [ 'shape' => 'ValueStringList', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastAccessTime' => [ 'shape' => 'Timestamp', ], 'StorageDescriptor' => [ 'shape' => 'StorageDescriptor', ], 'Parameters' => [ 'shape' => 'ParametersMap', ], 'LastAnalyzedTime' => [ 'shape' => 'Timestamp', ], 'CatalogId' => [ 'shape' => 'CatalogIdString', ], ], ], 'PartitionError' => [ 'type' => 'structure', 'members' => [ 'PartitionValues' => [ 'shape' => 'ValueStringList', ], 'ErrorDetail' => [ 'shape' => 'ErrorDetail', ], ], ], 'PartitionErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'PartitionError', ], ], 'PartitionIndex' => [ 'type' => 'structure', 'required' => [ 'Keys', 'IndexName', ], 'members' => [ 'Keys' => [ 'shape' => 'KeyList', ], 'IndexName' => [ 'shape' => 'NameString', ], ], ], 'PartitionIndexDescriptor' => [ 'type' => 'structure', 'required' => [ 'IndexName', 'Keys', 'IndexStatus', ], 'members' => [ 'IndexName' => [ 'shape' => 'NameString', ], 'Keys' => [ 'shape' => 'KeySchemaElementList', ], 'IndexStatus' => [ 'shape' => 'PartitionIndexStatus', ], 'BackfillErrors' => [ 'shape' => 'BackfillErrors', ], ], ], 'PartitionIndexDescriptorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PartitionIndexDescriptor', ], ], 'PartitionIndexList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PartitionIndex', ], 'max' => 3, ], 'PartitionIndexStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'DELETING', 'FAILED', ], ], 'PartitionInput' => [ 'type' => 'structure', 'members' => [ 'Values' => [ 'shape' => 'ValueStringList', ], 'LastAccessTime' => [ 'shape' => 'Timestamp', ], 'StorageDescriptor' => [ 'shape' => 'StorageDescriptor', ], 'Parameters' => [ 'shape' => 'ParametersMap', ], 'LastAnalyzedTime' => [ 'shape' => 'Timestamp', ], ], ], 'PartitionInputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PartitionInput', ], 'max' => 100, 'min' => 0, ], 'PartitionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Partition', ], ], 'PartitionValueList' => [ 'type' => 'structure', 'required' => [ 'Values', ], 'members' => [ 'Values' => [ 'shape' => 'ValueStringList', ], ], ], 'Path' => [ 'type' => 'string', ], 'PathList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Path', ], ], 'Permission' => [ 'type' => 'string', 'enum' => [ 'ALL', 'SELECT', 'ALTER', 'DROP', 'DELETE', 'INSERT', 'CREATE_DATABASE', 'CREATE_TABLE', 'DATA_LOCATION_ACCESS', ], ], 'PermissionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Permission', ], ], 'PhysicalConnectionRequirements' => [ 'type' => 'structure', 'members' => [ 'SubnetId' => [ 'shape' => 'NameString', ], 'SecurityGroupIdList' => [ 'shape' => 'SecurityGroupIdList', ], 'AvailabilityZone' => [ 'shape' => 'NameString', ], ], ], 'PolicyJsonString' => [ 'type' => 'string', 'max' => 10240, 'min' => 2, ], 'Predecessor' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], 'RunId' => [ 'shape' => 'IdString', ], ], ], 'PredecessorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Predecessor', ], ], 'Predicate' => [ 'type' => 'structure', 'members' => [ 'Logical' => [ 'shape' => 'Logical', ], 'Conditions' => [ 'shape' => 'ConditionList', ], ], ], 'PredicateString' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'PrincipalPermissions' => [ 'type' => 'structure', 'members' => [ 'Principal' => [ 'shape' => 'DataLakePrincipal', ], 'Permissions' => [ 'shape' => 'PermissionList', ], ], ], 'PrincipalPermissionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PrincipalPermissions', ], ], 'PrincipalType' => [ 'type' => 'string', 'enum' => [ 'USER', 'ROLE', 'GROUP', ], ], 'PropertyPredicate' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'ValueString', ], 'Value' => [ 'shape' => 'ValueString', ], 'Comparator' => [ 'shape' => 'Comparator', ], ], ], 'PublicKeysList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GenericString', ], 'max' => 5, ], 'PutDataCatalogEncryptionSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'DataCatalogEncryptionSettings', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DataCatalogEncryptionSettings' => [ 'shape' => 'DataCatalogEncryptionSettings', ], ], ], 'PutDataCatalogEncryptionSettingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'PutResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'PolicyInJson', ], 'members' => [ 'PolicyInJson' => [ 'shape' => 'PolicyJsonString', ], 'ResourceArn' => [ 'shape' => 'GlueResourceArn', ], 'PolicyHashCondition' => [ 'shape' => 'HashString', ], 'PolicyExistsCondition' => [ 'shape' => 'ExistCondition', ], 'EnableHybrid' => [ 'shape' => 'EnableHybridValues', ], ], ], 'PutResourcePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'PolicyHash' => [ 'shape' => 'HashString', ], ], ], 'PutSchemaVersionMetadataInput' => [ 'type' => 'structure', 'required' => [ 'MetadataKeyValue', ], 'members' => [ 'SchemaId' => [ 'shape' => 'SchemaId', ], 'SchemaVersionNumber' => [ 'shape' => 'SchemaVersionNumber', ], 'SchemaVersionId' => [ 'shape' => 'SchemaVersionIdString', ], 'MetadataKeyValue' => [ 'shape' => 'MetadataKeyValuePair', ], ], ], 'PutSchemaVersionMetadataResponse' => [ 'type' => 'structure', 'members' => [ 'SchemaArn' => [ 'shape' => 'GlueResourceArn', ], 'SchemaName' => [ 'shape' => 'SchemaRegistryNameString', ], 'RegistryName' => [ 'shape' => 'SchemaRegistryNameString', ], 'LatestVersion' => [ 'shape' => 'LatestSchemaVersionBoolean', ], 'VersionNumber' => [ 'shape' => 'VersionLongNumber', ], 'SchemaVersionId' => [ 'shape' => 'SchemaVersionIdString', ], 'MetadataKey' => [ 'shape' => 'MetadataKeyString', ], 'MetadataValue' => [ 'shape' => 'MetadataValueString', ], ], ], 'PutWorkflowRunPropertiesRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'RunId', 'RunProperties', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'RunId' => [ 'shape' => 'IdString', ], 'RunProperties' => [ 'shape' => 'WorkflowRunProperties', ], ], ], 'PutWorkflowRunPropertiesResponse' => [ 'type' => 'structure', 'members' => [], ], 'PythonScript' => [ 'type' => 'string', ], 'PythonVersionString' => [ 'type' => 'string', 'pattern' => '^[2-3]$', ], 'QuerySchemaVersionMetadataInput' => [ 'type' => 'structure', 'members' => [ 'SchemaId' => [ 'shape' => 'SchemaId', ], 'SchemaVersionNumber' => [ 'shape' => 'SchemaVersionNumber', ], 'SchemaVersionId' => [ 'shape' => 'SchemaVersionIdString', ], 'MetadataList' => [ 'shape' => 'MetadataList', ], 'MaxResults' => [ 'shape' => 'QuerySchemaVersionMetadataMaxResults', ], 'NextToken' => [ 'shape' => 'SchemaRegistryTokenString', ], ], ], 'QuerySchemaVersionMetadataMaxResults' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'QuerySchemaVersionMetadataResponse' => [ 'type' => 'structure', 'members' => [ 'MetadataInfoMap' => [ 'shape' => 'MetadataInfoMap', ], 'SchemaVersionId' => [ 'shape' => 'SchemaVersionIdString', ], 'NextToken' => [ 'shape' => 'SchemaRegistryTokenString', ], ], ], 'RecordsCount' => [ 'type' => 'long', 'box' => true, ], 'RecrawlBehavior' => [ 'type' => 'string', 'enum' => [ 'CRAWL_EVERYTHING', 'CRAWL_NEW_FOLDERS_ONLY', 'CRAWL_EVENT_MODE', ], ], 'RecrawlPolicy' => [ 'type' => 'structure', 'members' => [ 'RecrawlBehavior' => [ 'shape' => 'RecrawlBehavior', ], ], ], 'RegisterSchemaVersionInput' => [ 'type' => 'structure', 'required' => [ 'SchemaId', 'SchemaDefinition', ], 'members' => [ 'SchemaId' => [ 'shape' => 'SchemaId', ], 'SchemaDefinition' => [ 'shape' => 'SchemaDefinitionString', ], ], ], 'RegisterSchemaVersionResponse' => [ 'type' => 'structure', 'members' => [ 'SchemaVersionId' => [ 'shape' => 'SchemaVersionIdString', ], 'VersionNumber' => [ 'shape' => 'VersionLongNumber', ], 'Status' => [ 'shape' => 'SchemaVersionStatus', ], ], ], 'RegistryId' => [ 'type' => 'structure', 'members' => [ 'RegistryName' => [ 'shape' => 'SchemaRegistryNameString', ], 'RegistryArn' => [ 'shape' => 'GlueResourceArn', ], ], ], 'RegistryListDefinition' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegistryListItem', ], ], 'RegistryListItem' => [ 'type' => 'structure', 'members' => [ 'RegistryName' => [ 'shape' => 'SchemaRegistryNameString', ], 'RegistryArn' => [ 'shape' => 'GlueResourceArn', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Status' => [ 'shape' => 'RegistryStatus', ], 'CreatedTime' => [ 'shape' => 'CreatedTimestamp', ], 'UpdatedTime' => [ 'shape' => 'UpdatedTimestamp', ], ], ], 'RegistryStatus' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'DELETING', ], ], 'RemoveSchemaVersionMetadataInput' => [ 'type' => 'structure', 'required' => [ 'MetadataKeyValue', ], 'members' => [ 'SchemaId' => [ 'shape' => 'SchemaId', ], 'SchemaVersionNumber' => [ 'shape' => 'SchemaVersionNumber', ], 'SchemaVersionId' => [ 'shape' => 'SchemaVersionIdString', ], 'MetadataKeyValue' => [ 'shape' => 'MetadataKeyValuePair', ], ], ], 'RemoveSchemaVersionMetadataResponse' => [ 'type' => 'structure', 'members' => [ 'SchemaArn' => [ 'shape' => 'GlueResourceArn', ], 'SchemaName' => [ 'shape' => 'SchemaRegistryNameString', ], 'RegistryName' => [ 'shape' => 'SchemaRegistryNameString', ], 'LatestVersion' => [ 'shape' => 'LatestSchemaVersionBoolean', ], 'VersionNumber' => [ 'shape' => 'VersionLongNumber', ], 'SchemaVersionId' => [ 'shape' => 'SchemaVersionIdString', ], 'MetadataKey' => [ 'shape' => 'MetadataKeyString', ], 'MetadataValue' => [ 'shape' => 'MetadataValueString', ], ], ], 'ReplaceBoolean' => [ 'type' => 'boolean', ], 'ResetJobBookmarkRequest' => [ 'type' => 'structure', 'required' => [ 'JobName', ], 'members' => [ 'JobName' => [ 'shape' => 'JobName', ], 'RunId' => [ 'shape' => 'RunId', ], ], ], 'ResetJobBookmarkResponse' => [ 'type' => 'structure', 'members' => [ 'JobBookmarkEntry' => [ 'shape' => 'JobBookmarkEntry', ], ], ], 'ResourceNotReadyException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'ResourceNumberLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'ResourceShareType' => [ 'type' => 'string', 'enum' => [ 'FOREIGN', 'ALL', ], ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'JAR', 'FILE', 'ARCHIVE', ], ], 'ResourceUri' => [ 'type' => 'structure', 'members' => [ 'ResourceType' => [ 'shape' => 'ResourceType', ], 'Uri' => [ 'shape' => 'URI', ], ], ], 'ResourceUriList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceUri', ], 'max' => 1000, 'min' => 0, ], 'ResumeWorkflowRunRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'RunId', 'NodeIds', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'RunId' => [ 'shape' => 'IdString', ], 'NodeIds' => [ 'shape' => 'NodeIdList', ], ], ], 'ResumeWorkflowRunResponse' => [ 'type' => 'structure', 'members' => [ 'RunId' => [ 'shape' => 'IdString', ], 'NodeIds' => [ 'shape' => 'NodeIdList', ], ], ], 'Role' => [ 'type' => 'string', ], 'RoleArn' => [ 'type' => 'string', 'pattern' => 'arn:aws:iam::\\d{12}:role/.*', ], 'RoleString' => [ 'type' => 'string', ], 'RowTag' => [ 'type' => 'string', ], 'RunId' => [ 'type' => 'string', ], 'S3Encryption' => [ 'type' => 'structure', 'members' => [ 'S3EncryptionMode' => [ 'shape' => 'S3EncryptionMode', ], 'KmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], ], ], 'S3EncryptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3Encryption', ], ], 'S3EncryptionMode' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'SSE-KMS', 'SSE-S3', ], ], 'S3Target' => [ 'type' => 'structure', 'members' => [ 'Path' => [ 'shape' => 'Path', ], 'Exclusions' => [ 'shape' => 'PathList', ], 'ConnectionName' => [ 'shape' => 'ConnectionName', ], 'SampleSize' => [ 'shape' => 'NullableInteger', ], 'EventQueueArn' => [ 'shape' => 'EventQueueArn', ], 'DlqEventQueueArn' => [ 'shape' => 'EventQueueArn', ], ], ], 'S3TargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3Target', ], ], 'ScalaCode' => [ 'type' => 'string', ], 'Schedule' => [ 'type' => 'structure', 'members' => [ 'ScheduleExpression' => [ 'shape' => 'CronExpression', ], 'State' => [ 'shape' => 'ScheduleState', ], ], ], 'ScheduleState' => [ 'type' => 'string', 'enum' => [ 'SCHEDULED', 'NOT_SCHEDULED', 'TRANSITIONING', ], ], 'SchedulerNotRunningException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'SchedulerRunningException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'SchedulerTransitioningException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'SchemaChangePolicy' => [ 'type' => 'structure', 'members' => [ 'UpdateBehavior' => [ 'shape' => 'UpdateBehavior', ], 'DeleteBehavior' => [ 'shape' => 'DeleteBehavior', ], ], ], 'SchemaCheckpointNumber' => [ 'type' => 'long', 'max' => 100000, 'min' => 1, ], 'SchemaColumn' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'ColumnNameString', ], 'DataType' => [ 'shape' => 'ColumnTypeString', ], ], ], 'SchemaDefinitionDiff' => [ 'type' => 'string', 'max' => 340000, 'min' => 1, 'pattern' => '.*\\S.*', ], 'SchemaDefinitionString' => [ 'type' => 'string', 'max' => 170000, 'min' => 1, 'pattern' => '.*\\S.*', ], 'SchemaDiffType' => [ 'type' => 'string', 'enum' => [ 'SYNTAX_DIFF', ], ], 'SchemaId' => [ 'type' => 'structure', 'members' => [ 'SchemaArn' => [ 'shape' => 'GlueResourceArn', ], 'SchemaName' => [ 'shape' => 'SchemaRegistryNameString', ], 'RegistryName' => [ 'shape' => 'SchemaRegistryNameString', ], ], ], 'SchemaListDefinition' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaListItem', ], ], 'SchemaListItem' => [ 'type' => 'structure', 'members' => [ 'RegistryName' => [ 'shape' => 'SchemaRegistryNameString', ], 'SchemaName' => [ 'shape' => 'SchemaRegistryNameString', ], 'SchemaArn' => [ 'shape' => 'GlueResourceArn', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'SchemaStatus' => [ 'shape' => 'SchemaStatus', ], 'CreatedTime' => [ 'shape' => 'CreatedTimestamp', ], 'UpdatedTime' => [ 'shape' => 'UpdatedTimestamp', ], ], ], 'SchemaPathString' => [ 'type' => 'string', ], 'SchemaReference' => [ 'type' => 'structure', 'members' => [ 'SchemaId' => [ 'shape' => 'SchemaId', ], 'SchemaVersionId' => [ 'shape' => 'SchemaVersionIdString', ], 'SchemaVersionNumber' => [ 'shape' => 'VersionLongNumber', 'box' => true, ], ], ], 'SchemaRegistryNameString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[a-zA-Z0-9-_$#.]+', ], 'SchemaRegistryTokenString' => [ 'type' => 'string', ], 'SchemaStatus' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'PENDING', 'DELETING', ], ], 'SchemaValidationError' => [ 'type' => 'string', 'max' => 5000, 'min' => 1, ], 'SchemaVersionErrorItem' => [ 'type' => 'structure', 'members' => [ 'VersionNumber' => [ 'shape' => 'VersionLongNumber', ], 'ErrorDetails' => [ 'shape' => 'ErrorDetails', ], ], ], 'SchemaVersionErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaVersionErrorItem', ], ], 'SchemaVersionIdString' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}', ], 'SchemaVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaVersionListItem', ], ], 'SchemaVersionListItem' => [ 'type' => 'structure', 'members' => [ 'SchemaArn' => [ 'shape' => 'GlueResourceArn', ], 'SchemaVersionId' => [ 'shape' => 'SchemaVersionIdString', ], 'VersionNumber' => [ 'shape' => 'VersionLongNumber', ], 'Status' => [ 'shape' => 'SchemaVersionStatus', ], 'CreatedTime' => [ 'shape' => 'CreatedTimestamp', ], ], ], 'SchemaVersionNumber' => [ 'type' => 'structure', 'members' => [ 'LatestVersion' => [ 'shape' => 'LatestSchemaVersionBoolean', ], 'VersionNumber' => [ 'shape' => 'VersionLongNumber', ], ], ], 'SchemaVersionStatus' => [ 'type' => 'string', 'enum' => [ 'AVAILABLE', 'PENDING', 'FAILURE', 'DELETING', ], ], 'ScriptLocationString' => [ 'type' => 'string', ], 'SearchPropertyPredicates' => [ 'type' => 'list', 'member' => [ 'shape' => 'PropertyPredicate', ], ], 'SearchTablesRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'NextToken' => [ 'shape' => 'Token', ], 'Filters' => [ 'shape' => 'SearchPropertyPredicates', ], 'SearchText' => [ 'shape' => 'ValueString', ], 'SortCriteria' => [ 'shape' => 'SortCriteria', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'ResourceShareType' => [ 'shape' => 'ResourceShareType', ], ], ], 'SearchTablesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'TableList' => [ 'shape' => 'TableList', ], ], ], 'SecurityConfiguration' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'CreatedTimeStamp' => [ 'shape' => 'TimestampValue', ], 'EncryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], ], ], 'SecurityConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityConfiguration', ], ], 'SecurityGroupIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], 'max' => 50, 'min' => 0, ], 'Segment' => [ 'type' => 'structure', 'required' => [ 'SegmentNumber', 'TotalSegments', ], 'members' => [ 'SegmentNumber' => [ 'shape' => 'NonNegativeInteger', ], 'TotalSegments' => [ 'shape' => 'TotalSegmentsInteger', ], ], ], 'SerDeInfo' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'SerializationLibrary' => [ 'shape' => 'NameString', ], 'Parameters' => [ 'shape' => 'ParametersMap', ], ], ], 'SkewedInfo' => [ 'type' => 'structure', 'members' => [ 'SkewedColumnNames' => [ 'shape' => 'NameStringList', ], 'SkewedColumnValues' => [ 'shape' => 'ColumnValueStringList', ], 'SkewedColumnValueLocationMaps' => [ 'shape' => 'LocationMap', ], ], ], 'Sort' => [ 'type' => 'string', 'enum' => [ 'ASC', 'DESC', ], ], 'SortCriteria' => [ 'type' => 'list', 'member' => [ 'shape' => 'SortCriterion', ], 'max' => 1, 'min' => 0, ], 'SortCriterion' => [ 'type' => 'structure', 'members' => [ 'FieldName' => [ 'shape' => 'ValueString', ], 'Sort' => [ 'shape' => 'Sort', ], ], ], 'SortDirectionType' => [ 'type' => 'string', 'enum' => [ 'DESCENDING', 'ASCENDING', ], ], 'StartBlueprintRunRequest' => [ 'type' => 'structure', 'required' => [ 'BlueprintName', 'RoleArn', ], 'members' => [ 'BlueprintName' => [ 'shape' => 'OrchestrationNameString', ], 'Parameters' => [ 'shape' => 'BlueprintParameters', ], 'RoleArn' => [ 'shape' => 'OrchestrationIAMRoleArn', ], ], ], 'StartBlueprintRunResponse' => [ 'type' => 'structure', 'members' => [ 'RunId' => [ 'shape' => 'IdString', ], ], ], 'StartCrawlerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'StartCrawlerResponse' => [ 'type' => 'structure', 'members' => [], ], 'StartCrawlerScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'CrawlerName', ], 'members' => [ 'CrawlerName' => [ 'shape' => 'NameString', ], ], ], 'StartCrawlerScheduleResponse' => [ 'type' => 'structure', 'members' => [], ], 'StartExportLabelsTaskRunRequest' => [ 'type' => 'structure', 'required' => [ 'TransformId', 'OutputS3Path', ], 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], 'OutputS3Path' => [ 'shape' => 'UriString', ], ], ], 'StartExportLabelsTaskRunResponse' => [ 'type' => 'structure', 'members' => [ 'TaskRunId' => [ 'shape' => 'HashString', ], ], ], 'StartImportLabelsTaskRunRequest' => [ 'type' => 'structure', 'required' => [ 'TransformId', 'InputS3Path', ], 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], 'InputS3Path' => [ 'shape' => 'UriString', ], 'ReplaceAllLabels' => [ 'shape' => 'ReplaceBoolean', ], ], ], 'StartImportLabelsTaskRunResponse' => [ 'type' => 'structure', 'members' => [ 'TaskRunId' => [ 'shape' => 'HashString', ], ], ], 'StartJobRunRequest' => [ 'type' => 'structure', 'required' => [ 'JobName', ], 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], 'JobRunId' => [ 'shape' => 'IdString', ], 'Arguments' => [ 'shape' => 'GenericMap', ], 'AllocatedCapacity' => [ 'shape' => 'IntegerValue', 'deprecated' => true, 'deprecatedMessage' => 'This property is deprecated, use MaxCapacity instead.', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'MaxCapacity' => [ 'shape' => 'NullableDouble', ], 'SecurityConfiguration' => [ 'shape' => 'NameString', ], 'NotificationProperty' => [ 'shape' => 'NotificationProperty', ], 'WorkerType' => [ 'shape' => 'WorkerType', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], ], ], 'StartJobRunResponse' => [ 'type' => 'structure', 'members' => [ 'JobRunId' => [ 'shape' => 'IdString', ], ], ], 'StartMLEvaluationTaskRunRequest' => [ 'type' => 'structure', 'required' => [ 'TransformId', ], 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], ], ], 'StartMLEvaluationTaskRunResponse' => [ 'type' => 'structure', 'members' => [ 'TaskRunId' => [ 'shape' => 'HashString', ], ], ], 'StartMLLabelingSetGenerationTaskRunRequest' => [ 'type' => 'structure', 'required' => [ 'TransformId', 'OutputS3Path', ], 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], 'OutputS3Path' => [ 'shape' => 'UriString', ], ], ], 'StartMLLabelingSetGenerationTaskRunResponse' => [ 'type' => 'structure', 'members' => [ 'TaskRunId' => [ 'shape' => 'HashString', ], ], ], 'StartTriggerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'StartTriggerResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'StartWorkflowRunRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'StartWorkflowRunResponse' => [ 'type' => 'structure', 'members' => [ 'RunId' => [ 'shape' => 'IdString', ], ], ], 'StartingEventBatchCondition' => [ 'type' => 'structure', 'members' => [ 'BatchSize' => [ 'shape' => 'NullableInteger', ], 'BatchWindow' => [ 'shape' => 'NullableInteger', ], ], ], 'StopCrawlerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'StopCrawlerResponse' => [ 'type' => 'structure', 'members' => [], ], 'StopCrawlerScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'CrawlerName', ], 'members' => [ 'CrawlerName' => [ 'shape' => 'NameString', ], ], ], 'StopCrawlerScheduleResponse' => [ 'type' => 'structure', 'members' => [], ], 'StopTriggerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'StopTriggerResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'StopWorkflowRunRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'RunId', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'RunId' => [ 'shape' => 'IdString', ], ], ], 'StopWorkflowRunResponse' => [ 'type' => 'structure', 'members' => [], ], 'StorageDescriptor' => [ 'type' => 'structure', 'members' => [ 'Columns' => [ 'shape' => 'ColumnList', ], 'Location' => [ 'shape' => 'LocationString', ], 'InputFormat' => [ 'shape' => 'FormatString', ], 'OutputFormat' => [ 'shape' => 'FormatString', ], 'Compressed' => [ 'shape' => 'Boolean', ], 'NumberOfBuckets' => [ 'shape' => 'Integer', ], 'SerdeInfo' => [ 'shape' => 'SerDeInfo', ], 'BucketColumns' => [ 'shape' => 'NameStringList', ], 'SortColumns' => [ 'shape' => 'OrderList', ], 'Parameters' => [ 'shape' => 'ParametersMap', ], 'SkewedInfo' => [ 'shape' => 'SkewedInfo', ], 'StoredAsSubDirectories' => [ 'shape' => 'Boolean', ], 'SchemaReference' => [ 'shape' => 'SchemaReference', ], ], ], 'StringColumnStatisticsData' => [ 'type' => 'structure', 'required' => [ 'MaximumLength', 'AverageLength', 'NumberOfNulls', 'NumberOfDistinctValues', ], 'members' => [ 'MaximumLength' => [ 'shape' => 'NonNegativeLong', ], 'AverageLength' => [ 'shape' => 'NonNegativeDouble', ], 'NumberOfNulls' => [ 'shape' => 'NonNegativeLong', ], 'NumberOfDistinctValues' => [ 'shape' => 'NonNegativeLong', ], ], ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GenericString', ], ], 'Table' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Owner' => [ 'shape' => 'NameString', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], 'LastAccessTime' => [ 'shape' => 'Timestamp', ], 'LastAnalyzedTime' => [ 'shape' => 'Timestamp', ], 'Retention' => [ 'shape' => 'NonNegativeInteger', ], 'StorageDescriptor' => [ 'shape' => 'StorageDescriptor', ], 'PartitionKeys' => [ 'shape' => 'ColumnList', ], 'ViewOriginalText' => [ 'shape' => 'ViewTextString', ], 'ViewExpandedText' => [ 'shape' => 'ViewTextString', ], 'TableType' => [ 'shape' => 'TableTypeString', ], 'Parameters' => [ 'shape' => 'ParametersMap', ], 'CreatedBy' => [ 'shape' => 'NameString', ], 'IsRegisteredWithLakeFormation' => [ 'shape' => 'Boolean', ], 'TargetTable' => [ 'shape' => 'TableIdentifier', ], 'CatalogId' => [ 'shape' => 'CatalogIdString', ], ], ], 'TableError' => [ 'type' => 'structure', 'members' => [ 'TableName' => [ 'shape' => 'NameString', ], 'ErrorDetail' => [ 'shape' => 'ErrorDetail', ], ], ], 'TableErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'TableError', ], ], 'TableIdentifier' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'Name' => [ 'shape' => 'NameString', ], ], ], 'TableInput' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Owner' => [ 'shape' => 'NameString', ], 'LastAccessTime' => [ 'shape' => 'Timestamp', ], 'LastAnalyzedTime' => [ 'shape' => 'Timestamp', ], 'Retention' => [ 'shape' => 'NonNegativeInteger', ], 'StorageDescriptor' => [ 'shape' => 'StorageDescriptor', ], 'PartitionKeys' => [ 'shape' => 'ColumnList', ], 'ViewOriginalText' => [ 'shape' => 'ViewTextString', ], 'ViewExpandedText' => [ 'shape' => 'ViewTextString', ], 'TableType' => [ 'shape' => 'TableTypeString', ], 'Parameters' => [ 'shape' => 'ParametersMap', ], 'TargetTable' => [ 'shape' => 'TableIdentifier', ], ], ], 'TableList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Table', ], ], 'TableName' => [ 'type' => 'string', ], 'TablePrefix' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'TableTypeString' => [ 'type' => 'string', 'max' => 255, ], 'TableVersion' => [ 'type' => 'structure', 'members' => [ 'Table' => [ 'shape' => 'Table', ], 'VersionId' => [ 'shape' => 'VersionString', ], ], ], 'TableVersionError' => [ 'type' => 'structure', 'members' => [ 'TableName' => [ 'shape' => 'NameString', ], 'VersionId' => [ 'shape' => 'VersionString', ], 'ErrorDetail' => [ 'shape' => 'ErrorDetail', ], ], ], 'TableVersionErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'TableVersionError', ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeysList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagsToAdd', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'GlueResourceArn', ], 'TagsToAdd' => [ 'shape' => 'TagsMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TagsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 0, ], 'TaskRun' => [ 'type' => 'structure', 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], 'TaskRunId' => [ 'shape' => 'HashString', ], 'Status' => [ 'shape' => 'TaskStatusType', ], 'LogGroupName' => [ 'shape' => 'GenericString', ], 'Properties' => [ 'shape' => 'TaskRunProperties', ], 'ErrorString' => [ 'shape' => 'GenericString', ], 'StartedOn' => [ 'shape' => 'Timestamp', ], 'LastModifiedOn' => [ 'shape' => 'Timestamp', ], 'CompletedOn' => [ 'shape' => 'Timestamp', ], 'ExecutionTime' => [ 'shape' => 'ExecutionTime', ], ], ], 'TaskRunFilterCriteria' => [ 'type' => 'structure', 'members' => [ 'TaskRunType' => [ 'shape' => 'TaskType', ], 'Status' => [ 'shape' => 'TaskStatusType', ], 'StartedBefore' => [ 'shape' => 'Timestamp', ], 'StartedAfter' => [ 'shape' => 'Timestamp', ], ], ], 'TaskRunList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TaskRun', ], ], 'TaskRunProperties' => [ 'type' => 'structure', 'members' => [ 'TaskType' => [ 'shape' => 'TaskType', ], 'ImportLabelsTaskRunProperties' => [ 'shape' => 'ImportLabelsTaskRunProperties', ], 'ExportLabelsTaskRunProperties' => [ 'shape' => 'ExportLabelsTaskRunProperties', ], 'LabelingSetGenerationTaskRunProperties' => [ 'shape' => 'LabelingSetGenerationTaskRunProperties', ], 'FindMatchesTaskRunProperties' => [ 'shape' => 'FindMatchesTaskRunProperties', ], ], ], 'TaskRunSortColumnType' => [ 'type' => 'string', 'enum' => [ 'TASK_RUN_TYPE', 'STATUS', 'STARTED', ], ], 'TaskRunSortCriteria' => [ 'type' => 'structure', 'required' => [ 'Column', 'SortDirection', ], 'members' => [ 'Column' => [ 'shape' => 'TaskRunSortColumnType', ], 'SortDirection' => [ 'shape' => 'SortDirectionType', ], ], ], 'TaskStatusType' => [ 'type' => 'string', 'enum' => [ 'STARTING', 'RUNNING', 'STOPPING', 'STOPPED', 'SUCCEEDED', 'FAILED', 'TIMEOUT', ], ], 'TaskType' => [ 'type' => 'string', 'enum' => [ 'EVALUATION', 'LABELING_SET_GENERATION', 'IMPORT_LABELS', 'EXPORT_LABELS', 'FIND_MATCHES', ], ], 'Timeout' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TimestampValue' => [ 'type' => 'timestamp', ], 'Token' => [ 'type' => 'string', ], 'TotalSegmentsInteger' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'TransactionIdString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\p{L}\\p{N}\\p{P}]*', ], 'TransformEncryption' => [ 'type' => 'structure', 'members' => [ 'MlUserDataEncryption' => [ 'shape' => 'MLUserDataEncryption', ], 'TaskRunSecurityConfigurationName' => [ 'shape' => 'NameString', ], ], ], 'TransformFilterCriteria' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'TransformType' => [ 'shape' => 'TransformType', ], 'Status' => [ 'shape' => 'TransformStatusType', ], 'GlueVersion' => [ 'shape' => 'GlueVersionString', ], 'CreatedBefore' => [ 'shape' => 'Timestamp', ], 'CreatedAfter' => [ 'shape' => 'Timestamp', ], 'LastModifiedBefore' => [ 'shape' => 'Timestamp', ], 'LastModifiedAfter' => [ 'shape' => 'Timestamp', ], 'Schema' => [ 'shape' => 'TransformSchema', ], ], ], 'TransformIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HashString', ], ], 'TransformList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MLTransform', ], ], 'TransformParameters' => [ 'type' => 'structure', 'required' => [ 'TransformType', ], 'members' => [ 'TransformType' => [ 'shape' => 'TransformType', ], 'FindMatchesParameters' => [ 'shape' => 'FindMatchesParameters', ], ], ], 'TransformSchema' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaColumn', ], 'max' => 100, ], 'TransformSortColumnType' => [ 'type' => 'string', 'enum' => [ 'NAME', 'TRANSFORM_TYPE', 'STATUS', 'CREATED', 'LAST_MODIFIED', ], ], 'TransformSortCriteria' => [ 'type' => 'structure', 'required' => [ 'Column', 'SortDirection', ], 'members' => [ 'Column' => [ 'shape' => 'TransformSortColumnType', ], 'SortDirection' => [ 'shape' => 'SortDirectionType', ], ], ], 'TransformStatusType' => [ 'type' => 'string', 'enum' => [ 'NOT_READY', 'READY', 'DELETING', ], ], 'TransformType' => [ 'type' => 'string', 'enum' => [ 'FIND_MATCHES', ], ], 'Trigger' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'WorkflowName' => [ 'shape' => 'NameString', ], 'Id' => [ 'shape' => 'IdString', ], 'Type' => [ 'shape' => 'TriggerType', ], 'State' => [ 'shape' => 'TriggerState', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Schedule' => [ 'shape' => 'GenericString', ], 'Actions' => [ 'shape' => 'ActionList', ], 'Predicate' => [ 'shape' => 'Predicate', ], 'EventBatchingCondition' => [ 'shape' => 'EventBatchingCondition', ], ], ], 'TriggerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Trigger', ], ], 'TriggerNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], ], 'TriggerNodeDetails' => [ 'type' => 'structure', 'members' => [ 'Trigger' => [ 'shape' => 'Trigger', ], ], ], 'TriggerState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'CREATED', 'ACTIVATING', 'ACTIVATED', 'DEACTIVATING', 'DEACTIVATED', 'DELETING', 'UPDATING', ], ], 'TriggerType' => [ 'type' => 'string', 'enum' => [ 'SCHEDULED', 'CONDITIONAL', 'ON_DEMAND', 'EVENT', ], ], 'TriggerUpdate' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Schedule' => [ 'shape' => 'GenericString', ], 'Actions' => [ 'shape' => 'ActionList', ], 'Predicate' => [ 'shape' => 'Predicate', ], 'EventBatchingCondition' => [ 'shape' => 'EventBatchingCondition', ], ], ], 'TypeString' => [ 'type' => 'string', 'max' => 20000, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'URI' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagsToRemove', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'GlueResourceArn', ], 'TagsToRemove' => [ 'shape' => 'TagKeysList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateBehavior' => [ 'type' => 'string', 'enum' => [ 'LOG', 'UPDATE_IN_DATABASE', ], ], 'UpdateBlueprintRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'BlueprintLocation', ], 'members' => [ 'Name' => [ 'shape' => 'OrchestrationNameString', ], 'Description' => [ 'shape' => 'Generic512CharString', ], 'BlueprintLocation' => [ 'shape' => 'OrchestrationS3Location', ], ], ], 'UpdateBlueprintResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'UpdateClassifierRequest' => [ 'type' => 'structure', 'members' => [ 'GrokClassifier' => [ 'shape' => 'UpdateGrokClassifierRequest', ], 'XMLClassifier' => [ 'shape' => 'UpdateXMLClassifierRequest', ], 'JsonClassifier' => [ 'shape' => 'UpdateJsonClassifierRequest', ], 'CsvClassifier' => [ 'shape' => 'UpdateCsvClassifierRequest', ], ], ], 'UpdateClassifierResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateColumnStatisticsForPartitionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'PartitionValues', 'ColumnStatisticsList', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionValues' => [ 'shape' => 'ValueStringList', ], 'ColumnStatisticsList' => [ 'shape' => 'UpdateColumnStatisticsList', ], ], ], 'UpdateColumnStatisticsForPartitionResponse' => [ 'type' => 'structure', 'members' => [ 'Errors' => [ 'shape' => 'ColumnStatisticsErrors', ], ], ], 'UpdateColumnStatisticsForTableRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'ColumnStatisticsList', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'ColumnStatisticsList' => [ 'shape' => 'UpdateColumnStatisticsList', ], ], ], 'UpdateColumnStatisticsForTableResponse' => [ 'type' => 'structure', 'members' => [ 'Errors' => [ 'shape' => 'ColumnStatisticsErrors', ], ], ], 'UpdateColumnStatisticsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnStatistics', ], 'max' => 25, 'min' => 0, ], 'UpdateConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'ConnectionInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Name' => [ 'shape' => 'NameString', ], 'ConnectionInput' => [ 'shape' => 'ConnectionInput', ], ], ], 'UpdateConnectionResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateCrawlerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Role' => [ 'shape' => 'Role', ], 'DatabaseName' => [ 'shape' => 'DatabaseName', ], 'Description' => [ 'shape' => 'DescriptionStringRemovable', ], 'Targets' => [ 'shape' => 'CrawlerTargets', ], 'Schedule' => [ 'shape' => 'CronExpression', ], 'Classifiers' => [ 'shape' => 'ClassifierNameList', ], 'TablePrefix' => [ 'shape' => 'TablePrefix', ], 'SchemaChangePolicy' => [ 'shape' => 'SchemaChangePolicy', ], 'RecrawlPolicy' => [ 'shape' => 'RecrawlPolicy', ], 'LineageConfiguration' => [ 'shape' => 'LineageConfiguration', ], 'Configuration' => [ 'shape' => 'CrawlerConfiguration', ], 'CrawlerSecurityConfiguration' => [ 'shape' => 'CrawlerSecurityConfiguration', ], ], ], 'UpdateCrawlerResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateCrawlerScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'CrawlerName', ], 'members' => [ 'CrawlerName' => [ 'shape' => 'NameString', ], 'Schedule' => [ 'shape' => 'CronExpression', ], ], ], 'UpdateCrawlerScheduleResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateCsvClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Delimiter' => [ 'shape' => 'CsvColumnDelimiter', ], 'QuoteSymbol' => [ 'shape' => 'CsvQuoteSymbol', ], 'ContainsHeader' => [ 'shape' => 'CsvHeaderOption', ], 'Header' => [ 'shape' => 'CsvHeader', ], 'DisableValueTrimming' => [ 'shape' => 'NullableBoolean', ], 'AllowSingleColumn' => [ 'shape' => 'NullableBoolean', ], ], ], 'UpdateDatabaseRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'DatabaseInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Name' => [ 'shape' => 'NameString', ], 'DatabaseInput' => [ 'shape' => 'DatabaseInput', ], ], ], 'UpdateDatabaseResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDevEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'EndpointName', ], 'members' => [ 'EndpointName' => [ 'shape' => 'GenericString', ], 'PublicKey' => [ 'shape' => 'GenericString', ], 'AddPublicKeys' => [ 'shape' => 'PublicKeysList', ], 'DeletePublicKeys' => [ 'shape' => 'PublicKeysList', ], 'CustomLibraries' => [ 'shape' => 'DevEndpointCustomLibraries', ], 'UpdateEtlLibraries' => [ 'shape' => 'BooleanValue', ], 'DeleteArguments' => [ 'shape' => 'StringList', ], 'AddArguments' => [ 'shape' => 'MapValue', ], ], ], 'UpdateDevEndpointResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateGrokClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Classification' => [ 'shape' => 'Classification', ], 'GrokPattern' => [ 'shape' => 'GrokPattern', ], 'CustomPatterns' => [ 'shape' => 'CustomPatterns', ], ], ], 'UpdateJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobName', 'JobUpdate', ], 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], 'JobUpdate' => [ 'shape' => 'JobUpdate', ], ], ], 'UpdateJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], ], ], 'UpdateJsonClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'JsonPath' => [ 'shape' => 'JsonPath', ], ], ], 'UpdateMLTransformRequest' => [ 'type' => 'structure', 'required' => [ 'TransformId', ], 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Parameters' => [ 'shape' => 'TransformParameters', ], 'Role' => [ 'shape' => 'RoleString', ], 'GlueVersion' => [ 'shape' => 'GlueVersionString', ], 'MaxCapacity' => [ 'shape' => 'NullableDouble', ], 'WorkerType' => [ 'shape' => 'WorkerType', ], 'NumberOfWorkers' => [ 'shape' => 'NullableInteger', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'MaxRetries' => [ 'shape' => 'NullableInteger', ], ], ], 'UpdateMLTransformResponse' => [ 'type' => 'structure', 'members' => [ 'TransformId' => [ 'shape' => 'HashString', ], ], ], 'UpdatePartitionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'PartitionValueList', 'PartitionInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionValueList' => [ 'shape' => 'BoundedPartitionValueList', ], 'PartitionInput' => [ 'shape' => 'PartitionInput', ], ], ], 'UpdatePartitionResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateRegistryInput' => [ 'type' => 'structure', 'required' => [ 'RegistryId', 'Description', ], 'members' => [ 'RegistryId' => [ 'shape' => 'RegistryId', ], 'Description' => [ 'shape' => 'DescriptionString', ], ], ], 'UpdateRegistryResponse' => [ 'type' => 'structure', 'members' => [ 'RegistryName' => [ 'shape' => 'SchemaRegistryNameString', ], 'RegistryArn' => [ 'shape' => 'GlueResourceArn', ], ], ], 'UpdateSchemaInput' => [ 'type' => 'structure', 'required' => [ 'SchemaId', ], 'members' => [ 'SchemaId' => [ 'shape' => 'SchemaId', ], 'SchemaVersionNumber' => [ 'shape' => 'SchemaVersionNumber', ], 'Compatibility' => [ 'shape' => 'Compatibility', ], 'Description' => [ 'shape' => 'DescriptionString', ], ], ], 'UpdateSchemaResponse' => [ 'type' => 'structure', 'members' => [ 'SchemaArn' => [ 'shape' => 'GlueResourceArn', ], 'SchemaName' => [ 'shape' => 'SchemaRegistryNameString', ], 'RegistryName' => [ 'shape' => 'SchemaRegistryNameString', ], ], ], 'UpdateTableRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableInput' => [ 'shape' => 'TableInput', ], 'SkipArchive' => [ 'shape' => 'BooleanNullable', ], 'TransactionId' => [ 'shape' => 'TransactionIdString', ], ], ], 'UpdateTableResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateTriggerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'TriggerUpdate', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'TriggerUpdate' => [ 'shape' => 'TriggerUpdate', ], ], ], 'UpdateTriggerResponse' => [ 'type' => 'structure', 'members' => [ 'Trigger' => [ 'shape' => 'Trigger', ], ], ], 'UpdateUserDefinedFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'FunctionName', 'FunctionInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'FunctionName' => [ 'shape' => 'NameString', ], 'FunctionInput' => [ 'shape' => 'UserDefinedFunctionInput', ], ], ], 'UpdateUserDefinedFunctionResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateWorkflowRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'GenericString', ], 'DefaultRunProperties' => [ 'shape' => 'WorkflowRunProperties', ], 'MaxConcurrentRuns' => [ 'shape' => 'NullableInteger', ], ], ], 'UpdateWorkflowResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'UpdateXMLClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Classification' => [ 'shape' => 'Classification', ], 'RowTag' => [ 'shape' => 'RowTag', ], ], ], 'UpdatedTimestamp' => [ 'type' => 'string', ], 'UriString' => [ 'type' => 'string', ], 'UserDefinedFunction' => [ 'type' => 'structure', 'members' => [ 'FunctionName' => [ 'shape' => 'NameString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'ClassName' => [ 'shape' => 'NameString', ], 'OwnerName' => [ 'shape' => 'NameString', ], 'OwnerType' => [ 'shape' => 'PrincipalType', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'ResourceUris' => [ 'shape' => 'ResourceUriList', ], 'CatalogId' => [ 'shape' => 'CatalogIdString', ], ], ], 'UserDefinedFunctionInput' => [ 'type' => 'structure', 'members' => [ 'FunctionName' => [ 'shape' => 'NameString', ], 'ClassName' => [ 'shape' => 'NameString', ], 'OwnerName' => [ 'shape' => 'NameString', ], 'OwnerType' => [ 'shape' => 'PrincipalType', ], 'ResourceUris' => [ 'shape' => 'ResourceUriList', ], ], ], 'UserDefinedFunctionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserDefinedFunction', ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'ValueString' => [ 'type' => 'string', 'max' => 1024, ], 'ValueStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValueString', ], ], 'VersionId' => [ 'type' => 'long', ], 'VersionLongNumber' => [ 'type' => 'long', 'max' => 100000, 'min' => 1, ], 'VersionMismatchException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'VersionString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'VersionsString' => [ 'type' => 'string', 'max' => 100000, 'min' => 1, 'pattern' => '[1-9][0-9]*|[1-9][0-9]*-[1-9][0-9]*', ], 'ViewTextString' => [ 'type' => 'string', 'max' => 409600, ], 'WorkerType' => [ 'type' => 'string', 'enum' => [ 'Standard', 'G.1X', 'G.2X', ], ], 'Workflow' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'GenericString', ], 'DefaultRunProperties' => [ 'shape' => 'WorkflowRunProperties', ], 'CreatedOn' => [ 'shape' => 'TimestampValue', ], 'LastModifiedOn' => [ 'shape' => 'TimestampValue', ], 'LastRun' => [ 'shape' => 'WorkflowRun', ], 'Graph' => [ 'shape' => 'WorkflowGraph', ], 'MaxConcurrentRuns' => [ 'shape' => 'NullableInteger', ], 'BlueprintDetails' => [ 'shape' => 'BlueprintDetails', ], ], ], 'WorkflowGraph' => [ 'type' => 'structure', 'members' => [ 'Nodes' => [ 'shape' => 'NodeList', ], 'Edges' => [ 'shape' => 'EdgeList', ], ], ], 'WorkflowNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], 'max' => 25, 'min' => 1, ], 'WorkflowRun' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'WorkflowRunId' => [ 'shape' => 'IdString', ], 'PreviousRunId' => [ 'shape' => 'IdString', ], 'WorkflowRunProperties' => [ 'shape' => 'WorkflowRunProperties', ], 'StartedOn' => [ 'shape' => 'TimestampValue', ], 'CompletedOn' => [ 'shape' => 'TimestampValue', ], 'Status' => [ 'shape' => 'WorkflowRunStatus', ], 'ErrorMessage' => [ 'shape' => 'ErrorString', ], 'Statistics' => [ 'shape' => 'WorkflowRunStatistics', ], 'Graph' => [ 'shape' => 'WorkflowGraph', ], 'StartingEventBatchCondition' => [ 'shape' => 'StartingEventBatchCondition', ], ], ], 'WorkflowRunProperties' => [ 'type' => 'map', 'key' => [ 'shape' => 'IdString', ], 'value' => [ 'shape' => 'GenericString', ], ], 'WorkflowRunStatistics' => [ 'type' => 'structure', 'members' => [ 'TotalActions' => [ 'shape' => 'IntegerValue', ], 'TimeoutActions' => [ 'shape' => 'IntegerValue', ], 'FailedActions' => [ 'shape' => 'IntegerValue', ], 'StoppedActions' => [ 'shape' => 'IntegerValue', ], 'SucceededActions' => [ 'shape' => 'IntegerValue', ], 'RunningActions' => [ 'shape' => 'IntegerValue', ], ], ], 'WorkflowRunStatus' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'COMPLETED', 'STOPPING', 'STOPPED', 'ERROR', ], ], 'WorkflowRuns' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkflowRun', ], 'max' => 1000, 'min' => 1, ], 'Workflows' => [ 'type' => 'list', 'member' => [ 'shape' => 'Workflow', ], 'max' => 25, 'min' => 1, ], 'XMLClassifier' => [ 'type' => 'structure', 'required' => [ 'Name', 'Classification', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Classification' => [ 'shape' => 'Classification', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastUpdated' => [ 'shape' => 'Timestamp', ], 'Version' => [ 'shape' => 'VersionId', ], 'RowTag' => [ 'shape' => 'RowTag', ], ], ], ],];
