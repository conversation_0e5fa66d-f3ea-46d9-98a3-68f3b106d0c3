<?php
// This file was auto-generated from sdk-root/src/data/securityhub/2018-10-26/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-10-26', 'endpointPrefix' => 'securityhub', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'AWS SecurityHub', 'serviceId' => 'SecurityHub', 'signatureVersion' => 'v4', 'signingName' => 'securityhub', 'uid' => 'securityhub-2018-10-26', ], 'operations' => [ 'AcceptAdministratorInvitation' => [ 'name' => 'AcceptAdministratorInvitation', 'http' => [ 'method' => 'POST', 'requestUri' => '/administrator', ], 'input' => [ 'shape' => 'AcceptAdministratorInvitationRequest', ], 'output' => [ 'shape' => 'AcceptAdministratorInvitationResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidAccessException', ], ], ], 'AcceptInvitation' => [ 'name' => 'AcceptInvitation', 'http' => [ 'method' => 'POST', 'requestUri' => '/master', ], 'input' => [ 'shape' => 'AcceptInvitationRequest', ], 'output' => [ 'shape' => 'AcceptInvitationResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidAccessException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This API has been deprecated, use AcceptAdministratorInvitation API instead.', ], 'BatchDisableStandards' => [ 'name' => 'BatchDisableStandards', 'http' => [ 'method' => 'POST', 'requestUri' => '/standards/deregister', ], 'input' => [ 'shape' => 'BatchDisableStandardsRequest', ], 'output' => [ 'shape' => 'BatchDisableStandardsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'BatchEnableStandards' => [ 'name' => 'BatchEnableStandards', 'http' => [ 'method' => 'POST', 'requestUri' => '/standards/register', ], 'input' => [ 'shape' => 'BatchEnableStandardsRequest', ], 'output' => [ 'shape' => 'BatchEnableStandardsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'BatchImportFindings' => [ 'name' => 'BatchImportFindings', 'http' => [ 'method' => 'POST', 'requestUri' => '/findings/import', ], 'input' => [ 'shape' => 'BatchImportFindingsRequest', ], 'output' => [ 'shape' => 'BatchImportFindingsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], ], ], 'BatchUpdateFindings' => [ 'name' => 'BatchUpdateFindings', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/findings/batchupdate', ], 'input' => [ 'shape' => 'BatchUpdateFindingsRequest', ], 'output' => [ 'shape' => 'BatchUpdateFindingsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], ], ], 'CreateActionTarget' => [ 'name' => 'CreateActionTarget', 'http' => [ 'method' => 'POST', 'requestUri' => '/actionTargets', ], 'input' => [ 'shape' => 'CreateActionTargetRequest', ], 'output' => [ 'shape' => 'CreateActionTargetResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceConflictException', ], ], ], 'CreateFindingAggregator' => [ 'name' => 'CreateFindingAggregator', 'http' => [ 'method' => 'POST', 'requestUri' => '/findingAggregator/create', ], 'input' => [ 'shape' => 'CreateFindingAggregatorRequest', ], 'output' => [ 'shape' => 'CreateFindingAggregatorResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'CreateInsight' => [ 'name' => 'CreateInsight', 'http' => [ 'method' => 'POST', 'requestUri' => '/insights', ], 'input' => [ 'shape' => 'CreateInsightRequest', ], 'output' => [ 'shape' => 'CreateInsightResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'ResourceConflictException', ], ], ], 'CreateMembers' => [ 'name' => 'CreateMembers', 'http' => [ 'method' => 'POST', 'requestUri' => '/members', ], 'input' => [ 'shape' => 'CreateMembersRequest', ], 'output' => [ 'shape' => 'CreateMembersResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'ResourceConflictException', ], ], ], 'DeclineInvitations' => [ 'name' => 'DeclineInvitations', 'http' => [ 'method' => 'POST', 'requestUri' => '/invitations/decline', ], 'input' => [ 'shape' => 'DeclineInvitationsRequest', ], 'output' => [ 'shape' => 'DeclineInvitationsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteActionTarget' => [ 'name' => 'DeleteActionTarget', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/actionTargets/{ActionTargetArn+}', ], 'input' => [ 'shape' => 'DeleteActionTargetRequest', ], 'output' => [ 'shape' => 'DeleteActionTargetResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteFindingAggregator' => [ 'name' => 'DeleteFindingAggregator', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/findingAggregator/delete/{FindingAggregatorArn+}', ], 'input' => [ 'shape' => 'DeleteFindingAggregatorRequest', ], 'output' => [ 'shape' => 'DeleteFindingAggregatorResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteInsight' => [ 'name' => 'DeleteInsight', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/insights/{InsightArn+}', ], 'input' => [ 'shape' => 'DeleteInsightRequest', ], 'output' => [ 'shape' => 'DeleteInsightResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteInvitations' => [ 'name' => 'DeleteInvitations', 'http' => [ 'method' => 'POST', 'requestUri' => '/invitations/delete', ], 'input' => [ 'shape' => 'DeleteInvitationsRequest', ], 'output' => [ 'shape' => 'DeleteInvitationsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidAccessException', ], ], ], 'DeleteMembers' => [ 'name' => 'DeleteMembers', 'http' => [ 'method' => 'POST', 'requestUri' => '/members/delete', ], 'input' => [ 'shape' => 'DeleteMembersRequest', ], 'output' => [ 'shape' => 'DeleteMembersResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeActionTargets' => [ 'name' => 'DescribeActionTargets', 'http' => [ 'method' => 'POST', 'requestUri' => '/actionTargets/get', ], 'input' => [ 'shape' => 'DescribeActionTargetsRequest', ], 'output' => [ 'shape' => 'DescribeActionTargetsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeHub' => [ 'name' => 'DescribeHub', 'http' => [ 'method' => 'GET', 'requestUri' => '/accounts', ], 'input' => [ 'shape' => 'DescribeHubRequest', ], 'output' => [ 'shape' => 'DescribeHubResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeOrganizationConfiguration' => [ 'name' => 'DescribeOrganizationConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/organization/configuration', ], 'input' => [ 'shape' => 'DescribeOrganizationConfigurationRequest', ], 'output' => [ 'shape' => 'DescribeOrganizationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'DescribeProducts' => [ 'name' => 'DescribeProducts', 'http' => [ 'method' => 'GET', 'requestUri' => '/products', ], 'input' => [ 'shape' => 'DescribeProductsRequest', ], 'output' => [ 'shape' => 'DescribeProductsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'DescribeStandards' => [ 'name' => 'DescribeStandards', 'http' => [ 'method' => 'GET', 'requestUri' => '/standards', ], 'input' => [ 'shape' => 'DescribeStandardsRequest', ], 'output' => [ 'shape' => 'DescribeStandardsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], ], ], 'DescribeStandardsControls' => [ 'name' => 'DescribeStandardsControls', 'http' => [ 'method' => 'GET', 'requestUri' => '/standards/controls/{StandardsSubscriptionArn+}', ], 'input' => [ 'shape' => 'DescribeStandardsControlsRequest', ], 'output' => [ 'shape' => 'DescribeStandardsControlsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DisableImportFindingsForProduct' => [ 'name' => 'DisableImportFindingsForProduct', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/productSubscriptions/{ProductSubscriptionArn+}', ], 'input' => [ 'shape' => 'DisableImportFindingsForProductRequest', ], 'output' => [ 'shape' => 'DisableImportFindingsForProductResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'DisableOrganizationAdminAccount' => [ 'name' => 'DisableOrganizationAdminAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/organization/admin/disable', ], 'input' => [ 'shape' => 'DisableOrganizationAdminAccountRequest', ], 'output' => [ 'shape' => 'DisableOrganizationAdminAccountResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'DisableSecurityHub' => [ 'name' => 'DisableSecurityHub', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/accounts', ], 'input' => [ 'shape' => 'DisableSecurityHubRequest', ], 'output' => [ 'shape' => 'DisableSecurityHubResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DisassociateFromAdministratorAccount' => [ 'name' => 'DisassociateFromAdministratorAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/administrator/disassociate', ], 'input' => [ 'shape' => 'DisassociateFromAdministratorAccountRequest', ], 'output' => [ 'shape' => 'DisassociateFromAdministratorAccountResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DisassociateFromMasterAccount' => [ 'name' => 'DisassociateFromMasterAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/master/disassociate', ], 'input' => [ 'shape' => 'DisassociateFromMasterAccountRequest', ], 'output' => [ 'shape' => 'DisassociateFromMasterAccountResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This API has been deprecated, use DisassociateFromAdministratorAccount API instead.', ], 'DisassociateMembers' => [ 'name' => 'DisassociateMembers', 'http' => [ 'method' => 'POST', 'requestUri' => '/members/disassociate', ], 'input' => [ 'shape' => 'DisassociateMembersRequest', ], 'output' => [ 'shape' => 'DisassociateMembersResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'EnableImportFindingsForProduct' => [ 'name' => 'EnableImportFindingsForProduct', 'http' => [ 'method' => 'POST', 'requestUri' => '/productSubscriptions', ], 'input' => [ 'shape' => 'EnableImportFindingsForProductRequest', ], 'output' => [ 'shape' => 'EnableImportFindingsForProductResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'EnableOrganizationAdminAccount' => [ 'name' => 'EnableOrganizationAdminAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/organization/admin/enable', ], 'input' => [ 'shape' => 'EnableOrganizationAdminAccountRequest', ], 'output' => [ 'shape' => 'EnableOrganizationAdminAccountResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'EnableSecurityHub' => [ 'name' => 'EnableSecurityHub', 'http' => [ 'method' => 'POST', 'requestUri' => '/accounts', ], 'input' => [ 'shape' => 'EnableSecurityHubRequest', ], 'output' => [ 'shape' => 'EnableSecurityHubResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetAdministratorAccount' => [ 'name' => 'GetAdministratorAccount', 'http' => [ 'method' => 'GET', 'requestUri' => '/administrator', ], 'input' => [ 'shape' => 'GetAdministratorAccountRequest', ], 'output' => [ 'shape' => 'GetAdministratorAccountResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetEnabledStandards' => [ 'name' => 'GetEnabledStandards', 'http' => [ 'method' => 'POST', 'requestUri' => '/standards/get', ], 'input' => [ 'shape' => 'GetEnabledStandardsRequest', ], 'output' => [ 'shape' => 'GetEnabledStandardsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'GetFindingAggregator' => [ 'name' => 'GetFindingAggregator', 'http' => [ 'method' => 'GET', 'requestUri' => '/findingAggregator/get/{FindingAggregatorArn+}', ], 'input' => [ 'shape' => 'GetFindingAggregatorRequest', ], 'output' => [ 'shape' => 'GetFindingAggregatorResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetFindings' => [ 'name' => 'GetFindings', 'http' => [ 'method' => 'POST', 'requestUri' => '/findings', ], 'input' => [ 'shape' => 'GetFindingsRequest', ], 'output' => [ 'shape' => 'GetFindingsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'GetInsightResults' => [ 'name' => 'GetInsightResults', 'http' => [ 'method' => 'GET', 'requestUri' => '/insights/results/{InsightArn+}', ], 'input' => [ 'shape' => 'GetInsightResultsRequest', ], 'output' => [ 'shape' => 'GetInsightResultsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetInsights' => [ 'name' => 'GetInsights', 'http' => [ 'method' => 'POST', 'requestUri' => '/insights/get', ], 'input' => [ 'shape' => 'GetInsightsRequest', ], 'output' => [ 'shape' => 'GetInsightsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetInvitationsCount' => [ 'name' => 'GetInvitationsCount', 'http' => [ 'method' => 'GET', 'requestUri' => '/invitations/count', ], 'input' => [ 'shape' => 'GetInvitationsCountRequest', ], 'output' => [ 'shape' => 'GetInvitationsCountResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'GetMasterAccount' => [ 'name' => 'GetMasterAccount', 'http' => [ 'method' => 'GET', 'requestUri' => '/master', ], 'input' => [ 'shape' => 'GetMasterAccountRequest', ], 'output' => [ 'shape' => 'GetMasterAccountResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'This API has been deprecated, use GetAdministratorAccount API instead.', ], 'GetMembers' => [ 'name' => 'GetMembers', 'http' => [ 'method' => 'POST', 'requestUri' => '/members/get', ], 'input' => [ 'shape' => 'GetMembersRequest', ], 'output' => [ 'shape' => 'GetMembersResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'InviteMembers' => [ 'name' => 'InviteMembers', 'http' => [ 'method' => 'POST', 'requestUri' => '/members/invite', ], 'input' => [ 'shape' => 'InviteMembersRequest', ], 'output' => [ 'shape' => 'InviteMembersResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListEnabledProductsForImport' => [ 'name' => 'ListEnabledProductsForImport', 'http' => [ 'method' => 'GET', 'requestUri' => '/productSubscriptions', ], 'input' => [ 'shape' => 'ListEnabledProductsForImportRequest', ], 'output' => [ 'shape' => 'ListEnabledProductsForImportResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], ], ], 'ListFindingAggregators' => [ 'name' => 'ListFindingAggregators', 'http' => [ 'method' => 'GET', 'requestUri' => '/findingAggregator/list', ], 'input' => [ 'shape' => 'ListFindingAggregatorsRequest', ], 'output' => [ 'shape' => 'ListFindingAggregatorsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'ListInvitations' => [ 'name' => 'ListInvitations', 'http' => [ 'method' => 'GET', 'requestUri' => '/invitations', ], 'input' => [ 'shape' => 'ListInvitationsRequest', ], 'output' => [ 'shape' => 'ListInvitationsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'ListMembers' => [ 'name' => 'ListMembers', 'http' => [ 'method' => 'GET', 'requestUri' => '/members', ], 'input' => [ 'shape' => 'ListMembersRequest', ], 'output' => [ 'shape' => 'ListMembersResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'ListOrganizationAdminAccounts' => [ 'name' => 'ListOrganizationAdminAccounts', 'http' => [ 'method' => 'GET', 'requestUri' => '/organization/admin', ], 'input' => [ 'shape' => 'ListOrganizationAdminAccountsRequest', ], 'output' => [ 'shape' => 'ListOrganizationAdminAccountsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{ResourceArn}', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{ResourceArn}', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{ResourceArn}', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateActionTarget' => [ 'name' => 'UpdateActionTarget', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/actionTargets/{ActionTargetArn+}', ], 'input' => [ 'shape' => 'UpdateActionTargetRequest', ], 'output' => [ 'shape' => 'UpdateActionTargetResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateFindingAggregator' => [ 'name' => 'UpdateFindingAggregator', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/findingAggregator/update', ], 'input' => [ 'shape' => 'UpdateFindingAggregatorRequest', ], 'output' => [ 'shape' => 'UpdateFindingAggregatorResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateFindings' => [ 'name' => 'UpdateFindings', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/findings', ], 'input' => [ 'shape' => 'UpdateFindingsRequest', ], 'output' => [ 'shape' => 'UpdateFindingsResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateInsight' => [ 'name' => 'UpdateInsight', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/insights/{InsightArn+}', ], 'input' => [ 'shape' => 'UpdateInsightRequest', ], 'output' => [ 'shape' => 'UpdateInsightResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateOrganizationConfiguration' => [ 'name' => 'UpdateOrganizationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/organization/configuration', ], 'input' => [ 'shape' => 'UpdateOrganizationConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateOrganizationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'UpdateSecurityHubConfiguration' => [ 'name' => 'UpdateSecurityHubConfiguration', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/accounts', ], 'input' => [ 'shape' => 'UpdateSecurityHubConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateSecurityHubConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateStandardsControl' => [ 'name' => 'UpdateStandardsControl', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/standards/control/{StandardsControlArn+}', ], 'input' => [ 'shape' => 'UpdateStandardsControlRequest', ], 'output' => [ 'shape' => 'UpdateStandardsControlResponse', ], 'errors' => [ [ 'shape' => 'InternalException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InvalidAccessException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], ], 'shapes' => [ 'AcceptAdministratorInvitationRequest' => [ 'type' => 'structure', 'required' => [ 'AdministratorId', 'InvitationId', ], 'members' => [ 'AdministratorId' => [ 'shape' => 'NonEmptyString', ], 'InvitationId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AcceptAdministratorInvitationResponse' => [ 'type' => 'structure', 'members' => [], ], 'AcceptInvitationRequest' => [ 'type' => 'structure', 'required' => [ 'MasterId', 'InvitationId', ], 'members' => [ 'MasterId' => [ 'shape' => 'NonEmptyString', ], 'InvitationId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AcceptInvitationResponse' => [ 'type' => 'structure', 'members' => [], ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], 'Code' => [ 'shape' => 'NonEmptyString', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'AccountDetails' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'Email' => [ 'shape' => 'NonEmptyString', ], ], ], 'AccountDetailsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountDetails', ], ], 'AccountId' => [ 'type' => 'string', ], 'AccountIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'Action' => [ 'type' => 'structure', 'members' => [ 'ActionType' => [ 'shape' => 'NonEmptyString', ], 'NetworkConnectionAction' => [ 'shape' => 'NetworkConnectionAction', ], 'AwsApiCallAction' => [ 'shape' => 'AwsApiCallAction', ], 'DnsRequestAction' => [ 'shape' => 'DnsRequestAction', ], 'PortProbeAction' => [ 'shape' => 'PortProbeAction', ], ], ], 'ActionLocalIpDetails' => [ 'type' => 'structure', 'members' => [ 'IpAddressV4' => [ 'shape' => 'NonEmptyString', ], ], ], 'ActionLocalPortDetails' => [ 'type' => 'structure', 'members' => [ 'Port' => [ 'shape' => 'Integer', ], 'PortName' => [ 'shape' => 'NonEmptyString', ], ], ], 'ActionRemoteIpDetails' => [ 'type' => 'structure', 'members' => [ 'IpAddressV4' => [ 'shape' => 'NonEmptyString', ], 'Organization' => [ 'shape' => 'IpOrganizationDetails', ], 'Country' => [ 'shape' => 'Country', ], 'City' => [ 'shape' => 'City', ], 'GeoLocation' => [ 'shape' => 'GeoLocation', ], ], ], 'ActionRemotePortDetails' => [ 'type' => 'structure', 'members' => [ 'Port' => [ 'shape' => 'Integer', ], 'PortName' => [ 'shape' => 'NonEmptyString', ], ], ], 'ActionTarget' => [ 'type' => 'structure', 'required' => [ 'ActionTargetArn', 'Name', 'Description', ], 'members' => [ 'ActionTargetArn' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], ], ], 'ActionTargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ActionTarget', ], ], 'Adjustment' => [ 'type' => 'structure', 'members' => [ 'Metric' => [ 'shape' => 'NonEmptyString', ], 'Reason' => [ 'shape' => 'NonEmptyString', ], ], ], 'AdjustmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Adjustment', ], ], 'AdminAccount' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'AdminStatus', ], ], ], 'AdminAccounts' => [ 'type' => 'list', 'member' => [ 'shape' => 'AdminAccount', ], ], 'AdminStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLE_IN_PROGRESS', ], ], 'AdminsMaxResults' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'ArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'AvailabilityZone' => [ 'type' => 'structure', 'members' => [ 'ZoneName' => [ 'shape' => 'NonEmptyString', ], 'SubnetId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AvailabilityZones' => [ 'type' => 'list', 'member' => [ 'shape' => 'AvailabilityZone', ], ], 'AwsApiCallAction' => [ 'type' => 'structure', 'members' => [ 'Api' => [ 'shape' => 'NonEmptyString', ], 'ServiceName' => [ 'shape' => 'NonEmptyString', ], 'CallerType' => [ 'shape' => 'NonEmptyString', ], 'RemoteIpDetails' => [ 'shape' => 'ActionRemoteIpDetails', ], 'DomainDetails' => [ 'shape' => 'AwsApiCallActionDomainDetails', ], 'AffectedResources' => [ 'shape' => 'FieldMap', ], 'FirstSeen' => [ 'shape' => 'NonEmptyString', ], 'LastSeen' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsApiCallActionDomainDetails' => [ 'type' => 'structure', 'members' => [ 'Domain' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsApiGatewayAccessLogSettings' => [ 'type' => 'structure', 'members' => [ 'Format' => [ 'shape' => 'NonEmptyString', ], 'DestinationArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsApiGatewayCanarySettings' => [ 'type' => 'structure', 'members' => [ 'PercentTraffic' => [ 'shape' => 'Double', ], 'DeploymentId' => [ 'shape' => 'NonEmptyString', ], 'StageVariableOverrides' => [ 'shape' => 'FieldMap', ], 'UseStageCache' => [ 'shape' => 'Boolean', ], ], ], 'AwsApiGatewayEndpointConfiguration' => [ 'type' => 'structure', 'members' => [ 'Types' => [ 'shape' => 'NonEmptyStringList', ], ], ], 'AwsApiGatewayMethodSettings' => [ 'type' => 'structure', 'members' => [ 'MetricsEnabled' => [ 'shape' => 'Boolean', ], 'LoggingLevel' => [ 'shape' => 'NonEmptyString', ], 'DataTraceEnabled' => [ 'shape' => 'Boolean', ], 'ThrottlingBurstLimit' => [ 'shape' => 'Integer', ], 'ThrottlingRateLimit' => [ 'shape' => 'Double', ], 'CachingEnabled' => [ 'shape' => 'Boolean', ], 'CacheTtlInSeconds' => [ 'shape' => 'Integer', ], 'CacheDataEncrypted' => [ 'shape' => 'Boolean', ], 'RequireAuthorizationForCacheControl' => [ 'shape' => 'Boolean', ], 'UnauthorizedCacheControlHeaderStrategy' => [ 'shape' => 'NonEmptyString', ], 'HttpMethod' => [ 'shape' => 'NonEmptyString', ], 'ResourcePath' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsApiGatewayMethodSettingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsApiGatewayMethodSettings', ], ], 'AwsApiGatewayRestApiDetails' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'CreatedDate' => [ 'shape' => 'NonEmptyString', ], 'Version' => [ 'shape' => 'NonEmptyString', ], 'BinaryMediaTypes' => [ 'shape' => 'NonEmptyStringList', ], 'MinimumCompressionSize' => [ 'shape' => 'Integer', ], 'ApiKeySource' => [ 'shape' => 'NonEmptyString', ], 'EndpointConfiguration' => [ 'shape' => 'AwsApiGatewayEndpointConfiguration', ], ], ], 'AwsApiGatewayStageDetails' => [ 'type' => 'structure', 'members' => [ 'DeploymentId' => [ 'shape' => 'NonEmptyString', ], 'ClientCertificateId' => [ 'shape' => 'NonEmptyString', ], 'StageName' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'CacheClusterEnabled' => [ 'shape' => 'Boolean', ], 'CacheClusterSize' => [ 'shape' => 'NonEmptyString', ], 'CacheClusterStatus' => [ 'shape' => 'NonEmptyString', ], 'MethodSettings' => [ 'shape' => 'AwsApiGatewayMethodSettingsList', ], 'Variables' => [ 'shape' => 'FieldMap', ], 'DocumentationVersion' => [ 'shape' => 'NonEmptyString', ], 'AccessLogSettings' => [ 'shape' => 'AwsApiGatewayAccessLogSettings', ], 'CanarySettings' => [ 'shape' => 'AwsApiGatewayCanarySettings', ], 'TracingEnabled' => [ 'shape' => 'Boolean', ], 'CreatedDate' => [ 'shape' => 'NonEmptyString', ], 'LastUpdatedDate' => [ 'shape' => 'NonEmptyString', ], 'WebAclArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsApiGatewayV2ApiDetails' => [ 'type' => 'structure', 'members' => [ 'ApiEndpoint' => [ 'shape' => 'NonEmptyString', ], 'ApiId' => [ 'shape' => 'NonEmptyString', ], 'ApiKeySelectionExpression' => [ 'shape' => 'NonEmptyString', ], 'CreatedDate' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'Version' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'ProtocolType' => [ 'shape' => 'NonEmptyString', ], 'RouteSelectionExpression' => [ 'shape' => 'NonEmptyString', ], 'CorsConfiguration' => [ 'shape' => 'AwsCorsConfiguration', ], ], ], 'AwsApiGatewayV2RouteSettings' => [ 'type' => 'structure', 'members' => [ 'DetailedMetricsEnabled' => [ 'shape' => 'Boolean', ], 'LoggingLevel' => [ 'shape' => 'NonEmptyString', ], 'DataTraceEnabled' => [ 'shape' => 'Boolean', ], 'ThrottlingBurstLimit' => [ 'shape' => 'Integer', ], 'ThrottlingRateLimit' => [ 'shape' => 'Double', ], ], ], 'AwsApiGatewayV2StageDetails' => [ 'type' => 'structure', 'members' => [ 'ClientCertificateId' => [ 'shape' => 'NonEmptyString', ], 'CreatedDate' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'DefaultRouteSettings' => [ 'shape' => 'AwsApiGatewayV2RouteSettings', ], 'DeploymentId' => [ 'shape' => 'NonEmptyString', ], 'LastUpdatedDate' => [ 'shape' => 'NonEmptyString', ], 'RouteSettings' => [ 'shape' => 'AwsApiGatewayV2RouteSettings', ], 'StageName' => [ 'shape' => 'NonEmptyString', ], 'StageVariables' => [ 'shape' => 'FieldMap', ], 'AccessLogSettings' => [ 'shape' => 'AwsApiGatewayAccessLogSettings', ], 'AutoDeploy' => [ 'shape' => 'Boolean', ], 'LastDeploymentStatusMessage' => [ 'shape' => 'NonEmptyString', ], 'ApiGatewayManaged' => [ 'shape' => 'Boolean', ], ], ], 'AwsAutoScalingAutoScalingGroupDetails' => [ 'type' => 'structure', 'members' => [ 'LaunchConfigurationName' => [ 'shape' => 'NonEmptyString', ], 'LoadBalancerNames' => [ 'shape' => 'StringList', ], 'HealthCheckType' => [ 'shape' => 'NonEmptyString', ], 'HealthCheckGracePeriod' => [ 'shape' => 'Integer', ], 'CreatedTime' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsAutoScalingLaunchConfigurationBlockDeviceMappingsDetails' => [ 'type' => 'structure', 'members' => [ 'DeviceName' => [ 'shape' => 'NonEmptyString', ], 'Ebs' => [ 'shape' => 'AwsAutoScalingLaunchConfigurationBlockDeviceMappingsEbsDetails', ], 'NoDevice' => [ 'shape' => 'Boolean', ], 'VirtualName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsAutoScalingLaunchConfigurationBlockDeviceMappingsEbsDetails' => [ 'type' => 'structure', 'members' => [ 'DeleteOnTermination' => [ 'shape' => 'Boolean', ], 'Encrypted' => [ 'shape' => 'Boolean', ], 'Iops' => [ 'shape' => 'Integer', ], 'SnapshotId' => [ 'shape' => 'NonEmptyString', ], 'VolumeSize' => [ 'shape' => 'Integer', ], 'VolumeType' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsAutoScalingLaunchConfigurationBlockDeviceMappingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsAutoScalingLaunchConfigurationBlockDeviceMappingsDetails', ], ], 'AwsAutoScalingLaunchConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'AssociatePublicIpAddress' => [ 'shape' => 'Boolean', ], 'BlockDeviceMappings' => [ 'shape' => 'AwsAutoScalingLaunchConfigurationBlockDeviceMappingsList', ], 'ClassicLinkVpcId' => [ 'shape' => 'NonEmptyString', ], 'ClassicLinkVpcSecurityGroups' => [ 'shape' => 'NonEmptyStringList', ], 'CreatedTime' => [ 'shape' => 'NonEmptyString', ], 'EbsOptimized' => [ 'shape' => 'Boolean', ], 'IamInstanceProfile' => [ 'shape' => 'NonEmptyString', ], 'ImageId' => [ 'shape' => 'NonEmptyString', ], 'InstanceMonitoring' => [ 'shape' => 'AwsAutoScalingLaunchConfigurationInstanceMonitoringDetails', ], 'InstanceType' => [ 'shape' => 'NonEmptyString', ], 'KernelId' => [ 'shape' => 'NonEmptyString', ], 'KeyName' => [ 'shape' => 'NonEmptyString', ], 'LaunchConfigurationName' => [ 'shape' => 'NonEmptyString', ], 'PlacementTenancy' => [ 'shape' => 'NonEmptyString', ], 'RamdiskId' => [ 'shape' => 'NonEmptyString', ], 'SecurityGroups' => [ 'shape' => 'NonEmptyStringList', ], 'SpotPrice' => [ 'shape' => 'NonEmptyString', ], 'UserData' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsAutoScalingLaunchConfigurationInstanceMonitoringDetails' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'AwsCertificateManagerCertificateDetails' => [ 'type' => 'structure', 'members' => [ 'CertificateAuthorityArn' => [ 'shape' => 'NonEmptyString', ], 'CreatedAt' => [ 'shape' => 'NonEmptyString', ], 'DomainName' => [ 'shape' => 'NonEmptyString', ], 'DomainValidationOptions' => [ 'shape' => 'AwsCertificateManagerCertificateDomainValidationOptions', ], 'ExtendedKeyUsages' => [ 'shape' => 'AwsCertificateManagerCertificateExtendedKeyUsages', ], 'FailureReason' => [ 'shape' => 'NonEmptyString', ], 'ImportedAt' => [ 'shape' => 'NonEmptyString', ], 'InUseBy' => [ 'shape' => 'StringList', ], 'IssuedAt' => [ 'shape' => 'NonEmptyString', ], 'Issuer' => [ 'shape' => 'NonEmptyString', ], 'KeyAlgorithm' => [ 'shape' => 'NonEmptyString', ], 'KeyUsages' => [ 'shape' => 'AwsCertificateManagerCertificateKeyUsages', ], 'NotAfter' => [ 'shape' => 'NonEmptyString', ], 'NotBefore' => [ 'shape' => 'NonEmptyString', ], 'Options' => [ 'shape' => 'AwsCertificateManagerCertificateOptions', ], 'RenewalEligibility' => [ 'shape' => 'NonEmptyString', ], 'RenewalSummary' => [ 'shape' => 'AwsCertificateManagerCertificateRenewalSummary', ], 'Serial' => [ 'shape' => 'NonEmptyString', ], 'SignatureAlgorithm' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'Subject' => [ 'shape' => 'NonEmptyString', ], 'SubjectAlternativeNames' => [ 'shape' => 'StringList', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCertificateManagerCertificateDomainValidationOption' => [ 'type' => 'structure', 'members' => [ 'DomainName' => [ 'shape' => 'NonEmptyString', ], 'ResourceRecord' => [ 'shape' => 'AwsCertificateManagerCertificateResourceRecord', ], 'ValidationDomain' => [ 'shape' => 'NonEmptyString', ], 'ValidationEmails' => [ 'shape' => 'StringList', ], 'ValidationMethod' => [ 'shape' => 'NonEmptyString', ], 'ValidationStatus' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCertificateManagerCertificateDomainValidationOptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsCertificateManagerCertificateDomainValidationOption', ], ], 'AwsCertificateManagerCertificateExtendedKeyUsage' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'OId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCertificateManagerCertificateExtendedKeyUsages' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsCertificateManagerCertificateExtendedKeyUsage', ], ], 'AwsCertificateManagerCertificateKeyUsage' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCertificateManagerCertificateKeyUsages' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsCertificateManagerCertificateKeyUsage', ], ], 'AwsCertificateManagerCertificateOptions' => [ 'type' => 'structure', 'members' => [ 'CertificateTransparencyLoggingPreference' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCertificateManagerCertificateRenewalSummary' => [ 'type' => 'structure', 'members' => [ 'DomainValidationOptions' => [ 'shape' => 'AwsCertificateManagerCertificateDomainValidationOptions', ], 'RenewalStatus' => [ 'shape' => 'NonEmptyString', ], 'RenewalStatusReason' => [ 'shape' => 'NonEmptyString', ], 'UpdatedAt' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCertificateManagerCertificateResourceRecord' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCloudFrontDistributionCacheBehavior' => [ 'type' => 'structure', 'members' => [ 'ViewerProtocolPolicy' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCloudFrontDistributionCacheBehaviors' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'AwsCloudFrontDistributionCacheBehaviorsItemList', ], ], ], 'AwsCloudFrontDistributionCacheBehaviorsItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsCloudFrontDistributionCacheBehavior', ], ], 'AwsCloudFrontDistributionDefaultCacheBehavior' => [ 'type' => 'structure', 'members' => [ 'ViewerProtocolPolicy' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCloudFrontDistributionDetails' => [ 'type' => 'structure', 'members' => [ 'CacheBehaviors' => [ 'shape' => 'AwsCloudFrontDistributionCacheBehaviors', ], 'DefaultCacheBehavior' => [ 'shape' => 'AwsCloudFrontDistributionDefaultCacheBehavior', ], 'DefaultRootObject' => [ 'shape' => 'NonEmptyString', ], 'DomainName' => [ 'shape' => 'NonEmptyString', ], 'ETag' => [ 'shape' => 'NonEmptyString', ], 'LastModifiedTime' => [ 'shape' => 'NonEmptyString', ], 'Logging' => [ 'shape' => 'AwsCloudFrontDistributionLogging', ], 'Origins' => [ 'shape' => 'AwsCloudFrontDistributionOrigins', ], 'OriginGroups' => [ 'shape' => 'AwsCloudFrontDistributionOriginGroups', ], 'ViewerCertificate' => [ 'shape' => 'AwsCloudFrontDistributionViewerCertificate', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'WebAclId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCloudFrontDistributionLogging' => [ 'type' => 'structure', 'members' => [ 'Bucket' => [ 'shape' => 'NonEmptyString', ], 'Enabled' => [ 'shape' => 'Boolean', ], 'IncludeCookies' => [ 'shape' => 'Boolean', ], 'Prefix' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCloudFrontDistributionOriginGroup' => [ 'type' => 'structure', 'members' => [ 'FailoverCriteria' => [ 'shape' => 'AwsCloudFrontDistributionOriginGroupFailover', ], ], ], 'AwsCloudFrontDistributionOriginGroupFailover' => [ 'type' => 'structure', 'members' => [ 'StatusCodes' => [ 'shape' => 'AwsCloudFrontDistributionOriginGroupFailoverStatusCodes', ], ], ], 'AwsCloudFrontDistributionOriginGroupFailoverStatusCodes' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'AwsCloudFrontDistributionOriginGroupFailoverStatusCodesItemList', ], 'Quantity' => [ 'shape' => 'Integer', ], ], ], 'AwsCloudFrontDistributionOriginGroupFailoverStatusCodesItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Integer', ], ], 'AwsCloudFrontDistributionOriginGroups' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'AwsCloudFrontDistributionOriginGroupsItemList', ], ], ], 'AwsCloudFrontDistributionOriginGroupsItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsCloudFrontDistributionOriginGroup', ], ], 'AwsCloudFrontDistributionOriginItem' => [ 'type' => 'structure', 'members' => [ 'DomainName' => [ 'shape' => 'NonEmptyString', ], 'Id' => [ 'shape' => 'NonEmptyString', ], 'OriginPath' => [ 'shape' => 'NonEmptyString', ], 'S3OriginConfig' => [ 'shape' => 'AwsCloudFrontDistributionOriginS3OriginConfig', ], ], ], 'AwsCloudFrontDistributionOriginItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsCloudFrontDistributionOriginItem', ], ], 'AwsCloudFrontDistributionOriginS3OriginConfig' => [ 'type' => 'structure', 'members' => [ 'OriginAccessIdentity' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCloudFrontDistributionOrigins' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'AwsCloudFrontDistributionOriginItemList', ], ], ], 'AwsCloudFrontDistributionViewerCertificate' => [ 'type' => 'structure', 'members' => [ 'AcmCertificateArn' => [ 'shape' => 'NonEmptyString', ], 'Certificate' => [ 'shape' => 'NonEmptyString', ], 'CertificateSource' => [ 'shape' => 'NonEmptyString', ], 'CloudFrontDefaultCertificate' => [ 'shape' => 'Boolean', ], 'IamCertificateId' => [ 'shape' => 'NonEmptyString', ], 'MinimumProtocolVersion' => [ 'shape' => 'NonEmptyString', ], 'SslSupportMethod' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCloudTrailTrailDetails' => [ 'type' => 'structure', 'members' => [ 'CloudWatchLogsLogGroupArn' => [ 'shape' => 'NonEmptyString', ], 'CloudWatchLogsRoleArn' => [ 'shape' => 'NonEmptyString', ], 'HasCustomEventSelectors' => [ 'shape' => 'Boolean', ], 'HomeRegion' => [ 'shape' => 'NonEmptyString', ], 'IncludeGlobalServiceEvents' => [ 'shape' => 'Boolean', ], 'IsMultiRegionTrail' => [ 'shape' => 'Boolean', ], 'IsOrganizationTrail' => [ 'shape' => 'Boolean', ], 'KmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'LogFileValidationEnabled' => [ 'shape' => 'Boolean', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'S3BucketName' => [ 'shape' => 'NonEmptyString', ], 'S3KeyPrefix' => [ 'shape' => 'NonEmptyString', ], 'SnsTopicArn' => [ 'shape' => 'NonEmptyString', ], 'SnsTopicName' => [ 'shape' => 'NonEmptyString', ], 'TrailArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCodeBuildProjectArtifactsDetails' => [ 'type' => 'structure', 'members' => [ 'ArtifactIdentifier' => [ 'shape' => 'NonEmptyString', ], 'EncryptionDisabled' => [ 'shape' => 'Boolean', ], 'Location' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'NamespaceType' => [ 'shape' => 'NonEmptyString', ], 'OverrideArtifactName' => [ 'shape' => 'Boolean', ], 'Packaging' => [ 'shape' => 'NonEmptyString', ], 'Path' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCodeBuildProjectArtifactsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsCodeBuildProjectArtifactsDetails', ], ], 'AwsCodeBuildProjectDetails' => [ 'type' => 'structure', 'members' => [ 'EncryptionKey' => [ 'shape' => 'NonEmptyString', ], 'Artifacts' => [ 'shape' => 'AwsCodeBuildProjectArtifactsList', ], 'Environment' => [ 'shape' => 'AwsCodeBuildProjectEnvironment', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Source' => [ 'shape' => 'AwsCodeBuildProjectSource', ], 'ServiceRole' => [ 'shape' => 'NonEmptyString', ], 'LogsConfig' => [ 'shape' => 'AwsCodeBuildProjectLogsConfigDetails', ], 'VpcConfig' => [ 'shape' => 'AwsCodeBuildProjectVpcConfig', ], ], ], 'AwsCodeBuildProjectEnvironment' => [ 'type' => 'structure', 'members' => [ 'Certificate' => [ 'shape' => 'NonEmptyString', ], 'EnvironmentVariables' => [ 'shape' => 'AwsCodeBuildProjectEnvironmentEnvironmentVariablesList', ], 'PrivilegedMode' => [ 'shape' => 'Boolean', ], 'ImagePullCredentialsType' => [ 'shape' => 'NonEmptyString', ], 'RegistryCredential' => [ 'shape' => 'AwsCodeBuildProjectEnvironmentRegistryCredential', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCodeBuildProjectEnvironmentEnvironmentVariablesDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCodeBuildProjectEnvironmentEnvironmentVariablesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsCodeBuildProjectEnvironmentEnvironmentVariablesDetails', ], ], 'AwsCodeBuildProjectEnvironmentRegistryCredential' => [ 'type' => 'structure', 'members' => [ 'Credential' => [ 'shape' => 'NonEmptyString', ], 'CredentialProvider' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCodeBuildProjectLogsConfigCloudWatchLogsDetails' => [ 'type' => 'structure', 'members' => [ 'GroupName' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'StreamName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCodeBuildProjectLogsConfigDetails' => [ 'type' => 'structure', 'members' => [ 'CloudWatchLogs' => [ 'shape' => 'AwsCodeBuildProjectLogsConfigCloudWatchLogsDetails', ], 'S3Logs' => [ 'shape' => 'AwsCodeBuildProjectLogsConfigS3LogsDetails', ], ], ], 'AwsCodeBuildProjectLogsConfigS3LogsDetails' => [ 'type' => 'structure', 'members' => [ 'EncryptionDisabled' => [ 'shape' => 'Boolean', ], 'Location' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsCodeBuildProjectSource' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'NonEmptyString', ], 'Location' => [ 'shape' => 'NonEmptyString', ], 'GitCloneDepth' => [ 'shape' => 'Integer', ], 'InsecureSsl' => [ 'shape' => 'Boolean', ], ], ], 'AwsCodeBuildProjectVpcConfig' => [ 'type' => 'structure', 'members' => [ 'VpcId' => [ 'shape' => 'NonEmptyString', ], 'Subnets' => [ 'shape' => 'NonEmptyStringList', ], 'SecurityGroupIds' => [ 'shape' => 'NonEmptyStringList', ], ], ], 'AwsCorsConfiguration' => [ 'type' => 'structure', 'members' => [ 'AllowOrigins' => [ 'shape' => 'NonEmptyStringList', ], 'AllowCredentials' => [ 'shape' => 'Boolean', ], 'ExposeHeaders' => [ 'shape' => 'NonEmptyStringList', ], 'MaxAge' => [ 'shape' => 'Integer', ], 'AllowMethods' => [ 'shape' => 'NonEmptyStringList', ], 'AllowHeaders' => [ 'shape' => 'NonEmptyStringList', ], ], ], 'AwsDynamoDbTableAttributeDefinition' => [ 'type' => 'structure', 'members' => [ 'AttributeName' => [ 'shape' => 'NonEmptyString', ], 'AttributeType' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsDynamoDbTableAttributeDefinitionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsDynamoDbTableAttributeDefinition', ], ], 'AwsDynamoDbTableBillingModeSummary' => [ 'type' => 'structure', 'members' => [ 'BillingMode' => [ 'shape' => 'NonEmptyString', ], 'LastUpdateToPayPerRequestDateTime' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsDynamoDbTableDetails' => [ 'type' => 'structure', 'members' => [ 'AttributeDefinitions' => [ 'shape' => 'AwsDynamoDbTableAttributeDefinitionList', ], 'BillingModeSummary' => [ 'shape' => 'AwsDynamoDbTableBillingModeSummary', ], 'CreationDateTime' => [ 'shape' => 'NonEmptyString', ], 'GlobalSecondaryIndexes' => [ 'shape' => 'AwsDynamoDbTableGlobalSecondaryIndexList', ], 'GlobalTableVersion' => [ 'shape' => 'NonEmptyString', ], 'ItemCount' => [ 'shape' => 'Integer', ], 'KeySchema' => [ 'shape' => 'AwsDynamoDbTableKeySchemaList', ], 'LatestStreamArn' => [ 'shape' => 'NonEmptyString', ], 'LatestStreamLabel' => [ 'shape' => 'NonEmptyString', ], 'LocalSecondaryIndexes' => [ 'shape' => 'AwsDynamoDbTableLocalSecondaryIndexList', ], 'ProvisionedThroughput' => [ 'shape' => 'AwsDynamoDbTableProvisionedThroughput', ], 'Replicas' => [ 'shape' => 'AwsDynamoDbTableReplicaList', ], 'RestoreSummary' => [ 'shape' => 'AwsDynamoDbTableRestoreSummary', ], 'SseDescription' => [ 'shape' => 'AwsDynamoDbTableSseDescription', ], 'StreamSpecification' => [ 'shape' => 'AwsDynamoDbTableStreamSpecification', ], 'TableId' => [ 'shape' => 'NonEmptyString', ], 'TableName' => [ 'shape' => 'NonEmptyString', ], 'TableSizeBytes' => [ 'shape' => 'SizeBytes', ], 'TableStatus' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsDynamoDbTableGlobalSecondaryIndex' => [ 'type' => 'structure', 'members' => [ 'Backfilling' => [ 'shape' => 'Boolean', ], 'IndexArn' => [ 'shape' => 'NonEmptyString', ], 'IndexName' => [ 'shape' => 'NonEmptyString', ], 'IndexSizeBytes' => [ 'shape' => 'SizeBytes', ], 'IndexStatus' => [ 'shape' => 'NonEmptyString', ], 'ItemCount' => [ 'shape' => 'Integer', ], 'KeySchema' => [ 'shape' => 'AwsDynamoDbTableKeySchemaList', ], 'Projection' => [ 'shape' => 'AwsDynamoDbTableProjection', ], 'ProvisionedThroughput' => [ 'shape' => 'AwsDynamoDbTableProvisionedThroughput', ], ], ], 'AwsDynamoDbTableGlobalSecondaryIndexList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsDynamoDbTableGlobalSecondaryIndex', ], ], 'AwsDynamoDbTableKeySchema' => [ 'type' => 'structure', 'members' => [ 'AttributeName' => [ 'shape' => 'NonEmptyString', ], 'KeyType' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsDynamoDbTableKeySchemaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsDynamoDbTableKeySchema', ], ], 'AwsDynamoDbTableLocalSecondaryIndex' => [ 'type' => 'structure', 'members' => [ 'IndexArn' => [ 'shape' => 'NonEmptyString', ], 'IndexName' => [ 'shape' => 'NonEmptyString', ], 'KeySchema' => [ 'shape' => 'AwsDynamoDbTableKeySchemaList', ], 'Projection' => [ 'shape' => 'AwsDynamoDbTableProjection', ], ], ], 'AwsDynamoDbTableLocalSecondaryIndexList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsDynamoDbTableLocalSecondaryIndex', ], ], 'AwsDynamoDbTableProjection' => [ 'type' => 'structure', 'members' => [ 'NonKeyAttributes' => [ 'shape' => 'StringList', ], 'ProjectionType' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsDynamoDbTableProvisionedThroughput' => [ 'type' => 'structure', 'members' => [ 'LastDecreaseDateTime' => [ 'shape' => 'NonEmptyString', ], 'LastIncreaseDateTime' => [ 'shape' => 'NonEmptyString', ], 'NumberOfDecreasesToday' => [ 'shape' => 'Integer', ], 'ReadCapacityUnits' => [ 'shape' => 'Integer', ], 'WriteCapacityUnits' => [ 'shape' => 'Integer', ], ], ], 'AwsDynamoDbTableProvisionedThroughputOverride' => [ 'type' => 'structure', 'members' => [ 'ReadCapacityUnits' => [ 'shape' => 'Integer', ], ], ], 'AwsDynamoDbTableReplica' => [ 'type' => 'structure', 'members' => [ 'GlobalSecondaryIndexes' => [ 'shape' => 'AwsDynamoDbTableReplicaGlobalSecondaryIndexList', ], 'KmsMasterKeyId' => [ 'shape' => 'NonEmptyString', ], 'ProvisionedThroughputOverride' => [ 'shape' => 'AwsDynamoDbTableProvisionedThroughputOverride', ], 'RegionName' => [ 'shape' => 'NonEmptyString', ], 'ReplicaStatus' => [ 'shape' => 'NonEmptyString', ], 'ReplicaStatusDescription' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsDynamoDbTableReplicaGlobalSecondaryIndex' => [ 'type' => 'structure', 'members' => [ 'IndexName' => [ 'shape' => 'NonEmptyString', ], 'ProvisionedThroughputOverride' => [ 'shape' => 'AwsDynamoDbTableProvisionedThroughputOverride', ], ], ], 'AwsDynamoDbTableReplicaGlobalSecondaryIndexList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsDynamoDbTableReplicaGlobalSecondaryIndex', ], ], 'AwsDynamoDbTableReplicaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsDynamoDbTableReplica', ], ], 'AwsDynamoDbTableRestoreSummary' => [ 'type' => 'structure', 'members' => [ 'SourceBackupArn' => [ 'shape' => 'NonEmptyString', ], 'SourceTableArn' => [ 'shape' => 'NonEmptyString', ], 'RestoreDateTime' => [ 'shape' => 'NonEmptyString', ], 'RestoreInProgress' => [ 'shape' => 'Boolean', ], ], ], 'AwsDynamoDbTableSseDescription' => [ 'type' => 'structure', 'members' => [ 'InaccessibleEncryptionDateTime' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'SseType' => [ 'shape' => 'NonEmptyString', ], 'KmsMasterKeyArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsDynamoDbTableStreamSpecification' => [ 'type' => 'structure', 'members' => [ 'StreamEnabled' => [ 'shape' => 'Boolean', ], 'StreamViewType' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2EipDetails' => [ 'type' => 'structure', 'members' => [ 'InstanceId' => [ 'shape' => 'NonEmptyString', ], 'PublicIp' => [ 'shape' => 'NonEmptyString', ], 'AllocationId' => [ 'shape' => 'NonEmptyString', ], 'AssociationId' => [ 'shape' => 'NonEmptyString', ], 'Domain' => [ 'shape' => 'NonEmptyString', ], 'PublicIpv4Pool' => [ 'shape' => 'NonEmptyString', ], 'NetworkBorderGroup' => [ 'shape' => 'NonEmptyString', ], 'NetworkInterfaceId' => [ 'shape' => 'NonEmptyString', ], 'NetworkInterfaceOwnerId' => [ 'shape' => 'NonEmptyString', ], 'PrivateIpAddress' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2InstanceDetails' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'NonEmptyString', ], 'ImageId' => [ 'shape' => 'NonEmptyString', ], 'IpV4Addresses' => [ 'shape' => 'StringList', ], 'IpV6Addresses' => [ 'shape' => 'StringList', ], 'KeyName' => [ 'shape' => 'NonEmptyString', ], 'IamInstanceProfileArn' => [ 'shape' => 'NonEmptyString', ], 'VpcId' => [ 'shape' => 'NonEmptyString', ], 'SubnetId' => [ 'shape' => 'NonEmptyString', ], 'LaunchedAt' => [ 'shape' => 'NonEmptyString', ], 'NetworkInterfaces' => [ 'shape' => 'AwsEc2InstanceNetworkInterfacesList', ], ], ], 'AwsEc2InstanceNetworkInterfacesDetails' => [ 'type' => 'structure', 'members' => [ 'NetworkInterfaceId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2InstanceNetworkInterfacesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2InstanceNetworkInterfacesDetails', ], ], 'AwsEc2NetworkAclAssociation' => [ 'type' => 'structure', 'members' => [ 'NetworkAclAssociationId' => [ 'shape' => 'NonEmptyString', ], 'NetworkAclId' => [ 'shape' => 'NonEmptyString', ], 'SubnetId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2NetworkAclAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2NetworkAclAssociation', ], ], 'AwsEc2NetworkAclDetails' => [ 'type' => 'structure', 'members' => [ 'IsDefault' => [ 'shape' => 'Boolean', ], 'NetworkAclId' => [ 'shape' => 'NonEmptyString', ], 'OwnerId' => [ 'shape' => 'NonEmptyString', ], 'VpcId' => [ 'shape' => 'NonEmptyString', ], 'Associations' => [ 'shape' => 'AwsEc2NetworkAclAssociationList', ], 'Entries' => [ 'shape' => 'AwsEc2NetworkAclEntryList', ], ], ], 'AwsEc2NetworkAclEntry' => [ 'type' => 'structure', 'members' => [ 'CidrBlock' => [ 'shape' => 'NonEmptyString', ], 'Egress' => [ 'shape' => 'Boolean', ], 'IcmpTypeCode' => [ 'shape' => 'IcmpTypeCode', ], 'Ipv6CidrBlock' => [ 'shape' => 'NonEmptyString', ], 'PortRange' => [ 'shape' => 'PortRangeFromTo', ], 'Protocol' => [ 'shape' => 'NonEmptyString', ], 'RuleAction' => [ 'shape' => 'NonEmptyString', ], 'RuleNumber' => [ 'shape' => 'Integer', ], ], ], 'AwsEc2NetworkAclEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2NetworkAclEntry', ], ], 'AwsEc2NetworkInterfaceAttachment' => [ 'type' => 'structure', 'members' => [ 'AttachTime' => [ 'shape' => 'NonEmptyString', ], 'AttachmentId' => [ 'shape' => 'NonEmptyString', ], 'DeleteOnTermination' => [ 'shape' => 'Boolean', ], 'DeviceIndex' => [ 'shape' => 'Integer', ], 'InstanceId' => [ 'shape' => 'NonEmptyString', ], 'InstanceOwnerId' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2NetworkInterfaceDetails' => [ 'type' => 'structure', 'members' => [ 'Attachment' => [ 'shape' => 'AwsEc2NetworkInterfaceAttachment', ], 'NetworkInterfaceId' => [ 'shape' => 'NonEmptyString', ], 'SecurityGroups' => [ 'shape' => 'AwsEc2NetworkInterfaceSecurityGroupList', ], 'SourceDestCheck' => [ 'shape' => 'Boolean', ], 'IpV6Addresses' => [ 'shape' => 'AwsEc2NetworkInterfaceIpV6AddressList', ], 'PrivateIpAddresses' => [ 'shape' => 'AwsEc2NetworkInterfacePrivateIpAddressList', ], 'PublicDnsName' => [ 'shape' => 'NonEmptyString', ], 'PublicIp' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2NetworkInterfaceIpV6AddressDetail' => [ 'type' => 'structure', 'members' => [ 'IpV6Address' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2NetworkInterfaceIpV6AddressList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2NetworkInterfaceIpV6AddressDetail', ], ], 'AwsEc2NetworkInterfacePrivateIpAddressDetail' => [ 'type' => 'structure', 'members' => [ 'PrivateIpAddress' => [ 'shape' => 'NonEmptyString', ], 'PrivateDnsName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2NetworkInterfacePrivateIpAddressList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2NetworkInterfacePrivateIpAddressDetail', ], ], 'AwsEc2NetworkInterfaceSecurityGroup' => [ 'type' => 'structure', 'members' => [ 'GroupName' => [ 'shape' => 'NonEmptyString', ], 'GroupId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2NetworkInterfaceSecurityGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2NetworkInterfaceSecurityGroup', ], ], 'AwsEc2SecurityGroupDetails' => [ 'type' => 'structure', 'members' => [ 'GroupName' => [ 'shape' => 'NonEmptyString', ], 'GroupId' => [ 'shape' => 'NonEmptyString', ], 'OwnerId' => [ 'shape' => 'NonEmptyString', ], 'VpcId' => [ 'shape' => 'NonEmptyString', ], 'IpPermissions' => [ 'shape' => 'AwsEc2SecurityGroupIpPermissionList', ], 'IpPermissionsEgress' => [ 'shape' => 'AwsEc2SecurityGroupIpPermissionList', ], ], ], 'AwsEc2SecurityGroupIpPermission' => [ 'type' => 'structure', 'members' => [ 'IpProtocol' => [ 'shape' => 'NonEmptyString', ], 'FromPort' => [ 'shape' => 'Integer', ], 'ToPort' => [ 'shape' => 'Integer', ], 'UserIdGroupPairs' => [ 'shape' => 'AwsEc2SecurityGroupUserIdGroupPairList', ], 'IpRanges' => [ 'shape' => 'AwsEc2SecurityGroupIpRangeList', ], 'Ipv6Ranges' => [ 'shape' => 'AwsEc2SecurityGroupIpv6RangeList', ], 'PrefixListIds' => [ 'shape' => 'AwsEc2SecurityGroupPrefixListIdList', ], ], ], 'AwsEc2SecurityGroupIpPermissionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2SecurityGroupIpPermission', ], ], 'AwsEc2SecurityGroupIpRange' => [ 'type' => 'structure', 'members' => [ 'CidrIp' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2SecurityGroupIpRangeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2SecurityGroupIpRange', ], ], 'AwsEc2SecurityGroupIpv6Range' => [ 'type' => 'structure', 'members' => [ 'CidrIpv6' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2SecurityGroupIpv6RangeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2SecurityGroupIpv6Range', ], ], 'AwsEc2SecurityGroupPrefixListId' => [ 'type' => 'structure', 'members' => [ 'PrefixListId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2SecurityGroupPrefixListIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2SecurityGroupPrefixListId', ], ], 'AwsEc2SecurityGroupUserIdGroupPair' => [ 'type' => 'structure', 'members' => [ 'GroupId' => [ 'shape' => 'NonEmptyString', ], 'GroupName' => [ 'shape' => 'NonEmptyString', ], 'PeeringStatus' => [ 'shape' => 'NonEmptyString', ], 'UserId' => [ 'shape' => 'NonEmptyString', ], 'VpcId' => [ 'shape' => 'NonEmptyString', ], 'VpcPeeringConnectionId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2SecurityGroupUserIdGroupPairList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2SecurityGroupUserIdGroupPair', ], ], 'AwsEc2SubnetDetails' => [ 'type' => 'structure', 'members' => [ 'AssignIpv6AddressOnCreation' => [ 'shape' => 'Boolean', ], 'AvailabilityZone' => [ 'shape' => 'NonEmptyString', ], 'AvailabilityZoneId' => [ 'shape' => 'NonEmptyString', ], 'AvailableIpAddressCount' => [ 'shape' => 'Integer', ], 'CidrBlock' => [ 'shape' => 'NonEmptyString', ], 'DefaultForAz' => [ 'shape' => 'Boolean', ], 'MapPublicIpOnLaunch' => [ 'shape' => 'Boolean', ], 'OwnerId' => [ 'shape' => 'NonEmptyString', ], 'State' => [ 'shape' => 'NonEmptyString', ], 'SubnetArn' => [ 'shape' => 'NonEmptyString', ], 'SubnetId' => [ 'shape' => 'NonEmptyString', ], 'VpcId' => [ 'shape' => 'NonEmptyString', ], 'Ipv6CidrBlockAssociationSet' => [ 'shape' => 'Ipv6CidrBlockAssociationList', ], ], ], 'AwsEc2VolumeAttachment' => [ 'type' => 'structure', 'members' => [ 'AttachTime' => [ 'shape' => 'NonEmptyString', ], 'DeleteOnTermination' => [ 'shape' => 'Boolean', ], 'InstanceId' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2VolumeAttachmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2VolumeAttachment', ], ], 'AwsEc2VolumeDetails' => [ 'type' => 'structure', 'members' => [ 'CreateTime' => [ 'shape' => 'NonEmptyString', ], 'Encrypted' => [ 'shape' => 'Boolean', ], 'Size' => [ 'shape' => 'Integer', ], 'SnapshotId' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'KmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'Attachments' => [ 'shape' => 'AwsEc2VolumeAttachmentList', ], ], ], 'AwsEc2VpcDetails' => [ 'type' => 'structure', 'members' => [ 'CidrBlockAssociationSet' => [ 'shape' => 'CidrBlockAssociationList', ], 'Ipv6CidrBlockAssociationSet' => [ 'shape' => 'Ipv6CidrBlockAssociationList', ], 'DhcpOptionsId' => [ 'shape' => 'NonEmptyString', ], 'State' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2VpcEndpointServiceDetails' => [ 'type' => 'structure', 'members' => [ 'AcceptanceRequired' => [ 'shape' => 'Boolean', ], 'AvailabilityZones' => [ 'shape' => 'NonEmptyStringList', ], 'BaseEndpointDnsNames' => [ 'shape' => 'NonEmptyStringList', ], 'ManagesVpcEndpoints' => [ 'shape' => 'Boolean', ], 'GatewayLoadBalancerArns' => [ 'shape' => 'NonEmptyStringList', ], 'NetworkLoadBalancerArns' => [ 'shape' => 'NonEmptyStringList', ], 'PrivateDnsName' => [ 'shape' => 'NonEmptyString', ], 'ServiceId' => [ 'shape' => 'NonEmptyString', ], 'ServiceName' => [ 'shape' => 'NonEmptyString', ], 'ServiceState' => [ 'shape' => 'NonEmptyString', ], 'ServiceType' => [ 'shape' => 'AwsEc2VpcEndpointServiceServiceTypeList', ], ], ], 'AwsEc2VpcEndpointServiceServiceTypeDetails' => [ 'type' => 'structure', 'members' => [ 'ServiceType' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2VpcEndpointServiceServiceTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2VpcEndpointServiceServiceTypeDetails', ], ], 'AwsEc2VpnConnectionDetails' => [ 'type' => 'structure', 'members' => [ 'VpnConnectionId' => [ 'shape' => 'NonEmptyString', ], 'State' => [ 'shape' => 'NonEmptyString', ], 'CustomerGatewayId' => [ 'shape' => 'NonEmptyString', ], 'CustomerGatewayConfiguration' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'NonEmptyString', ], 'VpnGatewayId' => [ 'shape' => 'NonEmptyString', ], 'Category' => [ 'shape' => 'NonEmptyString', ], 'VgwTelemetry' => [ 'shape' => 'AwsEc2VpnConnectionVgwTelemetryList', ], 'Options' => [ 'shape' => 'AwsEc2VpnConnectionOptionsDetails', ], 'Routes' => [ 'shape' => 'AwsEc2VpnConnectionRoutesList', ], 'TransitGatewayId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2VpnConnectionOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'StaticRoutesOnly' => [ 'shape' => 'Boolean', ], 'TunnelOptions' => [ 'shape' => 'AwsEc2VpnConnectionOptionsTunnelOptionsList', ], ], ], 'AwsEc2VpnConnectionOptionsTunnelOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'DpdTimeoutSeconds' => [ 'shape' => 'Integer', ], 'IkeVersions' => [ 'shape' => 'NonEmptyStringList', ], 'OutsideIpAddress' => [ 'shape' => 'NonEmptyString', ], 'Phase1DhGroupNumbers' => [ 'shape' => 'IntegerList', ], 'Phase1EncryptionAlgorithms' => [ 'shape' => 'NonEmptyStringList', ], 'Phase1IntegrityAlgorithms' => [ 'shape' => 'NonEmptyStringList', ], 'Phase1LifetimeSeconds' => [ 'shape' => 'Integer', ], 'Phase2DhGroupNumbers' => [ 'shape' => 'IntegerList', ], 'Phase2EncryptionAlgorithms' => [ 'shape' => 'NonEmptyStringList', ], 'Phase2IntegrityAlgorithms' => [ 'shape' => 'NonEmptyStringList', ], 'Phase2LifetimeSeconds' => [ 'shape' => 'Integer', ], 'PreSharedKey' => [ 'shape' => 'NonEmptyString', ], 'RekeyFuzzPercentage' => [ 'shape' => 'Integer', ], 'RekeyMarginTimeSeconds' => [ 'shape' => 'Integer', ], 'ReplayWindowSize' => [ 'shape' => 'Integer', ], 'TunnelInsideCidr' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2VpnConnectionOptionsTunnelOptionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2VpnConnectionOptionsTunnelOptionsDetails', ], ], 'AwsEc2VpnConnectionRoutesDetails' => [ 'type' => 'structure', 'members' => [ 'DestinationCidrBlock' => [ 'shape' => 'NonEmptyString', ], 'State' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2VpnConnectionRoutesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2VpnConnectionRoutesDetails', ], ], 'AwsEc2VpnConnectionVgwTelemetryDetails' => [ 'type' => 'structure', 'members' => [ 'AcceptedRouteCount' => [ 'shape' => 'Integer', ], 'CertificateArn' => [ 'shape' => 'NonEmptyString', ], 'LastStatusChange' => [ 'shape' => 'NonEmptyString', ], 'OutsideIpAddress' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'StatusMessage' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEc2VpnConnectionVgwTelemetryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEc2VpnConnectionVgwTelemetryDetails', ], ], 'AwsEcrContainerImageDetails' => [ 'type' => 'structure', 'members' => [ 'RegistryId' => [ 'shape' => 'NonEmptyString', ], 'RepositoryName' => [ 'shape' => 'NonEmptyString', ], 'Architecture' => [ 'shape' => 'NonEmptyString', ], 'ImageDigest' => [ 'shape' => 'NonEmptyString', ], 'ImageTags' => [ 'shape' => 'NonEmptyStringList', ], 'ImagePublishedAt' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcrRepositoryDetails' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'NonEmptyString', ], 'ImageScanningConfiguration' => [ 'shape' => 'AwsEcrRepositoryImageScanningConfigurationDetails', ], 'ImageTagMutability' => [ 'shape' => 'NonEmptyString', ], 'LifecyclePolicy' => [ 'shape' => 'AwsEcrRepositoryLifecyclePolicyDetails', ], 'RepositoryName' => [ 'shape' => 'NonEmptyString', ], 'RepositoryPolicyText' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcrRepositoryImageScanningConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'ScanOnPush' => [ 'shape' => 'Boolean', ], ], ], 'AwsEcrRepositoryLifecyclePolicyDetails' => [ 'type' => 'structure', 'members' => [ 'LifecyclePolicyText' => [ 'shape' => 'NonEmptyString', ], 'RegistryId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsClusterClusterSettingsDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsClusterClusterSettingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsClusterClusterSettingsDetails', ], ], 'AwsEcsClusterConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'ExecuteCommandConfiguration' => [ 'shape' => 'AwsEcsClusterConfigurationExecuteCommandConfigurationDetails', ], ], ], 'AwsEcsClusterConfigurationExecuteCommandConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'KmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'LogConfiguration' => [ 'shape' => 'AwsEcsClusterConfigurationExecuteCommandConfigurationLogConfigurationDetails', ], 'Logging' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsClusterConfigurationExecuteCommandConfigurationLogConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'CloudWatchEncryptionEnabled' => [ 'shape' => 'Boolean', ], 'CloudWatchLogGroupName' => [ 'shape' => 'NonEmptyString', ], 'S3BucketName' => [ 'shape' => 'NonEmptyString', ], 'S3EncryptionEnabled' => [ 'shape' => 'Boolean', ], 'S3KeyPrefix' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsClusterDefaultCapacityProviderStrategyDetails' => [ 'type' => 'structure', 'members' => [ 'Base' => [ 'shape' => 'Integer', ], 'CapacityProvider' => [ 'shape' => 'NonEmptyString', ], 'Weight' => [ 'shape' => 'Integer', ], ], ], 'AwsEcsClusterDefaultCapacityProviderStrategyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsClusterDefaultCapacityProviderStrategyDetails', ], ], 'AwsEcsClusterDetails' => [ 'type' => 'structure', 'members' => [ 'CapacityProviders' => [ 'shape' => 'NonEmptyStringList', ], 'ClusterSettings' => [ 'shape' => 'AwsEcsClusterClusterSettingsList', ], 'Configuration' => [ 'shape' => 'AwsEcsClusterConfigurationDetails', ], 'DefaultCapacityProviderStrategy' => [ 'shape' => 'AwsEcsClusterDefaultCapacityProviderStrategyList', ], ], ], 'AwsEcsServiceCapacityProviderStrategyDetails' => [ 'type' => 'structure', 'members' => [ 'Base' => [ 'shape' => 'Integer', ], 'CapacityProvider' => [ 'shape' => 'NonEmptyString', ], 'Weight' => [ 'shape' => 'Integer', ], ], ], 'AwsEcsServiceCapacityProviderStrategyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsServiceCapacityProviderStrategyDetails', ], ], 'AwsEcsServiceDeploymentConfigurationDeploymentCircuitBreakerDetails' => [ 'type' => 'structure', 'members' => [ 'Enable' => [ 'shape' => 'Boolean', ], 'Rollback' => [ 'shape' => 'Boolean', ], ], ], 'AwsEcsServiceDeploymentConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'DeploymentCircuitBreaker' => [ 'shape' => 'AwsEcsServiceDeploymentConfigurationDeploymentCircuitBreakerDetails', ], 'MaximumPercent' => [ 'shape' => 'Integer', ], 'MinimumHealthyPercent' => [ 'shape' => 'Integer', ], ], ], 'AwsEcsServiceDeploymentControllerDetails' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsServiceDetails' => [ 'type' => 'structure', 'members' => [ 'CapacityProviderStrategy' => [ 'shape' => 'AwsEcsServiceCapacityProviderStrategyList', ], 'Cluster' => [ 'shape' => 'NonEmptyString', ], 'DeploymentConfiguration' => [ 'shape' => 'AwsEcsServiceDeploymentConfigurationDetails', ], 'DeploymentController' => [ 'shape' => 'AwsEcsServiceDeploymentControllerDetails', ], 'DesiredCount' => [ 'shape' => 'Integer', ], 'EnableEcsManagedTags' => [ 'shape' => 'Boolean', ], 'EnableExecuteCommand' => [ 'shape' => 'Boolean', ], 'HealthCheckGracePeriodSeconds' => [ 'shape' => 'Integer', ], 'LaunchType' => [ 'shape' => 'NonEmptyString', ], 'LoadBalancers' => [ 'shape' => 'AwsEcsServiceLoadBalancersList', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'NetworkConfiguration' => [ 'shape' => 'AwsEcsServiceNetworkConfigurationDetails', ], 'PlacementConstraints' => [ 'shape' => 'AwsEcsServicePlacementConstraintsList', ], 'PlacementStrategies' => [ 'shape' => 'AwsEcsServicePlacementStrategiesList', ], 'PlatformVersion' => [ 'shape' => 'NonEmptyString', ], 'PropagateTags' => [ 'shape' => 'NonEmptyString', ], 'Role' => [ 'shape' => 'NonEmptyString', ], 'SchedulingStrategy' => [ 'shape' => 'NonEmptyString', ], 'ServiceArn' => [ 'shape' => 'NonEmptyString', ], 'ServiceName' => [ 'shape' => 'NonEmptyString', ], 'ServiceRegistries' => [ 'shape' => 'AwsEcsServiceServiceRegistriesList', ], 'TaskDefinition' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsServiceLoadBalancersDetails' => [ 'type' => 'structure', 'members' => [ 'ContainerName' => [ 'shape' => 'NonEmptyString', ], 'ContainerPort' => [ 'shape' => 'Integer', ], 'LoadBalancerName' => [ 'shape' => 'NonEmptyString', ], 'TargetGroupArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsServiceLoadBalancersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsServiceLoadBalancersDetails', ], ], 'AwsEcsServiceNetworkConfigurationAwsVpcConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'AssignPublicIp' => [ 'shape' => 'NonEmptyString', ], 'SecurityGroups' => [ 'shape' => 'NonEmptyStringList', ], 'Subnets' => [ 'shape' => 'NonEmptyStringList', ], ], ], 'AwsEcsServiceNetworkConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'AwsVpcConfiguration' => [ 'shape' => 'AwsEcsServiceNetworkConfigurationAwsVpcConfigurationDetails', ], ], ], 'AwsEcsServicePlacementConstraintsDetails' => [ 'type' => 'structure', 'members' => [ 'Expression' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsServicePlacementConstraintsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsServicePlacementConstraintsDetails', ], ], 'AwsEcsServicePlacementStrategiesDetails' => [ 'type' => 'structure', 'members' => [ 'Field' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsServicePlacementStrategiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsServicePlacementStrategiesDetails', ], ], 'AwsEcsServiceServiceRegistriesDetails' => [ 'type' => 'structure', 'members' => [ 'ContainerName' => [ 'shape' => 'NonEmptyString', ], 'ContainerPort' => [ 'shape' => 'Integer', ], 'Port' => [ 'shape' => 'Integer', ], 'RegistryArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsServiceServiceRegistriesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsServiceServiceRegistriesDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsDependsOnDetails' => [ 'type' => 'structure', 'members' => [ 'Condition' => [ 'shape' => 'NonEmptyString', ], 'ContainerName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsDependsOnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsDependsOnDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsDetails' => [ 'type' => 'structure', 'members' => [ 'Command' => [ 'shape' => 'NonEmptyStringList', ], 'Cpu' => [ 'shape' => 'Integer', ], 'DependsOn' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsDependsOnList', ], 'DisableNetworking' => [ 'shape' => 'Boolean', ], 'DnsSearchDomains' => [ 'shape' => 'NonEmptyStringList', ], 'DnsServers' => [ 'shape' => 'NonEmptyStringList', ], 'DockerLabels' => [ 'shape' => 'FieldMap', ], 'DockerSecurityOptions' => [ 'shape' => 'NonEmptyStringList', ], 'EntryPoint' => [ 'shape' => 'NonEmptyStringList', ], 'Environment' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsEnvironmentList', ], 'EnvironmentFiles' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsEnvironmentFilesList', ], 'Essential' => [ 'shape' => 'Boolean', ], 'ExtraHosts' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsExtraHostsList', ], 'FirelensConfiguration' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsFirelensConfigurationDetails', ], 'HealthCheck' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsHealthCheckDetails', ], 'Hostname' => [ 'shape' => 'NonEmptyString', ], 'Image' => [ 'shape' => 'NonEmptyString', ], 'Interactive' => [ 'shape' => 'Boolean', ], 'Links' => [ 'shape' => 'NonEmptyStringList', ], 'LinuxParameters' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsLinuxParametersDetails', ], 'LogConfiguration' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsLogConfigurationDetails', ], 'Memory' => [ 'shape' => 'Integer', ], 'MemoryReservation' => [ 'shape' => 'Integer', ], 'MountPoints' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsMountPointsList', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'PortMappings' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsPortMappingsList', ], 'Privileged' => [ 'shape' => 'Boolean', ], 'PseudoTerminal' => [ 'shape' => 'Boolean', ], 'ReadonlyRootFilesystem' => [ 'shape' => 'Boolean', ], 'RepositoryCredentials' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsRepositoryCredentialsDetails', ], 'ResourceRequirements' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsResourceRequirementsList', ], 'Secrets' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsSecretsList', ], 'StartTimeout' => [ 'shape' => 'Integer', ], 'StopTimeout' => [ 'shape' => 'Integer', ], 'SystemControls' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsSystemControlsList', ], 'Ulimits' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsUlimitsList', ], 'User' => [ 'shape' => 'NonEmptyString', ], 'VolumesFrom' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsVolumesFromList', ], 'WorkingDirectory' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsEnvironmentDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsEnvironmentFilesDetails' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsEnvironmentFilesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsEnvironmentFilesDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsEnvironmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsEnvironmentDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsExtraHostsDetails' => [ 'type' => 'structure', 'members' => [ 'Hostname' => [ 'shape' => 'NonEmptyString', ], 'IpAddress' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsExtraHostsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsExtraHostsDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsFirelensConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'Options' => [ 'shape' => 'FieldMap', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsHealthCheckDetails' => [ 'type' => 'structure', 'members' => [ 'Command' => [ 'shape' => 'NonEmptyStringList', ], 'Interval' => [ 'shape' => 'Integer', ], 'Retries' => [ 'shape' => 'Integer', ], 'StartPeriod' => [ 'shape' => 'Integer', ], 'Timeout' => [ 'shape' => 'Integer', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsLinuxParametersCapabilitiesDetails' => [ 'type' => 'structure', 'members' => [ 'Add' => [ 'shape' => 'NonEmptyStringList', ], 'Drop' => [ 'shape' => 'NonEmptyStringList', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsLinuxParametersDetails' => [ 'type' => 'structure', 'members' => [ 'Capabilities' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsLinuxParametersCapabilitiesDetails', ], 'Devices' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsLinuxParametersDevicesList', ], 'InitProcessEnabled' => [ 'shape' => 'Boolean', ], 'MaxSwap' => [ 'shape' => 'Integer', ], 'SharedMemorySize' => [ 'shape' => 'Integer', ], 'Swappiness' => [ 'shape' => 'Integer', ], 'Tmpfs' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsLinuxParametersTmpfsList', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsLinuxParametersDevicesDetails' => [ 'type' => 'structure', 'members' => [ 'ContainerPath' => [ 'shape' => 'NonEmptyString', ], 'HostPath' => [ 'shape' => 'NonEmptyString', ], 'Permissions' => [ 'shape' => 'NonEmptyStringList', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsLinuxParametersDevicesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsLinuxParametersDevicesDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsLinuxParametersTmpfsDetails' => [ 'type' => 'structure', 'members' => [ 'ContainerPath' => [ 'shape' => 'NonEmptyString', ], 'MountOptions' => [ 'shape' => 'NonEmptyStringList', ], 'Size' => [ 'shape' => 'Integer', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsLinuxParametersTmpfsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsLinuxParametersTmpfsDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsLogConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'LogDriver' => [ 'shape' => 'NonEmptyString', ], 'Options' => [ 'shape' => 'FieldMap', ], 'SecretOptions' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsLogConfigurationSecretOptionsList', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsLogConfigurationSecretOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'ValueFrom' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsLogConfigurationSecretOptionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsLogConfigurationSecretOptionsDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsMountPointsDetails' => [ 'type' => 'structure', 'members' => [ 'ContainerPath' => [ 'shape' => 'NonEmptyString', ], 'ReadOnly' => [ 'shape' => 'Boolean', ], 'SourceVolume' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsMountPointsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsMountPointsDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsPortMappingsDetails' => [ 'type' => 'structure', 'members' => [ 'ContainerPort' => [ 'shape' => 'Integer', ], 'HostPort' => [ 'shape' => 'Integer', ], 'Protocol' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsPortMappingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsPortMappingsDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsRepositoryCredentialsDetails' => [ 'type' => 'structure', 'members' => [ 'CredentialsParameter' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsResourceRequirementsDetails' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsResourceRequirementsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsResourceRequirementsDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsSecretsDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'ValueFrom' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsSecretsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsSecretsDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsSystemControlsDetails' => [ 'type' => 'structure', 'members' => [ 'Namespace' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsSystemControlsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsSystemControlsDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsUlimitsDetails' => [ 'type' => 'structure', 'members' => [ 'HardLimit' => [ 'shape' => 'Integer', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'SoftLimit' => [ 'shape' => 'Integer', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsUlimitsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsUlimitsDetails', ], ], 'AwsEcsTaskDefinitionContainerDefinitionsVolumesFromDetails' => [ 'type' => 'structure', 'members' => [ 'ReadOnly' => [ 'shape' => 'Boolean', ], 'SourceContainer' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionContainerDefinitionsVolumesFromList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsVolumesFromDetails', ], ], 'AwsEcsTaskDefinitionDetails' => [ 'type' => 'structure', 'members' => [ 'ContainerDefinitions' => [ 'shape' => 'AwsEcsTaskDefinitionContainerDefinitionsList', ], 'Cpu' => [ 'shape' => 'NonEmptyString', ], 'ExecutionRoleArn' => [ 'shape' => 'NonEmptyString', ], 'Family' => [ 'shape' => 'NonEmptyString', ], 'InferenceAccelerators' => [ 'shape' => 'AwsEcsTaskDefinitionInferenceAcceleratorsList', ], 'IpcMode' => [ 'shape' => 'NonEmptyString', ], 'Memory' => [ 'shape' => 'NonEmptyString', ], 'NetworkMode' => [ 'shape' => 'NonEmptyString', ], 'PidMode' => [ 'shape' => 'NonEmptyString', ], 'PlacementConstraints' => [ 'shape' => 'AwsEcsTaskDefinitionPlacementConstraintsList', ], 'ProxyConfiguration' => [ 'shape' => 'AwsEcsTaskDefinitionProxyConfigurationDetails', ], 'RequiresCompatibilities' => [ 'shape' => 'NonEmptyStringList', ], 'TaskRoleArn' => [ 'shape' => 'NonEmptyString', ], 'Volumes' => [ 'shape' => 'AwsEcsTaskDefinitionVolumesList', ], ], ], 'AwsEcsTaskDefinitionInferenceAcceleratorsDetails' => [ 'type' => 'structure', 'members' => [ 'DeviceName' => [ 'shape' => 'NonEmptyString', ], 'DeviceType' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionInferenceAcceleratorsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionInferenceAcceleratorsDetails', ], ], 'AwsEcsTaskDefinitionPlacementConstraintsDetails' => [ 'type' => 'structure', 'members' => [ 'Expression' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionPlacementConstraintsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionPlacementConstraintsDetails', ], ], 'AwsEcsTaskDefinitionProxyConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'ContainerName' => [ 'shape' => 'NonEmptyString', ], 'ProxyConfigurationProperties' => [ 'shape' => 'AwsEcsTaskDefinitionProxyConfigurationProxyConfigurationPropertiesList', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionProxyConfigurationProxyConfigurationPropertiesDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionProxyConfigurationProxyConfigurationPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionProxyConfigurationProxyConfigurationPropertiesDetails', ], ], 'AwsEcsTaskDefinitionVolumesDetails' => [ 'type' => 'structure', 'members' => [ 'DockerVolumeConfiguration' => [ 'shape' => 'AwsEcsTaskDefinitionVolumesDockerVolumeConfigurationDetails', ], 'EfsVolumeConfiguration' => [ 'shape' => 'AwsEcsTaskDefinitionVolumesEfsVolumeConfigurationDetails', ], 'Host' => [ 'shape' => 'AwsEcsTaskDefinitionVolumesHostDetails', ], 'Name' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionVolumesDockerVolumeConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'Autoprovision' => [ 'shape' => 'Boolean', ], 'Driver' => [ 'shape' => 'NonEmptyString', ], 'DriverOpts' => [ 'shape' => 'FieldMap', ], 'Labels' => [ 'shape' => 'FieldMap', ], 'Scope' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionVolumesEfsVolumeConfigurationAuthorizationConfigDetails' => [ 'type' => 'structure', 'members' => [ 'AccessPointId' => [ 'shape' => 'NonEmptyString', ], 'Iam' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionVolumesEfsVolumeConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'AuthorizationConfig' => [ 'shape' => 'AwsEcsTaskDefinitionVolumesEfsVolumeConfigurationAuthorizationConfigDetails', ], 'FilesystemId' => [ 'shape' => 'NonEmptyString', ], 'RootDirectory' => [ 'shape' => 'NonEmptyString', ], 'TransitEncryption' => [ 'shape' => 'NonEmptyString', ], 'TransitEncryptionPort' => [ 'shape' => 'Integer', ], ], ], 'AwsEcsTaskDefinitionVolumesHostDetails' => [ 'type' => 'structure', 'members' => [ 'SourcePath' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsEcsTaskDefinitionVolumesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEcsTaskDefinitionVolumesDetails', ], ], 'AwsEksClusterDetails' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'NonEmptyString', ], 'CertificateAuthorityData' => [ 'shape' => 'NonEmptyString', ], 'ClusterStatus' => [ 'shape' => 'NonEmptyString', ], 'Endpoint' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'ResourcesVpcConfig' => [ 'shape' => 'AwsEksClusterResourcesVpcConfigDetails', ], 'RoleArn' => [ 'shape' => 'NonEmptyString', ], 'Version' => [ 'shape' => 'NonEmptyString', ], 'Logging' => [ 'shape' => 'AwsEksClusterLoggingDetails', ], ], ], 'AwsEksClusterLoggingClusterLoggingDetails' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'Types' => [ 'shape' => 'NonEmptyStringList', ], ], ], 'AwsEksClusterLoggingClusterLoggingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsEksClusterLoggingClusterLoggingDetails', ], ], 'AwsEksClusterLoggingDetails' => [ 'type' => 'structure', 'members' => [ 'ClusterLogging' => [ 'shape' => 'AwsEksClusterLoggingClusterLoggingList', ], ], ], 'AwsEksClusterResourcesVpcConfigDetails' => [ 'type' => 'structure', 'members' => [ 'SecurityGroupIds' => [ 'shape' => 'NonEmptyStringList', ], 'SubnetIds' => [ 'shape' => 'NonEmptyStringList', ], ], ], 'AwsElasticBeanstalkEnvironmentDetails' => [ 'type' => 'structure', 'members' => [ 'ApplicationName' => [ 'shape' => 'NonEmptyString', ], 'Cname' => [ 'shape' => 'NonEmptyString', ], 'DateCreated' => [ 'shape' => 'NonEmptyString', ], 'DateUpdated' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'EndpointUrl' => [ 'shape' => 'NonEmptyString', ], 'EnvironmentArn' => [ 'shape' => 'NonEmptyString', ], 'EnvironmentId' => [ 'shape' => 'NonEmptyString', ], 'EnvironmentLinks' => [ 'shape' => 'AwsElasticBeanstalkEnvironmentEnvironmentLinks', ], 'EnvironmentName' => [ 'shape' => 'NonEmptyString', ], 'OptionSettings' => [ 'shape' => 'AwsElasticBeanstalkEnvironmentOptionSettings', ], 'PlatformArn' => [ 'shape' => 'NonEmptyString', ], 'SolutionStackName' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'Tier' => [ 'shape' => 'AwsElasticBeanstalkEnvironmentTier', ], 'VersionLabel' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElasticBeanstalkEnvironmentEnvironmentLink' => [ 'type' => 'structure', 'members' => [ 'EnvironmentName' => [ 'shape' => 'NonEmptyString', ], 'LinkName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElasticBeanstalkEnvironmentEnvironmentLinks' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsElasticBeanstalkEnvironmentEnvironmentLink', ], ], 'AwsElasticBeanstalkEnvironmentOptionSetting' => [ 'type' => 'structure', 'members' => [ 'Namespace' => [ 'shape' => 'NonEmptyString', ], 'OptionName' => [ 'shape' => 'NonEmptyString', ], 'ResourceName' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElasticBeanstalkEnvironmentOptionSettings' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsElasticBeanstalkEnvironmentOptionSetting', ], ], 'AwsElasticBeanstalkEnvironmentTier' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'NonEmptyString', ], 'Version' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElasticsearchDomainDetails' => [ 'type' => 'structure', 'members' => [ 'AccessPolicies' => [ 'shape' => 'NonEmptyString', ], 'DomainEndpointOptions' => [ 'shape' => 'AwsElasticsearchDomainDomainEndpointOptions', ], 'DomainId' => [ 'shape' => 'NonEmptyString', ], 'DomainName' => [ 'shape' => 'NonEmptyString', ], 'Endpoint' => [ 'shape' => 'NonEmptyString', ], 'Endpoints' => [ 'shape' => 'FieldMap', ], 'ElasticsearchVersion' => [ 'shape' => 'NonEmptyString', ], 'ElasticsearchClusterConfig' => [ 'shape' => 'AwsElasticsearchDomainElasticsearchClusterConfigDetails', ], 'EncryptionAtRestOptions' => [ 'shape' => 'AwsElasticsearchDomainEncryptionAtRestOptions', ], 'LogPublishingOptions' => [ 'shape' => 'AwsElasticsearchDomainLogPublishingOptions', ], 'NodeToNodeEncryptionOptions' => [ 'shape' => 'AwsElasticsearchDomainNodeToNodeEncryptionOptions', ], 'ServiceSoftwareOptions' => [ 'shape' => 'AwsElasticsearchDomainServiceSoftwareOptions', ], 'VPCOptions' => [ 'shape' => 'AwsElasticsearchDomainVPCOptions', ], ], ], 'AwsElasticsearchDomainDomainEndpointOptions' => [ 'type' => 'structure', 'members' => [ 'EnforceHTTPS' => [ 'shape' => 'Boolean', ], 'TLSSecurityPolicy' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElasticsearchDomainElasticsearchClusterConfigDetails' => [ 'type' => 'structure', 'members' => [ 'DedicatedMasterCount' => [ 'shape' => 'Integer', ], 'DedicatedMasterEnabled' => [ 'shape' => 'Boolean', ], 'DedicatedMasterType' => [ 'shape' => 'NonEmptyString', ], 'InstanceCount' => [ 'shape' => 'Integer', ], 'InstanceType' => [ 'shape' => 'NonEmptyString', ], 'ZoneAwarenessConfig' => [ 'shape' => 'AwsElasticsearchDomainElasticsearchClusterConfigZoneAwarenessConfigDetails', ], 'ZoneAwarenessEnabled' => [ 'shape' => 'Boolean', ], ], ], 'AwsElasticsearchDomainElasticsearchClusterConfigZoneAwarenessConfigDetails' => [ 'type' => 'structure', 'members' => [ 'AvailabilityZoneCount' => [ 'shape' => 'Integer', ], ], ], 'AwsElasticsearchDomainEncryptionAtRestOptions' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'KmsKeyId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElasticsearchDomainLogPublishingOptions' => [ 'type' => 'structure', 'members' => [ 'IndexSlowLogs' => [ 'shape' => 'AwsElasticsearchDomainLogPublishingOptionsLogConfig', ], 'SearchSlowLogs' => [ 'shape' => 'AwsElasticsearchDomainLogPublishingOptionsLogConfig', ], 'AuditLogs' => [ 'shape' => 'AwsElasticsearchDomainLogPublishingOptionsLogConfig', ], ], ], 'AwsElasticsearchDomainLogPublishingOptionsLogConfig' => [ 'type' => 'structure', 'members' => [ 'CloudWatchLogsLogGroupArn' => [ 'shape' => 'NonEmptyString', ], 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'AwsElasticsearchDomainNodeToNodeEncryptionOptions' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'AwsElasticsearchDomainServiceSoftwareOptions' => [ 'type' => 'structure', 'members' => [ 'AutomatedUpdateDate' => [ 'shape' => 'NonEmptyString', ], 'Cancellable' => [ 'shape' => 'Boolean', ], 'CurrentVersion' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'NewVersion' => [ 'shape' => 'NonEmptyString', ], 'UpdateAvailable' => [ 'shape' => 'Boolean', ], 'UpdateStatus' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElasticsearchDomainVPCOptions' => [ 'type' => 'structure', 'members' => [ 'AvailabilityZones' => [ 'shape' => 'NonEmptyStringList', ], 'SecurityGroupIds' => [ 'shape' => 'NonEmptyStringList', ], 'SubnetIds' => [ 'shape' => 'NonEmptyStringList', ], 'VPCId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElbAppCookieStickinessPolicies' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsElbAppCookieStickinessPolicy', ], ], 'AwsElbAppCookieStickinessPolicy' => [ 'type' => 'structure', 'members' => [ 'CookieName' => [ 'shape' => 'NonEmptyString', ], 'PolicyName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElbLbCookieStickinessPolicies' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsElbLbCookieStickinessPolicy', ], ], 'AwsElbLbCookieStickinessPolicy' => [ 'type' => 'structure', 'members' => [ 'CookieExpirationPeriod' => [ 'shape' => 'Long', ], 'PolicyName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElbLoadBalancerAccessLog' => [ 'type' => 'structure', 'members' => [ 'EmitInterval' => [ 'shape' => 'Integer', ], 'Enabled' => [ 'shape' => 'Boolean', ], 'S3BucketName' => [ 'shape' => 'NonEmptyString', ], 'S3BucketPrefix' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElbLoadBalancerAttributes' => [ 'type' => 'structure', 'members' => [ 'AccessLog' => [ 'shape' => 'AwsElbLoadBalancerAccessLog', ], 'ConnectionDraining' => [ 'shape' => 'AwsElbLoadBalancerConnectionDraining', ], 'ConnectionSettings' => [ 'shape' => 'AwsElbLoadBalancerConnectionSettings', ], 'CrossZoneLoadBalancing' => [ 'shape' => 'AwsElbLoadBalancerCrossZoneLoadBalancing', ], ], ], 'AwsElbLoadBalancerBackendServerDescription' => [ 'type' => 'structure', 'members' => [ 'InstancePort' => [ 'shape' => 'Integer', ], 'PolicyNames' => [ 'shape' => 'StringList', ], ], ], 'AwsElbLoadBalancerBackendServerDescriptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsElbLoadBalancerBackendServerDescription', ], ], 'AwsElbLoadBalancerConnectionDraining' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'Timeout' => [ 'shape' => 'Integer', ], ], ], 'AwsElbLoadBalancerConnectionSettings' => [ 'type' => 'structure', 'members' => [ 'IdleTimeout' => [ 'shape' => 'Integer', ], ], ], 'AwsElbLoadBalancerCrossZoneLoadBalancing' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'AwsElbLoadBalancerDetails' => [ 'type' => 'structure', 'members' => [ 'AvailabilityZones' => [ 'shape' => 'StringList', ], 'BackendServerDescriptions' => [ 'shape' => 'AwsElbLoadBalancerBackendServerDescriptions', ], 'CanonicalHostedZoneName' => [ 'shape' => 'NonEmptyString', ], 'CanonicalHostedZoneNameID' => [ 'shape' => 'NonEmptyString', ], 'CreatedTime' => [ 'shape' => 'NonEmptyString', ], 'DnsName' => [ 'shape' => 'NonEmptyString', ], 'HealthCheck' => [ 'shape' => 'AwsElbLoadBalancerHealthCheck', ], 'Instances' => [ 'shape' => 'AwsElbLoadBalancerInstances', ], 'ListenerDescriptions' => [ 'shape' => 'AwsElbLoadBalancerListenerDescriptions', ], 'LoadBalancerAttributes' => [ 'shape' => 'AwsElbLoadBalancerAttributes', ], 'LoadBalancerName' => [ 'shape' => 'NonEmptyString', ], 'Policies' => [ 'shape' => 'AwsElbLoadBalancerPolicies', ], 'Scheme' => [ 'shape' => 'NonEmptyString', ], 'SecurityGroups' => [ 'shape' => 'StringList', ], 'SourceSecurityGroup' => [ 'shape' => 'AwsElbLoadBalancerSourceSecurityGroup', ], 'Subnets' => [ 'shape' => 'StringList', ], 'VpcId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElbLoadBalancerHealthCheck' => [ 'type' => 'structure', 'members' => [ 'HealthyThreshold' => [ 'shape' => 'Integer', ], 'Interval' => [ 'shape' => 'Integer', ], 'Target' => [ 'shape' => 'NonEmptyString', ], 'Timeout' => [ 'shape' => 'Integer', ], 'UnhealthyThreshold' => [ 'shape' => 'Integer', ], ], ], 'AwsElbLoadBalancerInstance' => [ 'type' => 'structure', 'members' => [ 'InstanceId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElbLoadBalancerInstances' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsElbLoadBalancerInstance', ], ], 'AwsElbLoadBalancerListener' => [ 'type' => 'structure', 'members' => [ 'InstancePort' => [ 'shape' => 'Integer', ], 'InstanceProtocol' => [ 'shape' => 'NonEmptyString', ], 'LoadBalancerPort' => [ 'shape' => 'Integer', ], 'Protocol' => [ 'shape' => 'NonEmptyString', ], 'SslCertificateId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElbLoadBalancerListenerDescription' => [ 'type' => 'structure', 'members' => [ 'Listener' => [ 'shape' => 'AwsElbLoadBalancerListener', ], 'PolicyNames' => [ 'shape' => 'StringList', ], ], ], 'AwsElbLoadBalancerListenerDescriptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsElbLoadBalancerListenerDescription', ], ], 'AwsElbLoadBalancerPolicies' => [ 'type' => 'structure', 'members' => [ 'AppCookieStickinessPolicies' => [ 'shape' => 'AwsElbAppCookieStickinessPolicies', ], 'LbCookieStickinessPolicies' => [ 'shape' => 'AwsElbLbCookieStickinessPolicies', ], 'OtherPolicies' => [ 'shape' => 'StringList', ], ], ], 'AwsElbLoadBalancerSourceSecurityGroup' => [ 'type' => 'structure', 'members' => [ 'GroupName' => [ 'shape' => 'NonEmptyString', ], 'OwnerAlias' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElbv2LoadBalancerAttribute' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsElbv2LoadBalancerAttributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsElbv2LoadBalancerAttribute', ], ], 'AwsElbv2LoadBalancerDetails' => [ 'type' => 'structure', 'members' => [ 'AvailabilityZones' => [ 'shape' => 'AvailabilityZones', ], 'CanonicalHostedZoneId' => [ 'shape' => 'NonEmptyString', ], 'CreatedTime' => [ 'shape' => 'NonEmptyString', ], 'DNSName' => [ 'shape' => 'NonEmptyString', ], 'IpAddressType' => [ 'shape' => 'NonEmptyString', ], 'Scheme' => [ 'shape' => 'NonEmptyString', ], 'SecurityGroups' => [ 'shape' => 'SecurityGroups', ], 'State' => [ 'shape' => 'LoadBalancerState', ], 'Type' => [ 'shape' => 'NonEmptyString', ], 'VpcId' => [ 'shape' => 'NonEmptyString', ], 'LoadBalancerAttributes' => [ 'shape' => 'AwsElbv2LoadBalancerAttributes', ], ], ], 'AwsIamAccessKeyDetails' => [ 'type' => 'structure', 'members' => [ 'UserName' => [ 'shape' => 'NonEmptyString', 'deprecated' => true, 'deprecatedMessage' => 'This filter is deprecated. Instead, use PrincipalName.', ], 'Status' => [ 'shape' => 'AwsIamAccessKeyStatus', ], 'CreatedAt' => [ 'shape' => 'NonEmptyString', ], 'PrincipalId' => [ 'shape' => 'NonEmptyString', ], 'PrincipalType' => [ 'shape' => 'NonEmptyString', ], 'PrincipalName' => [ 'shape' => 'NonEmptyString', ], 'AccountId' => [ 'shape' => 'NonEmptyString', ], 'AccessKeyId' => [ 'shape' => 'NonEmptyString', ], 'SessionContext' => [ 'shape' => 'AwsIamAccessKeySessionContext', ], ], ], 'AwsIamAccessKeySessionContext' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'AwsIamAccessKeySessionContextAttributes', ], 'SessionIssuer' => [ 'shape' => 'AwsIamAccessKeySessionContextSessionIssuer', ], ], ], 'AwsIamAccessKeySessionContextAttributes' => [ 'type' => 'structure', 'members' => [ 'MfaAuthenticated' => [ 'shape' => 'Boolean', ], 'CreationDate' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsIamAccessKeySessionContextSessionIssuer' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'NonEmptyString', ], 'PrincipalId' => [ 'shape' => 'NonEmptyString', ], 'Arn' => [ 'shape' => 'NonEmptyString', ], 'AccountId' => [ 'shape' => 'NonEmptyString', ], 'UserName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsIamAccessKeyStatus' => [ 'type' => 'string', 'enum' => [ 'Active', 'Inactive', ], ], 'AwsIamAttachedManagedPolicy' => [ 'type' => 'structure', 'members' => [ 'PolicyName' => [ 'shape' => 'NonEmptyString', ], 'PolicyArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsIamAttachedManagedPolicyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsIamAttachedManagedPolicy', ], ], 'AwsIamGroupDetails' => [ 'type' => 'structure', 'members' => [ 'AttachedManagedPolicies' => [ 'shape' => 'AwsIamAttachedManagedPolicyList', ], 'CreateDate' => [ 'shape' => 'NonEmptyString', ], 'GroupId' => [ 'shape' => 'NonEmptyString', ], 'GroupName' => [ 'shape' => 'NonEmptyString', ], 'GroupPolicyList' => [ 'shape' => 'AwsIamGroupPolicyList', ], 'Path' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsIamGroupPolicy' => [ 'type' => 'structure', 'members' => [ 'PolicyName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsIamGroupPolicyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsIamGroupPolicy', ], ], 'AwsIamInstanceProfile' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'NonEmptyString', ], 'CreateDate' => [ 'shape' => 'NonEmptyString', ], 'InstanceProfileId' => [ 'shape' => 'NonEmptyString', ], 'InstanceProfileName' => [ 'shape' => 'NonEmptyString', ], 'Path' => [ 'shape' => 'NonEmptyString', ], 'Roles' => [ 'shape' => 'AwsIamInstanceProfileRoles', ], ], ], 'AwsIamInstanceProfileList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsIamInstanceProfile', ], ], 'AwsIamInstanceProfileRole' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'NonEmptyString', ], 'AssumeRolePolicyDocument' => [ 'shape' => 'AwsIamRoleAssumeRolePolicyDocument', ], 'CreateDate' => [ 'shape' => 'NonEmptyString', ], 'Path' => [ 'shape' => 'NonEmptyString', ], 'RoleId' => [ 'shape' => 'NonEmptyString', ], 'RoleName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsIamInstanceProfileRoles' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsIamInstanceProfileRole', ], ], 'AwsIamPermissionsBoundary' => [ 'type' => 'structure', 'members' => [ 'PermissionsBoundaryArn' => [ 'shape' => 'NonEmptyString', ], 'PermissionsBoundaryType' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsIamPolicyDetails' => [ 'type' => 'structure', 'members' => [ 'AttachmentCount' => [ 'shape' => 'Integer', ], 'CreateDate' => [ 'shape' => 'NonEmptyString', ], 'DefaultVersionId' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'IsAttachable' => [ 'shape' => 'Boolean', ], 'Path' => [ 'shape' => 'NonEmptyString', ], 'PermissionsBoundaryUsageCount' => [ 'shape' => 'Integer', ], 'PolicyId' => [ 'shape' => 'NonEmptyString', ], 'PolicyName' => [ 'shape' => 'NonEmptyString', ], 'PolicyVersionList' => [ 'shape' => 'AwsIamPolicyVersionList', ], 'UpdateDate' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsIamPolicyVersion' => [ 'type' => 'structure', 'members' => [ 'VersionId' => [ 'shape' => 'NonEmptyString', ], 'IsDefaultVersion' => [ 'shape' => 'Boolean', ], 'CreateDate' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsIamPolicyVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsIamPolicyVersion', ], ], 'AwsIamRoleAssumeRolePolicyDocument' => [ 'type' => 'string', 'max' => 131072, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u00A1-\\u00FF]+', ], 'AwsIamRoleDetails' => [ 'type' => 'structure', 'members' => [ 'AssumeRolePolicyDocument' => [ 'shape' => 'AwsIamRoleAssumeRolePolicyDocument', ], 'AttachedManagedPolicies' => [ 'shape' => 'AwsIamAttachedManagedPolicyList', ], 'CreateDate' => [ 'shape' => 'NonEmptyString', ], 'InstanceProfileList' => [ 'shape' => 'AwsIamInstanceProfileList', ], 'PermissionsBoundary' => [ 'shape' => 'AwsIamPermissionsBoundary', ], 'RoleId' => [ 'shape' => 'NonEmptyString', ], 'RoleName' => [ 'shape' => 'NonEmptyString', ], 'RolePolicyList' => [ 'shape' => 'AwsIamRolePolicyList', ], 'MaxSessionDuration' => [ 'shape' => 'Integer', ], 'Path' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsIamRolePolicy' => [ 'type' => 'structure', 'members' => [ 'PolicyName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsIamRolePolicyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsIamRolePolicy', ], ], 'AwsIamUserDetails' => [ 'type' => 'structure', 'members' => [ 'AttachedManagedPolicies' => [ 'shape' => 'AwsIamAttachedManagedPolicyList', ], 'CreateDate' => [ 'shape' => 'NonEmptyString', ], 'GroupList' => [ 'shape' => 'StringList', ], 'Path' => [ 'shape' => 'NonEmptyString', ], 'PermissionsBoundary' => [ 'shape' => 'AwsIamPermissionsBoundary', ], 'UserId' => [ 'shape' => 'NonEmptyString', ], 'UserName' => [ 'shape' => 'NonEmptyString', ], 'UserPolicyList' => [ 'shape' => 'AwsIamUserPolicyList', ], ], ], 'AwsIamUserPolicy' => [ 'type' => 'structure', 'members' => [ 'PolicyName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsIamUserPolicyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsIamUserPolicy', ], ], 'AwsKmsKeyDetails' => [ 'type' => 'structure', 'members' => [ 'AWSAccountId' => [ 'shape' => 'NonEmptyString', ], 'CreationDate' => [ 'shape' => 'Double', ], 'KeyId' => [ 'shape' => 'NonEmptyString', ], 'KeyManager' => [ 'shape' => 'NonEmptyString', ], 'KeyState' => [ 'shape' => 'NonEmptyString', ], 'Origin' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'KeyRotationStatus' => [ 'shape' => 'Boolean', ], ], ], 'AwsLambdaFunctionCode' => [ 'type' => 'structure', 'members' => [ 'S3Bucket' => [ 'shape' => 'NonEmptyString', ], 'S3Key' => [ 'shape' => 'NonEmptyString', ], 'S3ObjectVersion' => [ 'shape' => 'NonEmptyString', ], 'ZipFile' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsLambdaFunctionDeadLetterConfig' => [ 'type' => 'structure', 'members' => [ 'TargetArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsLambdaFunctionDetails' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'AwsLambdaFunctionCode', ], 'CodeSha256' => [ 'shape' => 'NonEmptyString', ], 'DeadLetterConfig' => [ 'shape' => 'AwsLambdaFunctionDeadLetterConfig', ], 'Environment' => [ 'shape' => 'AwsLambdaFunctionEnvironment', ], 'FunctionName' => [ 'shape' => 'NonEmptyString', ], 'Handler' => [ 'shape' => 'NonEmptyString', ], 'KmsKeyArn' => [ 'shape' => 'NonEmptyString', ], 'LastModified' => [ 'shape' => 'NonEmptyString', ], 'Layers' => [ 'shape' => 'AwsLambdaFunctionLayerList', ], 'MasterArn' => [ 'shape' => 'NonEmptyString', ], 'MemorySize' => [ 'shape' => 'Integer', ], 'RevisionId' => [ 'shape' => 'NonEmptyString', ], 'Role' => [ 'shape' => 'NonEmptyString', ], 'Runtime' => [ 'shape' => 'NonEmptyString', ], 'Timeout' => [ 'shape' => 'Integer', ], 'TracingConfig' => [ 'shape' => 'AwsLambdaFunctionTracingConfig', ], 'VpcConfig' => [ 'shape' => 'AwsLambdaFunctionVpcConfig', ], 'Version' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsLambdaFunctionEnvironment' => [ 'type' => 'structure', 'members' => [ 'Variables' => [ 'shape' => 'FieldMap', ], 'Error' => [ 'shape' => 'AwsLambdaFunctionEnvironmentError', ], ], ], 'AwsLambdaFunctionEnvironmentError' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'NonEmptyString', ], 'Message' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsLambdaFunctionLayer' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'NonEmptyString', ], 'CodeSize' => [ 'shape' => 'Integer', ], ], ], 'AwsLambdaFunctionLayerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsLambdaFunctionLayer', ], ], 'AwsLambdaFunctionTracingConfig' => [ 'type' => 'structure', 'members' => [ 'Mode' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsLambdaFunctionVpcConfig' => [ 'type' => 'structure', 'members' => [ 'SecurityGroupIds' => [ 'shape' => 'NonEmptyStringList', ], 'SubnetIds' => [ 'shape' => 'NonEmptyStringList', ], 'VpcId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsLambdaLayerVersionDetails' => [ 'type' => 'structure', 'members' => [ 'Version' => [ 'shape' => 'AwsLambdaLayerVersionNumber', ], 'CompatibleRuntimes' => [ 'shape' => 'NonEmptyStringList', ], 'CreatedDate' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsLambdaLayerVersionNumber' => [ 'type' => 'long', ], 'AwsOpenSearchServiceDomainClusterConfigDetails' => [ 'type' => 'structure', 'members' => [ 'InstanceCount' => [ 'shape' => 'Integer', ], 'WarmEnabled' => [ 'shape' => 'Boolean', ], 'WarmCount' => [ 'shape' => 'Integer', ], 'DedicatedMasterEnabled' => [ 'shape' => 'Boolean', ], 'ZoneAwarenessConfig' => [ 'shape' => 'AwsOpenSearchServiceDomainClusterConfigZoneAwarenessConfigDetails', ], 'DedicatedMasterCount' => [ 'shape' => 'Integer', ], 'InstanceType' => [ 'shape' => 'NonEmptyString', ], 'WarmType' => [ 'shape' => 'NonEmptyString', ], 'ZoneAwarenessEnabled' => [ 'shape' => 'Boolean', ], 'DedicatedMasterType' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsOpenSearchServiceDomainClusterConfigZoneAwarenessConfigDetails' => [ 'type' => 'structure', 'members' => [ 'AvailabilityZoneCount' => [ 'shape' => 'Integer', ], ], ], 'AwsOpenSearchServiceDomainDetails' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'NonEmptyString', ], 'AccessPolicies' => [ 'shape' => 'NonEmptyString', ], 'DomainName' => [ 'shape' => 'NonEmptyString', ], 'Id' => [ 'shape' => 'NonEmptyString', ], 'DomainEndpoint' => [ 'shape' => 'NonEmptyString', ], 'EngineVersion' => [ 'shape' => 'NonEmptyString', ], 'EncryptionAtRestOptions' => [ 'shape' => 'AwsOpenSearchServiceDomainEncryptionAtRestOptionsDetails', ], 'NodeToNodeEncryptionOptions' => [ 'shape' => 'AwsOpenSearchServiceDomainNodeToNodeEncryptionOptionsDetails', ], 'ServiceSoftwareOptions' => [ 'shape' => 'AwsOpenSearchServiceDomainServiceSoftwareOptionsDetails', ], 'ClusterConfig' => [ 'shape' => 'AwsOpenSearchServiceDomainClusterConfigDetails', ], 'DomainEndpointOptions' => [ 'shape' => 'AwsOpenSearchServiceDomainDomainEndpointOptionsDetails', ], 'VpcOptions' => [ 'shape' => 'AwsOpenSearchServiceDomainVpcOptionsDetails', ], 'LogPublishingOptions' => [ 'shape' => 'AwsOpenSearchServiceDomainLogPublishingOptionsDetails', ], 'DomainEndpoints' => [ 'shape' => 'FieldMap', ], ], ], 'AwsOpenSearchServiceDomainDomainEndpointOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'CustomEndpointCertificateArn' => [ 'shape' => 'NonEmptyString', ], 'CustomEndpointEnabled' => [ 'shape' => 'Boolean', ], 'EnforceHTTPS' => [ 'shape' => 'Boolean', ], 'CustomEndpoint' => [ 'shape' => 'NonEmptyString', ], 'TLSSecurityPolicy' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsOpenSearchServiceDomainEncryptionAtRestOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], 'KmsKeyId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsOpenSearchServiceDomainLogPublishingOption' => [ 'type' => 'structure', 'members' => [ 'CloudWatchLogsLogGroupArn' => [ 'shape' => 'NonEmptyString', ], 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'AwsOpenSearchServiceDomainLogPublishingOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'IndexSlowLogs' => [ 'shape' => 'AwsOpenSearchServiceDomainLogPublishingOption', ], 'SearchSlowLogs' => [ 'shape' => 'AwsOpenSearchServiceDomainLogPublishingOption', ], 'AuditLogs' => [ 'shape' => 'AwsOpenSearchServiceDomainLogPublishingOption', ], ], ], 'AwsOpenSearchServiceDomainNodeToNodeEncryptionOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'AwsOpenSearchServiceDomainServiceSoftwareOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'AutomatedUpdateDate' => [ 'shape' => 'NonEmptyString', ], 'Cancellable' => [ 'shape' => 'Boolean', ], 'CurrentVersion' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'NewVersion' => [ 'shape' => 'NonEmptyString', ], 'UpdateAvailable' => [ 'shape' => 'Boolean', ], 'UpdateStatus' => [ 'shape' => 'NonEmptyString', ], 'OptionalDeployment' => [ 'shape' => 'Boolean', ], ], ], 'AwsOpenSearchServiceDomainVpcOptionsDetails' => [ 'type' => 'structure', 'members' => [ 'SecurityGroupIds' => [ 'shape' => 'NonEmptyStringList', ], 'SubnetIds' => [ 'shape' => 'NonEmptyStringList', ], ], ], 'AwsRdsDbClusterAssociatedRole' => [ 'type' => 'structure', 'members' => [ 'RoleArn' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbClusterAssociatedRoles' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRdsDbClusterAssociatedRole', ], ], 'AwsRdsDbClusterDetails' => [ 'type' => 'structure', 'members' => [ 'AllocatedStorage' => [ 'shape' => 'Integer', ], 'AvailabilityZones' => [ 'shape' => 'StringList', ], 'BackupRetentionPeriod' => [ 'shape' => 'Integer', ], 'DatabaseName' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'Endpoint' => [ 'shape' => 'NonEmptyString', ], 'ReaderEndpoint' => [ 'shape' => 'NonEmptyString', ], 'CustomEndpoints' => [ 'shape' => 'StringList', ], 'MultiAz' => [ 'shape' => 'Boolean', ], 'Engine' => [ 'shape' => 'NonEmptyString', ], 'EngineVersion' => [ 'shape' => 'NonEmptyString', ], 'Port' => [ 'shape' => 'Integer', ], 'MasterUsername' => [ 'shape' => 'NonEmptyString', ], 'PreferredBackupWindow' => [ 'shape' => 'NonEmptyString', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'NonEmptyString', ], 'ReadReplicaIdentifiers' => [ 'shape' => 'StringList', ], 'VpcSecurityGroups' => [ 'shape' => 'AwsRdsDbInstanceVpcSecurityGroups', ], 'HostedZoneId' => [ 'shape' => 'NonEmptyString', ], 'StorageEncrypted' => [ 'shape' => 'Boolean', ], 'KmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'DbClusterResourceId' => [ 'shape' => 'NonEmptyString', ], 'AssociatedRoles' => [ 'shape' => 'AwsRdsDbClusterAssociatedRoles', ], 'ClusterCreateTime' => [ 'shape' => 'NonEmptyString', ], 'EnabledCloudWatchLogsExports' => [ 'shape' => 'StringList', ], 'EngineMode' => [ 'shape' => 'NonEmptyString', ], 'DeletionProtection' => [ 'shape' => 'Boolean', ], 'HttpEndpointEnabled' => [ 'shape' => 'Boolean', ], 'ActivityStreamStatus' => [ 'shape' => 'NonEmptyString', ], 'CopyTagsToSnapshot' => [ 'shape' => 'Boolean', ], 'CrossAccountClone' => [ 'shape' => 'Boolean', ], 'DomainMemberships' => [ 'shape' => 'AwsRdsDbDomainMemberships', ], 'DbClusterParameterGroup' => [ 'shape' => 'NonEmptyString', ], 'DbSubnetGroup' => [ 'shape' => 'NonEmptyString', ], 'DbClusterOptionGroupMemberships' => [ 'shape' => 'AwsRdsDbClusterOptionGroupMemberships', ], 'DbClusterIdentifier' => [ 'shape' => 'NonEmptyString', ], 'DbClusterMembers' => [ 'shape' => 'AwsRdsDbClusterMembers', ], 'IamDatabaseAuthenticationEnabled' => [ 'shape' => 'Boolean', ], ], ], 'AwsRdsDbClusterMember' => [ 'type' => 'structure', 'members' => [ 'IsClusterWriter' => [ 'shape' => 'Boolean', ], 'PromotionTier' => [ 'shape' => 'Integer', ], 'DbInstanceIdentifier' => [ 'shape' => 'NonEmptyString', ], 'DbClusterParameterGroupStatus' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbClusterMembers' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRdsDbClusterMember', ], ], 'AwsRdsDbClusterOptionGroupMembership' => [ 'type' => 'structure', 'members' => [ 'DbClusterOptionGroupName' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbClusterOptionGroupMemberships' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRdsDbClusterOptionGroupMembership', ], ], 'AwsRdsDbClusterSnapshotDetails' => [ 'type' => 'structure', 'members' => [ 'AvailabilityZones' => [ 'shape' => 'StringList', ], 'SnapshotCreateTime' => [ 'shape' => 'NonEmptyString', ], 'Engine' => [ 'shape' => 'NonEmptyString', ], 'AllocatedStorage' => [ 'shape' => 'Integer', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'Port' => [ 'shape' => 'Integer', ], 'VpcId' => [ 'shape' => 'NonEmptyString', ], 'ClusterCreateTime' => [ 'shape' => 'NonEmptyString', ], 'MasterUsername' => [ 'shape' => 'NonEmptyString', ], 'EngineVersion' => [ 'shape' => 'NonEmptyString', ], 'LicenseModel' => [ 'shape' => 'NonEmptyString', ], 'SnapshotType' => [ 'shape' => 'NonEmptyString', ], 'PercentProgress' => [ 'shape' => 'Integer', ], 'StorageEncrypted' => [ 'shape' => 'Boolean', ], 'KmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'DbClusterIdentifier' => [ 'shape' => 'NonEmptyString', ], 'DbClusterSnapshotIdentifier' => [ 'shape' => 'NonEmptyString', ], 'IamDatabaseAuthenticationEnabled' => [ 'shape' => 'Boolean', ], ], ], 'AwsRdsDbDomainMembership' => [ 'type' => 'structure', 'members' => [ 'Domain' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'Fqdn' => [ 'shape' => 'NonEmptyString', ], 'IamRoleName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbDomainMemberships' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRdsDbDomainMembership', ], ], 'AwsRdsDbInstanceAssociatedRole' => [ 'type' => 'structure', 'members' => [ 'RoleArn' => [ 'shape' => 'NonEmptyString', ], 'FeatureName' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbInstanceAssociatedRoles' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRdsDbInstanceAssociatedRole', ], ], 'AwsRdsDbInstanceDetails' => [ 'type' => 'structure', 'members' => [ 'AssociatedRoles' => [ 'shape' => 'AwsRdsDbInstanceAssociatedRoles', ], 'CACertificateIdentifier' => [ 'shape' => 'NonEmptyString', ], 'DBClusterIdentifier' => [ 'shape' => 'NonEmptyString', ], 'DBInstanceIdentifier' => [ 'shape' => 'NonEmptyString', ], 'DBInstanceClass' => [ 'shape' => 'NonEmptyString', ], 'DbInstancePort' => [ 'shape' => 'Integer', ], 'DbiResourceId' => [ 'shape' => 'NonEmptyString', ], 'DBName' => [ 'shape' => 'NonEmptyString', ], 'DeletionProtection' => [ 'shape' => 'Boolean', ], 'Endpoint' => [ 'shape' => 'AwsRdsDbInstanceEndpoint', ], 'Engine' => [ 'shape' => 'NonEmptyString', ], 'EngineVersion' => [ 'shape' => 'NonEmptyString', ], 'IAMDatabaseAuthenticationEnabled' => [ 'shape' => 'Boolean', ], 'InstanceCreateTime' => [ 'shape' => 'NonEmptyString', ], 'KmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'PubliclyAccessible' => [ 'shape' => 'Boolean', ], 'StorageEncrypted' => [ 'shape' => 'Boolean', ], 'TdeCredentialArn' => [ 'shape' => 'NonEmptyString', ], 'VpcSecurityGroups' => [ 'shape' => 'AwsRdsDbInstanceVpcSecurityGroups', ], 'MultiAz' => [ 'shape' => 'Boolean', ], 'EnhancedMonitoringResourceArn' => [ 'shape' => 'NonEmptyString', ], 'DbInstanceStatus' => [ 'shape' => 'NonEmptyString', ], 'MasterUsername' => [ 'shape' => 'NonEmptyString', ], 'AllocatedStorage' => [ 'shape' => 'Integer', ], 'PreferredBackupWindow' => [ 'shape' => 'NonEmptyString', ], 'BackupRetentionPeriod' => [ 'shape' => 'Integer', ], 'DbSecurityGroups' => [ 'shape' => 'StringList', ], 'DbParameterGroups' => [ 'shape' => 'AwsRdsDbParameterGroups', ], 'AvailabilityZone' => [ 'shape' => 'NonEmptyString', ], 'DbSubnetGroup' => [ 'shape' => 'AwsRdsDbSubnetGroup', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'NonEmptyString', ], 'PendingModifiedValues' => [ 'shape' => 'AwsRdsDbPendingModifiedValues', ], 'LatestRestorableTime' => [ 'shape' => 'NonEmptyString', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'Boolean', ], 'ReadReplicaSourceDBInstanceIdentifier' => [ 'shape' => 'NonEmptyString', ], 'ReadReplicaDBInstanceIdentifiers' => [ 'shape' => 'StringList', ], 'ReadReplicaDBClusterIdentifiers' => [ 'shape' => 'StringList', ], 'LicenseModel' => [ 'shape' => 'NonEmptyString', ], 'Iops' => [ 'shape' => 'Integer', ], 'OptionGroupMemberships' => [ 'shape' => 'AwsRdsDbOptionGroupMemberships', ], 'CharacterSetName' => [ 'shape' => 'NonEmptyString', ], 'SecondaryAvailabilityZone' => [ 'shape' => 'NonEmptyString', ], 'StatusInfos' => [ 'shape' => 'AwsRdsDbStatusInfos', ], 'StorageType' => [ 'shape' => 'NonEmptyString', ], 'DomainMemberships' => [ 'shape' => 'AwsRdsDbDomainMemberships', ], 'CopyTagsToSnapshot' => [ 'shape' => 'Boolean', ], 'MonitoringInterval' => [ 'shape' => 'Integer', ], 'MonitoringRoleArn' => [ 'shape' => 'NonEmptyString', ], 'PromotionTier' => [ 'shape' => 'Integer', ], 'Timezone' => [ 'shape' => 'NonEmptyString', ], 'PerformanceInsightsEnabled' => [ 'shape' => 'Boolean', ], 'PerformanceInsightsKmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'PerformanceInsightsRetentionPeriod' => [ 'shape' => 'Integer', ], 'EnabledCloudWatchLogsExports' => [ 'shape' => 'StringList', ], 'ProcessorFeatures' => [ 'shape' => 'AwsRdsDbProcessorFeatures', ], 'ListenerEndpoint' => [ 'shape' => 'AwsRdsDbInstanceEndpoint', ], 'MaxAllocatedStorage' => [ 'shape' => 'Integer', ], ], ], 'AwsRdsDbInstanceEndpoint' => [ 'type' => 'structure', 'members' => [ 'Address' => [ 'shape' => 'NonEmptyString', ], 'Port' => [ 'shape' => 'Integer', ], 'HostedZoneId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbInstanceVpcSecurityGroup' => [ 'type' => 'structure', 'members' => [ 'VpcSecurityGroupId' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbInstanceVpcSecurityGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRdsDbInstanceVpcSecurityGroup', ], ], 'AwsRdsDbOptionGroupMembership' => [ 'type' => 'structure', 'members' => [ 'OptionGroupName' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbOptionGroupMemberships' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRdsDbOptionGroupMembership', ], ], 'AwsRdsDbParameterGroup' => [ 'type' => 'structure', 'members' => [ 'DbParameterGroupName' => [ 'shape' => 'NonEmptyString', ], 'ParameterApplyStatus' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbParameterGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRdsDbParameterGroup', ], ], 'AwsRdsDbPendingModifiedValues' => [ 'type' => 'structure', 'members' => [ 'DbInstanceClass' => [ 'shape' => 'NonEmptyString', ], 'AllocatedStorage' => [ 'shape' => 'Integer', ], 'MasterUserPassword' => [ 'shape' => 'NonEmptyString', ], 'Port' => [ 'shape' => 'Integer', ], 'BackupRetentionPeriod' => [ 'shape' => 'Integer', ], 'MultiAZ' => [ 'shape' => 'Boolean', ], 'EngineVersion' => [ 'shape' => 'NonEmptyString', ], 'LicenseModel' => [ 'shape' => 'NonEmptyString', ], 'Iops' => [ 'shape' => 'Integer', ], 'DbInstanceIdentifier' => [ 'shape' => 'NonEmptyString', ], 'StorageType' => [ 'shape' => 'NonEmptyString', ], 'CaCertificateIdentifier' => [ 'shape' => 'NonEmptyString', ], 'DbSubnetGroupName' => [ 'shape' => 'NonEmptyString', ], 'PendingCloudWatchLogsExports' => [ 'shape' => 'AwsRdsPendingCloudWatchLogsExports', ], 'ProcessorFeatures' => [ 'shape' => 'AwsRdsDbProcessorFeatures', ], ], ], 'AwsRdsDbProcessorFeature' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbProcessorFeatures' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRdsDbProcessorFeature', ], ], 'AwsRdsDbSnapshotDetails' => [ 'type' => 'structure', 'members' => [ 'DbSnapshotIdentifier' => [ 'shape' => 'NonEmptyString', ], 'DbInstanceIdentifier' => [ 'shape' => 'NonEmptyString', ], 'SnapshotCreateTime' => [ 'shape' => 'NonEmptyString', ], 'Engine' => [ 'shape' => 'NonEmptyString', ], 'AllocatedStorage' => [ 'shape' => 'Integer', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'Port' => [ 'shape' => 'Integer', ], 'AvailabilityZone' => [ 'shape' => 'NonEmptyString', ], 'VpcId' => [ 'shape' => 'NonEmptyString', ], 'InstanceCreateTime' => [ 'shape' => 'NonEmptyString', ], 'MasterUsername' => [ 'shape' => 'NonEmptyString', ], 'EngineVersion' => [ 'shape' => 'NonEmptyString', ], 'LicenseModel' => [ 'shape' => 'NonEmptyString', ], 'SnapshotType' => [ 'shape' => 'NonEmptyString', ], 'Iops' => [ 'shape' => 'Integer', ], 'OptionGroupName' => [ 'shape' => 'NonEmptyString', ], 'PercentProgress' => [ 'shape' => 'Integer', ], 'SourceRegion' => [ 'shape' => 'NonEmptyString', ], 'SourceDbSnapshotIdentifier' => [ 'shape' => 'NonEmptyString', ], 'StorageType' => [ 'shape' => 'NonEmptyString', ], 'TdeCredentialArn' => [ 'shape' => 'NonEmptyString', ], 'Encrypted' => [ 'shape' => 'Boolean', ], 'KmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'Timezone' => [ 'shape' => 'NonEmptyString', ], 'IamDatabaseAuthenticationEnabled' => [ 'shape' => 'Boolean', ], 'ProcessorFeatures' => [ 'shape' => 'AwsRdsDbProcessorFeatures', ], 'DbiResourceId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbStatusInfo' => [ 'type' => 'structure', 'members' => [ 'StatusType' => [ 'shape' => 'NonEmptyString', ], 'Normal' => [ 'shape' => 'Boolean', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'Message' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbStatusInfos' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRdsDbStatusInfo', ], ], 'AwsRdsDbSubnetGroup' => [ 'type' => 'structure', 'members' => [ 'DbSubnetGroupName' => [ 'shape' => 'NonEmptyString', ], 'DbSubnetGroupDescription' => [ 'shape' => 'NonEmptyString', ], 'VpcId' => [ 'shape' => 'NonEmptyString', ], 'SubnetGroupStatus' => [ 'shape' => 'NonEmptyString', ], 'Subnets' => [ 'shape' => 'AwsRdsDbSubnetGroupSubnets', ], 'DbSubnetGroupArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbSubnetGroupSubnet' => [ 'type' => 'structure', 'members' => [ 'SubnetIdentifier' => [ 'shape' => 'NonEmptyString', ], 'SubnetAvailabilityZone' => [ 'shape' => 'AwsRdsDbSubnetGroupSubnetAvailabilityZone', ], 'SubnetStatus' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbSubnetGroupSubnetAvailabilityZone' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsDbSubnetGroupSubnets' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRdsDbSubnetGroupSubnet', ], ], 'AwsRdsEventSubscriptionDetails' => [ 'type' => 'structure', 'members' => [ 'CustSubscriptionId' => [ 'shape' => 'NonEmptyString', ], 'CustomerAwsId' => [ 'shape' => 'NonEmptyString', ], 'Enabled' => [ 'shape' => 'Boolean', ], 'EventCategoriesList' => [ 'shape' => 'NonEmptyStringList', ], 'EventSubscriptionArn' => [ 'shape' => 'NonEmptyString', ], 'SnsTopicArn' => [ 'shape' => 'NonEmptyString', ], 'SourceIdsList' => [ 'shape' => 'NonEmptyStringList', ], 'SourceType' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'SubscriptionCreationTime' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRdsPendingCloudWatchLogsExports' => [ 'type' => 'structure', 'members' => [ 'LogTypesToEnable' => [ 'shape' => 'StringList', ], 'LogTypesToDisable' => [ 'shape' => 'StringList', ], ], ], 'AwsRedshiftClusterClusterNode' => [ 'type' => 'structure', 'members' => [ 'NodeRole' => [ 'shape' => 'NonEmptyString', ], 'PrivateIpAddress' => [ 'shape' => 'NonEmptyString', ], 'PublicIpAddress' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRedshiftClusterClusterNodes' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRedshiftClusterClusterNode', ], ], 'AwsRedshiftClusterClusterParameterGroup' => [ 'type' => 'structure', 'members' => [ 'ClusterParameterStatusList' => [ 'shape' => 'AwsRedshiftClusterClusterParameterStatusList', ], 'ParameterApplyStatus' => [ 'shape' => 'NonEmptyString', ], 'ParameterGroupName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRedshiftClusterClusterParameterGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRedshiftClusterClusterParameterGroup', ], ], 'AwsRedshiftClusterClusterParameterStatus' => [ 'type' => 'structure', 'members' => [ 'ParameterName' => [ 'shape' => 'NonEmptyString', ], 'ParameterApplyStatus' => [ 'shape' => 'NonEmptyString', ], 'ParameterApplyErrorDescription' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRedshiftClusterClusterParameterStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRedshiftClusterClusterParameterStatus', ], ], 'AwsRedshiftClusterClusterSecurityGroup' => [ 'type' => 'structure', 'members' => [ 'ClusterSecurityGroupName' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRedshiftClusterClusterSecurityGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRedshiftClusterClusterSecurityGroup', ], ], 'AwsRedshiftClusterClusterSnapshotCopyStatus' => [ 'type' => 'structure', 'members' => [ 'DestinationRegion' => [ 'shape' => 'NonEmptyString', ], 'ManualSnapshotRetentionPeriod' => [ 'shape' => 'Integer', ], 'RetentionPeriod' => [ 'shape' => 'Integer', ], 'SnapshotCopyGrantName' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRedshiftClusterDeferredMaintenanceWindow' => [ 'type' => 'structure', 'members' => [ 'DeferMaintenanceEndTime' => [ 'shape' => 'NonEmptyString', ], 'DeferMaintenanceIdentifier' => [ 'shape' => 'NonEmptyString', ], 'DeferMaintenanceStartTime' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRedshiftClusterDeferredMaintenanceWindows' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRedshiftClusterDeferredMaintenanceWindow', ], ], 'AwsRedshiftClusterDetails' => [ 'type' => 'structure', 'members' => [ 'AllowVersionUpgrade' => [ 'shape' => 'Boolean', ], 'AutomatedSnapshotRetentionPeriod' => [ 'shape' => 'Integer', ], 'AvailabilityZone' => [ 'shape' => 'NonEmptyString', ], 'ClusterAvailabilityStatus' => [ 'shape' => 'NonEmptyString', ], 'ClusterCreateTime' => [ 'shape' => 'NonEmptyString', ], 'ClusterIdentifier' => [ 'shape' => 'NonEmptyString', ], 'ClusterNodes' => [ 'shape' => 'AwsRedshiftClusterClusterNodes', ], 'ClusterParameterGroups' => [ 'shape' => 'AwsRedshiftClusterClusterParameterGroups', ], 'ClusterPublicKey' => [ 'shape' => 'NonEmptyString', ], 'ClusterRevisionNumber' => [ 'shape' => 'NonEmptyString', ], 'ClusterSecurityGroups' => [ 'shape' => 'AwsRedshiftClusterClusterSecurityGroups', ], 'ClusterSnapshotCopyStatus' => [ 'shape' => 'AwsRedshiftClusterClusterSnapshotCopyStatus', ], 'ClusterStatus' => [ 'shape' => 'NonEmptyString', ], 'ClusterSubnetGroupName' => [ 'shape' => 'NonEmptyString', ], 'ClusterVersion' => [ 'shape' => 'NonEmptyString', ], 'DBName' => [ 'shape' => 'NonEmptyString', ], 'DeferredMaintenanceWindows' => [ 'shape' => 'AwsRedshiftClusterDeferredMaintenanceWindows', ], 'ElasticIpStatus' => [ 'shape' => 'AwsRedshiftClusterElasticIpStatus', ], 'ElasticResizeNumberOfNodeOptions' => [ 'shape' => 'NonEmptyString', ], 'Encrypted' => [ 'shape' => 'Boolean', ], 'Endpoint' => [ 'shape' => 'AwsRedshiftClusterEndpoint', ], 'EnhancedVpcRouting' => [ 'shape' => 'Boolean', ], 'ExpectedNextSnapshotScheduleTime' => [ 'shape' => 'NonEmptyString', ], 'ExpectedNextSnapshotScheduleTimeStatus' => [ 'shape' => 'NonEmptyString', ], 'HsmStatus' => [ 'shape' => 'AwsRedshiftClusterHsmStatus', ], 'IamRoles' => [ 'shape' => 'AwsRedshiftClusterIamRoles', ], 'KmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'MaintenanceTrackName' => [ 'shape' => 'NonEmptyString', ], 'ManualSnapshotRetentionPeriod' => [ 'shape' => 'Integer', ], 'MasterUsername' => [ 'shape' => 'NonEmptyString', ], 'NextMaintenanceWindowStartTime' => [ 'shape' => 'NonEmptyString', ], 'NodeType' => [ 'shape' => 'NonEmptyString', ], 'NumberOfNodes' => [ 'shape' => 'Integer', ], 'PendingActions' => [ 'shape' => 'StringList', ], 'PendingModifiedValues' => [ 'shape' => 'AwsRedshiftClusterPendingModifiedValues', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'NonEmptyString', ], 'PubliclyAccessible' => [ 'shape' => 'Boolean', ], 'ResizeInfo' => [ 'shape' => 'AwsRedshiftClusterResizeInfo', ], 'RestoreStatus' => [ 'shape' => 'AwsRedshiftClusterRestoreStatus', ], 'SnapshotScheduleIdentifier' => [ 'shape' => 'NonEmptyString', ], 'SnapshotScheduleState' => [ 'shape' => 'NonEmptyString', ], 'VpcId' => [ 'shape' => 'NonEmptyString', ], 'VpcSecurityGroups' => [ 'shape' => 'AwsRedshiftClusterVpcSecurityGroups', ], ], ], 'AwsRedshiftClusterElasticIpStatus' => [ 'type' => 'structure', 'members' => [ 'ElasticIp' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRedshiftClusterEndpoint' => [ 'type' => 'structure', 'members' => [ 'Address' => [ 'shape' => 'NonEmptyString', ], 'Port' => [ 'shape' => 'Integer', ], ], ], 'AwsRedshiftClusterHsmStatus' => [ 'type' => 'structure', 'members' => [ 'HsmClientCertificateIdentifier' => [ 'shape' => 'NonEmptyString', ], 'HsmConfigurationIdentifier' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRedshiftClusterIamRole' => [ 'type' => 'structure', 'members' => [ 'ApplyStatus' => [ 'shape' => 'NonEmptyString', ], 'IamRoleArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRedshiftClusterIamRoles' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRedshiftClusterIamRole', ], ], 'AwsRedshiftClusterPendingModifiedValues' => [ 'type' => 'structure', 'members' => [ 'AutomatedSnapshotRetentionPeriod' => [ 'shape' => 'Integer', ], 'ClusterIdentifier' => [ 'shape' => 'NonEmptyString', ], 'ClusterType' => [ 'shape' => 'NonEmptyString', ], 'ClusterVersion' => [ 'shape' => 'NonEmptyString', ], 'EncryptionType' => [ 'shape' => 'NonEmptyString', ], 'EnhancedVpcRouting' => [ 'shape' => 'Boolean', ], 'MaintenanceTrackName' => [ 'shape' => 'NonEmptyString', ], 'MasterUserPassword' => [ 'shape' => 'NonEmptyString', ], 'NodeType' => [ 'shape' => 'NonEmptyString', ], 'NumberOfNodes' => [ 'shape' => 'Integer', ], 'PubliclyAccessible' => [ 'shape' => 'Boolean', ], ], ], 'AwsRedshiftClusterResizeInfo' => [ 'type' => 'structure', 'members' => [ 'AllowCancelResize' => [ 'shape' => 'Boolean', ], 'ResizeType' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRedshiftClusterRestoreStatus' => [ 'type' => 'structure', 'members' => [ 'CurrentRestoreRateInMegaBytesPerSecond' => [ 'shape' => 'Double', ], 'ElapsedTimeInSeconds' => [ 'shape' => 'Long', ], 'EstimatedTimeToCompletionInSeconds' => [ 'shape' => 'Long', ], 'ProgressInMegaBytes' => [ 'shape' => 'Long', ], 'SnapshotSizeInMegaBytes' => [ 'shape' => 'Long', ], 'Status' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRedshiftClusterVpcSecurityGroup' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'NonEmptyString', ], 'VpcSecurityGroupId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsRedshiftClusterVpcSecurityGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRedshiftClusterVpcSecurityGroup', ], ], 'AwsS3AccountPublicAccessBlockDetails' => [ 'type' => 'structure', 'members' => [ 'BlockPublicAcls' => [ 'shape' => 'Boolean', ], 'BlockPublicPolicy' => [ 'shape' => 'Boolean', ], 'IgnorePublicAcls' => [ 'shape' => 'Boolean', ], 'RestrictPublicBuckets' => [ 'shape' => 'Boolean', ], ], ], 'AwsS3BucketBucketLifecycleConfigurationDetails' => [ 'type' => 'structure', 'members' => [ 'Rules' => [ 'shape' => 'AwsS3BucketBucketLifecycleConfigurationRulesList', ], ], ], 'AwsS3BucketBucketLifecycleConfigurationRulesAbortIncompleteMultipartUploadDetails' => [ 'type' => 'structure', 'members' => [ 'DaysAfterInitiation' => [ 'shape' => 'Integer', ], ], ], 'AwsS3BucketBucketLifecycleConfigurationRulesDetails' => [ 'type' => 'structure', 'members' => [ 'AbortIncompleteMultipartUpload' => [ 'shape' => 'AwsS3BucketBucketLifecycleConfigurationRulesAbortIncompleteMultipartUploadDetails', ], 'ExpirationDate' => [ 'shape' => 'NonEmptyString', ], 'ExpirationInDays' => [ 'shape' => 'Integer', ], 'ExpiredObjectDeleteMarker' => [ 'shape' => 'Boolean', ], 'Filter' => [ 'shape' => 'AwsS3BucketBucketLifecycleConfigurationRulesFilterDetails', ], 'ID' => [ 'shape' => 'NonEmptyString', ], 'NoncurrentVersionExpirationInDays' => [ 'shape' => 'Integer', ], 'NoncurrentVersionTransitions' => [ 'shape' => 'AwsS3BucketBucketLifecycleConfigurationRulesNoncurrentVersionTransitionsList', ], 'Prefix' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'Transitions' => [ 'shape' => 'AwsS3BucketBucketLifecycleConfigurationRulesTransitionsList', ], ], ], 'AwsS3BucketBucketLifecycleConfigurationRulesFilterDetails' => [ 'type' => 'structure', 'members' => [ 'Predicate' => [ 'shape' => 'AwsS3BucketBucketLifecycleConfigurationRulesFilterPredicateDetails', ], ], ], 'AwsS3BucketBucketLifecycleConfigurationRulesFilterPredicateDetails' => [ 'type' => 'structure', 'members' => [ 'Operands' => [ 'shape' => 'AwsS3BucketBucketLifecycleConfigurationRulesFilterPredicateOperandsList', ], 'Prefix' => [ 'shape' => 'NonEmptyString', ], 'Tag' => [ 'shape' => 'AwsS3BucketBucketLifecycleConfigurationRulesFilterPredicateTagDetails', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsS3BucketBucketLifecycleConfigurationRulesFilterPredicateOperandsDetails' => [ 'type' => 'structure', 'members' => [ 'Prefix' => [ 'shape' => 'NonEmptyString', ], 'Tag' => [ 'shape' => 'AwsS3BucketBucketLifecycleConfigurationRulesFilterPredicateOperandsTagDetails', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsS3BucketBucketLifecycleConfigurationRulesFilterPredicateOperandsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsS3BucketBucketLifecycleConfigurationRulesFilterPredicateOperandsDetails', ], ], 'AwsS3BucketBucketLifecycleConfigurationRulesFilterPredicateOperandsTagDetails' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsS3BucketBucketLifecycleConfigurationRulesFilterPredicateTagDetails' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsS3BucketBucketLifecycleConfigurationRulesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsS3BucketBucketLifecycleConfigurationRulesDetails', ], ], 'AwsS3BucketBucketLifecycleConfigurationRulesNoncurrentVersionTransitionsDetails' => [ 'type' => 'structure', 'members' => [ 'Days' => [ 'shape' => 'Integer', ], 'StorageClass' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsS3BucketBucketLifecycleConfigurationRulesNoncurrentVersionTransitionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsS3BucketBucketLifecycleConfigurationRulesNoncurrentVersionTransitionsDetails', ], ], 'AwsS3BucketBucketLifecycleConfigurationRulesTransitionsDetails' => [ 'type' => 'structure', 'members' => [ 'Date' => [ 'shape' => 'NonEmptyString', ], 'Days' => [ 'shape' => 'Integer', ], 'StorageClass' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsS3BucketBucketLifecycleConfigurationRulesTransitionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsS3BucketBucketLifecycleConfigurationRulesTransitionsDetails', ], ], 'AwsS3BucketDetails' => [ 'type' => 'structure', 'members' => [ 'OwnerId' => [ 'shape' => 'NonEmptyString', ], 'OwnerName' => [ 'shape' => 'NonEmptyString', ], 'OwnerAccountId' => [ 'shape' => 'NonEmptyString', ], 'CreatedAt' => [ 'shape' => 'NonEmptyString', ], 'ServerSideEncryptionConfiguration' => [ 'shape' => 'AwsS3BucketServerSideEncryptionConfiguration', ], 'BucketLifecycleConfiguration' => [ 'shape' => 'AwsS3BucketBucketLifecycleConfigurationDetails', ], 'PublicAccessBlockConfiguration' => [ 'shape' => 'AwsS3AccountPublicAccessBlockDetails', ], 'AccessControlList' => [ 'shape' => 'NonEmptyString', ], 'BucketLoggingConfiguration' => [ 'shape' => 'AwsS3BucketLoggingConfiguration', ], 'BucketWebsiteConfiguration' => [ 'shape' => 'AwsS3BucketWebsiteConfiguration', ], 'BucketNotificationConfiguration' => [ 'shape' => 'AwsS3BucketNotificationConfiguration', ], ], ], 'AwsS3BucketLoggingConfiguration' => [ 'type' => 'structure', 'members' => [ 'DestinationBucketName' => [ 'shape' => 'NonEmptyString', ], 'LogFilePrefix' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsS3BucketNotificationConfiguration' => [ 'type' => 'structure', 'members' => [ 'Configurations' => [ 'shape' => 'AwsS3BucketNotificationConfigurationDetails', ], ], ], 'AwsS3BucketNotificationConfigurationDetail' => [ 'type' => 'structure', 'members' => [ 'Events' => [ 'shape' => 'AwsS3BucketNotificationConfigurationEvents', ], 'Filter' => [ 'shape' => 'AwsS3BucketNotificationConfigurationFilter', ], 'Destination' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsS3BucketNotificationConfigurationDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsS3BucketNotificationConfigurationDetail', ], ], 'AwsS3BucketNotificationConfigurationEvents' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'AwsS3BucketNotificationConfigurationFilter' => [ 'type' => 'structure', 'members' => [ 'S3KeyFilter' => [ 'shape' => 'AwsS3BucketNotificationConfigurationS3KeyFilter', ], ], ], 'AwsS3BucketNotificationConfigurationS3KeyFilter' => [ 'type' => 'structure', 'members' => [ 'FilterRules' => [ 'shape' => 'AwsS3BucketNotificationConfigurationS3KeyFilterRules', ], ], ], 'AwsS3BucketNotificationConfigurationS3KeyFilterRule' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'AwsS3BucketNotificationConfigurationS3KeyFilterRuleName', ], 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsS3BucketNotificationConfigurationS3KeyFilterRuleName' => [ 'type' => 'string', 'enum' => [ 'Prefix', 'Suffix', ], ], 'AwsS3BucketNotificationConfigurationS3KeyFilterRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsS3BucketNotificationConfigurationS3KeyFilterRule', ], ], 'AwsS3BucketServerSideEncryptionByDefault' => [ 'type' => 'structure', 'members' => [ 'SSEAlgorithm' => [ 'shape' => 'NonEmptyString', ], 'KMSMasterKeyID' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsS3BucketServerSideEncryptionConfiguration' => [ 'type' => 'structure', 'members' => [ 'Rules' => [ 'shape' => 'AwsS3BucketServerSideEncryptionRules', ], ], ], 'AwsS3BucketServerSideEncryptionRule' => [ 'type' => 'structure', 'members' => [ 'ApplyServerSideEncryptionByDefault' => [ 'shape' => 'AwsS3BucketServerSideEncryptionByDefault', ], ], ], 'AwsS3BucketServerSideEncryptionRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsS3BucketServerSideEncryptionRule', ], ], 'AwsS3BucketWebsiteConfiguration' => [ 'type' => 'structure', 'members' => [ 'ErrorDocument' => [ 'shape' => 'NonEmptyString', ], 'IndexDocumentSuffix' => [ 'shape' => 'NonEmptyString', ], 'RedirectAllRequestsTo' => [ 'shape' => 'AwsS3BucketWebsiteConfigurationRedirectTo', ], 'RoutingRules' => [ 'shape' => 'AwsS3BucketWebsiteConfigurationRoutingRules', ], ], ], 'AwsS3BucketWebsiteConfigurationRedirectTo' => [ 'type' => 'structure', 'members' => [ 'Hostname' => [ 'shape' => 'NonEmptyString', ], 'Protocol' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsS3BucketWebsiteConfigurationRoutingRule' => [ 'type' => 'structure', 'members' => [ 'Condition' => [ 'shape' => 'AwsS3BucketWebsiteConfigurationRoutingRuleCondition', ], 'Redirect' => [ 'shape' => 'AwsS3BucketWebsiteConfigurationRoutingRuleRedirect', ], ], ], 'AwsS3BucketWebsiteConfigurationRoutingRuleCondition' => [ 'type' => 'structure', 'members' => [ 'HttpErrorCodeReturnedEquals' => [ 'shape' => 'NonEmptyString', ], 'KeyPrefixEquals' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsS3BucketWebsiteConfigurationRoutingRuleRedirect' => [ 'type' => 'structure', 'members' => [ 'Hostname' => [ 'shape' => 'NonEmptyString', ], 'HttpRedirectCode' => [ 'shape' => 'NonEmptyString', ], 'Protocol' => [ 'shape' => 'NonEmptyString', ], 'ReplaceKeyPrefixWith' => [ 'shape' => 'NonEmptyString', ], 'ReplaceKeyWith' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsS3BucketWebsiteConfigurationRoutingRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsS3BucketWebsiteConfigurationRoutingRule', ], ], 'AwsS3ObjectDetails' => [ 'type' => 'structure', 'members' => [ 'LastModified' => [ 'shape' => 'NonEmptyString', ], 'ETag' => [ 'shape' => 'NonEmptyString', ], 'VersionId' => [ 'shape' => 'NonEmptyString', ], 'ContentType' => [ 'shape' => 'NonEmptyString', ], 'ServerSideEncryption' => [ 'shape' => 'NonEmptyString', ], 'SSEKMSKeyId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsSecretsManagerSecretDetails' => [ 'type' => 'structure', 'members' => [ 'RotationRules' => [ 'shape' => 'AwsSecretsManagerSecretRotationRules', ], 'RotationOccurredWithinFrequency' => [ 'shape' => 'Boolean', ], 'KmsKeyId' => [ 'shape' => 'NonEmptyString', ], 'RotationEnabled' => [ 'shape' => 'Boolean', ], 'RotationLambdaArn' => [ 'shape' => 'NonEmptyString', ], 'Deleted' => [ 'shape' => 'Boolean', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsSecretsManagerSecretRotationRules' => [ 'type' => 'structure', 'members' => [ 'AutomaticallyAfterDays' => [ 'shape' => 'Integer', ], ], ], 'AwsSecurityFinding' => [ 'type' => 'structure', 'required' => [ 'SchemaVersion', 'Id', 'ProductArn', 'GeneratorId', 'AwsAccountId', 'CreatedAt', 'UpdatedAt', 'Title', 'Description', 'Resources', ], 'members' => [ 'SchemaVersion' => [ 'shape' => 'NonEmptyString', ], 'Id' => [ 'shape' => 'NonEmptyString', ], 'ProductArn' => [ 'shape' => 'NonEmptyString', ], 'ProductName' => [ 'shape' => 'NonEmptyString', ], 'CompanyName' => [ 'shape' => 'NonEmptyString', ], 'Region' => [ 'shape' => 'NonEmptyString', ], 'GeneratorId' => [ 'shape' => 'NonEmptyString', ], 'AwsAccountId' => [ 'shape' => 'NonEmptyString', ], 'Types' => [ 'shape' => 'TypeList', ], 'FirstObservedAt' => [ 'shape' => 'NonEmptyString', ], 'LastObservedAt' => [ 'shape' => 'NonEmptyString', ], 'CreatedAt' => [ 'shape' => 'NonEmptyString', ], 'UpdatedAt' => [ 'shape' => 'NonEmptyString', ], 'Severity' => [ 'shape' => 'Severity', ], 'Confidence' => [ 'shape' => 'Integer', ], 'Criticality' => [ 'shape' => 'Integer', ], 'Title' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'Remediation' => [ 'shape' => 'Remediation', ], 'SourceUrl' => [ 'shape' => 'NonEmptyString', ], 'ProductFields' => [ 'shape' => 'FieldMap', ], 'UserDefinedFields' => [ 'shape' => 'FieldMap', ], 'Malware' => [ 'shape' => 'MalwareList', ], 'Network' => [ 'shape' => 'Network', ], 'NetworkPath' => [ 'shape' => 'NetworkPathList', ], 'Process' => [ 'shape' => 'ProcessDetails', ], 'ThreatIntelIndicators' => [ 'shape' => 'ThreatIntelIndicatorList', ], 'Resources' => [ 'shape' => 'ResourceList', ], 'Compliance' => [ 'shape' => 'Compliance', ], 'VerificationState' => [ 'shape' => 'VerificationState', ], 'WorkflowState' => [ 'shape' => 'WorkflowState', ], 'Workflow' => [ 'shape' => 'Workflow', ], 'RecordState' => [ 'shape' => 'RecordState', ], 'RelatedFindings' => [ 'shape' => 'RelatedFindingList', ], 'Note' => [ 'shape' => 'Note', ], 'Vulnerabilities' => [ 'shape' => 'VulnerabilityList', ], 'PatchSummary' => [ 'shape' => 'PatchSummary', ], 'Action' => [ 'shape' => 'Action', ], 'FindingProviderFields' => [ 'shape' => 'FindingProviderFields', ], ], ], 'AwsSecurityFindingFilters' => [ 'type' => 'structure', 'members' => [ 'ProductArn' => [ 'shape' => 'StringFilterList', ], 'AwsAccountId' => [ 'shape' => 'StringFilterList', ], 'Id' => [ 'shape' => 'StringFilterList', ], 'GeneratorId' => [ 'shape' => 'StringFilterList', ], 'Region' => [ 'shape' => 'StringFilterList', ], 'Type' => [ 'shape' => 'StringFilterList', ], 'FirstObservedAt' => [ 'shape' => 'DateFilterList', ], 'LastObservedAt' => [ 'shape' => 'DateFilterList', ], 'CreatedAt' => [ 'shape' => 'DateFilterList', ], 'UpdatedAt' => [ 'shape' => 'DateFilterList', ], 'SeverityProduct' => [ 'shape' => 'NumberFilterList', 'deprecated' => true, 'deprecatedMessage' => 'This filter is deprecated. Instead, use FindingProviderSeverityOriginal.', ], 'SeverityNormalized' => [ 'shape' => 'NumberFilterList', 'deprecated' => true, 'deprecatedMessage' => 'This filter is deprecated. Instead, use SeverityLabel or FindingProviderFieldsSeverityLabel.', ], 'SeverityLabel' => [ 'shape' => 'StringFilterList', ], 'Confidence' => [ 'shape' => 'NumberFilterList', ], 'Criticality' => [ 'shape' => 'NumberFilterList', ], 'Title' => [ 'shape' => 'StringFilterList', ], 'Description' => [ 'shape' => 'StringFilterList', ], 'RecommendationText' => [ 'shape' => 'StringFilterList', ], 'SourceUrl' => [ 'shape' => 'StringFilterList', ], 'ProductFields' => [ 'shape' => 'MapFilterList', ], 'ProductName' => [ 'shape' => 'StringFilterList', ], 'CompanyName' => [ 'shape' => 'StringFilterList', ], 'UserDefinedFields' => [ 'shape' => 'MapFilterList', ], 'MalwareName' => [ 'shape' => 'StringFilterList', ], 'MalwareType' => [ 'shape' => 'StringFilterList', ], 'MalwarePath' => [ 'shape' => 'StringFilterList', ], 'MalwareState' => [ 'shape' => 'StringFilterList', ], 'NetworkDirection' => [ 'shape' => 'StringFilterList', ], 'NetworkProtocol' => [ 'shape' => 'StringFilterList', ], 'NetworkSourceIpV4' => [ 'shape' => 'IpFilterList', ], 'NetworkSourceIpV6' => [ 'shape' => 'IpFilterList', ], 'NetworkSourcePort' => [ 'shape' => 'NumberFilterList', ], 'NetworkSourceDomain' => [ 'shape' => 'StringFilterList', ], 'NetworkSourceMac' => [ 'shape' => 'StringFilterList', ], 'NetworkDestinationIpV4' => [ 'shape' => 'IpFilterList', ], 'NetworkDestinationIpV6' => [ 'shape' => 'IpFilterList', ], 'NetworkDestinationPort' => [ 'shape' => 'NumberFilterList', ], 'NetworkDestinationDomain' => [ 'shape' => 'StringFilterList', ], 'ProcessName' => [ 'shape' => 'StringFilterList', ], 'ProcessPath' => [ 'shape' => 'StringFilterList', ], 'ProcessPid' => [ 'shape' => 'NumberFilterList', ], 'ProcessParentPid' => [ 'shape' => 'NumberFilterList', ], 'ProcessLaunchedAt' => [ 'shape' => 'DateFilterList', ], 'ProcessTerminatedAt' => [ 'shape' => 'DateFilterList', ], 'ThreatIntelIndicatorType' => [ 'shape' => 'StringFilterList', ], 'ThreatIntelIndicatorValue' => [ 'shape' => 'StringFilterList', ], 'ThreatIntelIndicatorCategory' => [ 'shape' => 'StringFilterList', ], 'ThreatIntelIndicatorLastObservedAt' => [ 'shape' => 'DateFilterList', ], 'ThreatIntelIndicatorSource' => [ 'shape' => 'StringFilterList', ], 'ThreatIntelIndicatorSourceUrl' => [ 'shape' => 'StringFilterList', ], 'ResourceType' => [ 'shape' => 'StringFilterList', ], 'ResourceId' => [ 'shape' => 'StringFilterList', ], 'ResourcePartition' => [ 'shape' => 'StringFilterList', ], 'ResourceRegion' => [ 'shape' => 'StringFilterList', ], 'ResourceTags' => [ 'shape' => 'MapFilterList', ], 'ResourceAwsEc2InstanceType' => [ 'shape' => 'StringFilterList', ], 'ResourceAwsEc2InstanceImageId' => [ 'shape' => 'StringFilterList', ], 'ResourceAwsEc2InstanceIpV4Addresses' => [ 'shape' => 'IpFilterList', ], 'ResourceAwsEc2InstanceIpV6Addresses' => [ 'shape' => 'IpFilterList', ], 'ResourceAwsEc2InstanceKeyName' => [ 'shape' => 'StringFilterList', ], 'ResourceAwsEc2InstanceIamInstanceProfileArn' => [ 'shape' => 'StringFilterList', ], 'ResourceAwsEc2InstanceVpcId' => [ 'shape' => 'StringFilterList', ], 'ResourceAwsEc2InstanceSubnetId' => [ 'shape' => 'StringFilterList', ], 'ResourceAwsEc2InstanceLaunchedAt' => [ 'shape' => 'DateFilterList', ], 'ResourceAwsS3BucketOwnerId' => [ 'shape' => 'StringFilterList', ], 'ResourceAwsS3BucketOwnerName' => [ 'shape' => 'StringFilterList', ], 'ResourceAwsIamAccessKeyUserName' => [ 'shape' => 'StringFilterList', 'deprecated' => true, 'deprecatedMessage' => 'This filter is deprecated. Instead, use ResourceAwsIamAccessKeyPrincipalName.', ], 'ResourceAwsIamAccessKeyPrincipalName' => [ 'shape' => 'StringFilterList', ], 'ResourceAwsIamAccessKeyStatus' => [ 'shape' => 'StringFilterList', ], 'ResourceAwsIamAccessKeyCreatedAt' => [ 'shape' => 'DateFilterList', ], 'ResourceAwsIamUserUserName' => [ 'shape' => 'StringFilterList', ], 'ResourceContainerName' => [ 'shape' => 'StringFilterList', ], 'ResourceContainerImageId' => [ 'shape' => 'StringFilterList', ], 'ResourceContainerImageName' => [ 'shape' => 'StringFilterList', ], 'ResourceContainerLaunchedAt' => [ 'shape' => 'DateFilterList', ], 'ResourceDetailsOther' => [ 'shape' => 'MapFilterList', ], 'ComplianceStatus' => [ 'shape' => 'StringFilterList', ], 'VerificationState' => [ 'shape' => 'StringFilterList', ], 'WorkflowState' => [ 'shape' => 'StringFilterList', ], 'WorkflowStatus' => [ 'shape' => 'StringFilterList', ], 'RecordState' => [ 'shape' => 'StringFilterList', ], 'RelatedFindingsProductArn' => [ 'shape' => 'StringFilterList', ], 'RelatedFindingsId' => [ 'shape' => 'StringFilterList', ], 'NoteText' => [ 'shape' => 'StringFilterList', ], 'NoteUpdatedAt' => [ 'shape' => 'DateFilterList', ], 'NoteUpdatedBy' => [ 'shape' => 'StringFilterList', ], 'Keyword' => [ 'shape' => 'KeywordFilterList', 'deprecated' => true, 'deprecatedMessage' => 'The Keyword property is deprecated.', ], 'FindingProviderFieldsConfidence' => [ 'shape' => 'NumberFilterList', ], 'FindingProviderFieldsCriticality' => [ 'shape' => 'NumberFilterList', ], 'FindingProviderFieldsRelatedFindingsId' => [ 'shape' => 'StringFilterList', ], 'FindingProviderFieldsRelatedFindingsProductArn' => [ 'shape' => 'StringFilterList', ], 'FindingProviderFieldsSeverityLabel' => [ 'shape' => 'StringFilterList', ], 'FindingProviderFieldsSeverityOriginal' => [ 'shape' => 'StringFilterList', ], 'FindingProviderFieldsTypes' => [ 'shape' => 'StringFilterList', ], ], ], 'AwsSecurityFindingIdentifier' => [ 'type' => 'structure', 'required' => [ 'Id', 'ProductArn', ], 'members' => [ 'Id' => [ 'shape' => 'NonEmptyString', ], 'ProductArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsSecurityFindingIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsSecurityFindingIdentifier', ], ], 'AwsSecurityFindingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsSecurityFinding', ], ], 'AwsSnsTopicDetails' => [ 'type' => 'structure', 'members' => [ 'KmsMasterKeyId' => [ 'shape' => 'NonEmptyString', ], 'Subscription' => [ 'shape' => 'AwsSnsTopicSubscriptionList', ], 'TopicName' => [ 'shape' => 'NonEmptyString', ], 'Owner' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsSnsTopicSubscription' => [ 'type' => 'structure', 'members' => [ 'Endpoint' => [ 'shape' => 'NonEmptyString', ], 'Protocol' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsSnsTopicSubscriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsSnsTopicSubscription', ], ], 'AwsSqsQueueDetails' => [ 'type' => 'structure', 'members' => [ 'KmsDataKeyReusePeriodSeconds' => [ 'shape' => 'Integer', ], 'KmsMasterKeyId' => [ 'shape' => 'NonEmptyString', ], 'QueueName' => [ 'shape' => 'NonEmptyString', ], 'DeadLetterTargetArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsSsmComplianceSummary' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'NonEmptyString', ], 'CompliantCriticalCount' => [ 'shape' => 'Integer', ], 'CompliantHighCount' => [ 'shape' => 'Integer', ], 'CompliantMediumCount' => [ 'shape' => 'Integer', ], 'ExecutionType' => [ 'shape' => 'NonEmptyString', ], 'NonCompliantCriticalCount' => [ 'shape' => 'Integer', ], 'CompliantInformationalCount' => [ 'shape' => 'Integer', ], 'NonCompliantInformationalCount' => [ 'shape' => 'Integer', ], 'CompliantUnspecifiedCount' => [ 'shape' => 'Integer', ], 'NonCompliantLowCount' => [ 'shape' => 'Integer', ], 'NonCompliantHighCount' => [ 'shape' => 'Integer', ], 'CompliantLowCount' => [ 'shape' => 'Integer', ], 'ComplianceType' => [ 'shape' => 'NonEmptyString', ], 'PatchBaselineId' => [ 'shape' => 'NonEmptyString', ], 'OverallSeverity' => [ 'shape' => 'NonEmptyString', ], 'NonCompliantMediumCount' => [ 'shape' => 'Integer', ], 'NonCompliantUnspecifiedCount' => [ 'shape' => 'Integer', ], 'PatchGroup' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsSsmPatch' => [ 'type' => 'structure', 'members' => [ 'ComplianceSummary' => [ 'shape' => 'AwsSsmComplianceSummary', ], ], ], 'AwsSsmPatchComplianceDetails' => [ 'type' => 'structure', 'members' => [ 'Patch' => [ 'shape' => 'AwsSsmPatch', ], ], ], 'AwsWafRateBasedRuleDetails' => [ 'type' => 'structure', 'members' => [ 'MetricName' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'RateKey' => [ 'shape' => 'NonEmptyString', ], 'RateLimit' => [ 'shape' => 'Long', ], 'RuleId' => [ 'shape' => 'NonEmptyString', ], 'MatchPredicates' => [ 'shape' => 'AwsWafRateBasedRuleMatchPredicateList', ], ], ], 'AwsWafRateBasedRuleMatchPredicate' => [ 'type' => 'structure', 'members' => [ 'DataId' => [ 'shape' => 'NonEmptyString', ], 'Negated' => [ 'shape' => 'Boolean', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsWafRateBasedRuleMatchPredicateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsWafRateBasedRuleMatchPredicate', ], ], 'AwsWafRegionalRateBasedRuleDetails' => [ 'type' => 'structure', 'members' => [ 'MetricName' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'RateKey' => [ 'shape' => 'NonEmptyString', ], 'RateLimit' => [ 'shape' => 'Long', ], 'RuleId' => [ 'shape' => 'NonEmptyString', ], 'MatchPredicates' => [ 'shape' => 'AwsWafRegionalRateBasedRuleMatchPredicateList', ], ], ], 'AwsWafRegionalRateBasedRuleMatchPredicate' => [ 'type' => 'structure', 'members' => [ 'DataId' => [ 'shape' => 'NonEmptyString', ], 'Negated' => [ 'shape' => 'Boolean', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsWafRegionalRateBasedRuleMatchPredicateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsWafRegionalRateBasedRuleMatchPredicate', ], ], 'AwsWafWebAclDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'DefaultAction' => [ 'shape' => 'NonEmptyString', ], 'Rules' => [ 'shape' => 'AwsWafWebAclRuleList', ], 'WebAclId' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsWafWebAclRule' => [ 'type' => 'structure', 'members' => [ 'Action' => [ 'shape' => 'WafAction', ], 'ExcludedRules' => [ 'shape' => 'WafExcludedRuleList', ], 'OverrideAction' => [ 'shape' => 'WafOverrideAction', ], 'Priority' => [ 'shape' => 'Integer', ], 'RuleId' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'AwsWafWebAclRuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsWafWebAclRule', ], ], 'AwsXrayEncryptionConfigDetails' => [ 'type' => 'structure', 'members' => [ 'KeyId' => [ 'shape' => 'NonEmptyString', ], 'Status' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'BatchDisableStandardsRequest' => [ 'type' => 'structure', 'required' => [ 'StandardsSubscriptionArns', ], 'members' => [ 'StandardsSubscriptionArns' => [ 'shape' => 'StandardsSubscriptionArns', ], ], ], 'BatchDisableStandardsResponse' => [ 'type' => 'structure', 'members' => [ 'StandardsSubscriptions' => [ 'shape' => 'StandardsSubscriptions', ], ], ], 'BatchEnableStandardsRequest' => [ 'type' => 'structure', 'required' => [ 'StandardsSubscriptionRequests', ], 'members' => [ 'StandardsSubscriptionRequests' => [ 'shape' => 'StandardsSubscriptionRequests', ], ], ], 'BatchEnableStandardsResponse' => [ 'type' => 'structure', 'members' => [ 'StandardsSubscriptions' => [ 'shape' => 'StandardsSubscriptions', ], ], ], 'BatchImportFindingsRequest' => [ 'type' => 'structure', 'required' => [ 'Findings', ], 'members' => [ 'Findings' => [ 'shape' => 'BatchImportFindingsRequestFindingList', ], ], ], 'BatchImportFindingsRequestFindingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsSecurityFinding', ], 'max' => 100, 'min' => 1, ], 'BatchImportFindingsResponse' => [ 'type' => 'structure', 'required' => [ 'FailedCount', 'SuccessCount', ], 'members' => [ 'FailedCount' => [ 'shape' => 'Integer', ], 'SuccessCount' => [ 'shape' => 'Integer', ], 'FailedFindings' => [ 'shape' => 'ImportFindingsErrorList', ], ], ], 'BatchUpdateFindingsRequest' => [ 'type' => 'structure', 'required' => [ 'FindingIdentifiers', ], 'members' => [ 'FindingIdentifiers' => [ 'shape' => 'AwsSecurityFindingIdentifierList', ], 'Note' => [ 'shape' => 'NoteUpdate', ], 'Severity' => [ 'shape' => 'SeverityUpdate', ], 'VerificationState' => [ 'shape' => 'VerificationState', ], 'Confidence' => [ 'shape' => 'RatioScale', ], 'Criticality' => [ 'shape' => 'RatioScale', ], 'Types' => [ 'shape' => 'TypeList', ], 'UserDefinedFields' => [ 'shape' => 'FieldMap', ], 'Workflow' => [ 'shape' => 'WorkflowUpdate', ], 'RelatedFindings' => [ 'shape' => 'RelatedFindingList', ], ], ], 'BatchUpdateFindingsResponse' => [ 'type' => 'structure', 'required' => [ 'ProcessedFindings', 'UnprocessedFindings', ], 'members' => [ 'ProcessedFindings' => [ 'shape' => 'AwsSecurityFindingIdentifierList', ], 'UnprocessedFindings' => [ 'shape' => 'BatchUpdateFindingsUnprocessedFindingsList', ], ], ], 'BatchUpdateFindingsUnprocessedFinding' => [ 'type' => 'structure', 'required' => [ 'FindingIdentifier', 'ErrorCode', 'ErrorMessage', ], 'members' => [ 'FindingIdentifier' => [ 'shape' => 'AwsSecurityFindingIdentifier', ], 'ErrorCode' => [ 'shape' => 'NonEmptyString', ], 'ErrorMessage' => [ 'shape' => 'NonEmptyString', ], ], ], 'BatchUpdateFindingsUnprocessedFindingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchUpdateFindingsUnprocessedFinding', ], ], 'Boolean' => [ 'type' => 'boolean', ], 'CategoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'Cell' => [ 'type' => 'structure', 'members' => [ 'Column' => [ 'shape' => 'Long', ], 'Row' => [ 'shape' => 'Long', ], 'ColumnName' => [ 'shape' => 'NonEmptyString', ], 'CellReference' => [ 'shape' => 'NonEmptyString', ], ], ], 'Cells' => [ 'type' => 'list', 'member' => [ 'shape' => 'Cell', ], ], 'CidrBlockAssociation' => [ 'type' => 'structure', 'members' => [ 'AssociationId' => [ 'shape' => 'NonEmptyString', ], 'CidrBlock' => [ 'shape' => 'NonEmptyString', ], 'CidrBlockState' => [ 'shape' => 'NonEmptyString', ], ], ], 'CidrBlockAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CidrBlockAssociation', ], ], 'City' => [ 'type' => 'structure', 'members' => [ 'CityName' => [ 'shape' => 'NonEmptyString', ], ], ], 'ClassificationResult' => [ 'type' => 'structure', 'members' => [ 'MimeType' => [ 'shape' => 'NonEmptyString', ], 'SizeClassified' => [ 'shape' => 'Long', ], 'AdditionalOccurrences' => [ 'shape' => 'Boolean', ], 'Status' => [ 'shape' => 'ClassificationStatus', ], 'SensitiveData' => [ 'shape' => 'SensitiveDataResultList', ], 'CustomDataIdentifiers' => [ 'shape' => 'CustomDataIdentifiersResult', ], ], ], 'ClassificationStatus' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'NonEmptyString', ], 'Reason' => [ 'shape' => 'NonEmptyString', ], ], ], 'Compliance' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'ComplianceStatus', ], 'RelatedRequirements' => [ 'shape' => 'RelatedRequirementsList', ], 'StatusReasons' => [ 'shape' => 'StatusReasonsList', ], ], ], 'ComplianceStatus' => [ 'type' => 'string', 'enum' => [ 'PASSED', 'WARNING', 'FAILED', 'NOT_AVAILABLE', ], ], 'ContainerDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'ImageId' => [ 'shape' => 'NonEmptyString', ], 'ImageName' => [ 'shape' => 'NonEmptyString', ], 'LaunchedAt' => [ 'shape' => 'NonEmptyString', ], ], ], 'ControlStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'Country' => [ 'type' => 'structure', 'members' => [ 'CountryCode' => [ 'shape' => 'NonEmptyString', ], 'CountryName' => [ 'shape' => 'NonEmptyString', ], ], ], 'CreateActionTargetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Description', 'Id', ], 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'Id' => [ 'shape' => 'NonEmptyString', ], ], ], 'CreateActionTargetResponse' => [ 'type' => 'structure', 'required' => [ 'ActionTargetArn', ], 'members' => [ 'ActionTargetArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'CreateFindingAggregatorRequest' => [ 'type' => 'structure', 'required' => [ 'RegionLinkingMode', ], 'members' => [ 'RegionLinkingMode' => [ 'shape' => 'NonEmptyString', ], 'Regions' => [ 'shape' => 'StringList', ], ], ], 'CreateFindingAggregatorResponse' => [ 'type' => 'structure', 'members' => [ 'FindingAggregatorArn' => [ 'shape' => 'NonEmptyString', ], 'FindingAggregationRegion' => [ 'shape' => 'NonEmptyString', ], 'RegionLinkingMode' => [ 'shape' => 'NonEmptyString', ], 'Regions' => [ 'shape' => 'StringList', ], ], ], 'CreateInsightRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Filters', 'GroupByAttribute', ], 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Filters' => [ 'shape' => 'AwsSecurityFindingFilters', ], 'GroupByAttribute' => [ 'shape' => 'NonEmptyString', ], ], ], 'CreateInsightResponse' => [ 'type' => 'structure', 'required' => [ 'InsightArn', ], 'members' => [ 'InsightArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'CreateMembersRequest' => [ 'type' => 'structure', 'required' => [ 'AccountDetails', ], 'members' => [ 'AccountDetails' => [ 'shape' => 'AccountDetailsList', ], ], ], 'CreateMembersResponse' => [ 'type' => 'structure', 'members' => [ 'UnprocessedAccounts' => [ 'shape' => 'ResultList', ], ], ], 'CrossAccountMaxResults' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'CustomDataIdentifiersDetections' => [ 'type' => 'structure', 'members' => [ 'Count' => [ 'shape' => 'Long', ], 'Arn' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Occurrences' => [ 'shape' => 'Occurrences', ], ], ], 'CustomDataIdentifiersDetectionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomDataIdentifiersDetections', ], ], 'CustomDataIdentifiersResult' => [ 'type' => 'structure', 'members' => [ 'Detections' => [ 'shape' => 'CustomDataIdentifiersDetectionsList', ], 'TotalCount' => [ 'shape' => 'Long', ], ], ], 'Cvss' => [ 'type' => 'structure', 'members' => [ 'Version' => [ 'shape' => 'NonEmptyString', ], 'BaseScore' => [ 'shape' => 'Double', ], 'BaseVector' => [ 'shape' => 'NonEmptyString', ], 'Source' => [ 'shape' => 'NonEmptyString', ], 'Adjustments' => [ 'shape' => 'AdjustmentList', ], ], ], 'CvssList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Cvss', ], ], 'DataClassificationDetails' => [ 'type' => 'structure', 'members' => [ 'DetailedResultsLocation' => [ 'shape' => 'NonEmptyString', ], 'Result' => [ 'shape' => 'ClassificationResult', ], ], ], 'DateFilter' => [ 'type' => 'structure', 'members' => [ 'Start' => [ 'shape' => 'NonEmptyString', ], 'End' => [ 'shape' => 'NonEmptyString', ], 'DateRange' => [ 'shape' => 'DateRange', ], ], ], 'DateFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DateFilter', ], ], 'DateRange' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'Integer', ], 'Unit' => [ 'shape' => 'DateRangeUnit', ], ], ], 'DateRangeUnit' => [ 'type' => 'string', 'enum' => [ 'DAYS', ], ], 'DeclineInvitationsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountIds', ], 'members' => [ 'AccountIds' => [ 'shape' => 'AccountIdList', ], ], ], 'DeclineInvitationsResponse' => [ 'type' => 'structure', 'members' => [ 'UnprocessedAccounts' => [ 'shape' => 'ResultList', ], ], ], 'DeleteActionTargetRequest' => [ 'type' => 'structure', 'required' => [ 'ActionTargetArn', ], 'members' => [ 'ActionTargetArn' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'ActionTargetArn', ], ], ], 'DeleteActionTargetResponse' => [ 'type' => 'structure', 'required' => [ 'ActionTargetArn', ], 'members' => [ 'ActionTargetArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'DeleteFindingAggregatorRequest' => [ 'type' => 'structure', 'required' => [ 'FindingAggregatorArn', ], 'members' => [ 'FindingAggregatorArn' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'FindingAggregatorArn', ], ], ], 'DeleteFindingAggregatorResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteInsightRequest' => [ 'type' => 'structure', 'required' => [ 'InsightArn', ], 'members' => [ 'InsightArn' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'InsightArn', ], ], ], 'DeleteInsightResponse' => [ 'type' => 'structure', 'required' => [ 'InsightArn', ], 'members' => [ 'InsightArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'DeleteInvitationsRequest' => [ 'type' => 'structure', 'required' => [ 'AccountIds', ], 'members' => [ 'AccountIds' => [ 'shape' => 'AccountIdList', ], ], ], 'DeleteInvitationsResponse' => [ 'type' => 'structure', 'members' => [ 'UnprocessedAccounts' => [ 'shape' => 'ResultList', ], ], ], 'DeleteMembersRequest' => [ 'type' => 'structure', 'required' => [ 'AccountIds', ], 'members' => [ 'AccountIds' => [ 'shape' => 'AccountIdList', ], ], ], 'DeleteMembersResponse' => [ 'type' => 'structure', 'members' => [ 'UnprocessedAccounts' => [ 'shape' => 'ResultList', ], ], ], 'DescribeActionTargetsRequest' => [ 'type' => 'structure', 'members' => [ 'ActionTargetArns' => [ 'shape' => 'ArnList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'DescribeActionTargetsResponse' => [ 'type' => 'structure', 'required' => [ 'ActionTargets', ], 'members' => [ 'ActionTargets' => [ 'shape' => 'ActionTargetList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeHubRequest' => [ 'type' => 'structure', 'members' => [ 'HubArn' => [ 'shape' => 'NonEmptyString', 'location' => 'querystring', 'locationName' => 'HubArn', ], ], ], 'DescribeHubResponse' => [ 'type' => 'structure', 'members' => [ 'HubArn' => [ 'shape' => 'NonEmptyString', ], 'SubscribedAt' => [ 'shape' => 'NonEmptyString', ], 'AutoEnableControls' => [ 'shape' => 'Boolean', ], ], ], 'DescribeOrganizationConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'DescribeOrganizationConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'AutoEnable' => [ 'shape' => 'Boolean', ], 'MemberAccountLimitReached' => [ 'shape' => 'Boolean', ], ], ], 'DescribeProductsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'ProductArn' => [ 'shape' => 'NonEmptyString', 'location' => 'querystring', 'locationName' => 'ProductArn', ], ], ], 'DescribeProductsResponse' => [ 'type' => 'structure', 'required' => [ 'Products', ], 'members' => [ 'Products' => [ 'shape' => 'ProductsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeStandardsControlsRequest' => [ 'type' => 'structure', 'required' => [ 'StandardsSubscriptionArn', ], 'members' => [ 'StandardsSubscriptionArn' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'StandardsSubscriptionArn', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'DescribeStandardsControlsResponse' => [ 'type' => 'structure', 'members' => [ 'Controls' => [ 'shape' => 'StandardsControls', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeStandardsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'DescribeStandardsResponse' => [ 'type' => 'structure', 'members' => [ 'Standards' => [ 'shape' => 'Standards', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DisableImportFindingsForProductRequest' => [ 'type' => 'structure', 'required' => [ 'ProductSubscriptionArn', ], 'members' => [ 'ProductSubscriptionArn' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'ProductSubscriptionArn', ], ], ], 'DisableImportFindingsForProductResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisableOrganizationAdminAccountRequest' => [ 'type' => 'structure', 'required' => [ 'AdminAccountId', ], 'members' => [ 'AdminAccountId' => [ 'shape' => 'NonEmptyString', ], ], ], 'DisableOrganizationAdminAccountResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisableSecurityHubRequest' => [ 'type' => 'structure', 'members' => [], ], 'DisableSecurityHubResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateFromAdministratorAccountRequest' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateFromAdministratorAccountResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateFromMasterAccountRequest' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateFromMasterAccountResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateMembersRequest' => [ 'type' => 'structure', 'required' => [ 'AccountIds', ], 'members' => [ 'AccountIds' => [ 'shape' => 'AccountIdList', ], ], ], 'DisassociateMembersResponse' => [ 'type' => 'structure', 'members' => [], ], 'DnsRequestAction' => [ 'type' => 'structure', 'members' => [ 'Domain' => [ 'shape' => 'NonEmptyString', ], 'Protocol' => [ 'shape' => 'NonEmptyString', ], 'Blocked' => [ 'shape' => 'Boolean', ], ], ], 'Double' => [ 'type' => 'double', ], 'EnableImportFindingsForProductRequest' => [ 'type' => 'structure', 'required' => [ 'ProductArn', ], 'members' => [ 'ProductArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'EnableImportFindingsForProductResponse' => [ 'type' => 'structure', 'members' => [ 'ProductSubscriptionArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'EnableOrganizationAdminAccountRequest' => [ 'type' => 'structure', 'required' => [ 'AdminAccountId', ], 'members' => [ 'AdminAccountId' => [ 'shape' => 'NonEmptyString', ], ], ], 'EnableOrganizationAdminAccountResponse' => [ 'type' => 'structure', 'members' => [], ], 'EnableSecurityHubRequest' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagMap', ], 'EnableDefaultStandards' => [ 'shape' => 'Boolean', ], ], ], 'EnableSecurityHubResponse' => [ 'type' => 'structure', 'members' => [], ], 'FieldMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonEmptyString', ], 'value' => [ 'shape' => 'NonEmptyString', ], ], 'FindingAggregator' => [ 'type' => 'structure', 'members' => [ 'FindingAggregatorArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'FindingAggregatorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FindingAggregator', ], ], 'FindingProviderFields' => [ 'type' => 'structure', 'members' => [ 'Confidence' => [ 'shape' => 'RatioScale', ], 'Criticality' => [ 'shape' => 'RatioScale', ], 'RelatedFindings' => [ 'shape' => 'RelatedFindingList', ], 'Severity' => [ 'shape' => 'FindingProviderSeverity', ], 'Types' => [ 'shape' => 'TypeList', ], ], ], 'FindingProviderSeverity' => [ 'type' => 'structure', 'members' => [ 'Label' => [ 'shape' => 'SeverityLabel', ], 'Original' => [ 'shape' => 'NonEmptyString', ], ], ], 'GeoLocation' => [ 'type' => 'structure', 'members' => [ 'Lon' => [ 'shape' => 'Double', ], 'Lat' => [ 'shape' => 'Double', ], ], ], 'GetAdministratorAccountRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetAdministratorAccountResponse' => [ 'type' => 'structure', 'members' => [ 'Administrator' => [ 'shape' => 'Invitation', ], ], ], 'GetEnabledStandardsRequest' => [ 'type' => 'structure', 'members' => [ 'StandardsSubscriptionArns' => [ 'shape' => 'StandardsSubscriptionArns', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'GetEnabledStandardsResponse' => [ 'type' => 'structure', 'members' => [ 'StandardsSubscriptions' => [ 'shape' => 'StandardsSubscriptions', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetFindingAggregatorRequest' => [ 'type' => 'structure', 'required' => [ 'FindingAggregatorArn', ], 'members' => [ 'FindingAggregatorArn' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'FindingAggregatorArn', ], ], ], 'GetFindingAggregatorResponse' => [ 'type' => 'structure', 'members' => [ 'FindingAggregatorArn' => [ 'shape' => 'NonEmptyString', ], 'FindingAggregationRegion' => [ 'shape' => 'NonEmptyString', ], 'RegionLinkingMode' => [ 'shape' => 'NonEmptyString', ], 'Regions' => [ 'shape' => 'StringList', ], ], ], 'GetFindingsRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'AwsSecurityFindingFilters', ], 'SortCriteria' => [ 'shape' => 'SortCriteria', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'GetFindingsResponse' => [ 'type' => 'structure', 'required' => [ 'Findings', ], 'members' => [ 'Findings' => [ 'shape' => 'AwsSecurityFindingList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetInsightResultsRequest' => [ 'type' => 'structure', 'required' => [ 'InsightArn', ], 'members' => [ 'InsightArn' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'InsightArn', ], ], ], 'GetInsightResultsResponse' => [ 'type' => 'structure', 'required' => [ 'InsightResults', ], 'members' => [ 'InsightResults' => [ 'shape' => 'InsightResults', ], ], ], 'GetInsightsRequest' => [ 'type' => 'structure', 'members' => [ 'InsightArns' => [ 'shape' => 'ArnList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'GetInsightsResponse' => [ 'type' => 'structure', 'required' => [ 'Insights', ], 'members' => [ 'Insights' => [ 'shape' => 'InsightList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetInvitationsCountRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetInvitationsCountResponse' => [ 'type' => 'structure', 'members' => [ 'InvitationsCount' => [ 'shape' => 'Integer', ], ], ], 'GetMasterAccountRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetMasterAccountResponse' => [ 'type' => 'structure', 'members' => [ 'Master' => [ 'shape' => 'Invitation', ], ], ], 'GetMembersRequest' => [ 'type' => 'structure', 'required' => [ 'AccountIds', ], 'members' => [ 'AccountIds' => [ 'shape' => 'AccountIdList', ], ], ], 'GetMembersResponse' => [ 'type' => 'structure', 'members' => [ 'Members' => [ 'shape' => 'MemberList', ], 'UnprocessedAccounts' => [ 'shape' => 'ResultList', ], ], ], 'IcmpTypeCode' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'Integer', ], 'Type' => [ 'shape' => 'Integer', ], ], ], 'ImportFindingsError' => [ 'type' => 'structure', 'required' => [ 'Id', 'ErrorCode', 'ErrorMessage', ], 'members' => [ 'Id' => [ 'shape' => 'NonEmptyString', ], 'ErrorCode' => [ 'shape' => 'NonEmptyString', ], 'ErrorMessage' => [ 'shape' => 'NonEmptyString', ], ], ], 'ImportFindingsErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImportFindingsError', ], ], 'Insight' => [ 'type' => 'structure', 'required' => [ 'InsightArn', 'Name', 'Filters', 'GroupByAttribute', ], 'members' => [ 'InsightArn' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Filters' => [ 'shape' => 'AwsSecurityFindingFilters', ], 'GroupByAttribute' => [ 'shape' => 'NonEmptyString', ], ], ], 'InsightList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Insight', ], ], 'InsightResultValue' => [ 'type' => 'structure', 'required' => [ 'GroupByAttributeValue', 'Count', ], 'members' => [ 'GroupByAttributeValue' => [ 'shape' => 'NonEmptyString', ], 'Count' => [ 'shape' => 'Integer', ], ], ], 'InsightResultValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InsightResultValue', ], ], 'InsightResults' => [ 'type' => 'structure', 'required' => [ 'InsightArn', 'GroupByAttribute', 'ResultValues', ], 'members' => [ 'InsightArn' => [ 'shape' => 'NonEmptyString', ], 'GroupByAttribute' => [ 'shape' => 'NonEmptyString', ], 'ResultValues' => [ 'shape' => 'InsightResultValueList', ], ], ], 'Integer' => [ 'type' => 'integer', ], 'IntegerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Integer', ], ], 'IntegrationType' => [ 'type' => 'string', 'enum' => [ 'SEND_FINDINGS_TO_SECURITY_HUB', 'RECEIVE_FINDINGS_FROM_SECURITY_HUB', 'UPDATE_FINDINGS_IN_SECURITY_HUB', ], ], 'IntegrationTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IntegrationType', ], ], 'InternalException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], 'Code' => [ 'shape' => 'NonEmptyString', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, ], 'InvalidAccessException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], 'Code' => [ 'shape' => 'NonEmptyString', ], ], 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], 'InvalidInputException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], 'Code' => [ 'shape' => 'NonEmptyString', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'Invitation' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'InvitationId' => [ 'shape' => 'NonEmptyString', ], 'InvitedAt' => [ 'shape' => 'Timestamp', ], 'MemberStatus' => [ 'shape' => 'NonEmptyString', ], ], ], 'InvitationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Invitation', ], ], 'InviteMembersRequest' => [ 'type' => 'structure', 'required' => [ 'AccountIds', ], 'members' => [ 'AccountIds' => [ 'shape' => 'AccountIdList', ], ], ], 'InviteMembersResponse' => [ 'type' => 'structure', 'members' => [ 'UnprocessedAccounts' => [ 'shape' => 'ResultList', ], ], ], 'IpFilter' => [ 'type' => 'structure', 'members' => [ 'Cidr' => [ 'shape' => 'NonEmptyString', ], ], ], 'IpFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpFilter', ], ], 'IpOrganizationDetails' => [ 'type' => 'structure', 'members' => [ 'Asn' => [ 'shape' => 'Integer', ], 'AsnOrg' => [ 'shape' => 'NonEmptyString', ], 'Isp' => [ 'shape' => 'NonEmptyString', ], 'Org' => [ 'shape' => 'NonEmptyString', ], ], ], 'Ipv6CidrBlockAssociation' => [ 'type' => 'structure', 'members' => [ 'AssociationId' => [ 'shape' => 'NonEmptyString', ], 'Ipv6CidrBlock' => [ 'shape' => 'NonEmptyString', ], 'CidrBlockState' => [ 'shape' => 'NonEmptyString', ], ], ], 'Ipv6CidrBlockAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Ipv6CidrBlockAssociation', ], ], 'KeywordFilter' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'NonEmptyString', ], ], ], 'KeywordFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KeywordFilter', ], ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], 'Code' => [ 'shape' => 'NonEmptyString', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'ListEnabledProductsForImportRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListEnabledProductsForImportResponse' => [ 'type' => 'structure', 'members' => [ 'ProductSubscriptions' => [ 'shape' => 'ProductSubscriptionArnList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListFindingAggregatorsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListFindingAggregatorsResponse' => [ 'type' => 'structure', 'members' => [ 'FindingAggregators' => [ 'shape' => 'FindingAggregatorList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListInvitationsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'CrossAccountMaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListInvitationsResponse' => [ 'type' => 'structure', 'members' => [ 'Invitations' => [ 'shape' => 'InvitationList', ], 'NextToken' => [ 'shape' => 'NonEmptyString', ], ], ], 'ListMembersRequest' => [ 'type' => 'structure', 'members' => [ 'OnlyAssociated' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'OnlyAssociated', ], 'MaxResults' => [ 'shape' => 'CrossAccountMaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListMembersResponse' => [ 'type' => 'structure', 'members' => [ 'Members' => [ 'shape' => 'MemberList', ], 'NextToken' => [ 'shape' => 'NonEmptyString', ], ], ], 'ListOrganizationAdminAccountsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'AdminsMaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], ], ], 'ListOrganizationAdminAccountsResponse' => [ 'type' => 'structure', 'members' => [ 'AdminAccounts' => [ 'shape' => 'AdminAccounts', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'LoadBalancerState' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'NonEmptyString', ], 'Reason' => [ 'shape' => 'NonEmptyString', ], ], ], 'Long' => [ 'type' => 'long', ], 'Malware' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Type' => [ 'shape' => 'MalwareType', ], 'Path' => [ 'shape' => 'NonEmptyString', ], 'State' => [ 'shape' => 'MalwareState', ], ], ], 'MalwareList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Malware', ], ], 'MalwareState' => [ 'type' => 'string', 'enum' => [ 'OBSERVED', 'REMOVAL_FAILED', 'REMOVED', ], ], 'MalwareType' => [ 'type' => 'string', 'enum' => [ 'ADWARE', 'BLENDED_THREAT', 'BOTNET_AGENT', 'COIN_MINER', 'EXPLOIT_KIT', 'KEYLOGGER', 'MACRO', 'POTENTIALLY_UNWANTED', 'SPYWARE', 'RANSOMWARE', 'REMOTE_ACCESS', 'ROOTKIT', 'TROJAN', 'VIRUS', 'WORM', ], ], 'MapFilter' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'NonEmptyString', ], 'Value' => [ 'shape' => 'NonEmptyString', ], 'Comparison' => [ 'shape' => 'MapFilterComparison', ], ], ], 'MapFilterComparison' => [ 'type' => 'string', 'enum' => [ 'EQUALS', 'NOT_EQUALS', ], ], 'MapFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MapFilter', ], ], 'MaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'Member' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'Email' => [ 'shape' => 'NonEmptyString', ], 'MasterId' => [ 'shape' => 'NonEmptyString', 'deprecated' => true, 'deprecatedMessage' => 'This field is deprecated, use AdministratorId instead.', ], 'AdministratorId' => [ 'shape' => 'NonEmptyString', ], 'MemberStatus' => [ 'shape' => 'NonEmptyString', ], 'InvitedAt' => [ 'shape' => 'Timestamp', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'MemberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Member', ], ], 'Network' => [ 'type' => 'structure', 'members' => [ 'Direction' => [ 'shape' => 'NetworkDirection', ], 'Protocol' => [ 'shape' => 'NonEmptyString', ], 'OpenPortRange' => [ 'shape' => 'PortRange', ], 'SourceIpV4' => [ 'shape' => 'NonEmptyString', ], 'SourceIpV6' => [ 'shape' => 'NonEmptyString', ], 'SourcePort' => [ 'shape' => 'Integer', ], 'SourceDomain' => [ 'shape' => 'NonEmptyString', ], 'SourceMac' => [ 'shape' => 'NonEmptyString', ], 'DestinationIpV4' => [ 'shape' => 'NonEmptyString', ], 'DestinationIpV6' => [ 'shape' => 'NonEmptyString', ], 'DestinationPort' => [ 'shape' => 'Integer', ], 'DestinationDomain' => [ 'shape' => 'NonEmptyString', ], ], ], 'NetworkConnectionAction' => [ 'type' => 'structure', 'members' => [ 'ConnectionDirection' => [ 'shape' => 'NonEmptyString', ], 'RemoteIpDetails' => [ 'shape' => 'ActionRemoteIpDetails', ], 'RemotePortDetails' => [ 'shape' => 'ActionRemotePortDetails', ], 'LocalPortDetails' => [ 'shape' => 'ActionLocalPortDetails', ], 'Protocol' => [ 'shape' => 'NonEmptyString', ], 'Blocked' => [ 'shape' => 'Boolean', ], ], ], 'NetworkDirection' => [ 'type' => 'string', 'enum' => [ 'IN', 'OUT', ], ], 'NetworkHeader' => [ 'type' => 'structure', 'members' => [ 'Protocol' => [ 'shape' => 'NonEmptyString', ], 'Destination' => [ 'shape' => 'NetworkPathComponentDetails', ], 'Source' => [ 'shape' => 'NetworkPathComponentDetails', ], ], ], 'NetworkPathComponent' => [ 'type' => 'structure', 'members' => [ 'ComponentId' => [ 'shape' => 'NonEmptyString', ], 'ComponentType' => [ 'shape' => 'NonEmptyString', ], 'Egress' => [ 'shape' => 'NetworkHeader', ], 'Ingress' => [ 'shape' => 'NetworkHeader', ], ], ], 'NetworkPathComponentDetails' => [ 'type' => 'structure', 'members' => [ 'Address' => [ 'shape' => 'StringList', ], 'PortRanges' => [ 'shape' => 'PortRangeList', ], ], ], 'NetworkPathList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetworkPathComponent', ], ], 'NextToken' => [ 'type' => 'string', ], 'NonEmptyString' => [ 'type' => 'string', 'pattern' => '.*\\S.*', ], 'NonEmptyStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'Note' => [ 'type' => 'structure', 'required' => [ 'Text', 'UpdatedBy', 'UpdatedAt', ], 'members' => [ 'Text' => [ 'shape' => 'NonEmptyString', ], 'UpdatedBy' => [ 'shape' => 'NonEmptyString', ], 'UpdatedAt' => [ 'shape' => 'NonEmptyString', ], ], ], 'NoteUpdate' => [ 'type' => 'structure', 'required' => [ 'Text', 'UpdatedBy', ], 'members' => [ 'Text' => [ 'shape' => 'NonEmptyString', ], 'UpdatedBy' => [ 'shape' => 'NonEmptyString', ], ], ], 'NumberFilter' => [ 'type' => 'structure', 'members' => [ 'Gte' => [ 'shape' => 'Double', ], 'Lte' => [ 'shape' => 'Double', ], 'Eq' => [ 'shape' => 'Double', ], ], ], 'NumberFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NumberFilter', ], ], 'Occurrences' => [ 'type' => 'structure', 'members' => [ 'LineRanges' => [ 'shape' => 'Ranges', ], 'OffsetRanges' => [ 'shape' => 'Ranges', ], 'Pages' => [ 'shape' => 'Pages', ], 'Records' => [ 'shape' => 'Records', ], 'Cells' => [ 'shape' => 'Cells', ], ], ], 'Page' => [ 'type' => 'structure', 'members' => [ 'PageNumber' => [ 'shape' => 'Long', ], 'LineRange' => [ 'shape' => 'Range', ], 'OffsetRange' => [ 'shape' => 'Range', ], ], ], 'Pages' => [ 'type' => 'list', 'member' => [ 'shape' => 'Page', ], ], 'Partition' => [ 'type' => 'string', 'enum' => [ 'aws', 'aws-cn', 'aws-us-gov', ], ], 'PatchSummary' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'NonEmptyString', ], 'InstalledCount' => [ 'shape' => 'Integer', ], 'MissingCount' => [ 'shape' => 'Integer', ], 'FailedCount' => [ 'shape' => 'Integer', ], 'InstalledOtherCount' => [ 'shape' => 'Integer', ], 'InstalledRejectedCount' => [ 'shape' => 'Integer', ], 'InstalledPendingReboot' => [ 'shape' => 'Integer', ], 'OperationStartTime' => [ 'shape' => 'NonEmptyString', ], 'OperationEndTime' => [ 'shape' => 'NonEmptyString', ], 'RebootOption' => [ 'shape' => 'NonEmptyString', ], 'Operation' => [ 'shape' => 'NonEmptyString', ], ], ], 'PortProbeAction' => [ 'type' => 'structure', 'members' => [ 'PortProbeDetails' => [ 'shape' => 'PortProbeDetailList', ], 'Blocked' => [ 'shape' => 'Boolean', ], ], ], 'PortProbeDetail' => [ 'type' => 'structure', 'members' => [ 'LocalPortDetails' => [ 'shape' => 'ActionLocalPortDetails', ], 'LocalIpDetails' => [ 'shape' => 'ActionLocalIpDetails', ], 'RemoteIpDetails' => [ 'shape' => 'ActionRemoteIpDetails', ], ], ], 'PortProbeDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PortProbeDetail', ], ], 'PortRange' => [ 'type' => 'structure', 'members' => [ 'Begin' => [ 'shape' => 'Integer', ], 'End' => [ 'shape' => 'Integer', ], ], ], 'PortRangeFromTo' => [ 'type' => 'structure', 'members' => [ 'From' => [ 'shape' => 'Integer', ], 'To' => [ 'shape' => 'Integer', ], ], ], 'PortRangeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PortRange', ], ], 'ProcessDetails' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Path' => [ 'shape' => 'NonEmptyString', ], 'Pid' => [ 'shape' => 'Integer', ], 'ParentPid' => [ 'shape' => 'Integer', ], 'LaunchedAt' => [ 'shape' => 'NonEmptyString', ], 'TerminatedAt' => [ 'shape' => 'NonEmptyString', ], ], ], 'Product' => [ 'type' => 'structure', 'required' => [ 'ProductArn', ], 'members' => [ 'ProductArn' => [ 'shape' => 'NonEmptyString', ], 'ProductName' => [ 'shape' => 'NonEmptyString', ], 'CompanyName' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'Categories' => [ 'shape' => 'CategoryList', ], 'IntegrationTypes' => [ 'shape' => 'IntegrationTypeList', ], 'MarketplaceUrl' => [ 'shape' => 'NonEmptyString', ], 'ActivationUrl' => [ 'shape' => 'NonEmptyString', ], 'ProductSubscriptionResourcePolicy' => [ 'shape' => 'NonEmptyString', ], ], ], 'ProductSubscriptionArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'ProductsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Product', ], ], 'Range' => [ 'type' => 'structure', 'members' => [ 'Start' => [ 'shape' => 'Long', ], 'End' => [ 'shape' => 'Long', ], 'StartColumn' => [ 'shape' => 'Long', ], ], ], 'Ranges' => [ 'type' => 'list', 'member' => [ 'shape' => 'Range', ], ], 'RatioScale' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'Recommendation' => [ 'type' => 'structure', 'members' => [ 'Text' => [ 'shape' => 'NonEmptyString', ], 'Url' => [ 'shape' => 'NonEmptyString', ], ], ], 'Record' => [ 'type' => 'structure', 'members' => [ 'JsonPath' => [ 'shape' => 'NonEmptyString', ], 'RecordIndex' => [ 'shape' => 'Long', ], ], ], 'RecordState' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'ARCHIVED', ], ], 'Records' => [ 'type' => 'list', 'member' => [ 'shape' => 'Record', ], ], 'RelatedFinding' => [ 'type' => 'structure', 'required' => [ 'ProductArn', 'Id', ], 'members' => [ 'ProductArn' => [ 'shape' => 'NonEmptyString', ], 'Id' => [ 'shape' => 'NonEmptyString', ], ], ], 'RelatedFindingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RelatedFinding', ], ], 'RelatedRequirementsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'Remediation' => [ 'type' => 'structure', 'members' => [ 'Recommendation' => [ 'shape' => 'Recommendation', ], ], ], 'Resource' => [ 'type' => 'structure', 'required' => [ 'Type', 'Id', ], 'members' => [ 'Type' => [ 'shape' => 'NonEmptyString', ], 'Id' => [ 'shape' => 'NonEmptyString', ], 'Partition' => [ 'shape' => 'Partition', ], 'Region' => [ 'shape' => 'NonEmptyString', ], 'ResourceRole' => [ 'shape' => 'NonEmptyString', ], 'Tags' => [ 'shape' => 'FieldMap', ], 'DataClassification' => [ 'shape' => 'DataClassificationDetails', ], 'Details' => [ 'shape' => 'ResourceDetails', ], ], ], 'ResourceArn' => [ 'type' => 'string', 'pattern' => '^arn:aws:securityhub:.*', ], 'ResourceConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], 'Code' => [ 'shape' => 'NonEmptyString', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ResourceDetails' => [ 'type' => 'structure', 'members' => [ 'AwsAutoScalingAutoScalingGroup' => [ 'shape' => 'AwsAutoScalingAutoScalingGroupDetails', ], 'AwsCodeBuildProject' => [ 'shape' => 'AwsCodeBuildProjectDetails', ], 'AwsCloudFrontDistribution' => [ 'shape' => 'AwsCloudFrontDistributionDetails', ], 'AwsEc2Instance' => [ 'shape' => 'AwsEc2InstanceDetails', ], 'AwsEc2NetworkInterface' => [ 'shape' => 'AwsEc2NetworkInterfaceDetails', ], 'AwsEc2SecurityGroup' => [ 'shape' => 'AwsEc2SecurityGroupDetails', ], 'AwsEc2Volume' => [ 'shape' => 'AwsEc2VolumeDetails', ], 'AwsEc2Vpc' => [ 'shape' => 'AwsEc2VpcDetails', ], 'AwsEc2Eip' => [ 'shape' => 'AwsEc2EipDetails', ], 'AwsEc2Subnet' => [ 'shape' => 'AwsEc2SubnetDetails', ], 'AwsEc2NetworkAcl' => [ 'shape' => 'AwsEc2NetworkAclDetails', ], 'AwsElbv2LoadBalancer' => [ 'shape' => 'AwsElbv2LoadBalancerDetails', ], 'AwsElasticBeanstalkEnvironment' => [ 'shape' => 'AwsElasticBeanstalkEnvironmentDetails', ], 'AwsElasticsearchDomain' => [ 'shape' => 'AwsElasticsearchDomainDetails', ], 'AwsS3Bucket' => [ 'shape' => 'AwsS3BucketDetails', ], 'AwsS3AccountPublicAccessBlock' => [ 'shape' => 'AwsS3AccountPublicAccessBlockDetails', ], 'AwsS3Object' => [ 'shape' => 'AwsS3ObjectDetails', ], 'AwsSecretsManagerSecret' => [ 'shape' => 'AwsSecretsManagerSecretDetails', ], 'AwsIamAccessKey' => [ 'shape' => 'AwsIamAccessKeyDetails', ], 'AwsIamUser' => [ 'shape' => 'AwsIamUserDetails', ], 'AwsIamPolicy' => [ 'shape' => 'AwsIamPolicyDetails', ], 'AwsApiGatewayV2Stage' => [ 'shape' => 'AwsApiGatewayV2StageDetails', ], 'AwsApiGatewayV2Api' => [ 'shape' => 'AwsApiGatewayV2ApiDetails', ], 'AwsDynamoDbTable' => [ 'shape' => 'AwsDynamoDbTableDetails', ], 'AwsApiGatewayStage' => [ 'shape' => 'AwsApiGatewayStageDetails', ], 'AwsApiGatewayRestApi' => [ 'shape' => 'AwsApiGatewayRestApiDetails', ], 'AwsCloudTrailTrail' => [ 'shape' => 'AwsCloudTrailTrailDetails', ], 'AwsSsmPatchCompliance' => [ 'shape' => 'AwsSsmPatchComplianceDetails', ], 'AwsCertificateManagerCertificate' => [ 'shape' => 'AwsCertificateManagerCertificateDetails', ], 'AwsRedshiftCluster' => [ 'shape' => 'AwsRedshiftClusterDetails', ], 'AwsElbLoadBalancer' => [ 'shape' => 'AwsElbLoadBalancerDetails', ], 'AwsIamGroup' => [ 'shape' => 'AwsIamGroupDetails', ], 'AwsIamRole' => [ 'shape' => 'AwsIamRoleDetails', ], 'AwsKmsKey' => [ 'shape' => 'AwsKmsKeyDetails', ], 'AwsLambdaFunction' => [ 'shape' => 'AwsLambdaFunctionDetails', ], 'AwsLambdaLayerVersion' => [ 'shape' => 'AwsLambdaLayerVersionDetails', ], 'AwsRdsDbInstance' => [ 'shape' => 'AwsRdsDbInstanceDetails', ], 'AwsSnsTopic' => [ 'shape' => 'AwsSnsTopicDetails', ], 'AwsSqsQueue' => [ 'shape' => 'AwsSqsQueueDetails', ], 'AwsWafWebAcl' => [ 'shape' => 'AwsWafWebAclDetails', ], 'AwsRdsDbSnapshot' => [ 'shape' => 'AwsRdsDbSnapshotDetails', ], 'AwsRdsDbClusterSnapshot' => [ 'shape' => 'AwsRdsDbClusterSnapshotDetails', ], 'AwsRdsDbCluster' => [ 'shape' => 'AwsRdsDbClusterDetails', ], 'AwsEcsCluster' => [ 'shape' => 'AwsEcsClusterDetails', ], 'AwsEcsTaskDefinition' => [ 'shape' => 'AwsEcsTaskDefinitionDetails', ], 'Container' => [ 'shape' => 'ContainerDetails', ], 'Other' => [ 'shape' => 'FieldMap', ], 'AwsRdsEventSubscription' => [ 'shape' => 'AwsRdsEventSubscriptionDetails', ], 'AwsEcsService' => [ 'shape' => 'AwsEcsServiceDetails', ], 'AwsAutoScalingLaunchConfiguration' => [ 'shape' => 'AwsAutoScalingLaunchConfigurationDetails', ], 'AwsEc2VpnConnection' => [ 'shape' => 'AwsEc2VpnConnectionDetails', ], 'AwsEcrContainerImage' => [ 'shape' => 'AwsEcrContainerImageDetails', ], 'AwsOpenSearchServiceDomain' => [ 'shape' => 'AwsOpenSearchServiceDomainDetails', ], 'AwsEc2VpcEndpointService' => [ 'shape' => 'AwsEc2VpcEndpointServiceDetails', ], 'AwsXrayEncryptionConfig' => [ 'shape' => 'AwsXrayEncryptionConfigDetails', ], 'AwsWafRateBasedRule' => [ 'shape' => 'AwsWafRateBasedRuleDetails', ], 'AwsWafRegionalRateBasedRule' => [ 'shape' => 'AwsWafRegionalRateBasedRuleDetails', ], 'AwsEcrRepository' => [ 'shape' => 'AwsEcrRepositoryDetails', ], 'AwsEksCluster' => [ 'shape' => 'AwsEksClusterDetails', ], ], ], 'ResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Resource', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'NonEmptyString', ], 'Code' => [ 'shape' => 'NonEmptyString', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'Result' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'ProcessingResult' => [ 'shape' => 'NonEmptyString', ], ], ], 'ResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Result', ], ], 'SecurityGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'SensitiveDataDetections' => [ 'type' => 'structure', 'members' => [ 'Count' => [ 'shape' => 'Long', ], 'Type' => [ 'shape' => 'NonEmptyString', ], 'Occurrences' => [ 'shape' => 'Occurrences', ], ], ], 'SensitiveDataDetectionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SensitiveDataDetections', ], ], 'SensitiveDataResult' => [ 'type' => 'structure', 'members' => [ 'Category' => [ 'shape' => 'NonEmptyString', ], 'Detections' => [ 'shape' => 'SensitiveDataDetectionsList', ], 'TotalCount' => [ 'shape' => 'Long', ], ], ], 'SensitiveDataResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SensitiveDataResult', ], ], 'Severity' => [ 'type' => 'structure', 'members' => [ 'Product' => [ 'shape' => 'Double', ], 'Label' => [ 'shape' => 'SeverityLabel', ], 'Normalized' => [ 'shape' => 'Integer', ], 'Original' => [ 'shape' => 'NonEmptyString', ], ], ], 'SeverityLabel' => [ 'type' => 'string', 'enum' => [ 'INFORMATIONAL', 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL', ], ], 'SeverityRating' => [ 'type' => 'string', 'enum' => [ 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL', ], ], 'SeverityUpdate' => [ 'type' => 'structure', 'members' => [ 'Normalized' => [ 'shape' => 'RatioScale', ], 'Product' => [ 'shape' => 'Double', ], 'Label' => [ 'shape' => 'SeverityLabel', ], ], ], 'SizeBytes' => [ 'type' => 'long', ], 'SoftwarePackage' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Version' => [ 'shape' => 'NonEmptyString', ], 'Epoch' => [ 'shape' => 'NonEmptyString', ], 'Release' => [ 'shape' => 'NonEmptyString', ], 'Architecture' => [ 'shape' => 'NonEmptyString', ], 'PackageManager' => [ 'shape' => 'NonEmptyString', ], 'FilePath' => [ 'shape' => 'NonEmptyString', ], ], ], 'SoftwarePackageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SoftwarePackage', ], ], 'SortCriteria' => [ 'type' => 'list', 'member' => [ 'shape' => 'SortCriterion', ], ], 'SortCriterion' => [ 'type' => 'structure', 'members' => [ 'Field' => [ 'shape' => 'NonEmptyString', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'asc', 'desc', ], ], 'Standard' => [ 'type' => 'structure', 'members' => [ 'StandardsArn' => [ 'shape' => 'NonEmptyString', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'EnabledByDefault' => [ 'shape' => 'Boolean', ], ], ], 'Standards' => [ 'type' => 'list', 'member' => [ 'shape' => 'Standard', ], ], 'StandardsControl' => [ 'type' => 'structure', 'members' => [ 'StandardsControlArn' => [ 'shape' => 'NonEmptyString', ], 'ControlStatus' => [ 'shape' => 'ControlStatus', ], 'DisabledReason' => [ 'shape' => 'NonEmptyString', ], 'ControlStatusUpdatedAt' => [ 'shape' => 'Timestamp', ], 'ControlId' => [ 'shape' => 'NonEmptyString', ], 'Title' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], 'RemediationUrl' => [ 'shape' => 'NonEmptyString', ], 'SeverityRating' => [ 'shape' => 'SeverityRating', ], 'RelatedRequirements' => [ 'shape' => 'RelatedRequirementsList', ], ], ], 'StandardsControls' => [ 'type' => 'list', 'member' => [ 'shape' => 'StandardsControl', ], ], 'StandardsInputParameterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonEmptyString', ], 'value' => [ 'shape' => 'NonEmptyString', ], ], 'StandardsStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'READY', 'FAILED', 'DELETING', 'INCOMPLETE', ], ], 'StandardsSubscription' => [ 'type' => 'structure', 'required' => [ 'StandardsSubscriptionArn', 'StandardsArn', 'StandardsInput', 'StandardsStatus', ], 'members' => [ 'StandardsSubscriptionArn' => [ 'shape' => 'NonEmptyString', ], 'StandardsArn' => [ 'shape' => 'NonEmptyString', ], 'StandardsInput' => [ 'shape' => 'StandardsInputParameterMap', ], 'StandardsStatus' => [ 'shape' => 'StandardsStatus', ], ], ], 'StandardsSubscriptionArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 25, 'min' => 1, ], 'StandardsSubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'StandardsArn', ], 'members' => [ 'StandardsArn' => [ 'shape' => 'NonEmptyString', ], 'StandardsInput' => [ 'shape' => 'StandardsInputParameterMap', ], ], ], 'StandardsSubscriptionRequests' => [ 'type' => 'list', 'member' => [ 'shape' => 'StandardsSubscriptionRequest', ], 'max' => 25, 'min' => 1, ], 'StandardsSubscriptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'StandardsSubscription', ], ], 'StatusReason' => [ 'type' => 'structure', 'required' => [ 'ReasonCode', ], 'members' => [ 'ReasonCode' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], ], ], 'StatusReasonsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StatusReason', ], ], 'StringFilter' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'NonEmptyString', ], 'Comparison' => [ 'shape' => 'StringFilterComparison', ], ], ], 'StringFilterComparison' => [ 'type' => 'string', 'enum' => [ 'EQUALS', 'PREFIX', 'NOT_EQUALS', 'PREFIX_NOT_EQUALS', ], ], 'StringFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringFilter', ], ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!aws:)[a-zA-Z+-=._:/]+$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, ], 'ThreatIntelIndicator' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'ThreatIntelIndicatorType', ], 'Value' => [ 'shape' => 'NonEmptyString', ], 'Category' => [ 'shape' => 'ThreatIntelIndicatorCategory', ], 'LastObservedAt' => [ 'shape' => 'NonEmptyString', ], 'Source' => [ 'shape' => 'NonEmptyString', ], 'SourceUrl' => [ 'shape' => 'NonEmptyString', ], ], ], 'ThreatIntelIndicatorCategory' => [ 'type' => 'string', 'enum' => [ 'BACKDOOR', 'CARD_STEALER', 'COMMAND_AND_CONTROL', 'DROP_SITE', 'EXPLOIT_SITE', 'KEYLOGGER', ], ], 'ThreatIntelIndicatorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ThreatIntelIndicator', ], ], 'ThreatIntelIndicatorType' => [ 'type' => 'string', 'enum' => [ 'DOMAIN', 'EMAIL_ADDRESS', 'HASH_MD5', 'HASH_SHA1', 'HASH_SHA256', 'HASH_SHA512', 'IPV4_ADDRESS', 'IPV6_ADDRESS', 'MUTEX', 'PROCESS', 'URL', ], ], 'Timestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'TypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'ResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateActionTargetRequest' => [ 'type' => 'structure', 'required' => [ 'ActionTargetArn', ], 'members' => [ 'ActionTargetArn' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'ActionTargetArn', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Description' => [ 'shape' => 'NonEmptyString', ], ], ], 'UpdateActionTargetResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateFindingAggregatorRequest' => [ 'type' => 'structure', 'required' => [ 'FindingAggregatorArn', 'RegionLinkingMode', ], 'members' => [ 'FindingAggregatorArn' => [ 'shape' => 'NonEmptyString', ], 'RegionLinkingMode' => [ 'shape' => 'NonEmptyString', ], 'Regions' => [ 'shape' => 'StringList', ], ], ], 'UpdateFindingAggregatorResponse' => [ 'type' => 'structure', 'members' => [ 'FindingAggregatorArn' => [ 'shape' => 'NonEmptyString', ], 'FindingAggregationRegion' => [ 'shape' => 'NonEmptyString', ], 'RegionLinkingMode' => [ 'shape' => 'NonEmptyString', ], 'Regions' => [ 'shape' => 'StringList', ], ], ], 'UpdateFindingsRequest' => [ 'type' => 'structure', 'required' => [ 'Filters', ], 'members' => [ 'Filters' => [ 'shape' => 'AwsSecurityFindingFilters', ], 'Note' => [ 'shape' => 'NoteUpdate', ], 'RecordState' => [ 'shape' => 'RecordState', ], ], ], 'UpdateFindingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateInsightRequest' => [ 'type' => 'structure', 'required' => [ 'InsightArn', ], 'members' => [ 'InsightArn' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'InsightArn', ], 'Name' => [ 'shape' => 'NonEmptyString', ], 'Filters' => [ 'shape' => 'AwsSecurityFindingFilters', ], 'GroupByAttribute' => [ 'shape' => 'NonEmptyString', ], ], ], 'UpdateInsightResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateOrganizationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'AutoEnable', ], 'members' => [ 'AutoEnable' => [ 'shape' => 'Boolean', ], ], ], 'UpdateOrganizationConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateSecurityHubConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'AutoEnableControls' => [ 'shape' => 'Boolean', ], ], ], 'UpdateSecurityHubConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateStandardsControlRequest' => [ 'type' => 'structure', 'required' => [ 'StandardsControlArn', ], 'members' => [ 'StandardsControlArn' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'StandardsControlArn', ], 'ControlStatus' => [ 'shape' => 'ControlStatus', ], 'DisabledReason' => [ 'shape' => 'NonEmptyString', ], ], ], 'UpdateStandardsControlResponse' => [ 'type' => 'structure', 'members' => [], ], 'VerificationState' => [ 'type' => 'string', 'enum' => [ 'UNKNOWN', 'TRUE_POSITIVE', 'FALSE_POSITIVE', 'BENIGN_POSITIVE', ], ], 'Vulnerability' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'NonEmptyString', ], 'VulnerablePackages' => [ 'shape' => 'SoftwarePackageList', ], 'Cvss' => [ 'shape' => 'CvssList', ], 'RelatedVulnerabilities' => [ 'shape' => 'StringList', ], 'Vendor' => [ 'shape' => 'VulnerabilityVendor', ], 'ReferenceUrls' => [ 'shape' => 'StringList', ], ], ], 'VulnerabilityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Vulnerability', ], ], 'VulnerabilityVendor' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NonEmptyString', ], 'Url' => [ 'shape' => 'NonEmptyString', ], 'VendorSeverity' => [ 'shape' => 'NonEmptyString', ], 'VendorCreatedAt' => [ 'shape' => 'NonEmptyString', ], 'VendorUpdatedAt' => [ 'shape' => 'NonEmptyString', ], ], ], 'WafAction' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'WafExcludedRule' => [ 'type' => 'structure', 'members' => [ 'RuleId' => [ 'shape' => 'NonEmptyString', ], ], ], 'WafExcludedRuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WafExcludedRule', ], ], 'WafOverrideAction' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'NonEmptyString', ], ], ], 'Workflow' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'WorkflowStatus', ], ], ], 'WorkflowState' => [ 'type' => 'string', 'deprecated' => true, 'deprecatedMessage' => 'This filter is deprecated. Instead, use SeverityLabel or FindingProviderFieldsSeverityLabel.', 'enum' => [ 'NEW', 'ASSIGNED', 'IN_PROGRESS', 'DEFERRED', 'RESOLVED', ], ], 'WorkflowStatus' => [ 'type' => 'string', 'enum' => [ 'NEW', 'NOTIFIED', 'RESOLVED', 'SUPPRESSED', ], ], 'WorkflowUpdate' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'WorkflowStatus', ], ], ], ],];
