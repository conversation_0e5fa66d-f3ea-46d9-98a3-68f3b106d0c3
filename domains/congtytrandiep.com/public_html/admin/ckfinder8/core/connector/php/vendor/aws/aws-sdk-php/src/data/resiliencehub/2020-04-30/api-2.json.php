<?php
// This file was auto-generated from sdk-root/src/data/resiliencehub/2020-04-30/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-04-30', 'endpointPrefix' => 'resiliencehub', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'AWS Resilience Hub', 'serviceId' => 'resiliencehub', 'signatureVersion' => 'v4', 'signingName' => 'resiliencehub', 'uid' => 'resiliencehub-2020-04-30', ], 'operations' => [ 'AddDraftAppVersionResourceMappings' => [ 'name' => 'AddDraftAppVersionResourceMappings', 'http' => [ 'method' => 'POST', 'requestUri' => '/add-draft-app-version-resource-mappings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AddDraftAppVersionResourceMappingsRequest', ], 'output' => [ 'shape' => 'AddDraftAppVersionResourceMappingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateApp' => [ 'name' => 'CreateApp', 'http' => [ 'method' => 'POST', 'requestUri' => '/create-app', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateAppRequest', ], 'output' => [ 'shape' => 'CreateAppResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateRecommendationTemplate' => [ 'name' => 'CreateRecommendationTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/create-recommendation-template', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateRecommendationTemplateRequest', ], 'output' => [ 'shape' => 'CreateRecommendationTemplateResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateResiliencyPolicy' => [ 'name' => 'CreateResiliencyPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/create-resiliency-policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateResiliencyPolicyRequest', ], 'output' => [ 'shape' => 'CreateResiliencyPolicyResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteApp' => [ 'name' => 'DeleteApp', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-app', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteAppRequest', ], 'output' => [ 'shape' => 'DeleteAppResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteAppAssessment' => [ 'name' => 'DeleteAppAssessment', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-app-assessment', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteAppAssessmentRequest', ], 'output' => [ 'shape' => 'DeleteAppAssessmentResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteRecommendationTemplate' => [ 'name' => 'DeleteRecommendationTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-recommendation-template', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteRecommendationTemplateRequest', ], 'output' => [ 'shape' => 'DeleteRecommendationTemplateResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteResiliencyPolicy' => [ 'name' => 'DeleteResiliencyPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-resiliency-policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteResiliencyPolicyRequest', ], 'output' => [ 'shape' => 'DeleteResiliencyPolicyResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeApp' => [ 'name' => 'DescribeApp', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-app', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeAppRequest', ], 'output' => [ 'shape' => 'DescribeAppResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeAppAssessment' => [ 'name' => 'DescribeAppAssessment', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-app-assessment', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeAppAssessmentRequest', ], 'output' => [ 'shape' => 'DescribeAppAssessmentResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeAppVersionResourcesResolutionStatus' => [ 'name' => 'DescribeAppVersionResourcesResolutionStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-app-version-resources-resolution-status', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeAppVersionResourcesResolutionStatusRequest', ], 'output' => [ 'shape' => 'DescribeAppVersionResourcesResolutionStatusResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeAppVersionTemplate' => [ 'name' => 'DescribeAppVersionTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-app-version-template', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeAppVersionTemplateRequest', ], 'output' => [ 'shape' => 'DescribeAppVersionTemplateResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeDraftAppVersionResourcesImportStatus' => [ 'name' => 'DescribeDraftAppVersionResourcesImportStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-draft-app-version-resources-import-status', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeDraftAppVersionResourcesImportStatusRequest', ], 'output' => [ 'shape' => 'DescribeDraftAppVersionResourcesImportStatusResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeResiliencyPolicy' => [ 'name' => 'DescribeResiliencyPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-resiliency-policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeResiliencyPolicyRequest', ], 'output' => [ 'shape' => 'DescribeResiliencyPolicyResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ImportResourcesToDraftAppVersion' => [ 'name' => 'ImportResourcesToDraftAppVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/import-resources-to-draft-app-version', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ImportResourcesToDraftAppVersionRequest', ], 'output' => [ 'shape' => 'ImportResourcesToDraftAppVersionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAlarmRecommendations' => [ 'name' => 'ListAlarmRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-alarm-recommendations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAlarmRecommendationsRequest', ], 'output' => [ 'shape' => 'ListAlarmRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAppAssessments' => [ 'name' => 'ListAppAssessments', 'http' => [ 'method' => 'GET', 'requestUri' => '/list-app-assessments', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAppAssessmentsRequest', ], 'output' => [ 'shape' => 'ListAppAssessmentsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAppComponentCompliances' => [ 'name' => 'ListAppComponentCompliances', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-app-component-compliances', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAppComponentCompliancesRequest', ], 'output' => [ 'shape' => 'ListAppComponentCompliancesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAppComponentRecommendations' => [ 'name' => 'ListAppComponentRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-app-component-recommendations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAppComponentRecommendationsRequest', ], 'output' => [ 'shape' => 'ListAppComponentRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAppVersionResourceMappings' => [ 'name' => 'ListAppVersionResourceMappings', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-app-version-resource-mappings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAppVersionResourceMappingsRequest', ], 'output' => [ 'shape' => 'ListAppVersionResourceMappingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAppVersionResources' => [ 'name' => 'ListAppVersionResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-app-version-resources', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAppVersionResourcesRequest', ], 'output' => [ 'shape' => 'ListAppVersionResourcesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAppVersions' => [ 'name' => 'ListAppVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-app-versions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAppVersionsRequest', ], 'output' => [ 'shape' => 'ListAppVersionsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListApps' => [ 'name' => 'ListApps', 'http' => [ 'method' => 'GET', 'requestUri' => '/list-apps', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAppsRequest', ], 'output' => [ 'shape' => 'ListAppsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListRecommendationTemplates' => [ 'name' => 'ListRecommendationTemplates', 'http' => [ 'method' => 'GET', 'requestUri' => '/list-recommendation-templates', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRecommendationTemplatesRequest', ], 'output' => [ 'shape' => 'ListRecommendationTemplatesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListResiliencyPolicies' => [ 'name' => 'ListResiliencyPolicies', 'http' => [ 'method' => 'GET', 'requestUri' => '/list-resiliency-policies', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListResiliencyPoliciesRequest', ], 'output' => [ 'shape' => 'ListResiliencyPoliciesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListSopRecommendations' => [ 'name' => 'ListSopRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-sop-recommendations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSopRecommendationsRequest', ], 'output' => [ 'shape' => 'ListSopRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListSuggestedResiliencyPolicies' => [ 'name' => 'ListSuggestedResiliencyPolicies', 'http' => [ 'method' => 'GET', 'requestUri' => '/list-suggested-resiliency-policies', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSuggestedResiliencyPoliciesRequest', ], 'output' => [ 'shape' => 'ListSuggestedResiliencyPoliciesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTestRecommendations' => [ 'name' => 'ListTestRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-test-recommendations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTestRecommendationsRequest', ], 'output' => [ 'shape' => 'ListTestRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListUnsupportedAppVersionResources' => [ 'name' => 'ListUnsupportedAppVersionResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-unsupported-app-version-resources', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListUnsupportedAppVersionResourcesRequest', ], 'output' => [ 'shape' => 'ListUnsupportedAppVersionResourcesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'PublishAppVersion' => [ 'name' => 'PublishAppVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/publish-app-version', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PublishAppVersionRequest', ], 'output' => [ 'shape' => 'PublishAppVersionResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'PutDraftAppVersionTemplate' => [ 'name' => 'PutDraftAppVersionTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/put-draft-app-version-template', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutDraftAppVersionTemplateRequest', ], 'output' => [ 'shape' => 'PutDraftAppVersionTemplateResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'RemoveDraftAppVersionResourceMappings' => [ 'name' => 'RemoveDraftAppVersionResourceMappings', 'http' => [ 'method' => 'POST', 'requestUri' => '/remove-draft-app-version-resource-mappings', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RemoveDraftAppVersionResourceMappingsRequest', ], 'output' => [ 'shape' => 'RemoveDraftAppVersionResourceMappingsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ResolveAppVersionResources' => [ 'name' => 'ResolveAppVersionResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/resolve-app-version-resources', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ResolveAppVersionResourcesRequest', ], 'output' => [ 'shape' => 'ResolveAppVersionResourcesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'StartAppAssessment' => [ 'name' => 'StartAppAssessment', 'http' => [ 'method' => 'POST', 'requestUri' => '/start-app-assessment', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartAppAssessmentRequest', ], 'output' => [ 'shape' => 'StartAppAssessmentResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateApp' => [ 'name' => 'UpdateApp', 'http' => [ 'method' => 'POST', 'requestUri' => '/update-app', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAppRequest', ], 'output' => [ 'shape' => 'UpdateAppResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateResiliencyPolicy' => [ 'name' => 'UpdateResiliencyPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/update-resiliency-policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateResiliencyPolicyRequest', ], 'output' => [ 'shape' => 'UpdateResiliencyPolicyResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String500', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AddDraftAppVersionResourceMappingsRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'resourceMappings', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'resourceMappings' => [ 'shape' => 'ResourceMappingList', ], ], ], 'AddDraftAppVersionResourceMappingsResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', 'resourceMappings', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'resourceMappings' => [ 'shape' => 'ResourceMappingList', ], ], ], 'AlarmRecommendation' => [ 'type' => 'structure', 'required' => [ 'name', 'recommendationId', 'referenceId', 'type', ], 'members' => [ 'appComponentName' => [ 'shape' => 'EntityId', ], 'description' => [ 'shape' => 'EntityDescription', ], 'items' => [ 'shape' => 'RecommendationItemList', ], 'name' => [ 'shape' => 'String500', ], 'prerequisite' => [ 'shape' => 'String500', ], 'recommendationId' => [ 'shape' => 'Uuid', ], 'referenceId' => [ 'shape' => 'SpecReferenceId', ], 'type' => [ 'shape' => 'AlarmType', ], ], ], 'AlarmRecommendationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AlarmRecommendation', ], ], 'AlarmType' => [ 'type' => 'string', 'enum' => [ 'Metric', 'Composite', 'Canary', 'Logs', 'Event', ], ], 'App' => [ 'type' => 'structure', 'required' => [ 'appArn', 'creationTime', 'name', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'complianceStatus' => [ 'shape' => 'AppComplianceStatusType', ], 'creationTime' => [ 'shape' => 'TimeStamp', ], 'description' => [ 'shape' => 'EntityDescription', ], 'lastAppComplianceEvaluationTime' => [ 'shape' => 'TimeStamp', ], 'lastResiliencyScoreEvaluationTime' => [ 'shape' => 'TimeStamp', ], 'name' => [ 'shape' => 'EntityName', ], 'policyArn' => [ 'shape' => 'Arn', ], 'resiliencyScore' => [ 'shape' => 'Double', ], 'status' => [ 'shape' => 'AppStatusType', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'AppAssessment' => [ 'type' => 'structure', 'required' => [ 'assessmentArn', 'assessmentStatus', 'invoker', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'assessmentArn' => [ 'shape' => 'Arn', ], 'assessmentName' => [ 'shape' => 'EntityName', ], 'assessmentStatus' => [ 'shape' => 'AssessmentStatus', ], 'compliance' => [ 'shape' => 'AssessmentCompliance', ], 'complianceStatus' => [ 'shape' => 'ComplianceStatus', ], 'cost' => [ 'shape' => 'Cost', ], 'endTime' => [ 'shape' => 'TimeStamp', ], 'invoker' => [ 'shape' => 'AssessmentInvoker', ], 'message' => [ 'shape' => 'String500', ], 'policy' => [ 'shape' => 'ResiliencyPolicy', ], 'resiliencyScore' => [ 'shape' => 'ResiliencyScore', ], 'startTime' => [ 'shape' => 'TimeStamp', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'AppAssessmentSummary' => [ 'type' => 'structure', 'required' => [ 'assessmentArn', 'assessmentStatus', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'assessmentArn' => [ 'shape' => 'Arn', ], 'assessmentName' => [ 'shape' => 'EntityName', ], 'assessmentStatus' => [ 'shape' => 'AssessmentStatus', ], 'complianceStatus' => [ 'shape' => 'ComplianceStatus', ], 'cost' => [ 'shape' => 'Cost', ], 'endTime' => [ 'shape' => 'TimeStamp', ], 'invoker' => [ 'shape' => 'AssessmentInvoker', ], 'message' => [ 'shape' => 'String500', ], 'resiliencyScore' => [ 'shape' => 'Double', ], 'startTime' => [ 'shape' => 'TimeStamp', ], ], ], 'AppAssessmentSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AppAssessmentSummary', ], ], 'AppComplianceStatusType' => [ 'type' => 'string', 'enum' => [ 'PolicyBreached', 'PolicyMet', 'NotAssessed', 'ChangesDetected', ], ], 'AppComponent' => [ 'type' => 'structure', 'required' => [ 'name', 'type', ], 'members' => [ 'name' => [ 'shape' => 'String255', ], 'type' => [ 'shape' => 'String255', ], ], ], 'AppComponentCompliance' => [ 'type' => 'structure', 'members' => [ 'appComponentName' => [ 'shape' => 'EntityId', ], 'compliance' => [ 'shape' => 'AssessmentCompliance', ], 'cost' => [ 'shape' => 'Cost', ], 'message' => [ 'shape' => 'String500', ], 'resiliencyScore' => [ 'shape' => 'ResiliencyScore', ], 'status' => [ 'shape' => 'ComplianceStatus', ], ], ], 'AppComponentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AppComponent', ], ], 'AppStatusType' => [ 'type' => 'string', 'enum' => [ 'Active', 'Deleting', ], ], 'AppSummary' => [ 'type' => 'structure', 'required' => [ 'appArn', 'creationTime', 'name', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'complianceStatus' => [ 'shape' => 'AppComplianceStatusType', ], 'creationTime' => [ 'shape' => 'TimeStamp', ], 'description' => [ 'shape' => 'EntityDescription', ], 'name' => [ 'shape' => 'EntityName', ], 'resiliencyScore' => [ 'shape' => 'Double', ], ], ], 'AppSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AppSummary', ], ], 'AppTemplateBody' => [ 'type' => 'string', 'max' => 5000, 'min' => 0, 'pattern' => '^[\\w\\s:,-\\.\'{}\\[\\]:"]+$', ], 'AppVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AppVersionSummary', ], ], 'AppVersionSummary' => [ 'type' => 'structure', 'required' => [ 'appVersion', ], 'members' => [ 'appVersion' => [ 'shape' => 'EntityVersion', ], ], ], 'Arn' => [ 'type' => 'string', 'pattern' => '^arn:(aws|aws-cn|aws-iso|aws-iso-[a-z]{1}|aws-us-gov):[A-Za-z0-9][A-Za-z0-9_/.-]{0,62}:([a-z]{2}-((iso[a-z]{0,1}-)|(gov-)){0,1}[a-z]+-[0-9]):[0-9]{12}:[A-Za-z0-9/][A-Za-z0-9:_/+=,@.-]{0,1023}$', ], 'ArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], ], 'AssessmentCompliance' => [ 'type' => 'map', 'key' => [ 'shape' => 'DisruptionType', ], 'value' => [ 'shape' => 'DisruptionCompliance', ], ], 'AssessmentInvoker' => [ 'type' => 'string', 'enum' => [ 'User', 'System', ], ], 'AssessmentStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'InProgress', 'Failed', 'Success', ], ], 'AssessmentStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssessmentStatus', ], 'max' => 10, 'min' => 1, ], 'AwsRegion' => [ 'type' => 'string', 'pattern' => '^[a-z]{2}-((iso[a-z]{0,1}-)|(gov-)){0,1}[a-z]+-[0-9]$', ], 'BooleanOptional' => [ 'type' => 'boolean', 'box' => true, ], 'ClientToken' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[A-za-z0-9_.-]{0,63}$', ], 'ComplianceStatus' => [ 'type' => 'string', 'enum' => [ 'PolicyBreached', 'PolicyMet', ], ], 'ComponentCompliancesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AppComponentCompliance', ], ], 'ComponentRecommendation' => [ 'type' => 'structure', 'required' => [ 'appComponentName', 'configRecommendations', 'recommendationStatus', ], 'members' => [ 'appComponentName' => [ 'shape' => 'EntityId', ], 'configRecommendations' => [ 'shape' => 'ConfigRecommendationList', ], 'recommendationStatus' => [ 'shape' => 'RecommendationComplianceStatus', ], ], ], 'ComponentRecommendationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComponentRecommendation', ], ], 'ConfigRecommendation' => [ 'type' => 'structure', 'required' => [ 'name', 'optimizationType', 'referenceId', ], 'members' => [ 'appComponentName' => [ 'shape' => 'EntityId', ], 'compliance' => [ 'shape' => 'AssessmentCompliance', ], 'cost' => [ 'shape' => 'Cost', ], 'description' => [ 'shape' => 'EntityDescription', ], 'haArchitecture' => [ 'shape' => 'HaArchitecture', ], 'name' => [ 'shape' => 'EntityName', ], 'optimizationType' => [ 'shape' => 'ConfigRecommendationOptimizationType', ], 'recommendationCompliance' => [ 'shape' => 'RecommendationCompliance', ], 'referenceId' => [ 'shape' => 'SpecReferenceId', ], 'suggestedChanges' => [ 'shape' => 'SuggestedChangesList', ], ], ], 'ConfigRecommendationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfigRecommendation', ], ], 'ConfigRecommendationOptimizationType' => [ 'type' => 'string', 'enum' => [ 'LeastCost', 'LeastChange', 'BestAZRecovery', 'LeastErrors', 'BestAttainable', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String500', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceType' => [ 'shape' => 'ResourceType', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'Cost' => [ 'type' => 'structure', 'required' => [ 'amount', 'currency', 'frequency', ], 'members' => [ 'amount' => [ 'shape' => 'Double', ], 'currency' => [ 'shape' => 'CurrencyCode', ], 'frequency' => [ 'shape' => 'CostFrequency', ], ], ], 'CostFrequency' => [ 'type' => 'string', 'enum' => [ 'Hourly', 'Daily', 'Monthly', 'Yearly', ], ], 'CreateAppRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'description' => [ 'shape' => 'EntityDescription', ], 'name' => [ 'shape' => 'EntityName', ], 'policyArn' => [ 'shape' => 'Arn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateAppResponse' => [ 'type' => 'structure', 'required' => [ 'app', ], 'members' => [ 'app' => [ 'shape' => 'App', ], ], ], 'CreateRecommendationTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentArn', 'name', ], 'members' => [ 'assessmentArn' => [ 'shape' => 'Arn', ], 'bucketName' => [ 'shape' => 'EntityName', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'format' => [ 'shape' => 'TemplateFormat', ], 'name' => [ 'shape' => 'EntityName', ], 'recommendationIds' => [ 'shape' => 'RecommendationIdList', ], 'recommendationTypes' => [ 'shape' => 'RenderRecommendationTypeList', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateRecommendationTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'recommendationTemplate' => [ 'shape' => 'RecommendationTemplate', ], ], ], 'CreateResiliencyPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'policy', 'policyName', 'tier', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'dataLocationConstraint' => [ 'shape' => 'DataLocationConstraint', ], 'policy' => [ 'shape' => 'DisruptionPolicy', ], 'policyDescription' => [ 'shape' => 'EntityDescription', ], 'policyName' => [ 'shape' => 'EntityName', ], 'tags' => [ 'shape' => 'TagMap', ], 'tier' => [ 'shape' => 'ResiliencyPolicyTier', ], ], ], 'CreateResiliencyPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'policy', ], 'members' => [ 'policy' => [ 'shape' => 'ResiliencyPolicy', ], ], ], 'CurrencyCode' => [ 'type' => 'string', 'max' => 3, 'min' => 0, ], 'CustomerId' => [ 'type' => 'string', 'pattern' => '^[0-9]{12}$', ], 'DataLocationConstraint' => [ 'type' => 'string', 'enum' => [ 'AnyLocation', 'SameContinent', 'SameCountry', ], ], 'DeleteAppAssessmentRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentArn', ], 'members' => [ 'assessmentArn' => [ 'shape' => 'Arn', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'DeleteAppAssessmentResponse' => [ 'type' => 'structure', 'required' => [ 'assessmentArn', 'assessmentStatus', ], 'members' => [ 'assessmentArn' => [ 'shape' => 'Arn', ], 'assessmentStatus' => [ 'shape' => 'AssessmentStatus', ], ], ], 'DeleteAppRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'forceDelete' => [ 'shape' => 'BooleanOptional', ], ], ], 'DeleteAppResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteRecommendationTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'recommendationTemplateArn', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'recommendationTemplateArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteRecommendationTemplateResponse' => [ 'type' => 'structure', 'required' => [ 'recommendationTemplateArn', 'status', ], 'members' => [ 'recommendationTemplateArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'RecommendationTemplateStatus', ], ], ], 'DeleteResiliencyPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'policyArn', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'policyArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteResiliencyPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'policyArn', ], 'members' => [ 'policyArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeAppAssessmentRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentArn', ], 'members' => [ 'assessmentArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeAppAssessmentResponse' => [ 'type' => 'structure', 'required' => [ 'assessment', ], 'members' => [ 'assessment' => [ 'shape' => 'AppAssessment', ], ], ], 'DescribeAppRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeAppResponse' => [ 'type' => 'structure', 'required' => [ 'app', ], 'members' => [ 'app' => [ 'shape' => 'App', ], ], ], 'DescribeAppVersionResourcesResolutionStatusRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'resolutionId' => [ 'shape' => 'String255', ], ], ], 'DescribeAppVersionResourcesResolutionStatusResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', 'resolutionId', 'status', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'errorMessage' => [ 'shape' => 'String500', ], 'resolutionId' => [ 'shape' => 'String255', ], 'status' => [ 'shape' => 'ResourceResolutionStatusType', ], ], ], 'DescribeAppVersionTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], ], ], 'DescribeAppVersionTemplateResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appTemplateBody', 'appVersion', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appTemplateBody' => [ 'shape' => 'AppTemplateBody', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], ], ], 'DescribeDraftAppVersionResourcesImportStatusRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeDraftAppVersionResourcesImportStatusResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', 'status', 'statusChangeTime', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'errorMessage' => [ 'shape' => 'String500', ], 'status' => [ 'shape' => 'ResourceImportStatusType', ], 'statusChangeTime' => [ 'shape' => 'TimeStamp', ], ], ], 'DescribeResiliencyPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'policyArn', ], 'members' => [ 'policyArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeResiliencyPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'policy', ], 'members' => [ 'policy' => [ 'shape' => 'ResiliencyPolicy', ], ], ], 'DisruptionCompliance' => [ 'type' => 'structure', 'required' => [ 'complianceStatus', ], 'members' => [ 'achievableRpoInSecs' => [ 'shape' => 'Seconds', ], 'achievableRtoInSecs' => [ 'shape' => 'Seconds', ], 'complianceStatus' => [ 'shape' => 'ComplianceStatus', ], 'currentRpoInSecs' => [ 'shape' => 'Seconds', ], 'currentRtoInSecs' => [ 'shape' => 'Seconds', ], 'message' => [ 'shape' => 'String500', ], 'rpoDescription' => [ 'shape' => 'String500', ], 'rpoReferenceId' => [ 'shape' => 'String500', ], 'rtoDescription' => [ 'shape' => 'String500', ], 'rtoReferenceId' => [ 'shape' => 'String500', ], ], ], 'DisruptionPolicy' => [ 'type' => 'map', 'key' => [ 'shape' => 'DisruptionType', ], 'value' => [ 'shape' => 'FailurePolicy', ], ], 'DisruptionResiliencyScore' => [ 'type' => 'map', 'key' => [ 'shape' => 'DisruptionType', ], 'value' => [ 'shape' => 'Double', ], ], 'DisruptionType' => [ 'type' => 'string', 'enum' => [ 'Software', 'Hardware', 'AZ', 'Region', ], ], 'DocumentName' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'Double' => [ 'type' => 'double', ], 'EntityDescription' => [ 'type' => 'string', 'max' => 500, 'min' => 0, ], 'EntityId' => [ 'type' => 'string', 'pattern' => '^\\S{1,100}$', ], 'EntityName' => [ 'type' => 'string', 'pattern' => '^[A-Za-z0-9][A-Za-z0-9_\\-]{1,59}$', ], 'EntityNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EntityName', ], ], 'EntityVersion' => [ 'type' => 'string', 'pattern' => '^\\S{1,50}$', ], 'EstimatedCostTier' => [ 'type' => 'string', 'enum' => [ 'L1', 'L2', 'L3', 'L4', ], ], 'FailurePolicy' => [ 'type' => 'structure', 'required' => [ 'rpoInSecs', 'rtoInSecs', ], 'members' => [ 'rpoInSecs' => [ 'shape' => 'Seconds', ], 'rtoInSecs' => [ 'shape' => 'Seconds', ], ], ], 'HaArchitecture' => [ 'type' => 'string', 'enum' => [ 'MultiSite', 'WarmStandby', 'PilotLight', 'BackupAndRestore', 'NoRecoveryPlan', ], ], 'ImportResourcesToDraftAppVersionRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'sourceArns', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'sourceArns' => [ 'shape' => 'ArnList', ], ], ], 'ImportResourcesToDraftAppVersionResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', 'sourceArns', 'status', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'sourceArns' => [ 'shape' => 'ArnList', ], 'status' => [ 'shape' => 'ResourceImportStatusType', ], ], ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String500', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'ListAlarmRecommendationsRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentArn', ], 'members' => [ 'assessmentArn' => [ 'shape' => 'Arn', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAlarmRecommendationsResponse' => [ 'type' => 'structure', 'required' => [ 'alarmRecommendations', ], 'members' => [ 'alarmRecommendations' => [ 'shape' => 'AlarmRecommendationList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAppAssessmentsRequest' => [ 'type' => 'structure', 'members' => [ 'appArn' => [ 'shape' => 'Arn', 'location' => 'querystring', 'locationName' => 'appArn', ], 'assessmentName' => [ 'shape' => 'EntityName', 'location' => 'querystring', 'locationName' => 'assessmentName', ], 'assessmentStatus' => [ 'shape' => 'AssessmentStatusList', 'location' => 'querystring', 'locationName' => 'assessmentStatus', ], 'complianceStatus' => [ 'shape' => 'ComplianceStatus', 'location' => 'querystring', 'locationName' => 'complianceStatus', ], 'invoker' => [ 'shape' => 'AssessmentInvoker', 'location' => 'querystring', 'locationName' => 'invoker', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'reverseOrder' => [ 'shape' => 'BooleanOptional', 'location' => 'querystring', 'locationName' => 'reverseOrder', ], ], ], 'ListAppAssessmentsResponse' => [ 'type' => 'structure', 'required' => [ 'assessmentSummaries', ], 'members' => [ 'assessmentSummaries' => [ 'shape' => 'AppAssessmentSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAppComponentCompliancesRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentArn', ], 'members' => [ 'assessmentArn' => [ 'shape' => 'Arn', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAppComponentCompliancesResponse' => [ 'type' => 'structure', 'required' => [ 'componentCompliances', ], 'members' => [ 'componentCompliances' => [ 'shape' => 'ComponentCompliancesList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAppComponentRecommendationsRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentArn', ], 'members' => [ 'assessmentArn' => [ 'shape' => 'Arn', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAppComponentRecommendationsResponse' => [ 'type' => 'structure', 'required' => [ 'componentRecommendations', ], 'members' => [ 'componentRecommendations' => [ 'shape' => 'ComponentRecommendationList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAppVersionResourceMappingsRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAppVersionResourceMappingsResponse' => [ 'type' => 'structure', 'required' => [ 'resourceMappings', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'resourceMappings' => [ 'shape' => 'ResourceMappingList', ], ], ], 'ListAppVersionResourcesRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'resolutionId' => [ 'shape' => 'String255', ], ], ], 'ListAppVersionResourcesResponse' => [ 'type' => 'structure', 'required' => [ 'physicalResources', 'resolutionId', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'physicalResources' => [ 'shape' => 'PhysicalResourceList', ], 'resolutionId' => [ 'shape' => 'String255', ], ], ], 'ListAppVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAppVersionsResponse' => [ 'type' => 'structure', 'required' => [ 'appVersions', ], 'members' => [ 'appVersions' => [ 'shape' => 'AppVersionList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListAppsRequest' => [ 'type' => 'structure', 'members' => [ 'appArn' => [ 'shape' => 'Arn', 'location' => 'querystring', 'locationName' => 'appArn', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'name' => [ 'shape' => 'EntityName', 'location' => 'querystring', 'locationName' => 'name', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListAppsResponse' => [ 'type' => 'structure', 'required' => [ 'appSummaries', ], 'members' => [ 'appSummaries' => [ 'shape' => 'AppSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListRecommendationTemplatesRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentArn', ], 'members' => [ 'assessmentArn' => [ 'shape' => 'Arn', 'location' => 'querystring', 'locationName' => 'assessmentArn', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'name' => [ 'shape' => 'EntityName', 'location' => 'querystring', 'locationName' => 'name', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'recommendationTemplateArn' => [ 'shape' => 'Arn', 'location' => 'querystring', 'locationName' => 'recommendationTemplateArn', ], 'reverseOrder' => [ 'shape' => 'BooleanOptional', 'location' => 'querystring', 'locationName' => 'reverseOrder', ], 'status' => [ 'shape' => 'RecommendationTemplateStatusList', 'location' => 'querystring', 'locationName' => 'status', ], ], ], 'ListRecommendationTemplatesResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'recommendationTemplates' => [ 'shape' => 'RecommendationTemplateList', ], ], ], 'ListResiliencyPoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'policyName' => [ 'shape' => 'EntityName', 'location' => 'querystring', 'locationName' => 'policyName', ], ], ], 'ListResiliencyPoliciesResponse' => [ 'type' => 'structure', 'required' => [ 'resiliencyPolicies', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'resiliencyPolicies' => [ 'shape' => 'ResiliencyPolicies', ], ], ], 'ListSopRecommendationsRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentArn', ], 'members' => [ 'assessmentArn' => [ 'shape' => 'Arn', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSopRecommendationsResponse' => [ 'type' => 'structure', 'required' => [ 'sopRecommendations', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'sopRecommendations' => [ 'shape' => 'SopRecommendationList', ], ], ], 'ListSuggestedResiliencyPoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListSuggestedResiliencyPoliciesResponse' => [ 'type' => 'structure', 'required' => [ 'resiliencyPolicies', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'resiliencyPolicies' => [ 'shape' => 'ResiliencyPolicies', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ListTestRecommendationsRequest' => [ 'type' => 'structure', 'required' => [ 'assessmentArn', ], 'members' => [ 'assessmentArn' => [ 'shape' => 'Arn', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTestRecommendationsResponse' => [ 'type' => 'structure', 'required' => [ 'testRecommendations', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'testRecommendations' => [ 'shape' => 'TestRecommendationList', ], ], ], 'ListUnsupportedAppVersionResourcesRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'resolutionId' => [ 'shape' => 'String255', ], ], ], 'ListUnsupportedAppVersionResourcesResponse' => [ 'type' => 'structure', 'required' => [ 'resolutionId', 'unsupportedResources', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'resolutionId' => [ 'shape' => 'String255', ], 'unsupportedResources' => [ 'shape' => 'UnsupportedResourceList', ], ], ], 'LogicalResourceId' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'String255', ], 'logicalStackName' => [ 'shape' => 'String255', ], 'resourceGroupName' => [ 'shape' => 'EntityName', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'NextToken' => [ 'type' => 'string', 'pattern' => '^\\S{1,2000}$', ], 'PhysicalIdentifierType' => [ 'type' => 'string', 'enum' => [ 'Arn', 'Native', ], ], 'PhysicalResource' => [ 'type' => 'structure', 'required' => [ 'logicalResourceId', 'physicalResourceId', 'resourceType', ], 'members' => [ 'appComponents' => [ 'shape' => 'AppComponentList', ], 'logicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'physicalResourceId' => [ 'shape' => 'PhysicalResourceId', ], 'resourceName' => [ 'shape' => 'EntityName', ], 'resourceType' => [ 'shape' => 'String255', ], ], ], 'PhysicalResourceId' => [ 'type' => 'structure', 'required' => [ 'identifier', 'type', ], 'members' => [ 'awsAccountId' => [ 'shape' => 'CustomerId', ], 'awsRegion' => [ 'shape' => 'AwsRegion', ], 'identifier' => [ 'shape' => 'String255', ], 'type' => [ 'shape' => 'PhysicalIdentifierType', ], ], ], 'PhysicalResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhysicalResource', ], ], 'PublishAppVersionRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], ], ], 'PublishAppVersionResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], ], ], 'PutDraftAppVersionTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appTemplateBody', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appTemplateBody' => [ 'shape' => 'AppTemplateBody', ], ], ], 'PutDraftAppVersionTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], ], ], 'RecommendationCompliance' => [ 'type' => 'map', 'key' => [ 'shape' => 'DisruptionType', ], 'value' => [ 'shape' => 'RecommendationDisruptionCompliance', ], ], 'RecommendationComplianceStatus' => [ 'type' => 'string', 'enum' => [ 'BreachedUnattainable', 'BreachedCanMeet', 'MetCanImprove', ], ], 'RecommendationDisruptionCompliance' => [ 'type' => 'structure', 'required' => [ 'expectedComplianceStatus', ], 'members' => [ 'expectedComplianceStatus' => [ 'shape' => 'ComplianceStatus', ], 'expectedRpoDescription' => [ 'shape' => 'String500', ], 'expectedRpoInSecs' => [ 'shape' => 'Seconds', ], 'expectedRtoDescription' => [ 'shape' => 'String500', ], 'expectedRtoInSecs' => [ 'shape' => 'Seconds', ], ], ], 'RecommendationIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Uuid', ], 'max' => 200, 'min' => 1, ], 'RecommendationItem' => [ 'type' => 'structure', 'members' => [ 'alreadyImplemented' => [ 'shape' => 'BooleanOptional', ], 'resourceId' => [ 'shape' => 'String500', ], 'targetAccountId' => [ 'shape' => 'CustomerId', ], 'targetRegion' => [ 'shape' => 'AwsRegion', ], ], ], 'RecommendationItemList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendationItem', ], ], 'RecommendationTemplate' => [ 'type' => 'structure', 'required' => [ 'assessmentArn', 'format', 'name', 'recommendationTemplateArn', 'recommendationTypes', 'status', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'assessmentArn' => [ 'shape' => 'Arn', ], 'endTime' => [ 'shape' => 'TimeStamp', ], 'format' => [ 'shape' => 'TemplateFormat', ], 'message' => [ 'shape' => 'String500', ], 'name' => [ 'shape' => 'EntityName', ], 'needsReplacements' => [ 'shape' => 'BooleanOptional', ], 'recommendationIds' => [ 'shape' => 'RecommendationIdList', ], 'recommendationTemplateArn' => [ 'shape' => 'Arn', ], 'recommendationTypes' => [ 'shape' => 'RenderRecommendationTypeList', ], 'startTime' => [ 'shape' => 'TimeStamp', ], 'status' => [ 'shape' => 'RecommendationTemplateStatus', ], 'tags' => [ 'shape' => 'TagMap', ], 'templatesLocation' => [ 'shape' => 'S3Location', ], ], ], 'RecommendationTemplateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendationTemplate', ], ], 'RecommendationTemplateStatus' => [ 'type' => 'string', 'enum' => [ 'Pending', 'InProgress', 'Failed', 'Success', ], ], 'RecommendationTemplateStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendationTemplateStatus', ], 'max' => 4, 'min' => 1, ], 'RemoveDraftAppVersionResourceMappingsRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appRegistryAppNames' => [ 'shape' => 'EntityNameList', ], 'logicalStackNames' => [ 'shape' => 'String255List', ], 'resourceGroupNames' => [ 'shape' => 'EntityNameList', ], 'resourceNames' => [ 'shape' => 'EntityNameList', ], ], ], 'RemoveDraftAppVersionResourceMappingsResponse' => [ 'type' => 'structure', 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], ], ], 'RenderRecommendationType' => [ 'type' => 'string', 'enum' => [ 'Alarm', 'Sop', 'Test', ], ], 'RenderRecommendationTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RenderRecommendationType', ], 'max' => 4, 'min' => 1, ], 'ResiliencyPolicies' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResiliencyPolicy', ], ], 'ResiliencyPolicy' => [ 'type' => 'structure', 'members' => [ 'creationTime' => [ 'shape' => 'TimeStamp', ], 'dataLocationConstraint' => [ 'shape' => 'DataLocationConstraint', ], 'estimatedCostTier' => [ 'shape' => 'EstimatedCostTier', ], 'policy' => [ 'shape' => 'DisruptionPolicy', ], 'policyArn' => [ 'shape' => 'Arn', ], 'policyDescription' => [ 'shape' => 'EntityDescription', ], 'policyName' => [ 'shape' => 'EntityName', ], 'tags' => [ 'shape' => 'TagMap', ], 'tier' => [ 'shape' => 'ResiliencyPolicyTier', ], ], ], 'ResiliencyPolicyTier' => [ 'type' => 'string', 'enum' => [ 'MissionCritical', 'Critical', 'Important', 'CoreServices', 'NonCritical', ], ], 'ResiliencyScore' => [ 'type' => 'structure', 'required' => [ 'disruptionScore', 'score', ], 'members' => [ 'disruptionScore' => [ 'shape' => 'DisruptionResiliencyScore', ], 'score' => [ 'shape' => 'Double', ], ], ], 'ResolveAppVersionResourcesRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], ], ], 'ResolveAppVersionResourcesResponse' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', 'resolutionId', 'status', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'resolutionId' => [ 'shape' => 'String255', ], 'status' => [ 'shape' => 'ResourceResolutionStatusType', ], ], ], 'ResourceId' => [ 'type' => 'string', 'pattern' => '.*', ], 'ResourceImportStatusType' => [ 'type' => 'string', 'enum' => [ 'Pending', 'InProgress', 'Failed', 'Success', ], ], 'ResourceMapping' => [ 'type' => 'structure', 'required' => [ 'mappingType', 'physicalResourceId', ], 'members' => [ 'appRegistryAppName' => [ 'shape' => 'EntityName', ], 'logicalStackName' => [ 'shape' => 'String255', ], 'mappingType' => [ 'shape' => 'ResourceMappingType', ], 'physicalResourceId' => [ 'shape' => 'PhysicalResourceId', ], 'resourceGroupName' => [ 'shape' => 'EntityName', ], 'resourceName' => [ 'shape' => 'EntityName', ], ], ], 'ResourceMappingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceMapping', ], ], 'ResourceMappingType' => [ 'type' => 'string', 'enum' => [ 'CfnStack', 'Resource', 'AppRegistryApp', 'ResourceGroup', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String500', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceType' => [ 'shape' => 'ResourceType', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResourceResolutionStatusType' => [ 'type' => 'string', 'enum' => [ 'Pending', 'InProgress', 'Failed', 'Success', ], ], 'ResourceType' => [ 'type' => 'string', 'pattern' => '.*', ], 'RetryAfterSeconds' => [ 'type' => 'integer', 'box' => true, ], 'S3Location' => [ 'type' => 'structure', 'members' => [ 'bucket' => [ 'shape' => 'String500', ], 'prefix' => [ 'shape' => 'String500', ], ], ], 'Seconds' => [ 'type' => 'integer', 'min' => 0, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String500', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SopRecommendation' => [ 'type' => 'structure', 'required' => [ 'recommendationId', 'referenceId', 'serviceType', ], 'members' => [ 'appComponentName' => [ 'shape' => 'EntityId', ], 'description' => [ 'shape' => 'String500', ], 'items' => [ 'shape' => 'RecommendationItemList', ], 'name' => [ 'shape' => 'DocumentName', ], 'prerequisite' => [ 'shape' => 'String500', ], 'recommendationId' => [ 'shape' => 'Uuid', ], 'referenceId' => [ 'shape' => 'SpecReferenceId', ], 'serviceType' => [ 'shape' => 'SopServiceType', ], ], ], 'SopRecommendationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SopRecommendation', ], ], 'SopServiceType' => [ 'type' => 'string', 'enum' => [ 'SSM', ], ], 'SpecReferenceId' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'StartAppAssessmentRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', 'appVersion', 'assessmentName', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'appVersion' => [ 'shape' => 'EntityVersion', ], 'assessmentName' => [ 'shape' => 'EntityName', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'StartAppAssessmentResponse' => [ 'type' => 'structure', 'required' => [ 'assessment', ], 'members' => [ 'assessment' => [ 'shape' => 'AppAssessment', ], ], ], 'String255' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'String255List' => [ 'type' => 'list', 'member' => [ 'shape' => 'String255', ], ], 'String500' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'SuggestedChangesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EntityDescription', ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!aws:).+$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, 'sensitive' => true, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 1, 'sensitive' => true, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TemplateFormat' => [ 'type' => 'string', 'enum' => [ 'CfnYaml', 'CfnJson', ], ], 'TestRecommendation' => [ 'type' => 'structure', 'required' => [ 'referenceId', ], 'members' => [ 'appComponentName' => [ 'shape' => 'EntityId', ], 'description' => [ 'shape' => 'String500', ], 'intent' => [ 'shape' => 'EntityDescription', ], 'items' => [ 'shape' => 'RecommendationItemList', ], 'name' => [ 'shape' => 'DocumentName', ], 'prerequisite' => [ 'shape' => 'String500', ], 'recommendationId' => [ 'shape' => 'Uuid', ], 'referenceId' => [ 'shape' => 'SpecReferenceId', ], 'risk' => [ 'shape' => 'TestRisk', ], 'type' => [ 'shape' => 'TestType', ], ], ], 'TestRecommendationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TestRecommendation', ], ], 'TestRisk' => [ 'type' => 'string', 'enum' => [ 'Small', 'Medium', 'High', ], ], 'TestType' => [ 'type' => 'string', 'enum' => [ 'Software', 'Hardware', 'AZ', 'Region', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String500', ], 'retryAfterSeconds' => [ 'shape' => 'RetryAfterSeconds', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'TimeStamp' => [ 'type' => 'timestamp', ], 'UnsupportedResource' => [ 'type' => 'structure', 'required' => [ 'logicalResourceId', 'physicalResourceId', 'resourceType', ], 'members' => [ 'logicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'physicalResourceId' => [ 'shape' => 'PhysicalResourceId', ], 'resourceType' => [ 'shape' => 'String255', ], ], ], 'UnsupportedResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UnsupportedResource', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAppRequest' => [ 'type' => 'structure', 'required' => [ 'appArn', ], 'members' => [ 'appArn' => [ 'shape' => 'Arn', ], 'clearResiliencyPolicyArn' => [ 'shape' => 'BooleanOptional', ], 'description' => [ 'shape' => 'EntityDescription', ], 'policyArn' => [ 'shape' => 'Arn', ], ], ], 'UpdateAppResponse' => [ 'type' => 'structure', 'required' => [ 'app', ], 'members' => [ 'app' => [ 'shape' => 'App', ], ], ], 'UpdateResiliencyPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'policyArn', ], 'members' => [ 'dataLocationConstraint' => [ 'shape' => 'DataLocationConstraint', ], 'policy' => [ 'shape' => 'DisruptionPolicy', ], 'policyArn' => [ 'shape' => 'Arn', ], 'policyDescription' => [ 'shape' => 'EntityDescription', ], 'policyName' => [ 'shape' => 'EntityName', ], 'tier' => [ 'shape' => 'ResiliencyPolicyTier', ], ], ], 'UpdateResiliencyPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'policy', ], 'members' => [ 'policy' => [ 'shape' => 'ResiliencyPolicy', ], ], ], 'Uuid' => [ 'type' => 'string', 'pattern' => '^[0-9a-f]{8}-[0-9a-f]{4}-[0-5][0-9a-f]{3}-[089ab][0-9a-f]{3}-[0-9a-f]{12}$', ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String500', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], ],];
