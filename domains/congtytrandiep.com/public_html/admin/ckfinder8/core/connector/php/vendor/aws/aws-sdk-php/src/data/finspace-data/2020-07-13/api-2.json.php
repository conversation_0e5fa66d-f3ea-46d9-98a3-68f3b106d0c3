<?php
// This file was auto-generated from sdk-root/src/data/finspace-data/2020-07-13/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-07-13', 'endpointPrefix' => 'finspace-api', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'FinSpace Data', 'serviceFullName' => 'FinSpace Public API', 'serviceId' => 'finspace data', 'signatureVersion' => 'v4', 'signingName' => 'finspace-api', 'uid' => 'finspace-2020-07-13', ], 'operations' => [ 'CreateChangeset' => [ 'name' => 'CreateChangeset', 'http' => [ 'method' => 'POST', 'requestUri' => '/datasets/{datasetId}/changesetsv2', ], 'input' => [ 'shape' => 'CreateChangesetRequest', ], 'output' => [ 'shape' => 'CreateChangesetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateDataView' => [ 'name' => 'CreateDataView', 'http' => [ 'method' => 'POST', 'requestUri' => '/datasets/{datasetId}/dataviewsv2', ], 'input' => [ 'shape' => 'CreateDataViewRequest', ], 'output' => [ 'shape' => 'CreateDataViewResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'CreateDataset' => [ 'name' => 'CreateDataset', 'http' => [ 'method' => 'POST', 'requestUri' => '/datasetsv2', ], 'input' => [ 'shape' => 'CreateDatasetRequest', ], 'output' => [ 'shape' => 'CreateDatasetResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteDataset' => [ 'name' => 'DeleteDataset', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/datasetsv2/{datasetId}', ], 'input' => [ 'shape' => 'DeleteDatasetRequest', ], 'output' => [ 'shape' => 'DeleteDatasetResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetChangeset' => [ 'name' => 'GetChangeset', 'http' => [ 'method' => 'GET', 'requestUri' => '/datasets/{datasetId}/changesetsv2/{changesetId}', ], 'input' => [ 'shape' => 'GetChangesetRequest', ], 'output' => [ 'shape' => 'GetChangesetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetDataView' => [ 'name' => 'GetDataView', 'http' => [ 'method' => 'GET', 'requestUri' => '/datasets/{datasetId}/dataviewsv2/{dataviewId}', ], 'input' => [ 'shape' => 'GetDataViewRequest', ], 'output' => [ 'shape' => 'GetDataViewResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetDataset' => [ 'name' => 'GetDataset', 'http' => [ 'method' => 'GET', 'requestUri' => '/datasetsv2/{datasetId}', ], 'input' => [ 'shape' => 'GetDatasetRequest', ], 'output' => [ 'shape' => 'GetDatasetResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetProgrammaticAccessCredentials' => [ 'name' => 'GetProgrammaticAccessCredentials', 'http' => [ 'method' => 'GET', 'requestUri' => '/credentials/programmatic', ], 'input' => [ 'shape' => 'GetProgrammaticAccessCredentialsRequest', ], 'output' => [ 'shape' => 'GetProgrammaticAccessCredentialsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetWorkingLocation' => [ 'name' => 'GetWorkingLocation', 'http' => [ 'method' => 'POST', 'requestUri' => '/workingLocationV1', ], 'input' => [ 'shape' => 'GetWorkingLocationRequest', ], 'output' => [ 'shape' => 'GetWorkingLocationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListChangesets' => [ 'name' => 'ListChangesets', 'http' => [ 'method' => 'GET', 'requestUri' => '/datasets/{datasetId}/changesetsv2', ], 'input' => [ 'shape' => 'ListChangesetsRequest', ], 'output' => [ 'shape' => 'ListChangesetsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListDataViews' => [ 'name' => 'ListDataViews', 'http' => [ 'method' => 'GET', 'requestUri' => '/datasets/{datasetId}/dataviewsv2', ], 'input' => [ 'shape' => 'ListDataViewsRequest', ], 'output' => [ 'shape' => 'ListDataViewsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListDatasets' => [ 'name' => 'ListDatasets', 'http' => [ 'method' => 'GET', 'requestUri' => '/datasetsv2', ], 'input' => [ 'shape' => 'ListDatasetsRequest', ], 'output' => [ 'shape' => 'ListDatasetsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateChangeset' => [ 'name' => 'UpdateChangeset', 'http' => [ 'method' => 'PUT', 'requestUri' => '/datasets/{datasetId}/changesetsv2/{changesetId}', ], 'input' => [ 'shape' => 'UpdateChangesetRequest', ], 'output' => [ 'shape' => 'UpdateChangesetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateDataset' => [ 'name' => 'UpdateDataset', 'http' => [ 'method' => 'PUT', 'requestUri' => '/datasetsv2/{datasetId}', ], 'input' => [ 'shape' => 'UpdateDatasetRequest', ], 'output' => [ 'shape' => 'UpdateDatasetResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'AliasString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^alias\\/\\S+', ], 'Boolean' => [ 'type' => 'boolean', ], 'ChangeType' => [ 'type' => 'string', 'enum' => [ 'REPLACE', 'APPEND', 'MODIFY', ], ], 'ChangesetArn' => [ 'type' => 'string', ], 'ChangesetErrorInfo' => [ 'type' => 'structure', 'members' => [ 'errorMessage' => [ 'shape' => 'ErrorMessage', ], 'errorCategory' => [ 'shape' => 'ErrorCategory', ], ], ], 'ChangesetId' => [ 'type' => 'string', 'max' => 26, 'min' => 1, ], 'ChangesetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChangesetSummary', ], ], 'ChangesetSummary' => [ 'type' => 'structure', 'members' => [ 'changesetId' => [ 'shape' => 'ChangesetId', ], 'changesetArn' => [ 'shape' => 'ChangesetArn', ], 'datasetId' => [ 'shape' => 'DatasetId', ], 'changeType' => [ 'shape' => 'ChangeType', ], 'sourceParams' => [ 'shape' => 'SourceParams', ], 'formatParams' => [ 'shape' => 'FormatParams', ], 'createTime' => [ 'shape' => 'TimestampEpoch', ], 'status' => [ 'shape' => 'IngestionStatus', ], 'errorInfo' => [ 'shape' => 'ChangesetErrorInfo', ], 'activeUntilTimestamp' => [ 'shape' => 'TimestampEpoch', 'box' => true, ], 'updatesChangesetId' => [ 'shape' => 'ChangesetId', ], 'updatedByChangesetId' => [ 'shape' => 'ChangesetId', ], ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '.*\\S.*', ], 'ColumnDataType' => [ 'type' => 'string', 'enum' => [ 'STRING', 'CHAR', 'INTEGER', 'TINYINT', 'SMALLINT', 'BIGINT', 'FLOAT', 'DOUBLE', 'DATE', 'DATETIME', 'BOOLEAN', 'BINARY', ], ], 'ColumnDefinition' => [ 'type' => 'structure', 'members' => [ 'dataType' => [ 'shape' => 'ColumnDataType', ], 'columnName' => [ 'shape' => 'ColumnName', ], 'columnDescription' => [ 'shape' => 'ColumnDescription', ], ], ], 'ColumnDescription' => [ 'type' => 'string', 'max' => 512, 'pattern' => '[\\s\\S]*\\S[\\s\\S]*', ], 'ColumnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnDefinition', ], ], 'ColumnName' => [ 'type' => 'string', 'max' => 126, 'pattern' => '.*\\S.*', ], 'ColumnNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnName', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'CreateChangesetRequest' => [ 'type' => 'structure', 'required' => [ 'datasetId', 'changeType', 'sourceParams', 'formatParams', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'datasetId' => [ 'shape' => 'DatasetId', 'location' => 'uri', 'locationName' => 'datasetId', ], 'changeType' => [ 'shape' => 'ChangeType', ], 'sourceParams' => [ 'shape' => 'SourceParams', ], 'formatParams' => [ 'shape' => 'FormatParams', ], ], ], 'CreateChangesetResponse' => [ 'type' => 'structure', 'members' => [ 'datasetId' => [ 'shape' => 'DatasetId', ], 'changesetId' => [ 'shape' => 'ChangesetId', ], ], ], 'CreateDataViewRequest' => [ 'type' => 'structure', 'required' => [ 'datasetId', 'destinationTypeParams', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'datasetId' => [ 'shape' => 'DatasetId', 'location' => 'uri', 'locationName' => 'datasetId', ], 'autoUpdate' => [ 'shape' => 'Boolean', ], 'sortColumns' => [ 'shape' => 'SortColumnList', ], 'partitionColumns' => [ 'shape' => 'PartitionColumnList', ], 'asOfTimestamp' => [ 'shape' => 'TimestampEpoch', 'box' => true, ], 'destinationTypeParams' => [ 'shape' => 'DataViewDestinationTypeParams', ], ], ], 'CreateDataViewResponse' => [ 'type' => 'structure', 'members' => [ 'datasetId' => [ 'shape' => 'DatasetId', ], 'dataViewId' => [ 'shape' => 'DataViewId', ], ], ], 'CreateDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'datasetTitle', 'kind', 'datasetDescription', 'permissionGroupParams', 'alias', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'datasetTitle' => [ 'shape' => 'DatasetTitle', ], 'kind' => [ 'shape' => 'DatasetKind', ], 'datasetDescription' => [ 'shape' => 'DatasetDescription', ], 'ownerInfo' => [ 'shape' => 'DatasetOwnerInfo', ], 'permissionGroupParams' => [ 'shape' => 'PermissionGroupParams', ], 'alias' => [ 'shape' => 'AliasString', ], 'schemaDefinition' => [ 'shape' => 'SchemaUnion', ], ], ], 'CreateDatasetResponse' => [ 'type' => 'structure', 'members' => [ 'datasetId' => [ 'shape' => 'DatasetId', ], ], ], 'Credentials' => [ 'type' => 'structure', 'members' => [ 'accessKeyId' => [ 'shape' => 'stringValueLength1to255', ], 'secretAccessKey' => [ 'shape' => 'stringValueMaxLength1000', ], 'sessionToken' => [ 'shape' => 'stringValueMaxLength1000', ], ], ], 'DataViewArn' => [ 'type' => 'string', ], 'DataViewDestinationType' => [ 'type' => 'string', ], 'DataViewDestinationTypeParams' => [ 'type' => 'structure', 'required' => [ 'destinationType', ], 'members' => [ 'destinationType' => [ 'shape' => 'DataViewDestinationType', ], ], ], 'DataViewErrorInfo' => [ 'type' => 'structure', 'members' => [ 'errorMessage' => [ 'shape' => 'ErrorMessage', ], 'errorCategory' => [ 'shape' => 'ErrorCategory', ], ], ], 'DataViewId' => [ 'type' => 'string', 'max' => 26, 'min' => 1, ], 'DataViewList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataViewSummary', ], ], 'DataViewStatus' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'STARTING', 'FAILED', 'CANCELLED', 'TIMEOUT', 'SUCCESS', 'PENDING', 'FAILED_CLEANUP_FAILED', ], ], 'DataViewSummary' => [ 'type' => 'structure', 'members' => [ 'dataViewId' => [ 'shape' => 'DataViewId', ], 'dataViewArn' => [ 'shape' => 'DataViewArn', ], 'datasetId' => [ 'shape' => 'DatasetId', ], 'asOfTimestamp' => [ 'shape' => 'TimestampEpoch', 'box' => true, ], 'partitionColumns' => [ 'shape' => 'PartitionColumnList', ], 'sortColumns' => [ 'shape' => 'SortColumnList', ], 'status' => [ 'shape' => 'DataViewStatus', ], 'errorInfo' => [ 'shape' => 'DataViewErrorInfo', ], 'destinationTypeProperties' => [ 'shape' => 'DataViewDestinationTypeParams', ], 'autoUpdate' => [ 'shape' => 'Boolean', ], 'createTime' => [ 'shape' => 'TimestampEpoch', ], 'lastModifiedTime' => [ 'shape' => 'TimestampEpoch', ], ], ], 'Dataset' => [ 'type' => 'structure', 'members' => [ 'datasetId' => [ 'shape' => 'DatasetId', ], 'datasetArn' => [ 'shape' => 'DatasetArn', ], 'datasetTitle' => [ 'shape' => 'DatasetTitle', ], 'kind' => [ 'shape' => 'DatasetKind', ], 'datasetDescription' => [ 'shape' => 'DatasetDescription', ], 'ownerInfo' => [ 'shape' => 'DatasetOwnerInfo', ], 'createTime' => [ 'shape' => 'TimestampEpoch', ], 'lastModifiedTime' => [ 'shape' => 'TimestampEpoch', ], 'schemaDefinition' => [ 'shape' => 'SchemaUnion', ], 'alias' => [ 'shape' => 'AliasString', ], ], ], 'DatasetArn' => [ 'type' => 'string', ], 'DatasetDescription' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\s\\S]*\\S[\\s\\S]*', ], 'DatasetId' => [ 'type' => 'string', 'max' => 26, 'min' => 1, ], 'DatasetKind' => [ 'type' => 'string', 'enum' => [ 'TABULAR', 'NON_TABULAR', ], ], 'DatasetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Dataset', ], ], 'DatasetOwnerInfo' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'OwnerName', ], 'phoneNumber' => [ 'shape' => 'PhoneNumber', ], 'email' => [ 'shape' => 'Email', ], ], ], 'DatasetStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'FAILED', 'SUCCESS', 'RUNNING', ], ], 'DatasetTitle' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '.*\\S.*', ], 'DeleteDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'datasetId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], 'datasetId' => [ 'shape' => 'DatasetId', 'location' => 'uri', 'locationName' => 'datasetId', ], ], ], 'DeleteDatasetResponse' => [ 'type' => 'structure', 'members' => [ 'datasetId' => [ 'shape' => 'DatasetId', ], ], ], 'Email' => [ 'type' => 'string', 'max' => 320, 'min' => 4, 'pattern' => '[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,4}', ], 'ErrorCategory' => [ 'type' => 'string', 'enum' => [ 'VALIDATION', 'SERVICE_QUOTA_EXCEEDED', 'ACCESS_DENIED', 'RESOURCE_NOT_FOUND', 'THROTTLING', 'INTERNAL_SERVICE_EXCEPTION', 'CANCELLED', 'USER_RECOVERABLE', ], ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 1000, ], 'FormatParams' => [ 'type' => 'map', 'key' => [ 'shape' => 'StringMapKey', ], 'value' => [ 'shape' => 'StringMapValue', ], ], 'GetChangesetRequest' => [ 'type' => 'structure', 'required' => [ 'datasetId', 'changesetId', ], 'members' => [ 'datasetId' => [ 'shape' => 'DatasetId', 'location' => 'uri', 'locationName' => 'datasetId', ], 'changesetId' => [ 'shape' => 'ChangesetId', 'location' => 'uri', 'locationName' => 'changesetId', ], ], ], 'GetChangesetResponse' => [ 'type' => 'structure', 'members' => [ 'changesetId' => [ 'shape' => 'ChangesetId', ], 'changesetArn' => [ 'shape' => 'ChangesetArn', ], 'datasetId' => [ 'shape' => 'DatasetId', ], 'changeType' => [ 'shape' => 'ChangeType', ], 'sourceParams' => [ 'shape' => 'SourceParams', ], 'formatParams' => [ 'shape' => 'FormatParams', ], 'createTime' => [ 'shape' => 'TimestampEpoch', ], 'status' => [ 'shape' => 'IngestionStatus', ], 'errorInfo' => [ 'shape' => 'ChangesetErrorInfo', ], 'activeUntilTimestamp' => [ 'shape' => 'TimestampEpoch', 'box' => true, ], 'updatesChangesetId' => [ 'shape' => 'ChangesetId', ], 'updatedByChangesetId' => [ 'shape' => 'ChangesetId', ], ], ], 'GetDataViewRequest' => [ 'type' => 'structure', 'required' => [ 'dataViewId', 'datasetId', ], 'members' => [ 'dataViewId' => [ 'shape' => 'DataViewId', 'location' => 'uri', 'locationName' => 'dataviewId', ], 'datasetId' => [ 'shape' => 'DatasetId', 'location' => 'uri', 'locationName' => 'datasetId', ], ], ], 'GetDataViewResponse' => [ 'type' => 'structure', 'members' => [ 'autoUpdate' => [ 'shape' => 'Boolean', ], 'partitionColumns' => [ 'shape' => 'PartitionColumnList', ], 'datasetId' => [ 'shape' => 'DatasetId', ], 'asOfTimestamp' => [ 'shape' => 'TimestampEpoch', 'box' => true, ], 'errorInfo' => [ 'shape' => 'DataViewErrorInfo', ], 'lastModifiedTime' => [ 'shape' => 'TimestampEpoch', ], 'createTime' => [ 'shape' => 'TimestampEpoch', ], 'sortColumns' => [ 'shape' => 'SortColumnList', ], 'dataViewId' => [ 'shape' => 'DataViewId', ], 'dataViewArn' => [ 'shape' => 'DataViewArn', ], 'destinationTypeParams' => [ 'shape' => 'DataViewDestinationTypeParams', ], 'status' => [ 'shape' => 'DataViewStatus', ], ], ], 'GetDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'datasetId', ], 'members' => [ 'datasetId' => [ 'shape' => 'StringValueLength1to255', 'location' => 'uri', 'locationName' => 'datasetId', ], ], ], 'GetDatasetResponse' => [ 'type' => 'structure', 'members' => [ 'datasetId' => [ 'shape' => 'DatasetId', ], 'datasetArn' => [ 'shape' => 'DatasetArn', ], 'datasetTitle' => [ 'shape' => 'DatasetTitle', ], 'kind' => [ 'shape' => 'DatasetKind', ], 'datasetDescription' => [ 'shape' => 'DatasetDescription', ], 'createTime' => [ 'shape' => 'TimestampEpoch', ], 'lastModifiedTime' => [ 'shape' => 'TimestampEpoch', ], 'schemaDefinition' => [ 'shape' => 'SchemaUnion', ], 'alias' => [ 'shape' => 'AliasString', ], 'status' => [ 'shape' => 'DatasetStatus', ], ], ], 'GetProgrammaticAccessCredentialsRequest' => [ 'type' => 'structure', 'required' => [ 'environmentId', ], 'members' => [ 'durationInMinutes' => [ 'shape' => 'SessionDuration', 'location' => 'querystring', 'locationName' => 'durationInMinutes', ], 'environmentId' => [ 'shape' => 'IdType', 'location' => 'querystring', 'locationName' => 'environmentId', ], ], ], 'GetProgrammaticAccessCredentialsResponse' => [ 'type' => 'structure', 'members' => [ 'credentials' => [ 'shape' => 'Credentials', ], 'durationInMinutes' => [ 'shape' => 'SessionDuration', ], ], ], 'GetWorkingLocationRequest' => [ 'type' => 'structure', 'members' => [ 'locationType' => [ 'shape' => 'locationType', ], ], ], 'GetWorkingLocationResponse' => [ 'type' => 'structure', 'members' => [ 's3Uri' => [ 'shape' => 'stringValueLength1to1024', ], 's3Path' => [ 'shape' => 'stringValueLength1to1024', ], 's3Bucket' => [ 'shape' => 'stringValueLength1to63', ], ], ], 'IdType' => [ 'type' => 'string', 'max' => 26, 'min' => 1, ], 'IngestionStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'FAILED', 'SUCCESS', 'RUNNING', 'STOP_REQUESTED', ], ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ListChangesetsRequest' => [ 'type' => 'structure', 'required' => [ 'datasetId', ], 'members' => [ 'datasetId' => [ 'shape' => 'DatasetId', 'location' => 'uri', 'locationName' => 'datasetId', ], 'maxResults' => [ 'shape' => 'ResultLimit', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListChangesetsResponse' => [ 'type' => 'structure', 'members' => [ 'changesets' => [ 'shape' => 'ChangesetList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListDataViewsRequest' => [ 'type' => 'structure', 'required' => [ 'datasetId', ], 'members' => [ 'datasetId' => [ 'shape' => 'DatasetId', 'location' => 'uri', 'locationName' => 'datasetId', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'ResultLimit', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListDataViewsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'dataViews' => [ 'shape' => 'DataViewList', ], ], ], 'ListDatasetsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'ResultLimit', 'box' => true, 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListDatasetsResponse' => [ 'type' => 'structure', 'members' => [ 'datasets' => [ 'shape' => 'DatasetList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'OwnerName' => [ 'type' => 'string', 'max' => 250, 'min' => 1, 'pattern' => '.*\\S.*', ], 'PaginationToken' => [ 'type' => 'string', ], 'PartitionColumnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringValueLength1to255', ], ], 'PermissionGroupId' => [ 'type' => 'string', 'max' => 26, 'min' => 1, ], 'PermissionGroupParams' => [ 'type' => 'structure', 'members' => [ 'permissionGroupId' => [ 'shape' => 'PermissionGroupId', ], 'datasetPermissions' => [ 'shape' => 'ResourcePermissionsList', ], ], ], 'PhoneNumber' => [ 'type' => 'string', 'max' => 20, 'min' => 10, 'pattern' => '^[\\+0-9\\#\\,\\(][\\+0-9\\-\\.\\/\\(\\)\\,\\#\\s]+$', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ResourcePermission' => [ 'type' => 'structure', 'members' => [ 'permission' => [ 'shape' => 'StringValueLength1to250', ], ], ], 'ResourcePermissionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourcePermission', ], ], 'ResultLimit' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'SchemaDefinition' => [ 'type' => 'structure', 'members' => [ 'columns' => [ 'shape' => 'ColumnList', ], 'primaryKeyColumns' => [ 'shape' => 'ColumnNameList', ], ], ], 'SchemaUnion' => [ 'type' => 'structure', 'members' => [ 'tabularSchemaConfig' => [ 'shape' => 'SchemaDefinition', ], ], ], 'SessionDuration' => [ 'type' => 'long', 'max' => 720, 'min' => 60, ], 'SortColumnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringValueLength1to255', ], ], 'SourceParams' => [ 'type' => 'map', 'key' => [ 'shape' => 'StringMapKey', ], 'value' => [ 'shape' => 'StringMapValue', ], ], 'StringMapKey' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[\\s\\S]*\\S[\\s\\S]*', ], 'StringMapValue' => [ 'type' => 'string', 'max' => 1000, 'pattern' => '[\\s\\S]*\\S[\\s\\S]*', ], 'StringValueLength1to250' => [ 'type' => 'string', 'max' => 250, 'min' => 1, 'pattern' => '[\\s\\S]*\\S[\\s\\S]*', ], 'StringValueLength1to255' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\s\\S]*\\S[\\s\\S]*', ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'TimestampEpoch' => [ 'type' => 'long', ], 'UpdateChangesetRequest' => [ 'type' => 'structure', 'required' => [ 'datasetId', 'changesetId', 'sourceParams', 'formatParams', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'datasetId' => [ 'shape' => 'DatasetId', 'location' => 'uri', 'locationName' => 'datasetId', ], 'changesetId' => [ 'shape' => 'ChangesetId', 'location' => 'uri', 'locationName' => 'changesetId', ], 'sourceParams' => [ 'shape' => 'SourceParams', ], 'formatParams' => [ 'shape' => 'FormatParams', ], ], ], 'UpdateChangesetResponse' => [ 'type' => 'structure', 'members' => [ 'changesetId' => [ 'shape' => 'ChangesetId', ], 'datasetId' => [ 'shape' => 'DatasetId', ], ], ], 'UpdateDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'datasetId', 'datasetTitle', 'kind', 'alias', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'datasetId' => [ 'shape' => 'DatasetId', 'location' => 'uri', 'locationName' => 'datasetId', ], 'datasetTitle' => [ 'shape' => 'DatasetTitle', ], 'kind' => [ 'shape' => 'DatasetKind', ], 'datasetDescription' => [ 'shape' => 'DatasetDescription', ], 'alias' => [ 'shape' => 'AliasString', ], 'schemaDefinition' => [ 'shape' => 'SchemaUnion', ], ], ], 'UpdateDatasetResponse' => [ 'type' => 'structure', 'members' => [ 'datasetId' => [ 'shape' => 'DatasetId', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'errorMessage' => [ 'type' => 'string', ], 'locationType' => [ 'type' => 'string', 'enum' => [ 'INGESTION', 'SAGEMAKER', ], ], 'stringValueLength1to1024' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '.*\\S.*', ], 'stringValueLength1to255' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'stringValueLength1to63' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '.*\\S.*', ], 'stringValueMaxLength1000' => [ 'type' => 'string', 'max' => 1000, ], ],];
