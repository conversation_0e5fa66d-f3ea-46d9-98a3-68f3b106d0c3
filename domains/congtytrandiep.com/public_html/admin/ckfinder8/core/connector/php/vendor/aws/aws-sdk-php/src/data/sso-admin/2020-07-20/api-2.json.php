<?php
// This file was auto-generated from sdk-root/src/data/sso-admin/2020-07-20/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-07-20', 'endpointPrefix' => 'sso', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceAbbreviation' => 'SSO Admin', 'serviceFullName' => 'AWS Single Sign-On Admin', 'serviceId' => 'SSO Admin', 'signatureVersion' => 'v4', 'signingName' => 'sso', 'targetPrefix' => 'SWBExternalService', 'uid' => 'sso-admin-2020-07-20', ], 'operations' => [ 'AttachManagedPolicyToPermissionSet' => [ 'name' => 'AttachManagedPolicyToPermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AttachManagedPolicyToPermissionSetRequest', ], 'output' => [ 'shape' => 'AttachManagedPolicyToPermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateAccountAssignment' => [ 'name' => 'CreateAccountAssignment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAccountAssignmentRequest', ], 'output' => [ 'shape' => 'CreateAccountAssignmentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateInstanceAccessControlAttributeConfiguration' => [ 'name' => 'CreateInstanceAccessControlAttributeConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateInstanceAccessControlAttributeConfigurationRequest', ], 'output' => [ 'shape' => 'CreateInstanceAccessControlAttributeConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreatePermissionSet' => [ 'name' => 'CreatePermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePermissionSetRequest', ], 'output' => [ 'shape' => 'CreatePermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteAccountAssignment' => [ 'name' => 'DeleteAccountAssignment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAccountAssignmentRequest', ], 'output' => [ 'shape' => 'DeleteAccountAssignmentResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteInlinePolicyFromPermissionSet' => [ 'name' => 'DeleteInlinePolicyFromPermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteInlinePolicyFromPermissionSetRequest', ], 'output' => [ 'shape' => 'DeleteInlinePolicyFromPermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteInstanceAccessControlAttributeConfiguration' => [ 'name' => 'DeleteInstanceAccessControlAttributeConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteInstanceAccessControlAttributeConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteInstanceAccessControlAttributeConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeletePermissionSet' => [ 'name' => 'DeletePermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePermissionSetRequest', ], 'output' => [ 'shape' => 'DeletePermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'DescribeAccountAssignmentCreationStatus' => [ 'name' => 'DescribeAccountAssignmentCreationStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAccountAssignmentCreationStatusRequest', ], 'output' => [ 'shape' => 'DescribeAccountAssignmentCreationStatusResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeAccountAssignmentDeletionStatus' => [ 'name' => 'DescribeAccountAssignmentDeletionStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAccountAssignmentDeletionStatusRequest', ], 'output' => [ 'shape' => 'DescribeAccountAssignmentDeletionStatusResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeInstanceAccessControlAttributeConfiguration' => [ 'name' => 'DescribeInstanceAccessControlAttributeConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeInstanceAccessControlAttributeConfigurationRequest', ], 'output' => [ 'shape' => 'DescribeInstanceAccessControlAttributeConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], ], ], 'DescribePermissionSet' => [ 'name' => 'DescribePermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePermissionSetRequest', ], 'output' => [ 'shape' => 'DescribePermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribePermissionSetProvisioningStatus' => [ 'name' => 'DescribePermissionSetProvisioningStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePermissionSetProvisioningStatusRequest', ], 'output' => [ 'shape' => 'DescribePermissionSetProvisioningStatusResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DetachManagedPolicyFromPermissionSet' => [ 'name' => 'DetachManagedPolicyFromPermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetachManagedPolicyFromPermissionSetRequest', ], 'output' => [ 'shape' => 'DetachManagedPolicyFromPermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetInlinePolicyForPermissionSet' => [ 'name' => 'GetInlinePolicyForPermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetInlinePolicyForPermissionSetRequest', ], 'output' => [ 'shape' => 'GetInlinePolicyForPermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAccountAssignmentCreationStatus' => [ 'name' => 'ListAccountAssignmentCreationStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAccountAssignmentCreationStatusRequest', ], 'output' => [ 'shape' => 'ListAccountAssignmentCreationStatusResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAccountAssignmentDeletionStatus' => [ 'name' => 'ListAccountAssignmentDeletionStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAccountAssignmentDeletionStatusRequest', ], 'output' => [ 'shape' => 'ListAccountAssignmentDeletionStatusResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAccountAssignments' => [ 'name' => 'ListAccountAssignments', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAccountAssignmentsRequest', ], 'output' => [ 'shape' => 'ListAccountAssignmentsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAccountsForProvisionedPermissionSet' => [ 'name' => 'ListAccountsForProvisionedPermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListAccountsForProvisionedPermissionSetRequest', ], 'output' => [ 'shape' => 'ListAccountsForProvisionedPermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListInstances' => [ 'name' => 'ListInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListInstancesRequest', ], 'output' => [ 'shape' => 'ListInstancesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListManagedPoliciesInPermissionSet' => [ 'name' => 'ListManagedPoliciesInPermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListManagedPoliciesInPermissionSetRequest', ], 'output' => [ 'shape' => 'ListManagedPoliciesInPermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListPermissionSetProvisioningStatus' => [ 'name' => 'ListPermissionSetProvisioningStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPermissionSetProvisioningStatusRequest', ], 'output' => [ 'shape' => 'ListPermissionSetProvisioningStatusResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListPermissionSets' => [ 'name' => 'ListPermissionSets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPermissionSetsRequest', ], 'output' => [ 'shape' => 'ListPermissionSetsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListPermissionSetsProvisionedToAccount' => [ 'name' => 'ListPermissionSetsProvisionedToAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPermissionSetsProvisionedToAccountRequest', ], 'output' => [ 'shape' => 'ListPermissionSetsProvisionedToAccountResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ProvisionPermissionSet' => [ 'name' => 'ProvisionPermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ProvisionPermissionSetRequest', ], 'output' => [ 'shape' => 'ProvisionPermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'PutInlinePolicyToPermissionSet' => [ 'name' => 'PutInlinePolicyToPermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutInlinePolicyToPermissionSetRequest', ], 'output' => [ 'shape' => 'PutInlinePolicyToPermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateInstanceAccessControlAttributeConfiguration' => [ 'name' => 'UpdateInstanceAccessControlAttributeConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateInstanceAccessControlAttributeConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateInstanceAccessControlAttributeConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdatePermissionSet' => [ 'name' => 'UpdatePermissionSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdatePermissionSetRequest', ], 'output' => [ 'shape' => 'UpdatePermissionSetResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], ], 'shapes' => [ 'AccessControlAttribute' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'AccessControlAttributeKey', ], 'Value' => [ 'shape' => 'AccessControlAttributeValue', ], ], ], 'AccessControlAttributeKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\p{L}\\p{Z}\\p{N}_.:\\/=+\\-@]+', ], 'AccessControlAttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccessControlAttribute', ], 'max' => 50, 'min' => 0, ], 'AccessControlAttributeValue' => [ 'type' => 'structure', 'required' => [ 'Source', ], 'members' => [ 'Source' => [ 'shape' => 'AccessControlAttributeValueSourceList', ], ], ], 'AccessControlAttributeValueSource' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[\\p{L}\\p{Z}\\p{N}_.:\\/=+\\-@\\[\\]\\{\\}\\$\\\\"]*', ], 'AccessControlAttributeValueSourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccessControlAttributeValueSource', ], 'max' => 1, 'min' => 1, ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'AccessDeniedExceptionMessage', ], ], 'exception' => true, ], 'AccessDeniedExceptionMessage' => [ 'type' => 'string', ], 'AccountAssignment' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], 'PrincipalType' => [ 'shape' => 'PrincipalType', ], 'PrincipalId' => [ 'shape' => 'PrincipalId', ], ], ], 'AccountAssignmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountAssignment', ], ], 'AccountAssignmentOperationStatus' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'StatusValues', ], 'RequestId' => [ 'shape' => 'UUId', ], 'FailureReason' => [ 'shape' => 'Reason', ], 'TargetId' => [ 'shape' => 'TargetId', ], 'TargetType' => [ 'shape' => 'TargetType', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], 'PrincipalType' => [ 'shape' => 'PrincipalType', ], 'PrincipalId' => [ 'shape' => 'PrincipalId', ], 'CreatedDate' => [ 'shape' => 'Date', ], ], ], 'AccountAssignmentOperationStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountAssignmentOperationStatusMetadata', ], ], 'AccountAssignmentOperationStatusMetadata' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'StatusValues', ], 'RequestId' => [ 'shape' => 'UUId', ], 'CreatedDate' => [ 'shape' => 'Date', ], ], ], 'AccountId' => [ 'type' => 'string', 'pattern' => '\\d{12}', ], 'AccountList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountId', ], ], 'AttachManagedPolicyToPermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'PermissionSetArn', 'ManagedPolicyArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], 'ManagedPolicyArn' => [ 'shape' => 'ManagedPolicyArn', ], ], ], 'AttachManagedPolicyToPermissionSetResponse' => [ 'type' => 'structure', 'members' => [], ], 'AttachedManagedPolicy' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'Name', ], 'Arn' => [ 'shape' => 'ManagedPolicyArn', ], ], ], 'AttachedManagedPolicyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttachedManagedPolicy', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ConflictExceptionMessage', ], ], 'exception' => true, ], 'ConflictExceptionMessage' => [ 'type' => 'string', ], 'CreateAccountAssignmentRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'TargetId', 'TargetType', 'PermissionSetArn', 'PrincipalType', 'PrincipalId', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'TargetId' => [ 'shape' => 'TargetId', ], 'TargetType' => [ 'shape' => 'TargetType', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], 'PrincipalType' => [ 'shape' => 'PrincipalType', ], 'PrincipalId' => [ 'shape' => 'PrincipalId', ], ], ], 'CreateAccountAssignmentResponse' => [ 'type' => 'structure', 'members' => [ 'AccountAssignmentCreationStatus' => [ 'shape' => 'AccountAssignmentOperationStatus', ], ], ], 'CreateInstanceAccessControlAttributeConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'InstanceAccessControlAttributeConfiguration', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'InstanceAccessControlAttributeConfiguration' => [ 'shape' => 'InstanceAccessControlAttributeConfiguration', ], ], ], 'CreateInstanceAccessControlAttributeConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreatePermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'InstanceArn', ], 'members' => [ 'Name' => [ 'shape' => 'PermissionSetName', ], 'Description' => [ 'shape' => 'PermissionSetDescription', ], 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'SessionDuration' => [ 'shape' => 'Duration', ], 'RelayState' => [ 'shape' => 'RelayState', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreatePermissionSetResponse' => [ 'type' => 'structure', 'members' => [ 'PermissionSet' => [ 'shape' => 'PermissionSet', ], ], ], 'Date' => [ 'type' => 'timestamp', ], 'DeleteAccountAssignmentRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'TargetId', 'TargetType', 'PermissionSetArn', 'PrincipalType', 'PrincipalId', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'TargetId' => [ 'shape' => 'TargetId', ], 'TargetType' => [ 'shape' => 'TargetType', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], 'PrincipalType' => [ 'shape' => 'PrincipalType', ], 'PrincipalId' => [ 'shape' => 'PrincipalId', ], ], ], 'DeleteAccountAssignmentResponse' => [ 'type' => 'structure', 'members' => [ 'AccountAssignmentDeletionStatus' => [ 'shape' => 'AccountAssignmentOperationStatus', ], ], ], 'DeleteInlinePolicyFromPermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'PermissionSetArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], ], ], 'DeleteInlinePolicyFromPermissionSetResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteInstanceAccessControlAttributeConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], ], ], 'DeleteInstanceAccessControlAttributeConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeletePermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'PermissionSetArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], ], ], 'DeletePermissionSetResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeAccountAssignmentCreationStatusRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'AccountAssignmentCreationRequestId', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'AccountAssignmentCreationRequestId' => [ 'shape' => 'UUId', ], ], ], 'DescribeAccountAssignmentCreationStatusResponse' => [ 'type' => 'structure', 'members' => [ 'AccountAssignmentCreationStatus' => [ 'shape' => 'AccountAssignmentOperationStatus', ], ], ], 'DescribeAccountAssignmentDeletionStatusRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'AccountAssignmentDeletionRequestId', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'AccountAssignmentDeletionRequestId' => [ 'shape' => 'UUId', ], ], ], 'DescribeAccountAssignmentDeletionStatusResponse' => [ 'type' => 'structure', 'members' => [ 'AccountAssignmentDeletionStatus' => [ 'shape' => 'AccountAssignmentOperationStatus', ], ], ], 'DescribeInstanceAccessControlAttributeConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], ], ], 'DescribeInstanceAccessControlAttributeConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'InstanceAccessControlAttributeConfigurationStatus', ], 'StatusReason' => [ 'shape' => 'InstanceAccessControlAttributeConfigurationStatusReason', ], 'InstanceAccessControlAttributeConfiguration' => [ 'shape' => 'InstanceAccessControlAttributeConfiguration', ], ], ], 'DescribePermissionSetProvisioningStatusRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'ProvisionPermissionSetRequestId', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'ProvisionPermissionSetRequestId' => [ 'shape' => 'UUId', ], ], ], 'DescribePermissionSetProvisioningStatusResponse' => [ 'type' => 'structure', 'members' => [ 'PermissionSetProvisioningStatus' => [ 'shape' => 'PermissionSetProvisioningStatus', ], ], ], 'DescribePermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'PermissionSetArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], ], ], 'DescribePermissionSetResponse' => [ 'type' => 'structure', 'members' => [ 'PermissionSet' => [ 'shape' => 'PermissionSet', ], ], ], 'DetachManagedPolicyFromPermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'PermissionSetArn', 'ManagedPolicyArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], 'ManagedPolicyArn' => [ 'shape' => 'ManagedPolicyArn', ], ], ], 'DetachManagedPolicyFromPermissionSetResponse' => [ 'type' => 'structure', 'members' => [], ], 'Duration' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^(-?)P(?=\\d|T\\d)(?:(\\d+)Y)?(?:(\\d+)M)?(?:(\\d+)([DW]))?(?:T(?:(\\d+)H)?(?:(\\d+)M)?(?:(\\d+(?:\\.\\d+)?)S)?)?$', ], 'GeneralArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 10, 'pattern' => 'arn:aws:sso:([a-zA-Z0-9-]+)?:(\\d{12})?:[a-zA-Z0-9-]+/[a-zA-Z0-9-/.]+', ], 'GetInlinePolicyForPermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'PermissionSetArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], ], ], 'GetInlinePolicyForPermissionSetResponse' => [ 'type' => 'structure', 'members' => [ 'InlinePolicy' => [ 'shape' => 'PermissionSetPolicyDocument', ], ], ], 'Id' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9-]*', ], 'InstanceAccessControlAttributeConfiguration' => [ 'type' => 'structure', 'required' => [ 'AccessControlAttributes', ], 'members' => [ 'AccessControlAttributes' => [ 'shape' => 'AccessControlAttributeList', ], ], ], 'InstanceAccessControlAttributeConfigurationStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'CREATION_IN_PROGRESS', 'CREATION_FAILED', ], ], 'InstanceAccessControlAttributeConfigurationStatusReason' => [ 'type' => 'string', ], 'InstanceArn' => [ 'type' => 'string', 'max' => 1224, 'min' => 10, 'pattern' => 'arn:aws:sso:::instance/(sso)?ins-[a-zA-Z0-9-.]{16}', ], 'InstanceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceMetadata', ], ], 'InstanceMetadata' => [ 'type' => 'structure', 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'IdentityStoreId' => [ 'shape' => 'Id', ], ], ], 'InternalFailureMessage' => [ 'type' => 'string', ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'InternalFailureMessage', ], ], 'exception' => true, ], 'ListAccountAssignmentCreationStatusRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', ], 'Filter' => [ 'shape' => 'OperationStatusFilter', ], ], ], 'ListAccountAssignmentCreationStatusResponse' => [ 'type' => 'structure', 'members' => [ 'AccountAssignmentsCreationStatus' => [ 'shape' => 'AccountAssignmentOperationStatusList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListAccountAssignmentDeletionStatusRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', ], 'Filter' => [ 'shape' => 'OperationStatusFilter', ], ], ], 'ListAccountAssignmentDeletionStatusResponse' => [ 'type' => 'structure', 'members' => [ 'AccountAssignmentsDeletionStatus' => [ 'shape' => 'AccountAssignmentOperationStatusList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListAccountAssignmentsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'AccountId', 'PermissionSetArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'AccountId' => [ 'shape' => 'TargetId', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListAccountAssignmentsResponse' => [ 'type' => 'structure', 'members' => [ 'AccountAssignments' => [ 'shape' => 'AccountAssignmentList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListAccountsForProvisionedPermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'PermissionSetArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], 'ProvisioningStatus' => [ 'shape' => 'ProvisioningStatus', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListAccountsForProvisionedPermissionSetResponse' => [ 'type' => 'structure', 'members' => [ 'AccountIds' => [ 'shape' => 'AccountList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListInstancesRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListInstancesResponse' => [ 'type' => 'structure', 'members' => [ 'Instances' => [ 'shape' => 'InstanceList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListManagedPoliciesInPermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'PermissionSetArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListManagedPoliciesInPermissionSetResponse' => [ 'type' => 'structure', 'members' => [ 'AttachedManagedPolicies' => [ 'shape' => 'AttachedManagedPolicyList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListPermissionSetProvisioningStatusRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', ], 'Filter' => [ 'shape' => 'OperationStatusFilter', ], ], ], 'ListPermissionSetProvisioningStatusResponse' => [ 'type' => 'structure', 'members' => [ 'PermissionSetsProvisioningStatus' => [ 'shape' => 'PermissionSetProvisioningStatusList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListPermissionSetsProvisionedToAccountRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'AccountId', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'AccountId' => [ 'shape' => 'AccountId', ], 'ProvisioningStatus' => [ 'shape' => 'ProvisioningStatus', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListPermissionSetsProvisionedToAccountResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'Token', ], 'PermissionSets' => [ 'shape' => 'PermissionSetList', ], ], ], 'ListPermissionSetsRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListPermissionSetsResponse' => [ 'type' => 'structure', 'members' => [ 'PermissionSets' => [ 'shape' => 'PermissionSetList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'ResourceArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'ResourceArn' => [ 'shape' => 'GeneralArn', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'ManagedPolicyArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, ], 'MaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'Name' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'OperationStatusFilter' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'StatusValues', ], ], ], 'PermissionSet' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'PermissionSetName', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], 'Description' => [ 'shape' => 'PermissionSetDescription', ], 'CreatedDate' => [ 'shape' => 'Date', ], 'SessionDuration' => [ 'shape' => 'Duration', ], 'RelayState' => [ 'shape' => 'RelayState', ], ], ], 'PermissionSetArn' => [ 'type' => 'string', 'max' => 1224, 'min' => 10, 'pattern' => 'arn:aws:sso:::permissionSet/(sso)?ins-[a-zA-Z0-9-.]{16}/ps-[a-zA-Z0-9-./]{16}', ], 'PermissionSetDescription' => [ 'type' => 'string', 'max' => 700, 'min' => 1, 'pattern' => '[\\p{L}\\p{M}\\p{Z}\\p{S}\\p{N}\\p{P}]*', ], 'PermissionSetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PermissionSetArn', ], ], 'PermissionSetName' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '[\\w+=,.@-]+', ], 'PermissionSetPolicyDocument' => [ 'type' => 'string', 'max' => 10240, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u00FF]+', 'sensitive' => true, ], 'PermissionSetProvisioningStatus' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'StatusValues', ], 'RequestId' => [ 'shape' => 'UUId', ], 'AccountId' => [ 'shape' => 'AccountId', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], 'FailureReason' => [ 'shape' => 'Reason', ], 'CreatedDate' => [ 'shape' => 'Date', ], ], ], 'PermissionSetProvisioningStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PermissionSetProvisioningStatusMetadata', ], ], 'PermissionSetProvisioningStatusMetadata' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'StatusValues', ], 'RequestId' => [ 'shape' => 'UUId', ], 'CreatedDate' => [ 'shape' => 'Date', ], ], ], 'PrincipalId' => [ 'type' => 'string', 'max' => 47, 'min' => 1, 'pattern' => '^([0-9a-f]{10}-|)[A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{12}$', ], 'PrincipalType' => [ 'type' => 'string', 'enum' => [ 'USER', 'GROUP', ], ], 'ProvisionPermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'PermissionSetArn', 'TargetType', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], 'TargetId' => [ 'shape' => 'TargetId', ], 'TargetType' => [ 'shape' => 'ProvisionTargetType', ], ], ], 'ProvisionPermissionSetResponse' => [ 'type' => 'structure', 'members' => [ 'PermissionSetProvisioningStatus' => [ 'shape' => 'PermissionSetProvisioningStatus', ], ], ], 'ProvisionTargetType' => [ 'type' => 'string', 'enum' => [ 'AWS_ACCOUNT', 'ALL_PROVISIONED_ACCOUNTS', ], ], 'ProvisioningStatus' => [ 'type' => 'string', 'enum' => [ 'LATEST_PERMISSION_SET_PROVISIONED', 'LATEST_PERMISSION_SET_NOT_PROVISIONED', ], ], 'PutInlinePolicyToPermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'PermissionSetArn', 'InlinePolicy', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], 'InlinePolicy' => [ 'shape' => 'PermissionSetPolicyDocument', ], ], ], 'PutInlinePolicyToPermissionSetResponse' => [ 'type' => 'structure', 'members' => [], ], 'Reason' => [ 'type' => 'string', 'pattern' => '[\\p{L}\\p{M}\\p{Z}\\p{S}\\p{N}\\p{P}]*', ], 'RelayState' => [ 'type' => 'string', 'max' => 240, 'min' => 1, 'pattern' => '[a-zA-Z0-9&$@#\\\\\\/%?=~\\-_\'"|!:,.;*+\\[\\]\\ \\(\\)\\{\\}]+', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ResourceNotFoundMessage', ], ], 'exception' => true, ], 'ResourceNotFoundMessage' => [ 'type' => 'string', ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ServiceQuotaExceededMessage', ], ], 'exception' => true, ], 'ServiceQuotaExceededMessage' => [ 'type' => 'string', ], 'StatusValues' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'FAILED', 'SUCCEEDED', ], ], 'Tag' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 50, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'ResourceArn', 'Tags', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'ResourceArn' => [ 'shape' => 'GeneralArn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TargetId' => [ 'type' => 'string', 'pattern' => '\\d{12}', ], 'TargetType' => [ 'type' => 'string', 'enum' => [ 'AWS_ACCOUNT', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ThrottlingExceptionMessage', ], ], 'exception' => true, ], 'ThrottlingExceptionMessage' => [ 'type' => 'string', ], 'Token' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '^[-a-zA-Z0-9+=/_]*', ], 'UUId' => [ 'type' => 'string', 'pattern' => '\\b[0-9a-f]{8}\\b-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-\\b[0-9a-f]{12}\\b', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'ResourceArn', 'TagKeys', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'ResourceArn' => [ 'shape' => 'GeneralArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateInstanceAccessControlAttributeConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'InstanceAccessControlAttributeConfiguration', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'InstanceAccessControlAttributeConfiguration' => [ 'shape' => 'InstanceAccessControlAttributeConfiguration', ], ], ], 'UpdateInstanceAccessControlAttributeConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdatePermissionSetRequest' => [ 'type' => 'structure', 'required' => [ 'InstanceArn', 'PermissionSetArn', ], 'members' => [ 'InstanceArn' => [ 'shape' => 'InstanceArn', ], 'PermissionSetArn' => [ 'shape' => 'PermissionSetArn', ], 'Description' => [ 'shape' => 'PermissionSetDescription', ], 'SessionDuration' => [ 'shape' => 'Duration', ], 'RelayState' => [ 'shape' => 'RelayState', ], ], ], 'UpdatePermissionSetResponse' => [ 'type' => 'structure', 'members' => [], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ValidationExceptionMessage', ], ], 'exception' => true, ], 'ValidationExceptionMessage' => [ 'type' => 'string', ], ],];
