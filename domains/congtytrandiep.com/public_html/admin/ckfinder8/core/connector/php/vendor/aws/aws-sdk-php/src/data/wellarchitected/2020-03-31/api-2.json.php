<?php
// This file was auto-generated from sdk-root/src/data/wellarchitected/2020-03-31/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-03-31', 'endpointPrefix' => 'wellarchitected', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'Well-Architected', 'serviceFullName' => 'AWS Well-Architected Tool', 'serviceId' => 'WellArchitected', 'signatureVersion' => 'v4', 'signingName' => 'wellarchitected', 'uid' => 'wellarchitected-2020-03-31', ], 'operations' => [ 'AssociateLenses' => [ 'name' => 'AssociateLenses', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/workloads/{WorkloadId}/associateLenses', ], 'input' => [ 'shape' => 'AssociateLensesInput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateLensShare' => [ 'name' => 'CreateLensShare', 'http' => [ 'method' => 'POST', 'requestUri' => '/lenses/{LensAlias}/shares', ], 'input' => [ 'shape' => 'CreateLensShareInput', ], 'output' => [ 'shape' => 'CreateLensShareOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateLensVersion' => [ 'name' => 'CreateLensVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/lenses/{LensAlias}/versions', ], 'input' => [ 'shape' => 'CreateLensVersionInput', ], 'output' => [ 'shape' => 'CreateLensVersionOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateMilestone' => [ 'name' => 'CreateMilestone', 'http' => [ 'method' => 'POST', 'requestUri' => '/workloads/{WorkloadId}/milestones', ], 'input' => [ 'shape' => 'CreateMilestoneInput', ], 'output' => [ 'shape' => 'CreateMilestoneOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateWorkload' => [ 'name' => 'CreateWorkload', 'http' => [ 'method' => 'POST', 'requestUri' => '/workloads', ], 'input' => [ 'shape' => 'CreateWorkloadInput', ], 'output' => [ 'shape' => 'CreateWorkloadOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateWorkloadShare' => [ 'name' => 'CreateWorkloadShare', 'http' => [ 'method' => 'POST', 'requestUri' => '/workloads/{WorkloadId}/shares', ], 'input' => [ 'shape' => 'CreateWorkloadShareInput', ], 'output' => [ 'shape' => 'CreateWorkloadShareOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteLens' => [ 'name' => 'DeleteLens', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/lenses/{LensAlias}', ], 'input' => [ 'shape' => 'DeleteLensInput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteLensShare' => [ 'name' => 'DeleteLensShare', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/lenses/{LensAlias}/shares/{ShareId}', ], 'input' => [ 'shape' => 'DeleteLensShareInput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteWorkload' => [ 'name' => 'DeleteWorkload', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/workloads/{WorkloadId}', ], 'input' => [ 'shape' => 'DeleteWorkloadInput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteWorkloadShare' => [ 'name' => 'DeleteWorkloadShare', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/workloads/{WorkloadId}/shares/{ShareId}', ], 'input' => [ 'shape' => 'DeleteWorkloadShareInput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DisassociateLenses' => [ 'name' => 'DisassociateLenses', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/workloads/{WorkloadId}/disassociateLenses', ], 'input' => [ 'shape' => 'DisassociateLensesInput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ExportLens' => [ 'name' => 'ExportLens', 'http' => [ 'method' => 'GET', 'requestUri' => '/lenses/{LensAlias}/export', ], 'input' => [ 'shape' => 'ExportLensInput', ], 'output' => [ 'shape' => 'ExportLensOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetAnswer' => [ 'name' => 'GetAnswer', 'http' => [ 'method' => 'GET', 'requestUri' => '/workloads/{WorkloadId}/lensReviews/{LensAlias}/answers/{QuestionId}', ], 'input' => [ 'shape' => 'GetAnswerInput', ], 'output' => [ 'shape' => 'GetAnswerOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetLens' => [ 'name' => 'GetLens', 'http' => [ 'method' => 'GET', 'requestUri' => '/lenses/{LensAlias}', ], 'input' => [ 'shape' => 'GetLensInput', ], 'output' => [ 'shape' => 'GetLensOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetLensReview' => [ 'name' => 'GetLensReview', 'http' => [ 'method' => 'GET', 'requestUri' => '/workloads/{WorkloadId}/lensReviews/{LensAlias}', ], 'input' => [ 'shape' => 'GetLensReviewInput', ], 'output' => [ 'shape' => 'GetLensReviewOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetLensReviewReport' => [ 'name' => 'GetLensReviewReport', 'http' => [ 'method' => 'GET', 'requestUri' => '/workloads/{WorkloadId}/lensReviews/{LensAlias}/report', ], 'input' => [ 'shape' => 'GetLensReviewReportInput', ], 'output' => [ 'shape' => 'GetLensReviewReportOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetLensVersionDifference' => [ 'name' => 'GetLensVersionDifference', 'http' => [ 'method' => 'GET', 'requestUri' => '/lenses/{LensAlias}/versionDifference', ], 'input' => [ 'shape' => 'GetLensVersionDifferenceInput', ], 'output' => [ 'shape' => 'GetLensVersionDifferenceOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetMilestone' => [ 'name' => 'GetMilestone', 'http' => [ 'method' => 'GET', 'requestUri' => '/workloads/{WorkloadId}/milestones/{MilestoneNumber}', ], 'input' => [ 'shape' => 'GetMilestoneInput', ], 'output' => [ 'shape' => 'GetMilestoneOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetWorkload' => [ 'name' => 'GetWorkload', 'http' => [ 'method' => 'GET', 'requestUri' => '/workloads/{WorkloadId}', ], 'input' => [ 'shape' => 'GetWorkloadInput', ], 'output' => [ 'shape' => 'GetWorkloadOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ImportLens' => [ 'name' => 'ImportLens', 'http' => [ 'method' => 'PUT', 'requestUri' => '/importLens', ], 'input' => [ 'shape' => 'ImportLensInput', ], 'output' => [ 'shape' => 'ImportLensOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListAnswers' => [ 'name' => 'ListAnswers', 'http' => [ 'method' => 'GET', 'requestUri' => '/workloads/{WorkloadId}/lensReviews/{LensAlias}/answers', ], 'input' => [ 'shape' => 'ListAnswersInput', ], 'output' => [ 'shape' => 'ListAnswersOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListLensReviewImprovements' => [ 'name' => 'ListLensReviewImprovements', 'http' => [ 'method' => 'GET', 'requestUri' => '/workloads/{WorkloadId}/lensReviews/{LensAlias}/improvements', ], 'input' => [ 'shape' => 'ListLensReviewImprovementsInput', ], 'output' => [ 'shape' => 'ListLensReviewImprovementsOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListLensReviews' => [ 'name' => 'ListLensReviews', 'http' => [ 'method' => 'GET', 'requestUri' => '/workloads/{WorkloadId}/lensReviews', ], 'input' => [ 'shape' => 'ListLensReviewsInput', ], 'output' => [ 'shape' => 'ListLensReviewsOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListLensShares' => [ 'name' => 'ListLensShares', 'http' => [ 'method' => 'GET', 'requestUri' => '/lenses/{LensAlias}/shares', ], 'input' => [ 'shape' => 'ListLensSharesInput', ], 'output' => [ 'shape' => 'ListLensSharesOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListLenses' => [ 'name' => 'ListLenses', 'http' => [ 'method' => 'GET', 'requestUri' => '/lenses', ], 'input' => [ 'shape' => 'ListLensesInput', ], 'output' => [ 'shape' => 'ListLensesOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListMilestones' => [ 'name' => 'ListMilestones', 'http' => [ 'method' => 'POST', 'requestUri' => '/workloads/{WorkloadId}/milestonesSummaries', ], 'input' => [ 'shape' => 'ListMilestonesInput', ], 'output' => [ 'shape' => 'ListMilestonesOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListNotifications' => [ 'name' => 'ListNotifications', 'http' => [ 'method' => 'POST', 'requestUri' => '/notifications', ], 'input' => [ 'shape' => 'ListNotificationsInput', ], 'output' => [ 'shape' => 'ListNotificationsOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListShareInvitations' => [ 'name' => 'ListShareInvitations', 'http' => [ 'method' => 'GET', 'requestUri' => '/shareInvitations', ], 'input' => [ 'shape' => 'ListShareInvitationsInput', ], 'output' => [ 'shape' => 'ListShareInvitationsOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{WorkloadArn}', ], 'input' => [ 'shape' => 'ListTagsForResourceInput', ], 'output' => [ 'shape' => 'ListTagsForResourceOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListWorkloadShares' => [ 'name' => 'ListWorkloadShares', 'http' => [ 'method' => 'GET', 'requestUri' => '/workloads/{WorkloadId}/shares', ], 'input' => [ 'shape' => 'ListWorkloadSharesInput', ], 'output' => [ 'shape' => 'ListWorkloadSharesOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListWorkloads' => [ 'name' => 'ListWorkloads', 'http' => [ 'method' => 'POST', 'requestUri' => '/workloadsSummaries', ], 'input' => [ 'shape' => 'ListWorkloadsInput', ], 'output' => [ 'shape' => 'ListWorkloadsOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{WorkloadArn}', ], 'input' => [ 'shape' => 'TagResourceInput', ], 'output' => [ 'shape' => 'TagResourceOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{WorkloadArn}', ], 'input' => [ 'shape' => 'UntagResourceInput', ], 'output' => [ 'shape' => 'UntagResourceOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateAnswer' => [ 'name' => 'UpdateAnswer', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/workloads/{WorkloadId}/lensReviews/{LensAlias}/answers/{QuestionId}', ], 'input' => [ 'shape' => 'UpdateAnswerInput', ], 'output' => [ 'shape' => 'UpdateAnswerOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateLensReview' => [ 'name' => 'UpdateLensReview', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/workloads/{WorkloadId}/lensReviews/{LensAlias}', ], 'input' => [ 'shape' => 'UpdateLensReviewInput', ], 'output' => [ 'shape' => 'UpdateLensReviewOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateShareInvitation' => [ 'name' => 'UpdateShareInvitation', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/shareInvitations/{ShareInvitationId}', ], 'input' => [ 'shape' => 'UpdateShareInvitationInput', ], 'output' => [ 'shape' => 'UpdateShareInvitationOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateWorkload' => [ 'name' => 'UpdateWorkload', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/workloads/{WorkloadId}', ], 'input' => [ 'shape' => 'UpdateWorkloadInput', ], 'output' => [ 'shape' => 'UpdateWorkloadOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateWorkloadShare' => [ 'name' => 'UpdateWorkloadShare', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/workloads/{WorkloadId}/shares/{ShareId}', ], 'input' => [ 'shape' => 'UpdateWorkloadShareInput', ], 'output' => [ 'shape' => 'UpdateWorkloadShareOutput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpgradeLensReview' => [ 'name' => 'UpgradeLensReview', 'http' => [ 'method' => 'PUT', 'requestUri' => '/workloads/{WorkloadId}/lensReviews/{LensAlias}/upgrade', ], 'input' => [ 'shape' => 'UpgradeLensReviewInput', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'Answer' => [ 'type' => 'structure', 'members' => [ 'QuestionId' => [ 'shape' => 'QuestionId', ], 'PillarId' => [ 'shape' => 'PillarId', ], 'QuestionTitle' => [ 'shape' => 'QuestionTitle', ], 'QuestionDescription' => [ 'shape' => 'QuestionDescription', ], 'ImprovementPlanUrl' => [ 'shape' => 'ImprovementPlanUrl', ], 'HelpfulResourceUrl' => [ 'shape' => 'HelpfulResourceUrl', ], 'HelpfulResourceDisplayText' => [ 'shape' => 'DisplayText', ], 'Choices' => [ 'shape' => 'Choices', ], 'SelectedChoices' => [ 'shape' => 'SelectedChoices', ], 'ChoiceAnswers' => [ 'shape' => 'ChoiceAnswers', ], 'IsApplicable' => [ 'shape' => 'IsApplicable', ], 'Risk' => [ 'shape' => 'Risk', ], 'Notes' => [ 'shape' => 'Notes', ], 'Reason' => [ 'shape' => 'AnswerReason', ], ], ], 'AnswerReason' => [ 'type' => 'string', 'enum' => [ 'OUT_OF_SCOPE', 'BUSINESS_PRIORITIES', 'ARCHITECTURE_CONSTRAINTS', 'OTHER', 'NONE', ], ], 'AnswerSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnswerSummary', ], ], 'AnswerSummary' => [ 'type' => 'structure', 'members' => [ 'QuestionId' => [ 'shape' => 'QuestionId', ], 'PillarId' => [ 'shape' => 'PillarId', ], 'QuestionTitle' => [ 'shape' => 'QuestionTitle', ], 'Choices' => [ 'shape' => 'Choices', ], 'SelectedChoices' => [ 'shape' => 'SelectedChoices', ], 'ChoiceAnswerSummaries' => [ 'shape' => 'ChoiceAnswerSummaries', ], 'IsApplicable' => [ 'shape' => 'IsApplicable', ], 'Risk' => [ 'shape' => 'Risk', ], 'Reason' => [ 'shape' => 'AnswerReason', ], ], ], 'AssociateLensesInput' => [ 'type' => 'structure', 'required' => [ 'WorkloadId', 'LensAliases', ], 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', 'location' => 'uri', 'locationName' => 'WorkloadId', ], 'LensAliases' => [ 'shape' => 'LensAliases', ], ], ], 'AwsAccountId' => [ 'type' => 'string', 'pattern' => '[0-9]{12}', ], 'AwsRegion' => [ 'type' => 'string', 'max' => 100, ], 'Base64String' => [ 'type' => 'string', ], 'Choice' => [ 'type' => 'structure', 'members' => [ 'ChoiceId' => [ 'shape' => 'ChoiceId', ], 'Title' => [ 'shape' => 'ChoiceTitle', ], 'Description' => [ 'shape' => 'ChoiceDescription', ], 'HelpfulResource' => [ 'shape' => 'ChoiceContent', ], 'ImprovementPlan' => [ 'shape' => 'ChoiceContent', ], ], ], 'ChoiceAnswer' => [ 'type' => 'structure', 'members' => [ 'ChoiceId' => [ 'shape' => 'ChoiceId', ], 'Status' => [ 'shape' => 'ChoiceStatus', ], 'Reason' => [ 'shape' => 'ChoiceReason', ], 'Notes' => [ 'shape' => 'ChoiceNotes', ], ], ], 'ChoiceAnswerSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChoiceAnswerSummary', ], ], 'ChoiceAnswerSummary' => [ 'type' => 'structure', 'members' => [ 'ChoiceId' => [ 'shape' => 'ChoiceId', ], 'Status' => [ 'shape' => 'ChoiceStatus', ], 'Reason' => [ 'shape' => 'ChoiceReason', ], ], ], 'ChoiceAnswers' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChoiceAnswer', ], ], 'ChoiceContent' => [ 'type' => 'structure', 'members' => [ 'DisplayText' => [ 'shape' => 'ChoiceContentDisplayText', ], 'Url' => [ 'shape' => 'ChoiceContentUrl', ], ], ], 'ChoiceContentDisplayText' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ChoiceContentUrl' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ChoiceDescription' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ChoiceId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'ChoiceImprovementPlan' => [ 'type' => 'structure', 'members' => [ 'ChoiceId' => [ 'shape' => 'ChoiceId', ], 'DisplayText' => [ 'shape' => 'DisplayText', ], 'ImprovementPlanUrl' => [ 'shape' => 'ImprovementPlanUrl', ], ], ], 'ChoiceImprovementPlans' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChoiceImprovementPlan', ], ], 'ChoiceNotes' => [ 'type' => 'string', 'max' => 250, ], 'ChoiceReason' => [ 'type' => 'string', 'enum' => [ 'OUT_OF_SCOPE', 'BUSINESS_PRIORITIES', 'ARCHITECTURE_CONSTRAINTS', 'OTHER', 'NONE', ], ], 'ChoiceStatus' => [ 'type' => 'string', 'enum' => [ 'SELECTED', 'NOT_APPLICABLE', 'UNSELECTED', ], ], 'ChoiceTitle' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'ChoiceUpdate' => [ 'type' => 'structure', 'required' => [ 'Status', ], 'members' => [ 'Status' => [ 'shape' => 'ChoiceStatus', ], 'Reason' => [ 'shape' => 'ChoiceReason', ], 'Notes' => [ 'shape' => 'ChoiceNotes', ], ], ], 'ChoiceUpdates' => [ 'type' => 'map', 'key' => [ 'shape' => 'ChoiceId', ], 'value' => [ 'shape' => 'ChoiceUpdate', ], ], 'Choices' => [ 'type' => 'list', 'member' => [ 'shape' => 'Choice', ], ], 'ClientRequestToken' => [ 'type' => 'string', ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'Message', 'ResourceId', 'ResourceType', ], 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'ResourceId' => [ 'shape' => 'ExceptionResourceId', ], 'ResourceType' => [ 'shape' => 'ExceptionResourceType', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'Count' => [ 'type' => 'integer', 'min' => 0, ], 'CreateLensShareInput' => [ 'type' => 'structure', 'required' => [ 'LensAlias', 'SharedWith', 'ClientRequestToken', ], 'members' => [ 'LensAlias' => [ 'shape' => 'LensAlias', 'location' => 'uri', 'locationName' => 'LensAlias', ], 'SharedWith' => [ 'shape' => 'SharedWith', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'CreateLensShareOutput' => [ 'type' => 'structure', 'members' => [ 'ShareId' => [ 'shape' => 'ShareId', ], ], ], 'CreateLensVersionInput' => [ 'type' => 'structure', 'required' => [ 'LensAlias', 'LensVersion', 'ClientRequestToken', ], 'members' => [ 'LensAlias' => [ 'shape' => 'LensAlias', 'location' => 'uri', 'locationName' => 'LensAlias', ], 'LensVersion' => [ 'shape' => 'LensVersion', ], 'IsMajorVersion' => [ 'shape' => 'IsMajorVersion', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'CreateLensVersionOutput' => [ 'type' => 'structure', 'members' => [ 'LensArn' => [ 'shape' => 'LensArn', ], 'LensVersion' => [ 'shape' => 'LensVersion', ], ], ], 'CreateMilestoneInput' => [ 'type' => 'structure', 'required' => [ 'WorkloadId', 'MilestoneName', 'ClientRequestToken', ], 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', 'location' => 'uri', 'locationName' => 'WorkloadId', ], 'MilestoneName' => [ 'shape' => 'MilestoneName', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'CreateMilestoneOutput' => [ 'type' => 'structure', 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', ], 'MilestoneNumber' => [ 'shape' => 'MilestoneNumber', ], ], ], 'CreateWorkloadInput' => [ 'type' => 'structure', 'required' => [ 'WorkloadName', 'Description', 'Environment', 'ReviewOwner', 'Lenses', 'ClientRequestToken', ], 'members' => [ 'WorkloadName' => [ 'shape' => 'WorkloadName', ], 'Description' => [ 'shape' => 'WorkloadDescription', ], 'Environment' => [ 'shape' => 'WorkloadEnvironment', ], 'AccountIds' => [ 'shape' => 'WorkloadAccountIds', ], 'AwsRegions' => [ 'shape' => 'WorkloadAwsRegions', ], 'NonAwsRegions' => [ 'shape' => 'WorkloadNonAwsRegions', ], 'PillarPriorities' => [ 'shape' => 'WorkloadPillarPriorities', ], 'ArchitecturalDesign' => [ 'shape' => 'WorkloadArchitecturalDesign', ], 'ReviewOwner' => [ 'shape' => 'WorkloadReviewOwner', ], 'IndustryType' => [ 'shape' => 'WorkloadIndustryType', ], 'Industry' => [ 'shape' => 'WorkloadIndustry', ], 'Lenses' => [ 'shape' => 'WorkloadLenses', ], 'Notes' => [ 'shape' => 'Notes', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateWorkloadOutput' => [ 'type' => 'structure', 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', ], 'WorkloadArn' => [ 'shape' => 'WorkloadArn', ], ], ], 'CreateWorkloadShareInput' => [ 'type' => 'structure', 'required' => [ 'WorkloadId', 'SharedWith', 'PermissionType', 'ClientRequestToken', ], 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', 'location' => 'uri', 'locationName' => 'WorkloadId', ], 'SharedWith' => [ 'shape' => 'SharedWith', ], 'PermissionType' => [ 'shape' => 'PermissionType', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'CreateWorkloadShareOutput' => [ 'type' => 'structure', 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', ], 'ShareId' => [ 'shape' => 'ShareId', ], ], ], 'DeleteLensInput' => [ 'type' => 'structure', 'required' => [ 'LensAlias', 'ClientRequestToken', 'LensStatus', ], 'members' => [ 'LensAlias' => [ 'shape' => 'LensAlias', 'location' => 'uri', 'locationName' => 'LensAlias', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'ClientRequestToken', ], 'LensStatus' => [ 'shape' => 'LensStatusType', 'location' => 'querystring', 'locationName' => 'LensStatus', ], ], ], 'DeleteLensShareInput' => [ 'type' => 'structure', 'required' => [ 'ShareId', 'LensAlias', 'ClientRequestToken', ], 'members' => [ 'ShareId' => [ 'shape' => 'ShareId', 'location' => 'uri', 'locationName' => 'ShareId', ], 'LensAlias' => [ 'shape' => 'LensAlias', 'location' => 'uri', 'locationName' => 'LensAlias', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'ClientRequestToken', ], ], ], 'DeleteWorkloadInput' => [ 'type' => 'structure', 'required' => [ 'WorkloadId', 'ClientRequestToken', ], 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', 'location' => 'uri', 'locationName' => 'WorkloadId', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'ClientRequestToken', ], ], ], 'DeleteWorkloadShareInput' => [ 'type' => 'structure', 'required' => [ 'ShareId', 'WorkloadId', 'ClientRequestToken', ], 'members' => [ 'ShareId' => [ 'shape' => 'ShareId', 'location' => 'uri', 'locationName' => 'ShareId', ], 'WorkloadId' => [ 'shape' => 'WorkloadId', 'location' => 'uri', 'locationName' => 'WorkloadId', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'ClientRequestToken', ], ], ], 'DifferenceStatus' => [ 'type' => 'string', 'enum' => [ 'UPDATED', 'NEW', 'DELETED', ], ], 'DisassociateLensesInput' => [ 'type' => 'structure', 'required' => [ 'WorkloadId', 'LensAliases', ], 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', 'location' => 'uri', 'locationName' => 'WorkloadId', ], 'LensAliases' => [ 'shape' => 'LensAliases', ], ], ], 'DisplayText' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'ExceptionMessage' => [ 'type' => 'string', ], 'ExceptionResourceId' => [ 'type' => 'string', ], 'ExceptionResourceType' => [ 'type' => 'string', ], 'ExportLensInput' => [ 'type' => 'structure', 'required' => [ 'LensAlias', ], 'members' => [ 'LensAlias' => [ 'shape' => 'LensAlias', 'location' => 'uri', 'locationName' => 'LensAlias', ], 'LensVersion' => [ 'shape' => 'LensVersion', 'location' => 'querystring', 'locationName' => 'LensVersion', ], ], ], 'ExportLensOutput' => [ 'type' => 'structure', 'members' => [ 'LensJSON' => [ 'shape' => 'LensJSON', ], ], ], 'GetAnswerInput' => [ 'type' => 'structure', 'required' => [ 'WorkloadId', 'LensAlias', 'QuestionId', ], 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', 'location' => 'uri', 'locationName' => 'WorkloadId', ], 'LensAlias' => [ 'shape' => 'LensAlias', 'location' => 'uri', 'locationName' => 'LensAlias', ], 'QuestionId' => [ 'shape' => 'QuestionId', 'location' => 'uri', 'locationName' => 'QuestionId', ], 'MilestoneNumber' => [ 'shape' => 'MilestoneNumber', 'location' => 'querystring', 'locationName' => 'MilestoneNumber', ], ], ], 'GetAnswerOutput' => [ 'type' => 'structure', 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', ], 'MilestoneNumber' => [ 'shape' => 'MilestoneNumber', ], 'LensAlias' => [ 'shape' => 'LensAlias', ], 'LensArn' => [ 'shape' => 'LensArn', ], 'Answer' => [ 'shape' => 'Answer', ], ], ], 'GetLensInput' => [ 'type' => 'structure', 'required' => [ 'LensAlias', ], 'members' => [ 'LensAlias' => [ 'shape' => 'LensAlias', 'location' => 'uri', 'locationName' => 'LensAlias', ], 'LensVersion' => [ 'shape' => 'LensVersion', 'location' => 'querystring', 'locationName' => 'LensVersion', ], ], ], 'GetLensOutput' => [ 'type' => 'structure', 'members' => [ 'Lens' => [ 'shape' => 'Lens', ], ], ], 'GetLensReviewInput' => [ 'type' => 'structure', 'required' => [ 'WorkloadId', 'LensAlias', ], 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', 'location' => 'uri', 'locationName' => 'WorkloadId', ], 'LensAlias' => [ 'shape' => 'LensAlias', 'location' => 'uri', 'locationName' => 'LensAlias', ], 'MilestoneNumber' => [ 'shape' => 'MilestoneNumber', 'location' => 'querystring', 'locationName' => 'MilestoneNumber', ], ], ], 'GetLensReviewOutput' => [ 'type' => 'structure', 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', ], 'MilestoneNumber' => [ 'shape' => 'MilestoneNumber', ], 'LensReview' => [ 'shape' => 'LensReview', ], ], ], 'GetLensReviewReportInput' => [ 'type' => 'structure', 'required' => [ 'WorkloadId', 'LensAlias', ], 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', 'location' => 'uri', 'locationName' => 'WorkloadId', ], 'LensAlias' => [ 'shape' => 'LensAlias', 'location' => 'uri', 'locationName' => 'LensAlias', ], 'MilestoneNumber' => [ 'shape' => 'MilestoneNumber', 'location' => 'querystring', 'locationName' => 'MilestoneNumber', ], ], ], 'GetLensReviewReportOutput' => [ 'type' => 'structure', 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', ], 'MilestoneNumber' => [ 'shape' => 'MilestoneNumber', ], 'LensReviewReport' => [ 'shape' => 'LensReviewReport', ], ], ], 'GetLensVersionDifferenceInput' => [ 'type' => 'structure', 'required' => [ 'LensAlias', ], 'members' => [ 'LensAlias' => [ 'shape' => 'LensAlias', 'location' => 'uri', 'locationName' => 'LensAlias', ], 'BaseLensVersion' => [ 'shape' => 'LensVersion', 'location' => 'querystring', 'locationName' => 'BaseLensVersion', ], 'TargetLensVersion' => [ 'shape' => 'LensVersion', 'location' => 'querystring', 'locationName' => 'TargetLensVersion', ], ], ], 'GetLensVersionDifferenceOutput' => [ 'type' => 'structure', 'members' => [ 'LensAlias' => [ 'shape' => 'LensAlias', ], 'LensArn' => [ 'shape' => 'LensArn', ], 'BaseLensVersion' => [ 'shape' => 'LensVersion', ], 'TargetLensVersion' => [ 'shape' => 'LensVersion', ], 'LatestLensVersion' => [ 'shape' => 'LensVersion', ], 'VersionDifferences' => [ 'shape' => 'VersionDifferences', ], ], ], 'GetMilestoneInput' => [ 'type' => 'structure', 'required' => [ 'WorkloadId', 'MilestoneNumber', ], 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', 'location' => 'uri', 'locationName' => 'WorkloadId', ], 'MilestoneNumber' => [ 'shape' => 'MilestoneNumber', 'location' => 'uri', 'locationName' => 'MilestoneNumber', ], ], ], 'GetMilestoneOutput' => [ 'type' => 'structure', 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', ], 'Milestone' => [ 'shape' => 'Milestone', ], ], ], 'GetWorkloadInput' => [ 'type' => 'structure', 'required' => [ 'WorkloadId', ], 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', 'location' => 'uri', 'locationName' => 'WorkloadId', ], ], ], 'GetWorkloadOutput' => [ 'type' => 'structure', 'members' => [ 'Workload' => [ 'shape' => 'Workload', ], ], ], 'HelpfulResourceUrl' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'ImportLensInput' => [ 'type' => 'structure', 'required' => [ 'JSONString', 'ClientRequestToken', ], 'members' => [ 'LensAlias' => [ 'shape' => 'LensAlias', ], 'JSONString' => [ 'shape' => 'LensJSON', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'ImportLensOutput' => [ 'type' => 'structure', 'members' => [ 'LensArn' => [ 'shape' => 'LensArn', ], 'Status' => [ 'shape' => 'ImportLensStatus', ], ], ], 'ImportLensStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'COMPLETE', 'ERROR', ], ], 'ImprovementPlanUrl' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'ImprovementSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImprovementSummary', ], ], 'ImprovementSummary' => [ 'type' => 'structure', 'members' => [ 'QuestionId' => [ 'shape' => 'QuestionId', ], 'PillarId' => [ 'shape' => 'PillarId', ], 'QuestionTitle' => [ 'shape' => 'QuestionTitle', ], 'Risk' => [ 'shape' => 'Risk', ], 'ImprovementPlanUrl' => [ 'shape' => 'ImprovementPlanUrl', ], 'ImprovementPlans' => [ 'shape' => 'ChoiceImprovementPlans', ], ], ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'IsApplicable' => [ 'type' => 'boolean', ], 'IsMajorVersion' => [ 'type' => 'boolean', ], 'IsReviewOwnerUpdateAcknowledged' => [ 'type' => 'boolean', ], 'Lens' => [ 'type' => 'structure', 'members' => [ 'LensArn' => [ 'shape' => 'LensArn', ], 'LensVersion' => [ 'shape' => 'LensVersion', ], 'Name' => [ 'shape' => 'LensName', ], 'Description' => [ 'shape' => 'LensDescription', ], 'Owner' => [ 'shape' => 'LensOwner', ], 'ShareInvitationId' => [ 'shape' => 'ShareInvitationId', ], ], ], 'LensAlias' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'LensAliases' => [ 'type' => 'list', 'member' => [ 'shape' => 'LensAlias', ], 'min' => 1, ], 'LensArn' => [ 'type' => 'string', ], 'LensDescription' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'LensJSON' => [ 'type' => 'string', 'max' => 500000, 'min' => 2, ], 'LensName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'LensNamePrefix' => [ 'type' => 'string', 'max' => 100, ], 'LensOwner' => [ 'type' => 'string', ], 'LensReview' => [ 'type' => 'structure', 'members' => [ 'LensAlias' => [ 'shape' => 'LensAlias', ], 'LensArn' => [ 'shape' => 'LensArn', ], 'LensVersion' => [ 'shape' => 'LensVersion', ], 'LensName' => [ 'shape' => 'LensName', ], 'LensStatus' => [ 'shape' => 'LensStatus', ], 'PillarReviewSummaries' => [ 'shape' => 'PillarReviewSummaries', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'Notes' => [ 'shape' => 'Notes', ], 'RiskCounts' => [ 'shape' => 'RiskCounts', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'LensReviewReport' => [ 'type' => 'structure', 'members' => [ 'LensAlias' => [ 'shape' => 'LensAlias', ], 'LensArn' => [ 'shape' => 'LensArn', ], 'Base64String' => [ 'shape' => 'Base64String', ], ], ], 'LensReviewSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'LensReviewSummary', ], ], 'LensReviewSummary' => [ 'type' => 'structure', 'members' => [ 'LensAlias' => [ 'shape' => 'LensAlias', ], 'LensArn' => [ 'shape' => 'LensArn', ], 'LensVersion' => [ 'shape' => 'LensVersion', ], 'LensName' => [ 'shape' => 'LensName', ], 'LensStatus' => [ 'shape' => 'LensStatus', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'RiskCounts' => [ 'shape' => 'RiskCounts', ], ], ], 'LensShareSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'LensShareSummary', ], ], 'LensShareSummary' => [ 'type' => 'structure', 'members' => [ 'ShareId' => [ 'shape' => 'ShareId', ], 'SharedWith' => [ 'shape' => 'SharedWith', ], 'Status' => [ 'shape' => 'ShareStatus', ], ], ], 'LensStatus' => [ 'type' => 'string', 'enum' => [ 'CURRENT', 'NOT_CURRENT', 'DEPRECATED', 'DELETED', 'UNSHARED', ], ], 'LensStatusType' => [ 'type' => 'string', 'enum' => [ 'ALL', 'DRAFT', 'PUBLISHED', ], ], 'LensSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'LensSummary', ], ], 'LensSummary' => [ 'type' => 'structure', 'members' => [ 'LensArn' => [ 'shape' => 'LensArn', ], 'LensAlias' => [ 'shape' => 'LensAlias', ], 'LensName' => [ 'shape' => 'LensName', ], 'LensType' => [ 'shape' => 'LensType', ], 'Description' => [ 'shape' => 'LensDescription', ], 'CreatedAt' => [ 'shape' => 'Timestamp', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'LensVersion' => [ 'shape' => 'LensVersion', ], 'Owner' => [ 'shape' => 'AwsAccountId', ], 'LensStatus' => [ 'shape' => 'LensStatus', ], ], ], 'LensType' => [ 'type' => 'string', 'enum' => [ 'AWS_OFFICIAL', 'CUSTOM_SHARED', 'CUSTOM_SELF', ], ], 'LensUpgradeSummary' => [ 'type' => 'structure', 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', ], 'WorkloadName' => [ 'shape' => 'WorkloadName', ], 'LensAlias' => [ 'shape' => 'LensAlias', ], 'LensArn' => [ 'shape' => 'LensArn', ], 'CurrentLensVersion' => [ 'shape' => 'LensVersion', ], 'LatestLensVersion' => [ 'shape' => 'LensVersion', ], ], ], 'LensVersion' => [ 'type' => 'string', 'max' => 32, 'min' => 1, ], 'ListAnswersInput' => [ 'type' => 'structure', 'required' => [ 'WorkloadId', 'LensAlias', ], 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', 'location' => 'uri', 'locationName' => 'WorkloadId', ], 'LensAlias' => [ 'shape' => 'LensAlias', 'location' => 'uri', 'locationName' => 'LensAlias', ], 'PillarId' => [ 'shape' => 'PillarId', 'location' => 'querystring', 'locationName' => 'PillarId', ], 'MilestoneNumber' => [ 'shape' => 'MilestoneNumber', 'location' => 'querystring', 'locationName' => 'MilestoneNumber', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'ListAnswersMaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListAnswersMaxResults' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'ListAnswersOutput' => [ 'type' => 'structure', 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', ], 'MilestoneNumber' => [ 'shape' => 'MilestoneNumber', ], 'LensAlias' => [ 'shape' => 'LensAlias', ], 'LensArn' => [ 'shape' => 'LensArn', ], 'AnswerSummaries' => [ 'shape' => 'AnswerSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListLensReviewImprovementsInput' => [ 'type' => 'structure', 'required' => [ 'WorkloadId', 'LensAlias', ], 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', 'location' => 'uri', 'locationName' => 'WorkloadId', ], 'LensAlias' => [ 'shape' => 'LensAlias', 'location' => 'uri', 'locationName' => 'LensAlias', ], 'PillarId' => [ 'shape' => 'PillarId', 'location' => 'querystring', 'locationName' => 'PillarId', ], 'MilestoneNumber' => [ 'shape' => 'MilestoneNumber', 'location' => 'querystring', 'locationName' => 'MilestoneNumber', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'ListLensReviewImprovementsMaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListLensReviewImprovementsMaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'ListLensReviewImprovementsOutput' => [ 'type' => 'structure', 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', ], 'MilestoneNumber' => [ 'shape' => 'MilestoneNumber', ], 'LensAlias' => [ 'shape' => 'LensAlias', ], 'LensArn' => [ 'shape' => 'LensArn', ], 'ImprovementSummaries' => [ 'shape' => 'ImprovementSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListLensReviewsInput' => [ 'type' => 'structure', 'required' => [ 'WorkloadId', ], 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', 'location' => 'uri', 'locationName' => 'WorkloadId', ], 'MilestoneNumber' => [ 'shape' => 'MilestoneNumber', 'location' => 'querystring', 'locationName' => 'MilestoneNumber', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListLensReviewsOutput' => [ 'type' => 'structure', 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', ], 'MilestoneNumber' => [ 'shape' => 'MilestoneNumber', ], 'LensReviewSummaries' => [ 'shape' => 'LensReviewSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListLensSharesInput' => [ 'type' => 'structure', 'required' => [ 'LensAlias', ], 'members' => [ 'LensAlias' => [ 'shape' => 'LensAlias', 'location' => 'uri', 'locationName' => 'LensAlias', ], 'SharedWithPrefix' => [ 'shape' => 'SharedWithPrefix', 'location' => 'querystring', 'locationName' => 'SharedWithPrefix', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'ListWorkloadSharesMaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListLensSharesOutput' => [ 'type' => 'structure', 'members' => [ 'LensShareSummaries' => [ 'shape' => 'LensShareSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListLensesInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], 'LensType' => [ 'shape' => 'LensType', 'location' => 'querystring', 'locationName' => 'LensType', ], 'LensStatus' => [ 'shape' => 'LensStatusType', 'location' => 'querystring', 'locationName' => 'LensStatus', ], 'LensName' => [ 'shape' => 'LensName', 'location' => 'querystring', 'locationName' => 'LensName', ], ], ], 'ListLensesOutput' => [ 'type' => 'structure', 'members' => [ 'LensSummaries' => [ 'shape' => 'LensSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListMilestonesInput' => [ 'type' => 'structure', 'required' => [ 'WorkloadId', ], 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', 'location' => 'uri', 'locationName' => 'WorkloadId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListMilestonesOutput' => [ 'type' => 'structure', 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', ], 'MilestoneSummaries' => [ 'shape' => 'MilestoneSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListNotificationsInput' => [ 'type' => 'structure', 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'ListNotificationsMaxResults', ], ], ], 'ListNotificationsMaxResults' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'ListNotificationsOutput' => [ 'type' => 'structure', 'members' => [ 'NotificationSummaries' => [ 'shape' => 'NotificationSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListShareInvitationsInput' => [ 'type' => 'structure', 'members' => [ 'WorkloadNamePrefix' => [ 'shape' => 'WorkloadNamePrefix', 'location' => 'querystring', 'locationName' => 'WorkloadNamePrefix', ], 'LensNamePrefix' => [ 'shape' => 'LensNamePrefix', 'location' => 'querystring', 'locationName' => 'LensNamePrefix', ], 'ShareResourceType' => [ 'shape' => 'ShareResourceType', 'location' => 'querystring', 'locationName' => 'ShareResourceType', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'ListShareInvitationsMaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListShareInvitationsMaxResults' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'ListShareInvitationsOutput' => [ 'type' => 'structure', 'members' => [ 'ShareInvitationSummaries' => [ 'shape' => 'ShareInvitationSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceInput' => [ 'type' => 'structure', 'required' => [ 'WorkloadArn', ], 'members' => [ 'WorkloadArn' => [ 'shape' => 'WorkloadArn', 'location' => 'uri', 'locationName' => 'WorkloadArn', ], ], ], 'ListTagsForResourceOutput' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'ListWorkloadSharesInput' => [ 'type' => 'structure', 'required' => [ 'WorkloadId', ], 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', 'location' => 'uri', 'locationName' => 'WorkloadId', ], 'SharedWithPrefix' => [ 'shape' => 'SharedWithPrefix', 'location' => 'querystring', 'locationName' => 'SharedWithPrefix', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'ListWorkloadSharesMaxResults', 'location' => 'querystring', 'locationName' => 'MaxResults', ], ], ], 'ListWorkloadSharesMaxResults' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'ListWorkloadSharesOutput' => [ 'type' => 'structure', 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', ], 'WorkloadShareSummaries' => [ 'shape' => 'WorkloadShareSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListWorkloadsInput' => [ 'type' => 'structure', 'members' => [ 'WorkloadNamePrefix' => [ 'shape' => 'WorkloadNamePrefix', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'ListWorkloadsMaxResults', ], ], ], 'ListWorkloadsMaxResults' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'ListWorkloadsOutput' => [ 'type' => 'structure', 'members' => [ 'WorkloadSummaries' => [ 'shape' => 'WorkloadSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'Milestone' => [ 'type' => 'structure', 'members' => [ 'MilestoneNumber' => [ 'shape' => 'MilestoneNumber', ], 'MilestoneName' => [ 'shape' => 'MilestoneName', ], 'RecordedAt' => [ 'shape' => 'Timestamp', ], 'Workload' => [ 'shape' => 'Workload', ], ], ], 'MilestoneName' => [ 'type' => 'string', 'max' => 100, 'min' => 3, ], 'MilestoneNumber' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'MilestoneSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'MilestoneSummary', ], ], 'MilestoneSummary' => [ 'type' => 'structure', 'members' => [ 'MilestoneNumber' => [ 'shape' => 'MilestoneNumber', ], 'MilestoneName' => [ 'shape' => 'MilestoneName', ], 'RecordedAt' => [ 'shape' => 'Timestamp', ], 'WorkloadSummary' => [ 'shape' => 'WorkloadSummary', ], ], ], 'NextToken' => [ 'type' => 'string', ], 'Notes' => [ 'type' => 'string', 'max' => 2084, ], 'NotificationSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'NotificationSummary', ], ], 'NotificationSummary' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'NotificationType', ], 'LensUpgradeSummary' => [ 'shape' => 'LensUpgradeSummary', ], ], ], 'NotificationType' => [ 'type' => 'string', 'enum' => [ 'LENS_VERSION_UPGRADED', 'LENS_VERSION_DEPRECATED', ], ], 'PermissionType' => [ 'type' => 'string', 'enum' => [ 'READONLY', 'CONTRIBUTOR', ], ], 'PillarDifference' => [ 'type' => 'structure', 'members' => [ 'PillarId' => [ 'shape' => 'PillarId', ], 'PillarName' => [ 'shape' => 'PillarName', ], 'DifferenceStatus' => [ 'shape' => 'DifferenceStatus', ], 'QuestionDifferences' => [ 'shape' => 'QuestionDifferences', ], ], ], 'PillarDifferences' => [ 'type' => 'list', 'member' => [ 'shape' => 'PillarDifference', ], ], 'PillarId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'PillarName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'PillarNotes' => [ 'type' => 'map', 'key' => [ 'shape' => 'PillarId', ], 'value' => [ 'shape' => 'Notes', ], ], 'PillarReviewSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'PillarReviewSummary', ], ], 'PillarReviewSummary' => [ 'type' => 'structure', 'members' => [ 'PillarId' => [ 'shape' => 'PillarId', ], 'PillarName' => [ 'shape' => 'PillarName', ], 'Notes' => [ 'shape' => 'Notes', ], 'RiskCounts' => [ 'shape' => 'RiskCounts', ], ], ], 'QuestionDescription' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'QuestionDifference' => [ 'type' => 'structure', 'members' => [ 'QuestionId' => [ 'shape' => 'QuestionId', ], 'QuestionTitle' => [ 'shape' => 'QuestionTitle', ], 'DifferenceStatus' => [ 'shape' => 'DifferenceStatus', ], ], ], 'QuestionDifferences' => [ 'type' => 'list', 'member' => [ 'shape' => 'QuestionDifference', ], ], 'QuestionId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'QuestionTitle' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'QuotaCode' => [ 'type' => 'string', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'Message', 'ResourceId', 'ResourceType', ], 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'ResourceId' => [ 'shape' => 'ExceptionResourceId', ], 'ResourceType' => [ 'shape' => 'ExceptionResourceType', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'Risk' => [ 'type' => 'string', 'enum' => [ 'UNANSWERED', 'HIGH', 'MEDIUM', 'NONE', 'NOT_APPLICABLE', ], ], 'RiskCounts' => [ 'type' => 'map', 'key' => [ 'shape' => 'Risk', ], 'value' => [ 'shape' => 'Count', ], ], 'SelectedChoices' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChoiceId', ], ], 'ServiceCode' => [ 'type' => 'string', ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'Message', 'QuotaCode', 'ServiceCode', ], 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'ResourceId' => [ 'shape' => 'ExceptionResourceId', ], 'ResourceType' => [ 'shape' => 'ExceptionResourceType', ], 'QuotaCode' => [ 'shape' => 'QuotaCode', ], 'ServiceCode' => [ 'shape' => 'ServiceCode', ], ], 'error' => [ 'httpStatusCode' => 402, ], 'exception' => true, ], 'ShareId' => [ 'type' => 'string', 'pattern' => '[0-9a-f]{32}', ], 'ShareInvitation' => [ 'type' => 'structure', 'members' => [ 'ShareInvitationId' => [ 'shape' => 'ShareInvitationId', ], 'ShareResourceType' => [ 'shape' => 'ShareResourceType', ], 'WorkloadId' => [ 'shape' => 'WorkloadId', ], 'LensAlias' => [ 'shape' => 'LensAlias', ], 'LensArn' => [ 'shape' => 'LensArn', ], ], ], 'ShareInvitationAction' => [ 'type' => 'string', 'enum' => [ 'ACCEPT', 'REJECT', ], ], 'ShareInvitationId' => [ 'type' => 'string', 'pattern' => '[0-9a-f]{32}', ], 'ShareInvitationSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ShareInvitationSummary', ], ], 'ShareInvitationSummary' => [ 'type' => 'structure', 'members' => [ 'ShareInvitationId' => [ 'shape' => 'ShareInvitationId', ], 'SharedBy' => [ 'shape' => 'AwsAccountId', ], 'SharedWith' => [ 'shape' => 'SharedWith', ], 'PermissionType' => [ 'shape' => 'PermissionType', ], 'ShareResourceType' => [ 'shape' => 'ShareResourceType', ], 'WorkloadName' => [ 'shape' => 'WorkloadName', ], 'WorkloadId' => [ 'shape' => 'WorkloadId', ], 'LensName' => [ 'shape' => 'LensName', ], 'LensArn' => [ 'shape' => 'LensArn', ], ], ], 'ShareResourceType' => [ 'type' => 'string', 'enum' => [ 'WORKLOAD', 'LENS', ], ], 'ShareStatus' => [ 'type' => 'string', 'enum' => [ 'ACCEPTED', 'REJECTED', 'PENDING', 'REVOKED', 'EXPIRED', ], ], 'SharedWith' => [ 'type' => 'string', 'max' => 2048, 'min' => 12, ], 'SharedWithPrefix' => [ 'type' => 'string', 'max' => 100, ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 1, ], 'TagResourceInput' => [ 'type' => 'structure', 'required' => [ 'WorkloadArn', 'Tags', ], 'members' => [ 'WorkloadArn' => [ 'shape' => 'WorkloadArn', 'location' => 'uri', 'locationName' => 'WorkloadArn', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceOutput' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'QuotaCode' => [ 'shape' => 'QuotaCode', ], 'ServiceCode' => [ 'shape' => 'ServiceCode', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UntagResourceInput' => [ 'type' => 'structure', 'required' => [ 'WorkloadArn', 'TagKeys', ], 'members' => [ 'WorkloadArn' => [ 'shape' => 'WorkloadArn', 'location' => 'uri', 'locationName' => 'WorkloadArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceOutput' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAnswerInput' => [ 'type' => 'structure', 'required' => [ 'WorkloadId', 'LensAlias', 'QuestionId', ], 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', 'location' => 'uri', 'locationName' => 'WorkloadId', ], 'LensAlias' => [ 'shape' => 'LensAlias', 'location' => 'uri', 'locationName' => 'LensAlias', ], 'QuestionId' => [ 'shape' => 'QuestionId', 'location' => 'uri', 'locationName' => 'QuestionId', ], 'SelectedChoices' => [ 'shape' => 'SelectedChoices', ], 'ChoiceUpdates' => [ 'shape' => 'ChoiceUpdates', ], 'Notes' => [ 'shape' => 'Notes', ], 'IsApplicable' => [ 'shape' => 'IsApplicable', ], 'Reason' => [ 'shape' => 'AnswerReason', ], ], ], 'UpdateAnswerOutput' => [ 'type' => 'structure', 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', ], 'LensAlias' => [ 'shape' => 'LensAlias', ], 'LensArn' => [ 'shape' => 'LensArn', ], 'Answer' => [ 'shape' => 'Answer', ], ], ], 'UpdateLensReviewInput' => [ 'type' => 'structure', 'required' => [ 'WorkloadId', 'LensAlias', ], 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', 'location' => 'uri', 'locationName' => 'WorkloadId', ], 'LensAlias' => [ 'shape' => 'LensAlias', 'location' => 'uri', 'locationName' => 'LensAlias', ], 'LensNotes' => [ 'shape' => 'Notes', ], 'PillarNotes' => [ 'shape' => 'PillarNotes', ], ], ], 'UpdateLensReviewOutput' => [ 'type' => 'structure', 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', ], 'LensReview' => [ 'shape' => 'LensReview', ], ], ], 'UpdateShareInvitationInput' => [ 'type' => 'structure', 'required' => [ 'ShareInvitationId', 'ShareInvitationAction', ], 'members' => [ 'ShareInvitationId' => [ 'shape' => 'ShareInvitationId', 'location' => 'uri', 'locationName' => 'ShareInvitationId', ], 'ShareInvitationAction' => [ 'shape' => 'ShareInvitationAction', ], ], ], 'UpdateShareInvitationOutput' => [ 'type' => 'structure', 'members' => [ 'ShareInvitation' => [ 'shape' => 'ShareInvitation', ], ], ], 'UpdateWorkloadInput' => [ 'type' => 'structure', 'required' => [ 'WorkloadId', ], 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', 'location' => 'uri', 'locationName' => 'WorkloadId', ], 'WorkloadName' => [ 'shape' => 'WorkloadName', ], 'Description' => [ 'shape' => 'WorkloadDescription', ], 'Environment' => [ 'shape' => 'WorkloadEnvironment', ], 'AccountIds' => [ 'shape' => 'WorkloadAccountIds', ], 'AwsRegions' => [ 'shape' => 'WorkloadAwsRegions', ], 'NonAwsRegions' => [ 'shape' => 'WorkloadNonAwsRegions', ], 'PillarPriorities' => [ 'shape' => 'WorkloadPillarPriorities', ], 'ArchitecturalDesign' => [ 'shape' => 'WorkloadArchitecturalDesign', ], 'ReviewOwner' => [ 'shape' => 'WorkloadReviewOwner', ], 'IsReviewOwnerUpdateAcknowledged' => [ 'shape' => 'IsReviewOwnerUpdateAcknowledged', ], 'IndustryType' => [ 'shape' => 'WorkloadIndustryType', ], 'Industry' => [ 'shape' => 'WorkloadIndustry', ], 'Notes' => [ 'shape' => 'Notes', ], 'ImprovementStatus' => [ 'shape' => 'WorkloadImprovementStatus', ], ], ], 'UpdateWorkloadOutput' => [ 'type' => 'structure', 'members' => [ 'Workload' => [ 'shape' => 'Workload', ], ], ], 'UpdateWorkloadShareInput' => [ 'type' => 'structure', 'required' => [ 'ShareId', 'WorkloadId', 'PermissionType', ], 'members' => [ 'ShareId' => [ 'shape' => 'ShareId', 'location' => 'uri', 'locationName' => 'ShareId', ], 'WorkloadId' => [ 'shape' => 'WorkloadId', 'location' => 'uri', 'locationName' => 'WorkloadId', ], 'PermissionType' => [ 'shape' => 'PermissionType', ], ], ], 'UpdateWorkloadShareOutput' => [ 'type' => 'structure', 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', ], 'WorkloadShare' => [ 'shape' => 'WorkloadShare', ], ], ], 'UpgradeLensReviewInput' => [ 'type' => 'structure', 'required' => [ 'WorkloadId', 'LensAlias', 'MilestoneName', ], 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', 'location' => 'uri', 'locationName' => 'WorkloadId', ], 'LensAlias' => [ 'shape' => 'LensAlias', 'location' => 'uri', 'locationName' => 'LensAlias', ], 'MilestoneName' => [ 'shape' => 'MilestoneName', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'Reason' => [ 'shape' => 'ValidationExceptionReason', ], 'Fields' => [ 'shape' => 'ValidationExceptionFieldList', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'Name', 'Message', ], 'members' => [ 'Name' => [ 'shape' => 'ValidationExceptionFieldName', ], 'Message' => [ 'shape' => 'ExceptionMessage', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionFieldName' => [ 'type' => 'string', ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'UNKNOWN_OPERATION', 'CANNOT_PARSE', 'FIELD_VALIDATION_FAILED', 'OTHER', ], ], 'VersionDifferences' => [ 'type' => 'structure', 'members' => [ 'PillarDifferences' => [ 'shape' => 'PillarDifferences', ], ], ], 'Workload' => [ 'type' => 'structure', 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', ], 'WorkloadArn' => [ 'shape' => 'WorkloadArn', ], 'WorkloadName' => [ 'shape' => 'WorkloadName', ], 'Description' => [ 'shape' => 'WorkloadDescription', ], 'Environment' => [ 'shape' => 'WorkloadEnvironment', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'AccountIds' => [ 'shape' => 'WorkloadAccountIds', ], 'AwsRegions' => [ 'shape' => 'WorkloadAwsRegions', ], 'NonAwsRegions' => [ 'shape' => 'WorkloadNonAwsRegions', ], 'ArchitecturalDesign' => [ 'shape' => 'WorkloadArchitecturalDesign', ], 'ReviewOwner' => [ 'shape' => 'WorkloadReviewOwner', ], 'ReviewRestrictionDate' => [ 'shape' => 'Timestamp', ], 'IsReviewOwnerUpdateAcknowledged' => [ 'shape' => 'IsReviewOwnerUpdateAcknowledged', ], 'IndustryType' => [ 'shape' => 'WorkloadIndustryType', ], 'Industry' => [ 'shape' => 'WorkloadIndustry', ], 'Notes' => [ 'shape' => 'Notes', ], 'ImprovementStatus' => [ 'shape' => 'WorkloadImprovementStatus', ], 'RiskCounts' => [ 'shape' => 'RiskCounts', ], 'PillarPriorities' => [ 'shape' => 'WorkloadPillarPriorities', ], 'Lenses' => [ 'shape' => 'WorkloadLenses', ], 'Owner' => [ 'shape' => 'AwsAccountId', ], 'ShareInvitationId' => [ 'shape' => 'ShareInvitationId', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'WorkloadAccountIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsAccountId', ], 'max' => 100, ], 'WorkloadArchitecturalDesign' => [ 'type' => 'string', 'max' => 2048, ], 'WorkloadArn' => [ 'type' => 'string', ], 'WorkloadAwsRegions' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsRegion', ], 'max' => 50, ], 'WorkloadDescription' => [ 'type' => 'string', 'max' => 250, 'min' => 3, ], 'WorkloadEnvironment' => [ 'type' => 'string', 'enum' => [ 'PRODUCTION', 'PREPRODUCTION', ], ], 'WorkloadId' => [ 'type' => 'string', 'pattern' => '[0-9a-f]{32}', ], 'WorkloadImprovementStatus' => [ 'type' => 'string', 'enum' => [ 'NOT_APPLICABLE', 'NOT_STARTED', 'IN_PROGRESS', 'COMPLETE', 'RISK_ACKNOWLEDGED', ], ], 'WorkloadIndustry' => [ 'type' => 'string', 'max' => 100, ], 'WorkloadIndustryType' => [ 'type' => 'string', 'max' => 100, ], 'WorkloadLenses' => [ 'type' => 'list', 'member' => [ 'shape' => 'LensAlias', ], ], 'WorkloadName' => [ 'type' => 'string', 'max' => 100, 'min' => 3, ], 'WorkloadNamePrefix' => [ 'type' => 'string', 'max' => 100, ], 'WorkloadNonAwsRegion' => [ 'type' => 'string', 'max' => 25, 'min' => 3, ], 'WorkloadNonAwsRegions' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkloadNonAwsRegion', ], 'max' => 5, ], 'WorkloadPillarPriorities' => [ 'type' => 'list', 'member' => [ 'shape' => 'PillarId', ], ], 'WorkloadReviewOwner' => [ 'type' => 'string', 'max' => 255, 'min' => 3, ], 'WorkloadShare' => [ 'type' => 'structure', 'members' => [ 'ShareId' => [ 'shape' => 'ShareId', ], 'SharedBy' => [ 'shape' => 'AwsAccountId', ], 'SharedWith' => [ 'shape' => 'SharedWith', ], 'PermissionType' => [ 'shape' => 'PermissionType', ], 'Status' => [ 'shape' => 'ShareStatus', ], 'WorkloadName' => [ 'shape' => 'WorkloadName', ], 'WorkloadId' => [ 'shape' => 'WorkloadId', ], ], ], 'WorkloadShareSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkloadShareSummary', ], ], 'WorkloadShareSummary' => [ 'type' => 'structure', 'members' => [ 'ShareId' => [ 'shape' => 'ShareId', ], 'SharedWith' => [ 'shape' => 'SharedWith', ], 'PermissionType' => [ 'shape' => 'PermissionType', ], 'Status' => [ 'shape' => 'ShareStatus', ], ], ], 'WorkloadSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'WorkloadSummary', ], ], 'WorkloadSummary' => [ 'type' => 'structure', 'members' => [ 'WorkloadId' => [ 'shape' => 'WorkloadId', ], 'WorkloadArn' => [ 'shape' => 'WorkloadArn', ], 'WorkloadName' => [ 'shape' => 'WorkloadName', ], 'Owner' => [ 'shape' => 'AwsAccountId', ], 'UpdatedAt' => [ 'shape' => 'Timestamp', ], 'Lenses' => [ 'shape' => 'WorkloadLenses', ], 'RiskCounts' => [ 'shape' => 'RiskCounts', ], 'ImprovementStatus' => [ 'shape' => 'WorkloadImprovementStatus', ], ], ], ],];
