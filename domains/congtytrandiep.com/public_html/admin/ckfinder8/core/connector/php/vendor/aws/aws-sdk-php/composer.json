{"name": "aws/aws-sdk-php", "homepage": "http://aws.amazon.com/sdkforphp", "description": "AWS SDK for PHP - Use Amazon Web Services in your PHP project", "keywords": ["aws", "amazon", "sdk", "s3", "ec2", "dynamodb", "cloud", "glacier"], "type": "library", "license": "Apache-2.0", "authors": [{"name": "Amazon Web Services", "homepage": "http://aws.amazon.com"}], "support": {"forum": "https://forums.aws.amazon.com/forum.jspa?forumID=80", "issues": "https://github.com/aws/aws-sdk-php/issues"}, "require": {"php": ">=5.5", "guzzlehttp/guzzle": "^5.3.3|^6.2.1|^7.0", "guzzlehttp/psr7": "^1.7.0|^2.0", "guzzlehttp/promises": "^1.4.0", "mtdowling/jmespath.php": "^2.6", "ext-pcre": "*", "ext-json": "*", "ext-simplexml": "*", "aws/aws-crt-php": "^1.0.2"}, "require-dev": {"ext-openssl": "*", "ext-dom": "*", "ext-pcntl": "*", "ext-sockets": "*", "phpunit/phpunit": "^4.8.35|^5.4.3", "behat/behat": "~3.0", "doctrine/cache": "~1.4", "aws/aws-php-sns-message-validator": "~1.0", "nette/neon": "^2.3", "andrewsville/php-token-reflection": "^1.4", "psr/cache": "^1.0", "psr/simple-cache": "^1.0", "paragonie/random_compat": ">= 2", "sebastian/comparator": "^1.2.3"}, "suggest": {"ext-openssl": "Allows working with CloudFront private distributions and verifying received SNS messages", "ext-curl": "To send requests using cURL", "ext-sockets": "To use client-side monitoring", "doctrine/cache": "To use the DoctrineCacheAdapter", "aws/aws-php-sns-message-validator": "To validate incoming SNS notifications"}, "autoload": {"psr-4": {"Aws\\": "src/"}, "files": ["src/functions.php"]}, "autoload-dev": {"psr-4": {"Aws\\Test\\": "tests/"}, "classmap": ["build/"]}, "extra": {"branch-alias": {"dev-master": "3.0-dev"}}}