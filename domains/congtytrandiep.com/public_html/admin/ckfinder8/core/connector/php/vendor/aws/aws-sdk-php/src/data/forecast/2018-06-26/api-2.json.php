<?php
// This file was auto-generated from sdk-root/src/data/forecast/2018-06-26/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-06-26', 'endpointPrefix' => 'forecast', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceFullName' => 'Amazon Forecast Service', 'serviceId' => 'forecast', 'signatureVersion' => 'v4', 'signingName' => 'forecast', 'targetPrefix' => 'AmazonForecast', 'uid' => 'forecast-2018-06-26', ], 'operations' => [ 'CreateAutoPredictor' => [ 'name' => 'CreateAutoPredictor', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAutoPredictorRequest', ], 'output' => [ 'shape' => 'CreateAutoPredictorResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateDataset' => [ 'name' => 'CreateDataset', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDatasetRequest', ], 'output' => [ 'shape' => 'CreateDatasetResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateDatasetGroup' => [ 'name' => 'CreateDatasetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDatasetGroupRequest', ], 'output' => [ 'shape' => 'CreateDatasetGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateDatasetImportJob' => [ 'name' => 'CreateDatasetImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDatasetImportJobRequest', ], 'output' => [ 'shape' => 'CreateDatasetImportJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateExplainability' => [ 'name' => 'CreateExplainability', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateExplainabilityRequest', ], 'output' => [ 'shape' => 'CreateExplainabilityResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateExplainabilityExport' => [ 'name' => 'CreateExplainabilityExport', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateExplainabilityExportRequest', ], 'output' => [ 'shape' => 'CreateExplainabilityExportResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateForecast' => [ 'name' => 'CreateForecast', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateForecastRequest', ], 'output' => [ 'shape' => 'CreateForecastResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateForecastExportJob' => [ 'name' => 'CreateForecastExportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateForecastExportJobRequest', ], 'output' => [ 'shape' => 'CreateForecastExportJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreatePredictor' => [ 'name' => 'CreatePredictor', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePredictorRequest', ], 'output' => [ 'shape' => 'CreatePredictorResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreatePredictorBacktestExportJob' => [ 'name' => 'CreatePredictorBacktestExportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePredictorBacktestExportJobRequest', ], 'output' => [ 'shape' => 'CreatePredictorBacktestExportJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'DeleteDataset' => [ 'name' => 'DeleteDataset', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDatasetRequest', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'DeleteDatasetGroup' => [ 'name' => 'DeleteDatasetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDatasetGroupRequest', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'DeleteDatasetImportJob' => [ 'name' => 'DeleteDatasetImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDatasetImportJobRequest', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'DeleteExplainability' => [ 'name' => 'DeleteExplainability', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteExplainabilityRequest', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'DeleteExplainabilityExport' => [ 'name' => 'DeleteExplainabilityExport', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteExplainabilityExportRequest', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'DeleteForecast' => [ 'name' => 'DeleteForecast', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteForecastRequest', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'DeleteForecastExportJob' => [ 'name' => 'DeleteForecastExportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteForecastExportJobRequest', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'DeletePredictor' => [ 'name' => 'DeletePredictor', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePredictorRequest', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'DeletePredictorBacktestExportJob' => [ 'name' => 'DeletePredictorBacktestExportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePredictorBacktestExportJobRequest', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'DeleteResourceTree' => [ 'name' => 'DeleteResourceTree', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteResourceTreeRequest', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'DescribeAutoPredictor' => [ 'name' => 'DescribeAutoPredictor', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAutoPredictorRequest', ], 'output' => [ 'shape' => 'DescribeAutoPredictorResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeDataset' => [ 'name' => 'DescribeDataset', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDatasetRequest', ], 'output' => [ 'shape' => 'DescribeDatasetResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeDatasetGroup' => [ 'name' => 'DescribeDatasetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDatasetGroupRequest', ], 'output' => [ 'shape' => 'DescribeDatasetGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeDatasetImportJob' => [ 'name' => 'DescribeDatasetImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDatasetImportJobRequest', ], 'output' => [ 'shape' => 'DescribeDatasetImportJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeExplainability' => [ 'name' => 'DescribeExplainability', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeExplainabilityRequest', ], 'output' => [ 'shape' => 'DescribeExplainabilityResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeExplainabilityExport' => [ 'name' => 'DescribeExplainabilityExport', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeExplainabilityExportRequest', ], 'output' => [ 'shape' => 'DescribeExplainabilityExportResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeForecast' => [ 'name' => 'DescribeForecast', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeForecastRequest', ], 'output' => [ 'shape' => 'DescribeForecastResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribeForecastExportJob' => [ 'name' => 'DescribeForecastExportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeForecastExportJobRequest', ], 'output' => [ 'shape' => 'DescribeForecastExportJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribePredictor' => [ 'name' => 'DescribePredictor', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePredictorRequest', ], 'output' => [ 'shape' => 'DescribePredictorResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DescribePredictorBacktestExportJob' => [ 'name' => 'DescribePredictorBacktestExportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePredictorBacktestExportJobRequest', ], 'output' => [ 'shape' => 'DescribePredictorBacktestExportJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'GetAccuracyMetrics' => [ 'name' => 'GetAccuracyMetrics', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAccuracyMetricsRequest', ], 'output' => [ 'shape' => 'GetAccuracyMetricsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], 'ListDatasetGroups' => [ 'name' => 'ListDatasetGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDatasetGroupsRequest', ], 'output' => [ 'shape' => 'ListDatasetGroupsResponse', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'ListDatasetImportJobs' => [ 'name' => 'ListDatasetImportJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDatasetImportJobsRequest', ], 'output' => [ 'shape' => 'ListDatasetImportJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidInputException', ], ], 'idempotent' => true, ], 'ListDatasets' => [ 'name' => 'ListDatasets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDatasetsRequest', ], 'output' => [ 'shape' => 'ListDatasetsResponse', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], ], 'idempotent' => true, ], 'ListExplainabilities' => [ 'name' => 'ListExplainabilities', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListExplainabilitiesRequest', ], 'output' => [ 'shape' => 'ListExplainabilitiesResponse', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidInputException', ], ], 'idempotent' => true, ], 'ListExplainabilityExports' => [ 'name' => 'ListExplainabilityExports', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListExplainabilityExportsRequest', ], 'output' => [ 'shape' => 'ListExplainabilityExportsResponse', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidInputException', ], ], 'idempotent' => true, ], 'ListForecastExportJobs' => [ 'name' => 'ListForecastExportJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListForecastExportJobsRequest', ], 'output' => [ 'shape' => 'ListForecastExportJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidInputException', ], ], 'idempotent' => true, ], 'ListForecasts' => [ 'name' => 'ListForecasts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListForecastsRequest', ], 'output' => [ 'shape' => 'ListForecastsResponse', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidInputException', ], ], 'idempotent' => true, ], 'ListPredictorBacktestExportJobs' => [ 'name' => 'ListPredictorBacktestExportJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPredictorBacktestExportJobsRequest', ], 'output' => [ 'shape' => 'ListPredictorBacktestExportJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidInputException', ], ], 'idempotent' => true, ], 'ListPredictors' => [ 'name' => 'ListPredictors', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPredictorsRequest', ], 'output' => [ 'shape' => 'ListPredictorsResponse', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidInputException', ], ], 'idempotent' => true, ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'StopResource' => [ 'name' => 'StopResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopResourceRequest', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'UpdateDatasetGroup' => [ 'name' => 'UpdateDatasetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDatasetGroupRequest', ], 'output' => [ 'shape' => 'UpdateDatasetGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AdditionalDataset' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], 'Configuration' => [ 'shape' => 'Configuration', ], ], ], 'AdditionalDatasets' => [ 'type' => 'list', 'member' => [ 'shape' => 'AdditionalDataset', ], 'max' => 2, 'min' => 1, ], 'Arn' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^[a-zA-Z0-9\\-\\_\\.\\/\\:]+$', ], 'ArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], ], 'AttributeConfig' => [ 'type' => 'structure', 'required' => [ 'AttributeName', 'Transformations', ], 'members' => [ 'AttributeName' => [ 'shape' => 'Name', ], 'Transformations' => [ 'shape' => 'Transformations', ], ], ], 'AttributeConfigs' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeConfig', ], 'max' => 50, 'min' => 1, ], 'AttributeType' => [ 'type' => 'string', 'enum' => [ 'string', 'integer', 'float', 'timestamp', 'geolocation', ], ], 'AutoMLOverrideStrategy' => [ 'type' => 'string', 'enum' => [ 'LatencyOptimized', 'AccuracyOptimized', ], ], 'Boolean' => [ 'type' => 'boolean', ], 'CategoricalParameterRange' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], 'Values' => [ 'shape' => 'Values', ], ], ], 'CategoricalParameterRanges' => [ 'type' => 'list', 'member' => [ 'shape' => 'CategoricalParameterRange', ], 'max' => 20, 'min' => 1, ], 'Configuration' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'Values', ], ], 'ContinuousParameterRange' => [ 'type' => 'structure', 'required' => [ 'Name', 'MaxValue', 'MinValue', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], 'MaxValue' => [ 'shape' => 'Double', ], 'MinValue' => [ 'shape' => 'Double', ], 'ScalingType' => [ 'shape' => 'ScalingType', ], ], ], 'ContinuousParameterRanges' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContinuousParameterRange', ], 'max' => 20, 'min' => 1, ], 'CreateAutoPredictorRequest' => [ 'type' => 'structure', 'required' => [ 'PredictorName', ], 'members' => [ 'PredictorName' => [ 'shape' => 'Name', ], 'ForecastHorizon' => [ 'shape' => 'Integer', ], 'ForecastTypes' => [ 'shape' => 'ForecastTypes', ], 'ForecastDimensions' => [ 'shape' => 'ForecastDimensions', ], 'ForecastFrequency' => [ 'shape' => 'Frequency', ], 'DataConfig' => [ 'shape' => 'DataConfig', ], 'EncryptionConfig' => [ 'shape' => 'EncryptionConfig', ], 'ReferencePredictorArn' => [ 'shape' => 'Arn', ], 'OptimizationMetric' => [ 'shape' => 'OptimizationMetric', ], 'ExplainPredictor' => [ 'shape' => 'Boolean', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateAutoPredictorResponse' => [ 'type' => 'structure', 'members' => [ 'PredictorArn' => [ 'shape' => 'Arn', ], ], ], 'CreateDatasetGroupRequest' => [ 'type' => 'structure', 'required' => [ 'DatasetGroupName', 'Domain', ], 'members' => [ 'DatasetGroupName' => [ 'shape' => 'Name', ], 'Domain' => [ 'shape' => 'Domain', ], 'DatasetArns' => [ 'shape' => 'ArnList', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateDatasetGroupResponse' => [ 'type' => 'structure', 'members' => [ 'DatasetGroupArn' => [ 'shape' => 'Arn', ], ], ], 'CreateDatasetImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'DatasetImportJobName', 'DatasetArn', 'DataSource', ], 'members' => [ 'DatasetImportJobName' => [ 'shape' => 'Name', ], 'DatasetArn' => [ 'shape' => 'Arn', ], 'DataSource' => [ 'shape' => 'DataSource', ], 'TimestampFormat' => [ 'shape' => 'TimestampFormat', ], 'TimeZone' => [ 'shape' => 'TimeZone', ], 'UseGeolocationForTimeZone' => [ 'shape' => 'UseGeolocationForTimeZone', ], 'GeolocationFormat' => [ 'shape' => 'GeolocationFormat', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateDatasetImportJobResponse' => [ 'type' => 'structure', 'members' => [ 'DatasetImportJobArn' => [ 'shape' => 'Arn', ], ], ], 'CreateDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'DatasetName', 'Domain', 'DatasetType', 'Schema', ], 'members' => [ 'DatasetName' => [ 'shape' => 'Name', ], 'Domain' => [ 'shape' => 'Domain', ], 'DatasetType' => [ 'shape' => 'DatasetType', ], 'DataFrequency' => [ 'shape' => 'Frequency', ], 'Schema' => [ 'shape' => 'Schema', ], 'EncryptionConfig' => [ 'shape' => 'EncryptionConfig', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateDatasetResponse' => [ 'type' => 'structure', 'members' => [ 'DatasetArn' => [ 'shape' => 'Arn', ], ], ], 'CreateExplainabilityExportRequest' => [ 'type' => 'structure', 'required' => [ 'ExplainabilityExportName', 'ExplainabilityArn', 'Destination', ], 'members' => [ 'ExplainabilityExportName' => [ 'shape' => 'Name', ], 'ExplainabilityArn' => [ 'shape' => 'Arn', ], 'Destination' => [ 'shape' => 'DataDestination', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateExplainabilityExportResponse' => [ 'type' => 'structure', 'members' => [ 'ExplainabilityExportArn' => [ 'shape' => 'Arn', ], ], ], 'CreateExplainabilityRequest' => [ 'type' => 'structure', 'required' => [ 'ExplainabilityName', 'ResourceArn', 'ExplainabilityConfig', ], 'members' => [ 'ExplainabilityName' => [ 'shape' => 'Name', ], 'ResourceArn' => [ 'shape' => 'Arn', ], 'ExplainabilityConfig' => [ 'shape' => 'ExplainabilityConfig', ], 'DataSource' => [ 'shape' => 'DataSource', ], 'Schema' => [ 'shape' => 'Schema', ], 'EnableVisualization' => [ 'shape' => 'Boolean', ], 'StartDateTime' => [ 'shape' => 'LocalDateTime', ], 'EndDateTime' => [ 'shape' => 'LocalDateTime', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateExplainabilityResponse' => [ 'type' => 'structure', 'members' => [ 'ExplainabilityArn' => [ 'shape' => 'Arn', ], ], ], 'CreateForecastExportJobRequest' => [ 'type' => 'structure', 'required' => [ 'ForecastExportJobName', 'ForecastArn', 'Destination', ], 'members' => [ 'ForecastExportJobName' => [ 'shape' => 'Name', ], 'ForecastArn' => [ 'shape' => 'Arn', ], 'Destination' => [ 'shape' => 'DataDestination', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateForecastExportJobResponse' => [ 'type' => 'structure', 'members' => [ 'ForecastExportJobArn' => [ 'shape' => 'Arn', ], ], ], 'CreateForecastRequest' => [ 'type' => 'structure', 'required' => [ 'ForecastName', 'PredictorArn', ], 'members' => [ 'ForecastName' => [ 'shape' => 'Name', ], 'PredictorArn' => [ 'shape' => 'Arn', ], 'ForecastTypes' => [ 'shape' => 'ForecastTypes', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateForecastResponse' => [ 'type' => 'structure', 'members' => [ 'ForecastArn' => [ 'shape' => 'Arn', ], ], ], 'CreatePredictorBacktestExportJobRequest' => [ 'type' => 'structure', 'required' => [ 'PredictorBacktestExportJobName', 'PredictorArn', 'Destination', ], 'members' => [ 'PredictorBacktestExportJobName' => [ 'shape' => 'Name', ], 'PredictorArn' => [ 'shape' => 'Arn', ], 'Destination' => [ 'shape' => 'DataDestination', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreatePredictorBacktestExportJobResponse' => [ 'type' => 'structure', 'members' => [ 'PredictorBacktestExportJobArn' => [ 'shape' => 'Arn', ], ], ], 'CreatePredictorRequest' => [ 'type' => 'structure', 'required' => [ 'PredictorName', 'ForecastHorizon', 'InputDataConfig', 'FeaturizationConfig', ], 'members' => [ 'PredictorName' => [ 'shape' => 'Name', ], 'AlgorithmArn' => [ 'shape' => 'Arn', ], 'ForecastHorizon' => [ 'shape' => 'Integer', ], 'ForecastTypes' => [ 'shape' => 'ForecastTypes', ], 'PerformAutoML' => [ 'shape' => 'Boolean', ], 'AutoMLOverrideStrategy' => [ 'shape' => 'AutoMLOverrideStrategy', ], 'PerformHPO' => [ 'shape' => 'Boolean', ], 'TrainingParameters' => [ 'shape' => 'TrainingParameters', ], 'EvaluationParameters' => [ 'shape' => 'EvaluationParameters', ], 'HPOConfig' => [ 'shape' => 'HyperParameterTuningJobConfig', ], 'InputDataConfig' => [ 'shape' => 'InputDataConfig', ], 'FeaturizationConfig' => [ 'shape' => 'FeaturizationConfig', ], 'EncryptionConfig' => [ 'shape' => 'EncryptionConfig', ], 'Tags' => [ 'shape' => 'Tags', ], 'OptimizationMetric' => [ 'shape' => 'OptimizationMetric', ], ], ], 'CreatePredictorResponse' => [ 'type' => 'structure', 'members' => [ 'PredictorArn' => [ 'shape' => 'Arn', ], ], ], 'DataConfig' => [ 'type' => 'structure', 'required' => [ 'DatasetGroupArn', ], 'members' => [ 'DatasetGroupArn' => [ 'shape' => 'Arn', ], 'AttributeConfigs' => [ 'shape' => 'AttributeConfigs', ], 'AdditionalDatasets' => [ 'shape' => 'AdditionalDatasets', ], ], ], 'DataDestination' => [ 'type' => 'structure', 'required' => [ 'S3Config', ], 'members' => [ 'S3Config' => [ 'shape' => 'S3Config', ], ], ], 'DataSource' => [ 'type' => 'structure', 'required' => [ 'S3Config', ], 'members' => [ 'S3Config' => [ 'shape' => 'S3Config', ], ], ], 'DatasetGroupSummary' => [ 'type' => 'structure', 'members' => [ 'DatasetGroupArn' => [ 'shape' => 'Arn', ], 'DatasetGroupName' => [ 'shape' => 'Name', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModificationTime' => [ 'shape' => 'Timestamp', ], ], ], 'DatasetGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatasetGroupSummary', ], ], 'DatasetImportJobSummary' => [ 'type' => 'structure', 'members' => [ 'DatasetImportJobArn' => [ 'shape' => 'Arn', ], 'DatasetImportJobName' => [ 'shape' => 'Name', ], 'DataSource' => [ 'shape' => 'DataSource', ], 'Status' => [ 'shape' => 'Status', ], 'Message' => [ 'shape' => 'ErrorMessage', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModificationTime' => [ 'shape' => 'Timestamp', ], ], ], 'DatasetImportJobs' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatasetImportJobSummary', ], ], 'DatasetSummary' => [ 'type' => 'structure', 'members' => [ 'DatasetArn' => [ 'shape' => 'Arn', ], 'DatasetName' => [ 'shape' => 'Name', ], 'DatasetType' => [ 'shape' => 'DatasetType', ], 'Domain' => [ 'shape' => 'Domain', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModificationTime' => [ 'shape' => 'Timestamp', ], ], ], 'DatasetType' => [ 'type' => 'string', 'enum' => [ 'TARGET_TIME_SERIES', 'RELATED_TIME_SERIES', 'ITEM_METADATA', ], ], 'Datasets' => [ 'type' => 'list', 'member' => [ 'shape' => 'DatasetSummary', ], ], 'DeleteDatasetGroupRequest' => [ 'type' => 'structure', 'required' => [ 'DatasetGroupArn', ], 'members' => [ 'DatasetGroupArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteDatasetImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'DatasetImportJobArn', ], 'members' => [ 'DatasetImportJobArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'DatasetArn', ], 'members' => [ 'DatasetArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteExplainabilityExportRequest' => [ 'type' => 'structure', 'required' => [ 'ExplainabilityExportArn', ], 'members' => [ 'ExplainabilityExportArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteExplainabilityRequest' => [ 'type' => 'structure', 'required' => [ 'ExplainabilityArn', ], 'members' => [ 'ExplainabilityArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteForecastExportJobRequest' => [ 'type' => 'structure', 'required' => [ 'ForecastExportJobArn', ], 'members' => [ 'ForecastExportJobArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteForecastRequest' => [ 'type' => 'structure', 'required' => [ 'ForecastArn', ], 'members' => [ 'ForecastArn' => [ 'shape' => 'Arn', ], ], ], 'DeletePredictorBacktestExportJobRequest' => [ 'type' => 'structure', 'required' => [ 'PredictorBacktestExportJobArn', ], 'members' => [ 'PredictorBacktestExportJobArn' => [ 'shape' => 'Arn', ], ], ], 'DeletePredictorRequest' => [ 'type' => 'structure', 'required' => [ 'PredictorArn', ], 'members' => [ 'PredictorArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteResourceTreeRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeAutoPredictorRequest' => [ 'type' => 'structure', 'required' => [ 'PredictorArn', ], 'members' => [ 'PredictorArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeAutoPredictorResponse' => [ 'type' => 'structure', 'members' => [ 'PredictorArn' => [ 'shape' => 'Arn', ], 'PredictorName' => [ 'shape' => 'Name', ], 'ForecastHorizon' => [ 'shape' => 'Integer', ], 'ForecastTypes' => [ 'shape' => 'ForecastTypes', ], 'ForecastFrequency' => [ 'shape' => 'Frequency', ], 'DatasetImportJobArns' => [ 'shape' => 'ArnList', ], 'DataConfig' => [ 'shape' => 'DataConfig', ], 'EncryptionConfig' => [ 'shape' => 'EncryptionConfig', ], 'ReferencePredictorSummary' => [ 'shape' => 'ReferencePredictorSummary', ], 'EstimatedTimeRemainingInMinutes' => [ 'shape' => 'Long', ], 'Status' => [ 'shape' => 'Status', ], 'Message' => [ 'shape' => 'Message', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModificationTime' => [ 'shape' => 'Timestamp', ], 'OptimizationMetric' => [ 'shape' => 'OptimizationMetric', ], 'ExplainabilityInfo' => [ 'shape' => 'ExplainabilityInfo', ], ], ], 'DescribeDatasetGroupRequest' => [ 'type' => 'structure', 'required' => [ 'DatasetGroupArn', ], 'members' => [ 'DatasetGroupArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeDatasetGroupResponse' => [ 'type' => 'structure', 'members' => [ 'DatasetGroupName' => [ 'shape' => 'Name', ], 'DatasetGroupArn' => [ 'shape' => 'Arn', ], 'DatasetArns' => [ 'shape' => 'ArnList', ], 'Domain' => [ 'shape' => 'Domain', ], 'Status' => [ 'shape' => 'Status', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModificationTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeDatasetImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'DatasetImportJobArn', ], 'members' => [ 'DatasetImportJobArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeDatasetImportJobResponse' => [ 'type' => 'structure', 'members' => [ 'DatasetImportJobName' => [ 'shape' => 'Name', ], 'DatasetImportJobArn' => [ 'shape' => 'Arn', ], 'DatasetArn' => [ 'shape' => 'Arn', ], 'TimestampFormat' => [ 'shape' => 'TimestampFormat', ], 'TimeZone' => [ 'shape' => 'TimeZone', ], 'UseGeolocationForTimeZone' => [ 'shape' => 'UseGeolocationForTimeZone', ], 'GeolocationFormat' => [ 'shape' => 'GeolocationFormat', ], 'DataSource' => [ 'shape' => 'DataSource', ], 'EstimatedTimeRemainingInMinutes' => [ 'shape' => 'Long', ], 'FieldStatistics' => [ 'shape' => 'FieldStatistics', ], 'DataSize' => [ 'shape' => 'Double', ], 'Status' => [ 'shape' => 'Status', ], 'Message' => [ 'shape' => 'Message', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModificationTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'DatasetArn', ], 'members' => [ 'DatasetArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeDatasetResponse' => [ 'type' => 'structure', 'members' => [ 'DatasetArn' => [ 'shape' => 'Arn', ], 'DatasetName' => [ 'shape' => 'Name', ], 'Domain' => [ 'shape' => 'Domain', ], 'DatasetType' => [ 'shape' => 'DatasetType', ], 'DataFrequency' => [ 'shape' => 'Frequency', ], 'Schema' => [ 'shape' => 'Schema', ], 'EncryptionConfig' => [ 'shape' => 'EncryptionConfig', ], 'Status' => [ 'shape' => 'Status', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModificationTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeExplainabilityExportRequest' => [ 'type' => 'structure', 'required' => [ 'ExplainabilityExportArn', ], 'members' => [ 'ExplainabilityExportArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeExplainabilityExportResponse' => [ 'type' => 'structure', 'members' => [ 'ExplainabilityExportArn' => [ 'shape' => 'Arn', ], 'ExplainabilityExportName' => [ 'shape' => 'Name', ], 'ExplainabilityArn' => [ 'shape' => 'Arn', ], 'Destination' => [ 'shape' => 'DataDestination', ], 'Message' => [ 'shape' => 'Message', ], 'Status' => [ 'shape' => 'Status', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModificationTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeExplainabilityRequest' => [ 'type' => 'structure', 'required' => [ 'ExplainabilityArn', ], 'members' => [ 'ExplainabilityArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeExplainabilityResponse' => [ 'type' => 'structure', 'members' => [ 'ExplainabilityArn' => [ 'shape' => 'Arn', ], 'ExplainabilityName' => [ 'shape' => 'Name', ], 'ResourceArn' => [ 'shape' => 'Arn', ], 'ExplainabilityConfig' => [ 'shape' => 'ExplainabilityConfig', ], 'EnableVisualization' => [ 'shape' => 'Boolean', ], 'DataSource' => [ 'shape' => 'DataSource', ], 'Schema' => [ 'shape' => 'Schema', ], 'StartDateTime' => [ 'shape' => 'LocalDateTime', ], 'EndDateTime' => [ 'shape' => 'LocalDateTime', ], 'EstimatedTimeRemainingInMinutes' => [ 'shape' => 'Long', ], 'Message' => [ 'shape' => 'Message', ], 'Status' => [ 'shape' => 'Status', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModificationTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeForecastExportJobRequest' => [ 'type' => 'structure', 'required' => [ 'ForecastExportJobArn', ], 'members' => [ 'ForecastExportJobArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeForecastExportJobResponse' => [ 'type' => 'structure', 'members' => [ 'ForecastExportJobArn' => [ 'shape' => 'Arn', ], 'ForecastExportJobName' => [ 'shape' => 'Name', ], 'ForecastArn' => [ 'shape' => 'Arn', ], 'Destination' => [ 'shape' => 'DataDestination', ], 'Message' => [ 'shape' => 'Message', ], 'Status' => [ 'shape' => 'Status', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModificationTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribeForecastRequest' => [ 'type' => 'structure', 'required' => [ 'ForecastArn', ], 'members' => [ 'ForecastArn' => [ 'shape' => 'Arn', ], ], ], 'DescribeForecastResponse' => [ 'type' => 'structure', 'members' => [ 'ForecastArn' => [ 'shape' => 'Arn', ], 'ForecastName' => [ 'shape' => 'Name', ], 'ForecastTypes' => [ 'shape' => 'ForecastTypes', ], 'PredictorArn' => [ 'shape' => 'Arn', ], 'DatasetGroupArn' => [ 'shape' => 'Arn', ], 'EstimatedTimeRemainingInMinutes' => [ 'shape' => 'Long', ], 'Status' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'ErrorMessage', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModificationTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribePredictorBacktestExportJobRequest' => [ 'type' => 'structure', 'required' => [ 'PredictorBacktestExportJobArn', ], 'members' => [ 'PredictorBacktestExportJobArn' => [ 'shape' => 'Arn', ], ], ], 'DescribePredictorBacktestExportJobResponse' => [ 'type' => 'structure', 'members' => [ 'PredictorBacktestExportJobArn' => [ 'shape' => 'Arn', ], 'PredictorBacktestExportJobName' => [ 'shape' => 'Name', ], 'PredictorArn' => [ 'shape' => 'Arn', ], 'Destination' => [ 'shape' => 'DataDestination', ], 'Message' => [ 'shape' => 'Message', ], 'Status' => [ 'shape' => 'Status', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModificationTime' => [ 'shape' => 'Timestamp', ], ], ], 'DescribePredictorRequest' => [ 'type' => 'structure', 'required' => [ 'PredictorArn', ], 'members' => [ 'PredictorArn' => [ 'shape' => 'Arn', ], ], ], 'DescribePredictorResponse' => [ 'type' => 'structure', 'members' => [ 'PredictorArn' => [ 'shape' => 'Name', ], 'PredictorName' => [ 'shape' => 'Name', ], 'AlgorithmArn' => [ 'shape' => 'Arn', ], 'AutoMLAlgorithmArns' => [ 'shape' => 'ArnList', ], 'ForecastHorizon' => [ 'shape' => 'Integer', ], 'ForecastTypes' => [ 'shape' => 'ForecastTypes', ], 'PerformAutoML' => [ 'shape' => 'Boolean', ], 'AutoMLOverrideStrategy' => [ 'shape' => 'AutoMLOverrideStrategy', ], 'PerformHPO' => [ 'shape' => 'Boolean', ], 'TrainingParameters' => [ 'shape' => 'TrainingParameters', ], 'EvaluationParameters' => [ 'shape' => 'EvaluationParameters', ], 'HPOConfig' => [ 'shape' => 'HyperParameterTuningJobConfig', ], 'InputDataConfig' => [ 'shape' => 'InputDataConfig', ], 'FeaturizationConfig' => [ 'shape' => 'FeaturizationConfig', ], 'EncryptionConfig' => [ 'shape' => 'EncryptionConfig', ], 'PredictorExecutionDetails' => [ 'shape' => 'PredictorExecutionDetails', ], 'EstimatedTimeRemainingInMinutes' => [ 'shape' => 'Long', ], 'IsAutoPredictor' => [ 'shape' => 'Boolean', ], 'DatasetImportJobArns' => [ 'shape' => 'ArnList', ], 'Status' => [ 'shape' => 'Status', ], 'Message' => [ 'shape' => 'Message', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModificationTime' => [ 'shape' => 'Timestamp', ], 'OptimizationMetric' => [ 'shape' => 'OptimizationMetric', ], ], ], 'Domain' => [ 'type' => 'string', 'enum' => [ 'RETAIL', 'CUSTOM', 'INVENTORY_PLANNING', 'EC2_CAPACITY', 'WORK_FORCE', 'WEB_TRAFFIC', 'METRICS', ], ], 'Double' => [ 'type' => 'double', ], 'EncryptionConfig' => [ 'type' => 'structure', 'required' => [ 'RoleArn', 'KMSKeyArn', ], 'members' => [ 'RoleArn' => [ 'shape' => 'Arn', ], 'KMSKeyArn' => [ 'shape' => 'KMSKeyArn', ], ], ], 'ErrorMessage' => [ 'type' => 'string', ], 'ErrorMetric' => [ 'type' => 'structure', 'members' => [ 'ForecastType' => [ 'shape' => 'ForecastType', ], 'WAPE' => [ 'shape' => 'Double', ], 'RMSE' => [ 'shape' => 'Double', ], 'MASE' => [ 'shape' => 'Double', ], 'MAPE' => [ 'shape' => 'Double', ], ], ], 'ErrorMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'ErrorMetric', ], ], 'EvaluationParameters' => [ 'type' => 'structure', 'members' => [ 'NumberOfBacktestWindows' => [ 'shape' => 'Integer', ], 'BackTestWindowOffset' => [ 'shape' => 'Integer', ], ], ], 'EvaluationResult' => [ 'type' => 'structure', 'members' => [ 'AlgorithmArn' => [ 'shape' => 'Arn', ], 'TestWindows' => [ 'shape' => 'TestWindows', ], ], ], 'EvaluationType' => [ 'type' => 'string', 'enum' => [ 'SUMMARY', 'COMPUTED', ], ], 'Explainabilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExplainabilitySummary', ], ], 'ExplainabilityConfig' => [ 'type' => 'structure', 'required' => [ 'TimeSeriesGranularity', 'TimePointGranularity', ], 'members' => [ 'TimeSeriesGranularity' => [ 'shape' => 'TimeSeriesGranularity', ], 'TimePointGranularity' => [ 'shape' => 'TimePointGranularity', ], ], ], 'ExplainabilityExportSummary' => [ 'type' => 'structure', 'members' => [ 'ExplainabilityExportArn' => [ 'shape' => 'Arn', ], 'ExplainabilityExportName' => [ 'shape' => 'Name', ], 'Destination' => [ 'shape' => 'DataDestination', ], 'Status' => [ 'shape' => 'Status', ], 'Message' => [ 'shape' => 'ErrorMessage', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModificationTime' => [ 'shape' => 'Timestamp', ], ], ], 'ExplainabilityExports' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExplainabilityExportSummary', ], ], 'ExplainabilityInfo' => [ 'type' => 'structure', 'members' => [ 'ExplainabilityArn' => [ 'shape' => 'Arn', ], 'Status' => [ 'shape' => 'Status', ], ], ], 'ExplainabilitySummary' => [ 'type' => 'structure', 'members' => [ 'ExplainabilityArn' => [ 'shape' => 'Arn', ], 'ExplainabilityName' => [ 'shape' => 'Name', ], 'ResourceArn' => [ 'shape' => 'Arn', ], 'ExplainabilityConfig' => [ 'shape' => 'ExplainabilityConfig', ], 'Status' => [ 'shape' => 'Status', ], 'Message' => [ 'shape' => 'Message', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModificationTime' => [ 'shape' => 'Timestamp', ], ], ], 'Featurization' => [ 'type' => 'structure', 'required' => [ 'AttributeName', ], 'members' => [ 'AttributeName' => [ 'shape' => 'Name', ], 'FeaturizationPipeline' => [ 'shape' => 'FeaturizationPipeline', ], ], ], 'FeaturizationConfig' => [ 'type' => 'structure', 'required' => [ 'ForecastFrequency', ], 'members' => [ 'ForecastFrequency' => [ 'shape' => 'Frequency', ], 'ForecastDimensions' => [ 'shape' => 'ForecastDimensions', ], 'Featurizations' => [ 'shape' => 'Featurizations', ], ], ], 'FeaturizationMethod' => [ 'type' => 'structure', 'required' => [ 'FeaturizationMethodName', ], 'members' => [ 'FeaturizationMethodName' => [ 'shape' => 'FeaturizationMethodName', ], 'FeaturizationMethodParameters' => [ 'shape' => 'FeaturizationMethodParameters', ], ], ], 'FeaturizationMethodName' => [ 'type' => 'string', 'enum' => [ 'filling', ], ], 'FeaturizationMethodParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'ParameterKey', ], 'value' => [ 'shape' => 'ParameterValue', ], 'max' => 20, 'min' => 1, ], 'FeaturizationPipeline' => [ 'type' => 'list', 'member' => [ 'shape' => 'FeaturizationMethod', ], 'max' => 1, 'min' => 1, ], 'Featurizations' => [ 'type' => 'list', 'member' => [ 'shape' => 'Featurization', ], 'max' => 50, 'min' => 1, ], 'FieldStatistics' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'Statistics', ], ], 'Filter' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', 'Condition', ], 'members' => [ 'Key' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'Arn', ], 'Condition' => [ 'shape' => 'FilterConditionString', ], ], ], 'FilterConditionString' => [ 'type' => 'string', 'enum' => [ 'IS', 'IS_NOT', ], ], 'Filters' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], ], 'ForecastDimensions' => [ 'type' => 'list', 'member' => [ 'shape' => 'Name', ], 'max' => 10, 'min' => 1, ], 'ForecastExportJobSummary' => [ 'type' => 'structure', 'members' => [ 'ForecastExportJobArn' => [ 'shape' => 'Arn', ], 'ForecastExportJobName' => [ 'shape' => 'Name', ], 'Destination' => [ 'shape' => 'DataDestination', ], 'Status' => [ 'shape' => 'Status', ], 'Message' => [ 'shape' => 'ErrorMessage', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModificationTime' => [ 'shape' => 'Timestamp', ], ], ], 'ForecastExportJobs' => [ 'type' => 'list', 'member' => [ 'shape' => 'ForecastExportJobSummary', ], ], 'ForecastSummary' => [ 'type' => 'structure', 'members' => [ 'ForecastArn' => [ 'shape' => 'Arn', ], 'ForecastName' => [ 'shape' => 'Name', ], 'PredictorArn' => [ 'shape' => 'String', ], 'CreatedUsingAutoPredictor' => [ 'shape' => 'Boolean', ], 'DatasetGroupArn' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'Status', ], 'Message' => [ 'shape' => 'ErrorMessage', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModificationTime' => [ 'shape' => 'Timestamp', ], ], ], 'ForecastType' => [ 'type' => 'string', 'max' => 4, 'min' => 2, 'pattern' => '(^0?\\.\\d\\d?$|^mean$)', ], 'ForecastTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ForecastType', ], 'max' => 20, 'min' => 1, ], 'Forecasts' => [ 'type' => 'list', 'member' => [ 'shape' => 'ForecastSummary', ], ], 'Frequency' => [ 'type' => 'string', 'max' => 5, 'min' => 1, 'pattern' => '^Y|M|W|D|H|30min|15min|10min|5min|1min$', ], 'GeolocationFormat' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^[a-zA-Z0-9_]+$', ], 'GetAccuracyMetricsRequest' => [ 'type' => 'structure', 'required' => [ 'PredictorArn', ], 'members' => [ 'PredictorArn' => [ 'shape' => 'Arn', ], ], ], 'GetAccuracyMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'PredictorEvaluationResults' => [ 'shape' => 'PredictorEvaluationResults', ], 'IsAutoPredictor' => [ 'shape' => 'Boolean', ], 'AutoMLOverrideStrategy' => [ 'shape' => 'AutoMLOverrideStrategy', ], 'OptimizationMetric' => [ 'shape' => 'OptimizationMetric', ], ], ], 'HyperParameterTuningJobConfig' => [ 'type' => 'structure', 'members' => [ 'ParameterRanges' => [ 'shape' => 'ParameterRanges', ], ], ], 'InputDataConfig' => [ 'type' => 'structure', 'required' => [ 'DatasetGroupArn', ], 'members' => [ 'DatasetGroupArn' => [ 'shape' => 'Arn', ], 'SupplementaryFeatures' => [ 'shape' => 'SupplementaryFeatures', ], ], ], 'Integer' => [ 'type' => 'integer', ], 'IntegerParameterRange' => [ 'type' => 'structure', 'required' => [ 'Name', 'MaxValue', 'MinValue', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], 'MaxValue' => [ 'shape' => 'Integer', ], 'MinValue' => [ 'shape' => 'Integer', ], 'ScalingType' => [ 'shape' => 'ScalingType', ], ], ], 'IntegerParameterRanges' => [ 'type' => 'list', 'member' => [ 'shape' => 'IntegerParameterRange', ], 'max' => 20, 'min' => 1, ], 'InvalidInputException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InvalidNextTokenException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'KMSKeyArn' => [ 'type' => 'string', 'max' => 256, 'pattern' => 'arn:aws:kms:.*:key/.*', ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ListDatasetGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListDatasetGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'DatasetGroups' => [ 'shape' => 'DatasetGroups', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDatasetImportJobsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'Filters' => [ 'shape' => 'Filters', ], ], ], 'ListDatasetImportJobsResponse' => [ 'type' => 'structure', 'members' => [ 'DatasetImportJobs' => [ 'shape' => 'DatasetImportJobs', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDatasetsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListDatasetsResponse' => [ 'type' => 'structure', 'members' => [ 'Datasets' => [ 'shape' => 'Datasets', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListExplainabilitiesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'Filters' => [ 'shape' => 'Filters', ], ], ], 'ListExplainabilitiesResponse' => [ 'type' => 'structure', 'members' => [ 'Explainabilities' => [ 'shape' => 'Explainabilities', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListExplainabilityExportsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'Filters' => [ 'shape' => 'Filters', ], ], ], 'ListExplainabilityExportsResponse' => [ 'type' => 'structure', 'members' => [ 'ExplainabilityExports' => [ 'shape' => 'ExplainabilityExports', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListForecastExportJobsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'Filters' => [ 'shape' => 'Filters', ], ], ], 'ListForecastExportJobsResponse' => [ 'type' => 'structure', 'members' => [ 'ForecastExportJobs' => [ 'shape' => 'ForecastExportJobs', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListForecastsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'Filters' => [ 'shape' => 'Filters', ], ], ], 'ListForecastsResponse' => [ 'type' => 'structure', 'members' => [ 'Forecasts' => [ 'shape' => 'Forecasts', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPredictorBacktestExportJobsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'Filters' => [ 'shape' => 'Filters', ], ], ], 'ListPredictorBacktestExportJobsResponse' => [ 'type' => 'structure', 'members' => [ 'PredictorBacktestExportJobs' => [ 'shape' => 'PredictorBacktestExportJobs', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPredictorsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'Filters' => [ 'shape' => 'Filters', ], ], ], 'ListPredictorsResponse' => [ 'type' => 'structure', 'members' => [ 'Predictors' => [ 'shape' => 'Predictors', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'Tags', ], ], ], 'LocalDateTime' => [ 'type' => 'string', 'max' => 19, 'pattern' => '^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}$', ], 'Long' => [ 'type' => 'long', ], 'MaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'Message' => [ 'type' => 'string', ], 'Metrics' => [ 'type' => 'structure', 'members' => [ 'RMSE' => [ 'shape' => 'Double', 'deprecated' => true, 'deprecatedMessage' => 'This property is deprecated, please refer to ErrorMetrics for both RMSE and WAPE', ], 'WeightedQuantileLosses' => [ 'shape' => 'WeightedQuantileLosses', ], 'ErrorMetrics' => [ 'shape' => 'ErrorMetrics', ], 'AverageWeightedQuantileLoss' => [ 'shape' => 'Double', ], ], ], 'Name' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-zA-Z][a-zA-Z0-9_]*', ], 'NextToken' => [ 'type' => 'string', 'max' => 3000, 'min' => 1, 'pattern' => '.+', ], 'OptimizationMetric' => [ 'type' => 'string', 'enum' => [ 'WAPE', 'RMSE', 'AverageWeightedQuantileLoss', 'MASE', 'MAPE', ], ], 'ParameterKey' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^[a-zA-Z0-9\\-\\_\\.\\/\\[\\]\\,\\\\]+$', ], 'ParameterRanges' => [ 'type' => 'structure', 'members' => [ 'CategoricalParameterRanges' => [ 'shape' => 'CategoricalParameterRanges', ], 'ContinuousParameterRanges' => [ 'shape' => 'ContinuousParameterRanges', ], 'IntegerParameterRanges' => [ 'shape' => 'IntegerParameterRanges', ], ], ], 'ParameterValue' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^[a-zA-Z0-9\\-\\_\\.\\/\\[\\]\\,\\"\\\\\\s]+$', ], 'PredictorBacktestExportJobSummary' => [ 'type' => 'structure', 'members' => [ 'PredictorBacktestExportJobArn' => [ 'shape' => 'Arn', ], 'PredictorBacktestExportJobName' => [ 'shape' => 'Name', ], 'Destination' => [ 'shape' => 'DataDestination', ], 'Status' => [ 'shape' => 'Status', ], 'Message' => [ 'shape' => 'ErrorMessage', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModificationTime' => [ 'shape' => 'Timestamp', ], ], ], 'PredictorBacktestExportJobs' => [ 'type' => 'list', 'member' => [ 'shape' => 'PredictorBacktestExportJobSummary', ], ], 'PredictorEvaluationResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'EvaluationResult', ], ], 'PredictorExecution' => [ 'type' => 'structure', 'members' => [ 'AlgorithmArn' => [ 'shape' => 'Arn', ], 'TestWindows' => [ 'shape' => 'TestWindowDetails', ], ], ], 'PredictorExecutionDetails' => [ 'type' => 'structure', 'members' => [ 'PredictorExecutions' => [ 'shape' => 'PredictorExecutions', ], ], ], 'PredictorExecutions' => [ 'type' => 'list', 'member' => [ 'shape' => 'PredictorExecution', ], 'max' => 5, 'min' => 1, ], 'PredictorSummary' => [ 'type' => 'structure', 'members' => [ 'PredictorArn' => [ 'shape' => 'Arn', ], 'PredictorName' => [ 'shape' => 'Name', ], 'DatasetGroupArn' => [ 'shape' => 'Arn', ], 'IsAutoPredictor' => [ 'shape' => 'Boolean', ], 'ReferencePredictorSummary' => [ 'shape' => 'ReferencePredictorSummary', ], 'Status' => [ 'shape' => 'Status', ], 'Message' => [ 'shape' => 'ErrorMessage', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastModificationTime' => [ 'shape' => 'Timestamp', ], ], ], 'Predictors' => [ 'type' => 'list', 'member' => [ 'shape' => 'PredictorSummary', ], ], 'ReferencePredictorSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'State' => [ 'shape' => 'State', ], ], ], 'ResourceAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResourceInUseException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'S3Config' => [ 'type' => 'structure', 'required' => [ 'Path', 'RoleArn', ], 'members' => [ 'Path' => [ 'shape' => 'S3Path', ], 'RoleArn' => [ 'shape' => 'Arn', ], 'KMSKeyArn' => [ 'shape' => 'KMSKeyArn', ], ], ], 'S3Path' => [ 'type' => 'string', 'max' => 4096, 'min' => 7, 'pattern' => '^s3://[a-z0-9].+$', ], 'ScalingType' => [ 'type' => 'string', 'enum' => [ 'Auto', 'Linear', 'Logarithmic', 'ReverseLogarithmic', ], ], 'Schema' => [ 'type' => 'structure', 'members' => [ 'Attributes' => [ 'shape' => 'SchemaAttributes', ], ], ], 'SchemaAttribute' => [ 'type' => 'structure', 'members' => [ 'AttributeName' => [ 'shape' => 'Name', ], 'AttributeType' => [ 'shape' => 'AttributeType', ], ], ], 'SchemaAttributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaAttribute', ], 'max' => 100, 'min' => 1, ], 'State' => [ 'type' => 'string', 'enum' => [ 'Active', 'Deleted', ], ], 'Statistics' => [ 'type' => 'structure', 'members' => [ 'Count' => [ 'shape' => 'Integer', ], 'CountDistinct' => [ 'shape' => 'Integer', ], 'CountNull' => [ 'shape' => 'Integer', ], 'CountNan' => [ 'shape' => 'Integer', ], 'Min' => [ 'shape' => 'String', ], 'Max' => [ 'shape' => 'String', ], 'Avg' => [ 'shape' => 'Double', ], 'Stddev' => [ 'shape' => 'Double', ], 'CountLong' => [ 'shape' => 'Long', ], 'CountDistinctLong' => [ 'shape' => 'Long', ], 'CountNullLong' => [ 'shape' => 'Long', ], 'CountNanLong' => [ 'shape' => 'Long', ], ], ], 'Status' => [ 'type' => 'string', 'max' => 256, ], 'StopResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', ], ], ], 'String' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^[a-zA-Z0-9\\_]+$', ], 'SupplementaryFeature' => [ 'type' => 'structure', 'required' => [ 'Name', 'Value', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], 'Value' => [ 'shape' => 'Value', ], ], ], 'SupplementaryFeatures' => [ 'type' => 'list', 'member' => [ 'shape' => 'SupplementaryFeature', ], 'max' => 2, 'min' => 1, ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', 'sensitive' => true, ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', 'sensitive' => true, ], 'Tags' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 0, ], 'TestWindowDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'TestWindowSummary', ], ], 'TestWindowSummary' => [ 'type' => 'structure', 'members' => [ 'TestWindowStart' => [ 'shape' => 'Timestamp', ], 'TestWindowEnd' => [ 'shape' => 'Timestamp', ], 'Status' => [ 'shape' => 'Status', ], 'Message' => [ 'shape' => 'ErrorMessage', ], ], ], 'TestWindows' => [ 'type' => 'list', 'member' => [ 'shape' => 'WindowSummary', ], ], 'TimePointGranularity' => [ 'type' => 'string', 'enum' => [ 'ALL', 'SPECIFIC', ], ], 'TimeSeriesGranularity' => [ 'type' => 'string', 'enum' => [ 'ALL', 'SPECIFIC', ], ], 'TimeZone' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^[a-zA-Z0-9\\/\\+\\-\\_]+$', ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TimestampFormat' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^[a-zA-Z0-9\\-\\:\\.\\,\\\'\\s]+$', ], 'TrainingParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'ParameterKey', ], 'value' => [ 'shape' => 'ParameterValue', ], 'max' => 100, 'min' => 0, ], 'Transformations' => [ 'type' => 'map', 'key' => [ 'shape' => 'Name', ], 'value' => [ 'shape' => 'Value', ], 'max' => 20, 'min' => 1, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'Arn', ], 'TagKeys' => [ 'shape' => 'TagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDatasetGroupRequest' => [ 'type' => 'structure', 'required' => [ 'DatasetGroupArn', 'DatasetArns', ], 'members' => [ 'DatasetGroupArn' => [ 'shape' => 'Arn', ], 'DatasetArns' => [ 'shape' => 'ArnList', ], ], ], 'UpdateDatasetGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'UseGeolocationForTimeZone' => [ 'type' => 'boolean', ], 'Value' => [ 'type' => 'string', 'max' => 256, 'pattern' => '^[a-zA-Z0-9\\_\\-]+$', ], 'Values' => [ 'type' => 'list', 'member' => [ 'shape' => 'Value', ], 'max' => 20, 'min' => 1, ], 'WeightedQuantileLoss' => [ 'type' => 'structure', 'members' => [ 'Quantile' => [ 'shape' => 'Double', ], 'LossValue' => [ 'shape' => 'Double', ], ], ], 'WeightedQuantileLosses' => [ 'type' => 'list', 'member' => [ 'shape' => 'WeightedQuantileLoss', ], ], 'WindowSummary' => [ 'type' => 'structure', 'members' => [ 'TestWindowStart' => [ 'shape' => 'Timestamp', ], 'TestWindowEnd' => [ 'shape' => 'Timestamp', ], 'ItemCount' => [ 'shape' => 'Integer', ], 'EvaluationType' => [ 'shape' => 'EvaluationType', ], 'Metrics' => [ 'shape' => 'Metrics', ], ], ], ],];
