﻿/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'link', 'ru', {
	acccessKey: 'Клавиша доступа',
	advanced: 'Дополнительно',
	advisoryContentType: 'Тип содержимого',
	advisoryTitle: 'Заголовок',
	anchor: {
		toolbar: 'Вставить / редактировать якорь',
		menu: 'Изменить якорь',
		title: 'Свойства якоря',
		name: '<PERSON><PERSON><PERSON> якоря',
		errorName: 'Пожалуйста, введите имя якоря',
		remove: 'Удалить якорь'
	},
	anchorId: 'По идентификатору',
	anchorName: 'По имени',
	charset: 'Кодировка ресурса',
	cssClasses: 'Классы CSS',
	download: 'Скачать как файл',
	displayText: 'Отображаемый текст',
	emailAddress: 'Email адрес',
	emailBody: 'Текст сообщения',
	emailSubject: 'Тема сообщения',
	id: 'Идентификатор',
	info: 'Информация о ссылке',
	langCode: 'Код языка',
	langDir: 'Направление текста',
	langDirLTR: 'Слева направо (LTR)',
	langDirRTL: 'Справа налево (RTL)',
	menu: 'Редактировать ссылку',
	name: 'Имя',
	noAnchors: '(В документе нет ни одного якоря)',
	noEmail: 'Пожалуйста, введите email адрес',
	noUrl: 'Пожалуйста, введите ссылку',
	other: '<другой>',
	popupDependent: 'Зависимое (Netscape)',
	popupFeatures: 'Параметры всплывающего окна',
	popupFullScreen: 'Полноэкранное (IE)',
	popupLeft: 'Отступ слева',
	popupLocationBar: 'Панель адреса',
	popupMenuBar: 'Панель меню',
	popupResizable: 'Изменяемый размер',
	popupScrollBars: 'Полосы прокрутки',
	popupStatusBar: 'Строка состояния',
	popupToolbar: 'Панель инструментов',
	popupTop: 'Отступ сверху',
	rel: 'Отношение',
	selectAnchor: 'Выберите якорь',
	styles: 'Стиль',
	tabIndex: 'Последовательность перехода',
	target: 'Цель',
	targetFrame: '<фрейм>',
	targetFrameName: 'Имя целевого фрейма',
	targetPopup: '<всплывающее окно>',
	targetPopupName: 'Имя всплывающего окна',
	title: 'Ссылка',
	toAnchor: 'Ссылка на якорь в тексте',
	toEmail: 'Email',
	toUrl: 'Ссылка',
	toolbar: 'Вставить/Редактировать ссылку',
	type: 'Тип ссылки',
	unlink: 'Убрать ссылку',
	upload: 'Загрузка'
} );
