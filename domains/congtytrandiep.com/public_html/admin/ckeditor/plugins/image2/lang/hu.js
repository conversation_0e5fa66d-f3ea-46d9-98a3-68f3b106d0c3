/*
Copyright (c) 2003-2022, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'image2', 'hu', {
	alt: 'Alternatív szöveg',
	btnUpload: '<PERSON><PERSON><PERSON><PERSON> a szerverre',
	captioned: '<PERSON><PERSON><PERSON>zo<PERSON> kép',
	captionPlaceholder: 'K<PERSON>pfeli<PERSON>',
	infoTab: 'Alaptulajdonságok',
	lockRatio: '<PERSON>r<PERSON><PERSON> megtart<PERSON>a',
	menu: 'Kép tulajdonságai',
	pathName: 'kép',
	pathNameCaption: 'felirat',
	resetSize: '<PERSON><PERSON><PERSON> méret',
	resizer: '<PERSON><PERSON>ts<PERSON> és húzza az átméretezéshez',
	title: 'Kép tulajdons<PERSON>gai',
	uploadTab: 'Feltöltés',
	urlMissing: '<PERSON><PERSON><PERSON>zik a kép URL-je',
	altMissing: 'Az alternatív szöveg hiányzik.'
} );
