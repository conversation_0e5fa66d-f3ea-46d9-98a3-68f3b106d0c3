/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'find', 'sr', {
	find: 'Претрага',
	findOptions: 'Find Options',
	findWhat: 'Пронађи:',
	matchCase: 'Разликуј велика и мала слова',
	matchCyclic: 'Match cyclic',
	matchWord: 'Упореди целе речи',
	notFoundMsg: 'Тражени текст није пронађен.',
	replace: 'Замена',
	replaceAll: 'Замени све',
	replaceSuccessMsg: '%1 occurrence(s) replaced.',
	replaceWith: 'Замени са:',
	title: 'Find and Replace'
} );
