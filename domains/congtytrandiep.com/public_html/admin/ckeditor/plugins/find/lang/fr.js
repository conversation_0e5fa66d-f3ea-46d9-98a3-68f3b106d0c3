/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'find', 'fr', {
	find: 'Rechercher',
	findOptions: 'Options de recherche',
	findWhat: 'Rechercher :',
	matchCase: 'Respecter la casse',
	matchCyclic: 'Bouc<PERSON>',
	matchWord: 'Mot entier uniquement',
	notFoundMsg: 'Le texte spécifié ne peut être trouvé.',
	replace: 'Remplacer',
	replaceAll: 'Remplacer tout',
	replaceSuccessMsg: '%1 occurrence(s) remplacée(s).',
	replaceWith: 'Remplacer par : ',
	title: 'Rechercher et remplacer'
} );
