/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'find', 'az', {
	find: 'Tap',
	findOptions: 'Ax<PERSON><PERSON>şın seçimləri',
	findWhat: 'Nəyi axtarmaq',
	matchCase: 'Reqistr nəzərə alınmaqla',
	matchCyclic: 'Dövrəvi axtar',
	matchWord: 'Tam sözünə uyğun',
	notFoundMsg: 'Daxil etdiyiniz sorğu ilə heç bir nəticə tapılmayıb',
	replace: 'Əvəz et',
	replaceAll: 'Hamısını əvəz et',
	replaceSuccessMsg: '%1 daxiletmə(lər) əvəz edilib',
	replaceWith: 'Əvəz etdirici mətn:',
	title: 'Tap və əvəz et'
} );
