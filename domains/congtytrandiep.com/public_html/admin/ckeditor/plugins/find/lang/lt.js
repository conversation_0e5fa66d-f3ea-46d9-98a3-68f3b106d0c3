/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'find', 'lt', {
	find: '<PERSON><PERSON><PERSON>',
	findOptions: '<PERSON><PERSON>škos nustatymai',
	findWhat: 'Surasti tekstą:',
	matchCase: 'Skirti didži<PERSON>sias ir mažąsias raides',
	matchCyclic: 'Su<PERSON>pantis cikliškumas',
	matchWord: 'Atitikti pilną žodį',
	notFoundMsg: 'Nurodytas tekstas nerastas.',
	replace: '<PERSON>eist<PERSON>',
	replaceAll: '<PERSON>eisti viską',
	replaceSuccessMsg: '%1 sutapimas(ų) buvo pakeisti.',
	replaceWith: 'Pakeisti tekstu:',
	title: 'Surasti ir pakeisti'
} );
