function klick(el) {
	var className = el.childNodes[0].getAttribute('class');
	var dialog = CKEDITOR.dialog.getCurrent();
	dialog.getContentElement('font-awesome','faicon').setValue(className);
	el.className = el.className.replace('active','');
	el.className += ' active';
}
function searchIcon(val){
	var fas = document.getElementById('ckeditor-fa-icons');
	var a = fas.getElementsByTagName('a');
	for(var i = 0, len = a.length, el, atr; i < len; i ++){
		el = a[i];
		atr = el.childNodes[0].getAttribute('class');
		if(atr && atr.indexOf(val) >= 0){
			el.style.display = 'inline-block';
		}else{
			el.style.display = 'none';
		}
	}
};
function clear(){
	var icons = document.getElementById('ckeditor-fa-icons');
	var activeIcon = icons.getElementsByClassName('active');
	var icon = icons.getElementsByTagName('a');
	for (var i=0; i < activeIcon.length; ++i) {
		activeIcon[i].className = activeIcon[i].className.replace('active','');
	}
	for(j=0; j < icon.length; ++j){
		icon[j].className = '';
		icon[j].style.display = '';
		icon[j].getElementsByTagName('span')[0].style.color = '';
	}
}
CKEDITOR.dialog.add('ckeditorFaDialog', function (editor) {
var faBrands = JSON.parse('[{"id":"42-group"},{"id":"500px"},{"id":"accessible-icon"},{"id":"accusoft"},{"id":"adn"},{"id":"adversal"},{"id":"affiliatetheme"},{"id":"airbnb"},{"id":"algolia"},{"id":"alipay"},{"id":"amazon"},{"id":"amazon-pay"},{"id":"amilia"},{"id":"android"},{"id":"angellist"},{"id":"angrycreative"},{"id":"angular"},{"id":"apper"},{"id":"apple"},{"id":"apple-pay"},{"id":"app-store"},{"id":"app-store-ios"},{"id":"artstation"},{"id":"asymmetrik"},{"id":"atlassian"},{"id":"audible"},{"id":"autoprefixer"},{"id":"avianex"},{"id":"aviato"},{"id":"aws"},{"id":"bandcamp"},{"id":"battle-net"},{"id":"behance"},{"id":"bilibili"},{"id":"bimobject"},{"id":"bitbucket"},{"id":"bitcoin"},{"id":"bity"},{"id":"blackberry"},{"id":"black-tie"},{"id":"blogger"},{"id":"blogger-b"},{"id":"bluetooth"},{"id":"bluetooth-b"},{"id":"bootstrap"},{"id":"bots"},{"id":"btc"},{"id":"buffer"},{"id":"buromobelexperte"},{"id":"buy-n-large"},{"id":"buysellads"},{"id":"canadian-maple-leaf"},{"id":"cc-amazon-pay"},{"id":"cc-amex"},{"id":"cc-apple-pay"},{"id":"cc-diners-club"},{"id":"cc-discover"},{"id":"cc-jcb"},{"id":"cc-mastercard"},{"id":"cc-paypal"},{"id":"cc-stripe"},{"id":"cc-visa"},{"id":"centercode"},{"id":"centos"},{"id":"chrome"},{"id":"chromecast"},{"id":"cloudflare"},{"id":"cloudscale"},{"id":"cloudsmith"},{"id":"cloudversify"},{"id":"cmplid"},{"id":"codepen"},{"id":"codiepie"},{"id":"confluence"},{"id":"connectdevelop"},{"id":"contao"},{"id":"cotton-bureau"},{"id":"cpanel"},{"id":"creative-commons"},{"id":"creative-commons-by"},{"id":"creative-commons-nc"},{"id":"creative-commons-nc-eu"},{"id":"creative-commons-nc-jp"},{"id":"creative-commons-nd"},{"id":"creative-commons-pd"},{"id":"creative-commons-pd-alt"},{"id":"creative-commons-remix"},{"id":"creative-commons-sa"},{"id":"creative-commons-sampling"},{"id":"creative-commons-sampling-plus"},{"id":"creative-commons-share"},{"id":"creative-commons-zero"},{"id":"critical-role"},{"id":"css3"},{"id":"css3-alt"},{"id":"cuttlefish"},{"id":"dailymotion"},{"id":"d-and-d"},{"id":"d-and-d-beyond"},{"id":"dashcube"},{"id":"deezer"},{"id":"delicious"},{"id":"deploydog"},{"id":"deskpro"},{"id":"dev"},{"id":"deviantart"},{"id":"dhl"},{"id":"diaspora"},{"id":"digg"},{"id":"digital-ocean"},{"id":"discord"},{"id":"discourse"},{"id":"dochub"},{"id":"docker"},{"id":"draft2digital"},{"id":"dribbble"},{"id":"dropbox"},{"id":"drupal"},{"id":"dyalog"},{"id":"earlybirds"},{"id":"ebay"},{"id":"edge"},{"id":"edge-legacy"},{"id":"elementor"},{"id":"ello"},{"id":"ember"},{"id":"empire"},{"id":"envira"},{"id":"erlang"},{"id":"ethereum"},{"id":"etsy"},{"id":"evernote"},{"id":"expeditedssl"},{"id":"facebook"},{"id":"facebook-f"},{"id":"facebook-messenger"},{"id":"fantasy-flight-games"},{"id":"fedex"},{"id":"fedora"},{"id":"figma"},{"id":"firefox"},{"id":"firefox-browser"},{"id":"firstdraft"},{"id":"first-order"},{"id":"first-order-alt"},{"id":"flickr"},{"id":"flipboard"},{"id":"fly"},{"id":"font-awesome"},{"id":"fonticons"},{"id":"fonticons-fi"},{"id":"fort-awesome"},{"id":"fort-awesome-alt"},{"id":"forumbee"},{"id":"foursquare"},{"id":"freebsd"},{"id":"free-code-camp"},{"id":"fulcrum"},{"id":"galactic-republic"},{"id":"galactic-senate"},{"id":"get-pocket"},{"id":"gg"},{"id":"gg-circle"},{"id":"git"},{"id":"git-alt"},{"id":"github"},{"id":"github-alt"},{"id":"gitkraken"},{"id":"gitlab"},{"id":"gitter"},{"id":"glide"},{"id":"glide-g"},{"id":"gofore"},{"id":"golang"},{"id":"goodreads"},{"id":"goodreads-g"},{"id":"google"},{"id":"google-drive"},{"id":"google-pay"},{"id":"google-play"},{"id":"google-plus"},{"id":"google-plus-g"},{"id":"google-wallet"},{"id":"gratipay"},{"id":"grav"},{"id":"gripfire"},{"id":"grunt"},{"id":"guilded"},{"id":"gulp"},{"id":"hacker-news"},{"id":"hackerrank"},{"id":"hashnode"},{"id":"hips"},{"id":"hire-a-helper"},{"id":"hive"},{"id":"hooli"},{"id":"hornbill"},{"id":"hotjar"},{"id":"houzz"},{"id":"html5"},{"id":"hubspot"},{"id":"ideal"},{"id":"imdb"},{"id":"instagram"},{"id":"instalod"},{"id":"intercom"},{"id":"internet-explorer"},{"id":"invision"},{"id":"ioxhost"},{"id":"itch-io"},{"id":"itunes"},{"id":"itunes-note"},{"id":"java"},{"id":"jedi-order"},{"id":"jenkins"},{"id":"jira"},{"id":"joget"},{"id":"joomla"},{"id":"js"},{"id":"jsfiddle"},{"id":"kaggle"},{"id":"keybase"},{"id":"keycdn"},{"id":"kickstarter"},{"id":"kickstarter-k"},{"id":"korvue"},{"id":"laravel"},{"id":"lastfm"},{"id":"leanpub"},{"id":"less"},{"id":"line"},{"id":"linkedin"},{"id":"linkedin-in"},{"id":"linode"},{"id":"linux"},{"id":"lyft"},{"id":"magento"},{"id":"mailchimp"},{"id":"mandalorian"},{"id":"markdown"},{"id":"mastodon"},{"id":"maxcdn"},{"id":"mdb"},{"id":"medapps"},{"id":"medium"},{"id":"medrt"},{"id":"meetup"},{"id":"megaport"},{"id":"mendeley"},{"id":"meta"},{"id":"microblog"},{"id":"microsoft"},{"id":"mix"},{"id":"mixcloud"},{"id":"mixer"},{"id":"mizuni"},{"id":"modx"},{"id":"monero"},{"id":"napster"},{"id":"neos"},{"id":"nfc-directional"},{"id":"nfc-symbol"},{"id":"nimblr"},{"id":"node"},{"id":"node-js"},{"id":"npm"},{"id":"ns8"},{"id":"nutritionix"},{"id":"octopus-deploy"},{"id":"odnoklassniki"},{"id":"odysee"},{"id":"old-republic"},{"id":"opencart"},{"id":"openid"},{"id":"opera"},{"id":"optin-monster"},{"id":"orcid"},{"id":"osi"},{"id":"padlet"},{"id":"page4"},{"id":"pagelines"},{"id":"palfed"},{"id":"patreon"},{"id":"paypal"},{"id":"perbyte"},{"id":"periscope"},{"id":"phabricator"},{"id":"phoenix-framework"},{"id":"phoenix-squadron"},{"id":"php"},{"id":"pied-piper"},{"id":"pied-piper-alt"},{"id":"pied-piper-hat"},{"id":"pied-piper-pp"},{"id":"pinterest"},{"id":"pinterest-p"},{"id":"pix"},{"id":"playstation"},{"id":"product-hunt"},{"id":"pushed"},{"id":"python"},{"id":"qq"},{"id":"quinscape"},{"id":"quora"},{"id":"raspberry-pi"},{"id":"ravelry"},{"id":"react"},{"id":"reacteurope"},{"id":"readme"},{"id":"rebel"},{"id":"reddit"},{"id":"reddit-alien"},{"id":"redhat"},{"id":"red-river"},{"id":"renren"},{"id":"replyd"},{"id":"researchgate"},{"id":"resolving"},{"id":"rev"},{"id":"rocketchat"},{"id":"rockrms"},{"id":"r-project"},{"id":"rust"},{"id":"safari"},{"id":"salesforce"},{"id":"sass"},{"id":"schlix"},{"id":"screenpal"},{"id":"scribd"},{"id":"searchengin"},{"id":"sellcast"},{"id":"sellsy"},{"id":"servicestack"},{"id":"shirtsinbulk"},{"id":"shopify"},{"id":"shopware"},{"id":"simplybuilt"},{"id":"sistrix"},{"id":"sith"},{"id":"sitrox"},{"id":"sketch"},{"id":"skyatlas"},{"id":"skype"},{"id":"slack"},{"id":"slideshare"},{"id":"snapchat"},{"id":"soundcloud"},{"id":"sourcetree"},{"id":"space-awesome"},{"id":"speakap"},{"id":"speaker-deck"},{"id":"spotify"},{"id":"square-behance"},{"id":"square-dribbble"},{"id":"square-facebook"},{"id":"square-font-awesome"},{"id":"square-font-awesome-stroke"},{"id":"square-git"},{"id":"square-github"},{"id":"square-gitlab"},{"id":"square-google-plus"},{"id":"square-hacker-news"},{"id":"square-instagram"},{"id":"square-js"},{"id":"square-lastfm"},{"id":"square-odnoklassniki"},{"id":"square-pied-piper"},{"id":"square-pinterest"},{"id":"square-reddit"},{"id":"square-snapchat"},{"id":"squarespace"},{"id":"square-steam"},{"id":"square-tumblr"},{"id":"square-twitter"},{"id":"square-viadeo"},{"id":"square-vimeo"},{"id":"square-whatsapp"},{"id":"square-xing"},{"id":"square-youtube"},{"id":"stack-exchange"},{"id":"stack-overflow"},{"id":"stackpath"},{"id":"staylinked"},{"id":"steam"},{"id":"steam-symbol"},{"id":"sticker-mule"},{"id":"strava"},{"id":"stripe"},{"id":"stripe-s"},{"id":"stubber"},{"id":"studiovinari"},{"id":"stumbleupon"},{"id":"stumbleupon-circle"},{"id":"superpowers"},{"id":"supple"},{"id":"suse"},{"id":"swift"},{"id":"symfony"},{"id":"teamspeak"},{"id":"telegram"},{"id":"tencent-weibo"},{"id":"themeco"},{"id":"themeisle"},{"id":"the-red-yeti"},{"id":"think-peaks"},{"id":"tiktok"},{"id":"trade-federation"},{"id":"trello"},{"id":"tumblr"},{"id":"twitch"},{"id":"twitter"},{"id":"typo3"},{"id":"uber"},{"id":"ubuntu"},{"id":"uikit"},{"id":"umbraco"},{"id":"uncharted"},{"id":"uniregistry"},{"id":"unity"},{"id":"unsplash"},{"id":"untappd"},{"id":"ups"},{"id":"usb"},{"id":"usps"},{"id":"ussunnah"},{"id":"vaadin"},{"id":"viacoin"},{"id":"viadeo"},{"id":"viber"},{"id":"vimeo"},{"id":"vimeo-v"},{"id":"vine"},{"id":"vk"},{"id":"vnv"},{"id":"vuejs"},{"id":"watchman-monitoring"},{"id":"waze"},{"id":"weebly"},{"id":"weibo"},{"id":"weixin"},{"id":"whatsapp"},{"id":"whmcs"},{"id":"wikipedia-w"},{"id":"windows"},{"id":"wirsindhandwerk"},{"id":"wix"},{"id":"wizards-of-the-coast"},{"id":"wodu"},{"id":"wolf-pack-battalion"},{"id":"wordpress"},{"id":"wordpress-simple"},{"id":"wpbeginner"},{"id":"wpexplorer"},{"id":"wpforms"},{"id":"wpressr"},{"id":"xbox"},{"id":"xing"},{"id":"yahoo"},{"id":"yammer"},{"id":"yandex"},{"id":"yandex-international"},{"id":"yarn"},{"id":"y-combinator"},{"id":"yelp"},{"id":"yoast"},{"id":"youtube"},{"id":"zhihu"}]');
var faRegular = JSON.parse('[{"id":"address-book"},{"id":"address-card"},{"id":"bell"},{"id":"bell-slash"},{"id":"bookmark"},{"id":"building"},{"id":"calendar"},{"id":"calendar-check"},{"id":"calendar-days"},{"id":"calendar-minus"},{"id":"calendar-plus"},{"id":"calendar-xmark"},{"id":"chart-bar"},{"id":"chess-bishop"},{"id":"chess-king"},{"id":"chess-knight"},{"id":"chess-pawn"},{"id":"chess-queen"},{"id":"chess-rook"},{"id":"circle"},{"id":"circle-check"},{"id":"circle-dot"},{"id":"circle-down"},{"id":"circle-left"},{"id":"circle-pause"},{"id":"circle-play"},{"id":"circle-question"},{"id":"circle-right"},{"id":"circle-stop"},{"id":"circle-up"},{"id":"circle-user"},{"id":"circle-xmark"},{"id":"clipboard"},{"id":"clock"},{"id":"clone"},{"id":"closed-captioning"},{"id":"comment"},{"id":"comment-dots"},{"id":"comments"},{"id":"compass"},{"id":"copy"},{"id":"copyright"},{"id":"credit-card"},{"id":"envelope"},{"id":"envelope-open"},{"id":"eye"},{"id":"eye-slash"},{"id":"face-angry"},{"id":"face-dizzy"},{"id":"face-flushed"},{"id":"face-frown"},{"id":"face-frown-open"},{"id":"face-grimace"},{"id":"face-grin"},{"id":"face-grin-beam"},{"id":"face-grin-beam-sweat"},{"id":"face-grin-hearts"},{"id":"face-grin-squint"},{"id":"face-grin-squint-tears"},{"id":"face-grin-stars"},{"id":"face-grin-tears"},{"id":"face-grin-tongue"},{"id":"face-grin-tongue-squint"},{"id":"face-grin-tongue-wink"},{"id":"face-grin-wide"},{"id":"face-grin-wink"},{"id":"face-kiss"},{"id":"face-kiss-beam"},{"id":"face-kiss-wink-heart"},{"id":"face-laugh"},{"id":"face-laugh-beam"},{"id":"face-laugh-squint"},{"id":"face-laugh-wink"},{"id":"face-meh"},{"id":"face-meh-blank"},{"id":"face-rolling-eyes"},{"id":"face-sad-cry"},{"id":"face-sad-tear"},{"id":"face-smile"},{"id":"face-smile-beam"},{"id":"face-smile-wink"},{"id":"face-surprise"},{"id":"face-tired"},{"id":"file"},{"id":"file-audio"},{"id":"file-code"},{"id":"file-excel"},{"id":"file-image"},{"id":"file-lines"},{"id":"file-pdf"},{"id":"file-powerpoint"},{"id":"file-video"},{"id":"file-word"},{"id":"file-zipper"},{"id":"flag"},{"id":"floppy-disk"},{"id":"folder"},{"id":"folder-closed"},{"id":"folder-open"},{"id":"font-awesome"},{"id":"futbol"},{"id":"gem"},{"id":"hand"},{"id":"hand-back-fist"},{"id":"hand-lizard"},{"id":"hand-peace"},{"id":"hand-point-down"},{"id":"hand-pointer"},{"id":"hand-point-left"},{"id":"hand-point-right"},{"id":"hand-point-up"},{"id":"hand-scissors"},{"id":"handshake"},{"id":"hand-spock"},{"id":"hard-drive"},{"id":"heart"},{"id":"hospital"},{"id":"hourglass"},{"id":"hourglass-half"},{"id":"id-badge"},{"id":"id-card"},{"id":"image"},{"id":"images"},{"id":"keyboard"},{"id":"lemon"},{"id":"life-ring"},{"id":"lightbulb"},{"id":"map"},{"id":"message"},{"id":"money-bill-1"},{"id":"moon"},{"id":"newspaper"},{"id":"note-sticky"},{"id":"object-group"},{"id":"object-ungroup"},{"id":"paper-plane"},{"id":"paste"},{"id":"pen-to-square"},{"id":"rectangle-list"},{"id":"rectangle-xmark"},{"id":"registered"},{"id":"share-from-square"},{"id":"snowflake"},{"id":"square"},{"id":"square-caret-down"},{"id":"square-caret-left"},{"id":"square-caret-right"},{"id":"square-caret-up"},{"id":"square-check"},{"id":"square-full"},{"id":"square-minus"},{"id":"square-plus"},{"id":"star"},{"id":"star-half"},{"id":"star-half-stroke"},{"id":"sun"},{"id":"thumbs-down"},{"id":"thumbs-up"},{"id":"trash-can"},{"id":"user"},{"id":"window-maximize"},{"id":"window-minimize"},{"id":"window-restore"}]');
var faSolid = JSON.parse('[{"id":"0"},{"id":"1"},{"id":"2"},{"id":"3"},{"id":"4"},{"id":"5"},{"id":"6"},{"id":"7"},{"id":"8"},{"id":"9"},{"id":"a"},{"id":"address-book"},{"id":"address-card"},{"id":"align-center"},{"id":"align-justify"},{"id":"align-left"},{"id":"align-right"},{"id":"anchor"},{"id":"anchor-circle-check"},{"id":"anchor-circle-exclamation"},{"id":"anchor-circle-xmark"},{"id":"anchor-lock"},{"id":"angle-down"},{"id":"angle-left"},{"id":"angle-right"},{"id":"angles-down"},{"id":"angles-left"},{"id":"angles-right"},{"id":"angles-up"},{"id":"angle-up"},{"id":"ankh"},{"id":"apple-whole"},{"id":"archway"},{"id":"arrow-down"},{"id":"arrow-down-1-9"},{"id":"arrow-down-9-1"},{"id":"arrow-down-a-z"},{"id":"arrow-down-long"},{"id":"arrow-down-short-wide"},{"id":"arrow-down-up-across-line"},{"id":"arrow-down-up-lock"},{"id":"arrow-down-wide-short"},{"id":"arrow-down-z-a"},{"id":"arrow-left"},{"id":"arrow-left-long"},{"id":"arrow-pointer"},{"id":"arrow-right"},{"id":"arrow-right-arrow-left"},{"id":"arrow-right-from-bracket"},{"id":"arrow-right-long"},{"id":"arrow-right-to-bracket"},{"id":"arrow-right-to-city"},{"id":"arrow-rotate-left"},{"id":"arrow-rotate-right"},{"id":"arrows-down-to-line"},{"id":"arrows-down-to-people"},{"id":"arrows-left-right"},{"id":"arrows-left-right-to-line"},{"id":"arrows-rotate"},{"id":"arrows-spin"},{"id":"arrows-split-up-and-left"},{"id":"arrows-to-circle"},{"id":"arrows-to-dot"},{"id":"arrows-to-eye"},{"id":"arrows-turn-right"},{"id":"arrows-turn-to-dots"},{"id":"arrows-up-down"},{"id":"arrows-up-down-left-right"},{"id":"arrows-up-to-line"},{"id":"arrow-trend-down"},{"id":"arrow-trend-up"},{"id":"arrow-turn-down"},{"id":"arrow-turn-up"},{"id":"arrow-up"},{"id":"arrow-up-1-9"},{"id":"arrow-up-9-1"},{"id":"arrow-up-a-z"},{"id":"arrow-up-from-bracket"},{"id":"arrow-up-from-ground-water"},{"id":"arrow-up-from-water-pump"},{"id":"arrow-up-long"},{"id":"arrow-up-right-dots"},{"id":"arrow-up-right-from-square"},{"id":"arrow-up-short-wide"},{"id":"arrow-up-wide-short"},{"id":"arrow-up-z-a"},{"id":"asterisk"},{"id":"at"},{"id":"atom"},{"id":"audio-description"},{"id":"austral-sign"},{"id":"award"},{"id":"b"},{"id":"baby"},{"id":"baby-carriage"},{"id":"backward"},{"id":"backward-fast"},{"id":"backward-step"},{"id":"bacon"},{"id":"bacteria"},{"id":"bacterium"},{"id":"bag-shopping"},{"id":"bahai"},{"id":"baht-sign"},{"id":"ban"},{"id":"bandage"},{"id":"bangladeshi-taka-sign"},{"id":"ban-smoking"},{"id":"barcode"},{"id":"bars"},{"id":"bars-progress"},{"id":"bars-staggered"},{"id":"baseball"},{"id":"baseball-bat-ball"},{"id":"basketball"},{"id":"basket-shopping"},{"id":"bath"},{"id":"battery-empty"},{"id":"battery-full"},{"id":"battery-half"},{"id":"battery-quarter"},{"id":"battery-three-quarters"},{"id":"bed"},{"id":"bed-pulse"},{"id":"beer-mug-empty"},{"id":"bell"},{"id":"bell-concierge"},{"id":"bell-slash"},{"id":"bezier-curve"},{"id":"bicycle"},{"id":"binoculars"},{"id":"biohazard"},{"id":"bitcoin-sign"},{"id":"blender"},{"id":"blender-phone"},{"id":"blog"},{"id":"bold"},{"id":"bolt"},{"id":"bolt-lightning"},{"id":"bomb"},{"id":"bone"},{"id":"bong"},{"id":"book"},{"id":"book-atlas"},{"id":"book-bible"},{"id":"book-bookmark"},{"id":"book-journal-whills"},{"id":"bookmark"},{"id":"book-medical"},{"id":"book-open"},{"id":"book-open-reader"},{"id":"book-quran"},{"id":"book-skull"},{"id":"book-tanakh"},{"id":"border-all"},{"id":"border-none"},{"id":"border-top-left"},{"id":"bore-hole"},{"id":"bottle-droplet"},{"id":"bottle-water"},{"id":"bowl-food"},{"id":"bowling-ball"},{"id":"bowl-rice"},{"id":"box"},{"id":"box-archive"},{"id":"boxes-packing"},{"id":"boxes-stacked"},{"id":"box-open"},{"id":"box-tissue"},{"id":"braille"},{"id":"brain"},{"id":"brazilian-real-sign"},{"id":"bread-slice"},{"id":"bridge"},{"id":"bridge-circle-check"},{"id":"bridge-circle-exclamation"},{"id":"bridge-circle-xmark"},{"id":"bridge-lock"},{"id":"bridge-water"},{"id":"briefcase"},{"id":"briefcase-medical"},{"id":"broom"},{"id":"broom-ball"},{"id":"brush"},{"id":"bucket"},{"id":"bug"},{"id":"bugs"},{"id":"bug-slash"},{"id":"building"},{"id":"building-circle-arrow-right"},{"id":"building-circle-check"},{"id":"building-circle-exclamation"},{"id":"building-circle-xmark"},{"id":"building-columns"},{"id":"building-flag"},{"id":"building-lock"},{"id":"building-ngo"},{"id":"building-shield"},{"id":"building-un"},{"id":"building-user"},{"id":"building-wheat"},{"id":"bullhorn"},{"id":"bullseye"},{"id":"burger"},{"id":"burst"},{"id":"bus"},{"id":"business-time"},{"id":"bus-simple"},{"id":"c"},{"id":"cable-car"},{"id":"cake-candles"},{"id":"calculator"},{"id":"calendar"},{"id":"calendar-check"},{"id":"calendar-day"},{"id":"calendar-days"},{"id":"calendar-minus"},{"id":"calendar-plus"},{"id":"calendar-week"},{"id":"calendar-xmark"},{"id":"camera"},{"id":"camera-retro"},{"id":"camera-rotate"},{"id":"campground"},{"id":"candy-cane"},{"id":"cannabis"},{"id":"capsules"},{"id":"car"},{"id":"caravan"},{"id":"car-battery"},{"id":"car-burst"},{"id":"caret-down"},{"id":"caret-left"},{"id":"caret-right"},{"id":"caret-up"},{"id":"car-on"},{"id":"car-rear"},{"id":"carrot"},{"id":"car-side"},{"id":"cart-arrow-down"},{"id":"cart-flatbed"},{"id":"cart-flatbed-suitcase"},{"id":"cart-plus"},{"id":"cart-shopping"},{"id":"car-tunnel"},{"id":"cash-register"},{"id":"cat"},{"id":"cedi-sign"},{"id":"cent-sign"},{"id":"certificate"},{"id":"chair"},{"id":"chalkboard"},{"id":"chalkboard-user"},{"id":"champagne-glasses"},{"id":"charging-station"},{"id":"chart-area"},{"id":"chart-bar"},{"id":"chart-column"},{"id":"chart-gantt"},{"id":"chart-line"},{"id":"chart-pie"},{"id":"chart-simple"},{"id":"check"},{"id":"check-double"},{"id":"check-to-slot"},{"id":"cheese"},{"id":"chess"},{"id":"chess-bishop"},{"id":"chess-board"},{"id":"chess-king"},{"id":"chess-knight"},{"id":"chess-pawn"},{"id":"chess-queen"},{"id":"chess-rook"},{"id":"chevron-down"},{"id":"chevron-left"},{"id":"chevron-right"},{"id":"chevron-up"},{"id":"child"},{"id":"child-combatant"},{"id":"child-dress"},{"id":"child-reaching"},{"id":"children"},{"id":"church"},{"id":"circle"},{"id":"circle-arrow-down"},{"id":"circle-arrow-left"},{"id":"circle-arrow-right"},{"id":"circle-arrow-up"},{"id":"circle-check"},{"id":"circle-chevron-down"},{"id":"circle-chevron-left"},{"id":"circle-chevron-right"},{"id":"circle-chevron-up"},{"id":"circle-dollar-to-slot"},{"id":"circle-dot"},{"id":"circle-down"},{"id":"circle-exclamation"},{"id":"circle-h"},{"id":"circle-half-stroke"},{"id":"circle-info"},{"id":"circle-left"},{"id":"circle-minus"},{"id":"circle-nodes"},{"id":"circle-notch"},{"id":"circle-pause"},{"id":"circle-play"},{"id":"circle-plus"},{"id":"circle-question"},{"id":"circle-radiation"},{"id":"circle-right"},{"id":"circle-stop"},{"id":"circle-up"},{"id":"circle-user"},{"id":"circle-xmark"},{"id":"city"},{"id":"clapperboard"},{"id":"clipboard"},{"id":"clipboard-check"},{"id":"clipboard-list"},{"id":"clipboard-question"},{"id":"clipboard-user"},{"id":"clock"},{"id":"clock-rotate-left"},{"id":"clone"},{"id":"closed-captioning"},{"id":"cloud"},{"id":"cloud-arrow-down"},{"id":"cloud-arrow-up"},{"id":"cloud-bolt"},{"id":"cloud-meatball"},{"id":"cloud-moon"},{"id":"cloud-moon-rain"},{"id":"cloud-rain"},{"id":"cloud-showers-heavy"},{"id":"cloud-showers-water"},{"id":"cloud-sun"},{"id":"cloud-sun-rain"},{"id":"clover"},{"id":"code"},{"id":"code-branch"},{"id":"code-commit"},{"id":"code-compare"},{"id":"code-fork"},{"id":"code-merge"},{"id":"code-pull-request"},{"id":"coins"},{"id":"colon-sign"},{"id":"comment"},{"id":"comment-dollar"},{"id":"comment-dots"},{"id":"comment-medical"},{"id":"comments"},{"id":"comments-dollar"},{"id":"comment-slash"},{"id":"comment-sms"},{"id":"compact-disc"},{"id":"compass"},{"id":"compass-drafting"},{"id":"compress"},{"id":"computer"},{"id":"computer-mouse"},{"id":"cookie"},{"id":"cookie-bite"},{"id":"copy"},{"id":"copyright"},{"id":"couch"},{"id":"cow"},{"id":"credit-card"},{"id":"crop"},{"id":"crop-simple"},{"id":"cross"},{"id":"crosshairs"},{"id":"crow"},{"id":"crown"},{"id":"crutch"},{"id":"cruzeiro-sign"},{"id":"cube"},{"id":"cubes"},{"id":"cubes-stacked"},{"id":"d"},{"id":"database"},{"id":"delete-left"},{"id":"democrat"},{"id":"desktop"},{"id":"dharmachakra"},{"id":"diagram-next"},{"id":"diagram-predecessor"},{"id":"diagram-project"},{"id":"diagram-successor"},{"id":"diamond"},{"id":"diamond-turn-right"},{"id":"dice"},{"id":"dice-d20"},{"id":"dice-d6"},{"id":"dice-five"},{"id":"dice-four"},{"id":"dice-one"},{"id":"dice-six"},{"id":"dice-three"},{"id":"dice-two"},{"id":"disease"},{"id":"display"},{"id":"divide"},{"id":"dna"},{"id":"dog"},{"id":"dollar-sign"},{"id":"dolly"},{"id":"dong-sign"},{"id":"door-closed"},{"id":"door-open"},{"id":"dove"},{"id":"down-left-and-up-right-to-center"},{"id":"download"},{"id":"down-long"},{"id":"dragon"},{"id":"draw-polygon"},{"id":"droplet"},{"id":"droplet-slash"},{"id":"drum"},{"id":"drum-steelpan"},{"id":"drumstick-bite"},{"id":"dumbbell"},{"id":"dumpster"},{"id":"dumpster-fire"},{"id":"dungeon"},{"id":"e"},{"id":"ear-deaf"},{"id":"ear-listen"},{"id":"earth-africa"},{"id":"earth-americas"},{"id":"earth-asia"},{"id":"earth-europe"},{"id":"earth-oceania"},{"id":"egg"},{"id":"eject"},{"id":"elevator"},{"id":"ellipsis"},{"id":"ellipsis-vertical"},{"id":"envelope"},{"id":"envelope-circle-check"},{"id":"envelope-open"},{"id":"envelope-open-text"},{"id":"envelopes-bulk"},{"id":"equals"},{"id":"eraser"},{"id":"ethernet"},{"id":"euro-sign"},{"id":"exclamation"},{"id":"expand"},{"id":"explosion"},{"id":"eye"},{"id":"eye-dropper"},{"id":"eye-low-vision"},{"id":"eye-slash"},{"id":"f"},{"id":"face-angry"},{"id":"face-dizzy"},{"id":"face-flushed"},{"id":"face-frown"},{"id":"face-frown-open"},{"id":"face-grimace"},{"id":"face-grin"},{"id":"face-grin-beam"},{"id":"face-grin-beam-sweat"},{"id":"face-grin-hearts"},{"id":"face-grin-squint"},{"id":"face-grin-squint-tears"},{"id":"face-grin-stars"},{"id":"face-grin-tears"},{"id":"face-grin-tongue"},{"id":"face-grin-tongue-squint"},{"id":"face-grin-tongue-wink"},{"id":"face-grin-wide"},{"id":"face-grin-wink"},{"id":"face-kiss"},{"id":"face-kiss-beam"},{"id":"face-kiss-wink-heart"},{"id":"face-laugh"},{"id":"face-laugh-beam"},{"id":"face-laugh-squint"},{"id":"face-laugh-wink"},{"id":"face-meh"},{"id":"face-meh-blank"},{"id":"face-rolling-eyes"},{"id":"face-sad-cry"},{"id":"face-sad-tear"},{"id":"face-smile"},{"id":"face-smile-beam"},{"id":"face-smile-wink"},{"id":"face-surprise"},{"id":"face-tired"},{"id":"fan"},{"id":"faucet"},{"id":"faucet-drip"},{"id":"fax"},{"id":"feather"},{"id":"feather-pointed"},{"id":"ferry"},{"id":"file"},{"id":"file-arrow-down"},{"id":"file-arrow-up"},{"id":"file-audio"},{"id":"file-circle-check"},{"id":"file-circle-exclamation"},{"id":"file-circle-minus"},{"id":"file-circle-plus"},{"id":"file-circle-question"},{"id":"file-circle-xmark"},{"id":"file-code"},{"id":"file-contract"},{"id":"file-csv"},{"id":"file-excel"},{"id":"file-export"},{"id":"file-image"},{"id":"file-import"},{"id":"file-invoice"},{"id":"file-invoice-dollar"},{"id":"file-lines"},{"id":"file-medical"},{"id":"file-pdf"},{"id":"file-pen"},{"id":"file-powerpoint"},{"id":"file-prescription"},{"id":"file-shield"},{"id":"file-signature"},{"id":"file-video"},{"id":"file-waveform"},{"id":"file-word"},{"id":"file-zipper"},{"id":"fill"},{"id":"fill-drip"},{"id":"film"},{"id":"filter"},{"id":"filter-circle-dollar"},{"id":"filter-circle-xmark"},{"id":"fingerprint"},{"id":"fire"},{"id":"fire-burner"},{"id":"fire-extinguisher"},{"id":"fire-flame-curved"},{"id":"fire-flame-simple"},{"id":"fish"},{"id":"fish-fins"},{"id":"flag"},{"id":"flag-checkered"},{"id":"flag-usa"},{"id":"flask"},{"id":"flask-vial"},{"id":"floppy-disk"},{"id":"florin-sign"},{"id":"folder"},{"id":"folder-closed"},{"id":"folder-minus"},{"id":"folder-open"},{"id":"folder-plus"},{"id":"folder-tree"},{"id":"font"},{"id":"font-awesome"},{"id":"football"},{"id":"forward"},{"id":"forward-fast"},{"id":"forward-step"},{"id":"franc-sign"},{"id":"frog"},{"id":"futbol"},{"id":"g"},{"id":"gamepad"},{"id":"gas-pump"},{"id":"gauge"},{"id":"gauge-high"},{"id":"gauge-simple"},{"id":"gauge-simple-high"},{"id":"gavel"},{"id":"gear"},{"id":"gears"},{"id":"gem"},{"id":"genderless"},{"id":"ghost"},{"id":"gift"},{"id":"gifts"},{"id":"glasses"},{"id":"glass-water"},{"id":"glass-water-droplet"},{"id":"globe"},{"id":"golf-ball-tee"},{"id":"gopuram"},{"id":"graduation-cap"},{"id":"greater-than"},{"id":"greater-than-equal"},{"id":"grip"},{"id":"grip-lines"},{"id":"grip-lines-vertical"},{"id":"grip-vertical"},{"id":"group-arrows-rotate"},{"id":"guarani-sign"},{"id":"guitar"},{"id":"gun"},{"id":"h"},{"id":"hammer"},{"id":"hamsa"},{"id":"hand"},{"id":"hand-back-fist"},{"id":"handcuffs"},{"id":"hand-dots"},{"id":"hand-fist"},{"id":"hand-holding"},{"id":"hand-holding-dollar"},{"id":"hand-holding-droplet"},{"id":"hand-holding-hand"},{"id":"hand-holding-heart"},{"id":"hand-holding-medical"},{"id":"hand-lizard"},{"id":"hand-middle-finger"},{"id":"hand-peace"},{"id":"hand-point-down"},{"id":"hand-pointer"},{"id":"hand-point-left"},{"id":"hand-point-right"},{"id":"hand-point-up"},{"id":"hands"},{"id":"hands-asl-interpreting"},{"id":"hands-bound"},{"id":"hands-bubbles"},{"id":"hand-scissors"},{"id":"hands-clapping"},{"id":"handshake"},{"id":"handshake-angle"},{"id":"handshake-simple"},{"id":"handshake-simple-slash"},{"id":"handshake-slash"},{"id":"hands-holding"},{"id":"hands-holding-child"},{"id":"hands-holding-circle"},{"id":"hand-sparkles"},{"id":"hand-spock"},{"id":"hands-praying"},{"id":"hanukiah"},{"id":"hard-drive"},{"id":"hashtag"},{"id":"hat-cowboy"},{"id":"hat-cowboy-side"},{"id":"hat-wizard"},{"id":"heading"},{"id":"headphones"},{"id":"headphones-simple"},{"id":"headset"},{"id":"head-side-cough"},{"id":"head-side-cough-slash"},{"id":"head-side-mask"},{"id":"head-side-virus"},{"id":"heart"},{"id":"heart-circle-bolt"},{"id":"heart-circle-check"},{"id":"heart-circle-exclamation"},{"id":"heart-circle-minus"},{"id":"heart-circle-plus"},{"id":"heart-circle-xmark"},{"id":"heart-crack"},{"id":"heart-pulse"},{"id":"helicopter"},{"id":"helicopter-symbol"},{"id":"helmet-safety"},{"id":"helmet-un"},{"id":"highlighter"},{"id":"hill-avalanche"},{"id":"hill-rockslide"},{"id":"hippo"},{"id":"hockey-puck"},{"id":"holly-berry"},{"id":"horse"},{"id":"horse-head"},{"id":"hospital"},{"id":"hospital-user"},{"id":"hotdog"},{"id":"hotel"},{"id":"hot-tub-person"},{"id":"hourglass"},{"id":"hourglass-end"},{"id":"hourglass-half"},{"id":"hourglass-start"},{"id":"house"},{"id":"house-chimney"},{"id":"house-chimney-crack"},{"id":"house-chimney-medical"},{"id":"house-chimney-user"},{"id":"house-chimney-window"},{"id":"house-circle-check"},{"id":"house-circle-exclamation"},{"id":"house-circle-xmark"},{"id":"house-crack"},{"id":"house-fire"},{"id":"house-flag"},{"id":"house-flood-water"},{"id":"house-flood-water-circle-arrow-right"},{"id":"house-laptop"},{"id":"house-lock"},{"id":"house-medical"},{"id":"house-medical-circle-check"},{"id":"house-medical-circle-exclamation"},{"id":"house-medical-circle-xmark"},{"id":"house-medical-flag"},{"id":"house-signal"},{"id":"house-tsunami"},{"id":"house-user"},{"id":"hryvnia-sign"},{"id":"hurricane"},{"id":"i"},{"id":"ice-cream"},{"id":"icicles"},{"id":"icons"},{"id":"i-cursor"},{"id":"id-badge"},{"id":"id-card"},{"id":"id-card-clip"},{"id":"igloo"},{"id":"image"},{"id":"image-portrait"},{"id":"images"},{"id":"inbox"},{"id":"indent"},{"id":"indian-rupee-sign"},{"id":"industry"},{"id":"infinity"},{"id":"info"},{"id":"italic"},{"id":"j"},{"id":"jar"},{"id":"jar-wheat"},{"id":"jedi"},{"id":"jet-fighter"},{"id":"jet-fighter-up"},{"id":"joint"},{"id":"jug-detergent"},{"id":"k"},{"id":"kaaba"},{"id":"key"},{"id":"keyboard"},{"id":"khanda"},{"id":"kip-sign"},{"id":"kitchen-set"},{"id":"kit-medical"},{"id":"kiwi-bird"},{"id":"l"},{"id":"landmark"},{"id":"landmark-dome"},{"id":"landmark-flag"},{"id":"land-mine-on"},{"id":"language"},{"id":"laptop"},{"id":"laptop-code"},{"id":"laptop-file"},{"id":"laptop-medical"},{"id":"lari-sign"},{"id":"layer-group"},{"id":"leaf"},{"id":"left-long"},{"id":"left-right"},{"id":"lemon"},{"id":"less-than"},{"id":"less-than-equal"},{"id":"life-ring"},{"id":"lightbulb"},{"id":"lines-leaning"},{"id":"link"},{"id":"link-slash"},{"id":"lira-sign"},{"id":"list"},{"id":"list-check"},{"id":"list-ol"},{"id":"list-ul"},{"id":"litecoin-sign"},{"id":"location-arrow"},{"id":"location-crosshairs"},{"id":"location-dot"},{"id":"location-pin"},{"id":"location-pin-lock"},{"id":"lock"},{"id":"lock-open"},{"id":"locust"},{"id":"lungs"},{"id":"lungs-virus"},{"id":"m"},{"id":"magnet"},{"id":"magnifying-glass"},{"id":"magnifying-glass-arrow-right"},{"id":"magnifying-glass-chart"},{"id":"magnifying-glass-dollar"},{"id":"magnifying-glass-location"},{"id":"magnifying-glass-minus"},{"id":"magnifying-glass-plus"},{"id":"manat-sign"},{"id":"map"},{"id":"map-location"},{"id":"map-location-dot"},{"id":"map-pin"},{"id":"marker"},{"id":"mars"},{"id":"mars-and-venus"},{"id":"mars-and-venus-burst"},{"id":"mars-double"},{"id":"mars-stroke"},{"id":"mars-stroke-right"},{"id":"mars-stroke-up"},{"id":"martini-glass"},{"id":"martini-glass-citrus"},{"id":"martini-glass-empty"},{"id":"mask"},{"id":"mask-face"},{"id":"masks-theater"},{"id":"mask-ventilator"},{"id":"mattress-pillow"},{"id":"maximize"},{"id":"medal"},{"id":"memory"},{"id":"menorah"},{"id":"mercury"},{"id":"message"},{"id":"meteor"},{"id":"microchip"},{"id":"microphone"},{"id":"microphone-lines"},{"id":"microphone-lines-slash"},{"id":"microphone-slash"},{"id":"microscope"},{"id":"mill-sign"},{"id":"minimize"},{"id":"minus"},{"id":"mitten"},{"id":"mobile"},{"id":"mobile-button"},{"id":"mobile-retro"},{"id":"mobile-screen"},{"id":"mobile-screen-button"},{"id":"money-bill"},{"id":"money-bill-1"},{"id":"money-bill-1-wave"},{"id":"money-bills"},{"id":"money-bill-transfer"},{"id":"money-bill-trend-up"},{"id":"money-bill-wave"},{"id":"money-bill-wheat"},{"id":"money-check"},{"id":"money-check-dollar"},{"id":"monument"},{"id":"moon"},{"id":"mortar-pestle"},{"id":"mosque"},{"id":"mosquito"},{"id":"mosquito-net"},{"id":"motorcycle"},{"id":"mound"},{"id":"mountain"},{"id":"mountain-city"},{"id":"mountain-sun"},{"id":"mug-hot"},{"id":"mug-saucer"},{"id":"music"},{"id":"n"},{"id":"naira-sign"},{"id":"network-wired"},{"id":"neuter"},{"id":"newspaper"},{"id":"notdef"},{"id":"not-equal"},{"id":"notes-medical"},{"id":"note-sticky"},{"id":"o"},{"id":"object-group"},{"id":"object-ungroup"},{"id":"oil-can"},{"id":"oil-well"},{"id":"om"},{"id":"otter"},{"id":"outdent"},{"id":"p"},{"id":"pager"},{"id":"paintbrush"},{"id":"paint-roller"},{"id":"palette"},{"id":"pallet"},{"id":"panorama"},{"id":"paperclip"},{"id":"paper-plane"},{"id":"parachute-box"},{"id":"paragraph"},{"id":"passport"},{"id":"paste"},{"id":"pause"},{"id":"paw"},{"id":"peace"},{"id":"pen"},{"id":"pencil"},{"id":"pen-clip"},{"id":"pen-fancy"},{"id":"pen-nib"},{"id":"pen-ruler"},{"id":"pen-to-square"},{"id":"people-arrows"},{"id":"people-carry-box"},{"id":"people-group"},{"id":"people-line"},{"id":"people-pulling"},{"id":"people-robbery"},{"id":"people-roof"},{"id":"pepper-hot"},{"id":"percent"},{"id":"person"},{"id":"person-arrow-down-to-line"},{"id":"person-arrow-up-from-line"},{"id":"person-biking"},{"id":"person-booth"},{"id":"person-breastfeeding"},{"id":"person-burst"},{"id":"person-cane"},{"id":"person-chalkboard"},{"id":"person-circle-check"},{"id":"person-circle-exclamation"},{"id":"person-circle-minus"},{"id":"person-circle-plus"},{"id":"person-circle-question"},{"id":"person-circle-xmark"},{"id":"person-digging"},{"id":"person-dots-from-line"},{"id":"person-dress"},{"id":"person-dress-burst"},{"id":"person-drowning"},{"id":"person-falling"},{"id":"person-falling-burst"},{"id":"person-half-dress"},{"id":"person-harassing"},{"id":"person-hiking"},{"id":"person-military-pointing"},{"id":"person-military-rifle"},{"id":"person-military-to-person"},{"id":"person-praying"},{"id":"person-pregnant"},{"id":"person-rays"},{"id":"person-rifle"},{"id":"person-running"},{"id":"person-shelter"},{"id":"person-skating"},{"id":"person-skiing"},{"id":"person-skiing-nordic"},{"id":"person-snowboarding"},{"id":"person-swimming"},{"id":"person-through-window"},{"id":"person-walking"},{"id":"person-walking-arrow-loop-left"},{"id":"person-walking-arrow-right"},{"id":"person-walking-dashed-line-arrow-right"},{"id":"person-walking-luggage"},{"id":"person-walking-with-cane"},{"id":"peseta-sign"},{"id":"peso-sign"},{"id":"phone"},{"id":"phone-flip"},{"id":"phone-slash"},{"id":"phone-volume"},{"id":"photo-film"},{"id":"piggy-bank"},{"id":"pills"},{"id":"pizza-slice"},{"id":"place-of-worship"},{"id":"plane"},{"id":"plane-arrival"},{"id":"plane-circle-check"},{"id":"plane-circle-exclamation"},{"id":"plane-circle-xmark"},{"id":"plane-departure"},{"id":"plane-lock"},{"id":"plane-slash"},{"id":"plane-up"},{"id":"plant-wilt"},{"id":"plate-wheat"},{"id":"play"},{"id":"plug"},{"id":"plug-circle-bolt"},{"id":"plug-circle-check"},{"id":"plug-circle-exclamation"},{"id":"plug-circle-minus"},{"id":"plug-circle-plus"},{"id":"plug-circle-xmark"},{"id":"plus"},{"id":"plus-minus"},{"id":"podcast"},{"id":"poo"},{"id":"poop"},{"id":"poo-storm"},{"id":"power-off"},{"id":"prescription"},{"id":"prescription-bottle"},{"id":"prescription-bottle-medical"},{"id":"print"},{"id":"pump-medical"},{"id":"pump-soap"},{"id":"puzzle-piece"},{"id":"q"},{"id":"qrcode"},{"id":"question"},{"id":"quote-left"},{"id":"quote-right"},{"id":"r"},{"id":"radiation"},{"id":"radio"},{"id":"rainbow"},{"id":"ranking-star"},{"id":"receipt"},{"id":"record-vinyl"},{"id":"rectangle-ad"},{"id":"rectangle-list"},{"id":"rectangle-xmark"},{"id":"recycle"},{"id":"registered"},{"id":"repeat"},{"id":"reply"},{"id":"reply-all"},{"id":"republican"},{"id":"restroom"},{"id":"retweet"},{"id":"ribbon"},{"id":"right-from-bracket"},{"id":"right-left"},{"id":"right-long"},{"id":"right-to-bracket"},{"id":"ring"},{"id":"road"},{"id":"road-barrier"},{"id":"road-bridge"},{"id":"road-circle-check"},{"id":"road-circle-exclamation"},{"id":"road-circle-xmark"},{"id":"road-lock"},{"id":"road-spikes"},{"id":"robot"},{"id":"rocket"},{"id":"rotate"},{"id":"rotate-left"},{"id":"rotate-right"},{"id":"route"},{"id":"rss"},{"id":"ruble-sign"},{"id":"rug"},{"id":"ruler"},{"id":"ruler-combined"},{"id":"ruler-horizontal"},{"id":"ruler-vertical"},{"id":"rupee-sign"},{"id":"rupiah-sign"},{"id":"s"},{"id":"sack-dollar"},{"id":"sack-xmark"},{"id":"sailboat"},{"id":"satellite"},{"id":"satellite-dish"},{"id":"scale-balanced"},{"id":"scale-unbalanced"},{"id":"scale-unbalanced-flip"},{"id":"school"},{"id":"school-circle-check"},{"id":"school-circle-exclamation"},{"id":"school-circle-xmark"},{"id":"school-flag"},{"id":"school-lock"},{"id":"scissors"},{"id":"screwdriver"},{"id":"screwdriver-wrench"},{"id":"scroll"},{"id":"scroll-torah"},{"id":"sd-card"},{"id":"section"},{"id":"seedling"},{"id":"server"},{"id":"shapes"},{"id":"share"},{"id":"share-from-square"},{"id":"share-nodes"},{"id":"sheet-plastic"},{"id":"shekel-sign"},{"id":"shield"},{"id":"shield-cat"},{"id":"shield-dog"},{"id":"shield-halved"},{"id":"shield-heart"},{"id":"shield-virus"},{"id":"ship"},{"id":"shirt"},{"id":"shoe-prints"},{"id":"shop"},{"id":"shop-lock"},{"id":"shop-slash"},{"id":"shower"},{"id":"shrimp"},{"id":"shuffle"},{"id":"shuttle-space"},{"id":"signal"},{"id":"signature"},{"id":"sign-hanging"},{"id":"signs-post"},{"id":"sim-card"},{"id":"sink"},{"id":"sitemap"},{"id":"skull"},{"id":"skull-crossbones"},{"id":"slash"},{"id":"sleigh"},{"id":"sliders"},{"id":"smog"},{"id":"smoking"},{"id":"snowflake"},{"id":"snowman"},{"id":"snowplow"},{"id":"soap"},{"id":"socks"},{"id":"solar-panel"},{"id":"sort"},{"id":"sort-down"},{"id":"sort-up"},{"id":"spa"},{"id":"spaghetti-monster-flying"},{"id":"spell-check"},{"id":"spider"},{"id":"spinner"},{"id":"splotch"},{"id":"spoon"},{"id":"spray-can"},{"id":"spray-can-sparkles"},{"id":"square"},{"id":"square-arrow-up-right"},{"id":"square-caret-down"},{"id":"square-caret-left"},{"id":"square-caret-right"},{"id":"square-caret-up"},{"id":"square-check"},{"id":"square-envelope"},{"id":"square-full"},{"id":"square-h"},{"id":"square-minus"},{"id":"square-nfi"},{"id":"square-parking"},{"id":"square-pen"},{"id":"square-person-confined"},{"id":"square-phone"},{"id":"square-phone-flip"},{"id":"square-plus"},{"id":"square-poll-horizontal"},{"id":"square-poll-vertical"},{"id":"square-root-variable"},{"id":"square-rss"},{"id":"square-share-nodes"},{"id":"square-up-right"},{"id":"square-virus"},{"id":"square-xmark"},{"id":"staff-snake"},{"id":"stairs"},{"id":"stamp"},{"id":"stapler"},{"id":"star"},{"id":"star-and-crescent"},{"id":"star-half"},{"id":"star-half-stroke"},{"id":"star-of-david"},{"id":"star-of-life"},{"id":"sterling-sign"},{"id":"stethoscope"},{"id":"stop"},{"id":"stopwatch"},{"id":"stopwatch-20"},{"id":"store"},{"id":"store-slash"},{"id":"street-view"},{"id":"strikethrough"},{"id":"stroopwafel"},{"id":"subscript"},{"id":"suitcase"},{"id":"suitcase-medical"},{"id":"suitcase-rolling"},{"id":"sun"},{"id":"sun-plant-wilt"},{"id":"superscript"},{"id":"swatchbook"},{"id":"synagogue"},{"id":"syringe"},{"id":"t"},{"id":"table"},{"id":"table-cells"},{"id":"table-cells-large"},{"id":"table-columns"},{"id":"table-list"},{"id":"tablet"},{"id":"tablet-button"},{"id":"table-tennis-paddle-ball"},{"id":"tablets"},{"id":"tablet-screen-button"},{"id":"tachograph-digital"},{"id":"tag"},{"id":"tags"},{"id":"tape"},{"id":"tarp"},{"id":"tarp-droplet"},{"id":"taxi"},{"id":"teeth"},{"id":"teeth-open"},{"id":"temperature-arrow-down"},{"id":"temperature-arrow-up"},{"id":"temperature-empty"},{"id":"temperature-full"},{"id":"temperature-half"},{"id":"temperature-high"},{"id":"temperature-low"},{"id":"temperature-quarter"},{"id":"temperature-three-quarters"},{"id":"tenge-sign"},{"id":"tent"},{"id":"tent-arrow-down-to-line"},{"id":"tent-arrow-left-right"},{"id":"tent-arrows-down"},{"id":"tent-arrow-turn-left"},{"id":"tents"},{"id":"terminal"},{"id":"text-height"},{"id":"text-slash"},{"id":"text-width"},{"id":"thermometer"},{"id":"thumbs-down"},{"id":"thumbs-up"},{"id":"thumbtack"},{"id":"ticket"},{"id":"ticket-simple"},{"id":"timeline"},{"id":"toggle-off"},{"id":"toggle-on"},{"id":"toilet"},{"id":"toilet-paper"},{"id":"toilet-paper-slash"},{"id":"toilet-portable"},{"id":"toilets-portable"},{"id":"toolbox"},{"id":"tooth"},{"id":"torii-gate"},{"id":"tornado"},{"id":"tower-broadcast"},{"id":"tower-cell"},{"id":"tower-observation"},{"id":"tractor"},{"id":"trademark"},{"id":"traffic-light"},{"id":"trailer"},{"id":"train"},{"id":"train-subway"},{"id":"train-tram"},{"id":"transgender"},{"id":"trash"},{"id":"trash-arrow-up"},{"id":"trash-can"},{"id":"trash-can-arrow-up"},{"id":"tree"},{"id":"tree-city"},{"id":"triangle-exclamation"},{"id":"trophy"},{"id":"trowel"},{"id":"trowel-bricks"},{"id":"truck"},{"id":"truck-arrow-right"},{"id":"truck-droplet"},{"id":"truck-fast"},{"id":"truck-field"},{"id":"truck-field-un"},{"id":"truck-front"},{"id":"truck-medical"},{"id":"truck-monster"},{"id":"truck-moving"},{"id":"truck-pickup"},{"id":"truck-plane"},{"id":"truck-ramp-box"},{"id":"tty"},{"id":"turkish-lira-sign"},{"id":"turn-down"},{"id":"turn-up"},{"id":"tv"},{"id":"u"},{"id":"umbrella"},{"id":"umbrella-beach"},{"id":"underline"},{"id":"universal-access"},{"id":"unlock"},{"id":"unlock-keyhole"},{"id":"up-down"},{"id":"up-down-left-right"},{"id":"upload"},{"id":"up-long"},{"id":"up-right-and-down-left-from-center"},{"id":"up-right-from-square"},{"id":"user"},{"id":"user-astronaut"},{"id":"user-check"},{"id":"user-clock"},{"id":"user-doctor"},{"id":"user-gear"},{"id":"user-graduate"},{"id":"user-group"},{"id":"user-injured"},{"id":"user-large"},{"id":"user-large-slash"},{"id":"user-lock"},{"id":"user-minus"},{"id":"user-ninja"},{"id":"user-nurse"},{"id":"user-pen"},{"id":"user-plus"},{"id":"users"},{"id":"users-between-lines"},{"id":"user-secret"},{"id":"users-gear"},{"id":"user-shield"},{"id":"user-slash"},{"id":"users-line"},{"id":"users-rays"},{"id":"users-rectangle"},{"id":"users-slash"},{"id":"users-viewfinder"},{"id":"user-tag"},{"id":"user-tie"},{"id":"user-xmark"},{"id":"utensils"},{"id":"v"},{"id":"van-shuttle"},{"id":"vault"},{"id":"vector-square"},{"id":"venus"},{"id":"venus-double"},{"id":"venus-mars"},{"id":"vest"},{"id":"vest-patches"},{"id":"vial"},{"id":"vial-circle-check"},{"id":"vials"},{"id":"vial-virus"},{"id":"video"},{"id":"video-slash"},{"id":"vihara"},{"id":"virus"},{"id":"virus-covid"},{"id":"virus-covid-slash"},{"id":"viruses"},{"id":"virus-slash"},{"id":"voicemail"},{"id":"volcano"},{"id":"volleyball"},{"id":"volume-high"},{"id":"volume-low"},{"id":"volume-off"},{"id":"volume-xmark"},{"id":"vr-cardboard"},{"id":"w"},{"id":"walkie-talkie"},{"id":"wallet"},{"id":"wand-magic"},{"id":"wand-magic-sparkles"},{"id":"wand-sparkles"},{"id":"warehouse"},{"id":"water"},{"id":"water-ladder"},{"id":"wave-square"},{"id":"weight-hanging"},{"id":"weight-scale"},{"id":"wheat-awn"},{"id":"wheat-awn-circle-exclamation"},{"id":"wheelchair"},{"id":"wheelchair-move"},{"id":"whiskey-glass"},{"id":"wifi"},{"id":"wind"},{"id":"window-maximize"},{"id":"window-minimize"},{"id":"window-restore"},{"id":"wine-bottle"},{"id":"wine-glass"},{"id":"wine-glass-empty"},{"id":"won-sign"},{"id":"worm"},{"id":"wrench"},{"id":"x"},{"id":"xmark"},{"id":"xmarks-lines"},{"id":"x-ray"},{"id":"y"},{"id":"yen-sign"},{"id":"yin-yang"},{"id":"z"}]');
function faIcons(fas, ty) {
	var icons='';
	for(var x in fas){
	var ids = fas[x].id;
	var id = ids.split('-').join('<br/>');
	icons += '<a href="#" onclick="klick(this);return false;"><span class="fa'+ty+' fa-'+ids+'"></span>'+id+'</a>';
	}
	return icons;
}
return {
	title:'FontAwesome6 Icons',
	minWidth:500,
	minHeight:400,
	resizable:false,
	contents:[{
	id:'font-awesome',
	label:'Add icon',
	elements:[
		{
		type:'hbox',
		widths:['25%','10%','15%','50%'],
		children:[
		{
			type:'text',
			id:'colorChooser',
			className:'colorChooser',
			label:'Color',
			setup:function(widget){
			var color = widget.data.color != '' ? widget.data.color:'';
			this.setValue(color);
			},
			commit:function(widget){
			widget.setData('color', this.getValue());
			}
		},
		{
			type:'button',label:'Color',style:'margin-top:1.35em',
			onClick:function(){
			editor.getColorFromDialog(function(color){
			document.getElementsByClassName('colorChooser')[0].getElementsByTagName('input')[0].value = color;
			}, this);
			}
		},
		{
			type:'text',id:'size',className:'size',label:'Size',setup: function(widget){this.setValue(widget.data.size);},
			commit: function(widget){widget.setData('size', this.getValue());}
		},
		{
			type:'text',id:'faSearch',className:'faSearch',label:'Search',onKeyUp:function(e){searchIcon(e.sender.$.value);}
		}
		]
		},
		{
		type:'hbox',
		widths:['15%','15%','15%','15%','40%'],
		children:[
		{
		type:'select',id:'fixwidth',className:'faSelect',label:'Fixed Width',items:[['No'],['Yes']],'default':'No',
			commit:function(widget){widget.setData('fixwidth',this.getValue());}
		},
		{
		type:'select',id:'bordered',className:'faSelect',label:'Bordered',items:[['No'],['Yes']],'default':'No',
			commit:function(widget){widget.setData('bordered',this.getValue());}
		},
		{
		type:'select',id:'spinning',className:'faSelect',label:'Spinning',items:[['No'],['Yes']],'default':'No',
			commit:function(widget){widget.setData('spinning',this.getValue());}
		},
		{
		type:'select',id:'rotating',className:'faSelect',label:'Rotating',items:[['No'],['fa-rotate-90'],['fa-rotate-180'],['fa-rotate-270'],['fa-flip-horizontal'],['fa-flip-vertical'],['fa-flip-both']],'default':'No',
			commit:function(widget){widget.setData('rotating',this.getValue());}
		},
		{
		type:'text',id:'faicon',className:'faSelect',label:'Selected',validate:CKEDITOR.dialog.validate.notEmpty("Select fontAwesome icon"),onLoad: function(){this.getInputElement().setAttribute('readOnly',true);},setup:function(widget){this.setValue(widget.data.class != '' ? widget.data.class:'');},commit:function(widget){widget.setData('class', this.getValue());}
		}
		]
		},
		{
		type:'hbox',
		widths:['33%','33%','33%'],
		children:[
		{
		type:'button',className:'faSelect',label:'Brands '+Object.keys(faBrands).length,
			onClick:function(){
			document.getElementById('ckeditor-fa-icons').innerHTML = faIcons(faBrands,'b');
			}
		},
		{
		type:'button',className:'faSelect',label:'Regular '+Object.keys(faRegular).length,
			onClick:function(){
			document.getElementById('ckeditor-fa-icons').innerHTML = faIcons(faRegular,'r');
			}
		},
		{
		type:'button',className:'faSelect',label:'Solid '+Object.keys(faSolid).length,
			onClick:function(){
			document.getElementById('ckeditor-fa-icons').innerHTML = faIcons(faSolid,'s');
			}
		}
		]
		},
		{type:'html',html:'<div id="ckeditor-fa-icons">' + faIcons(faBrands,'b') + '</div>'}
	]
	}],
	onOk:function () {
		clear();
		var dialog = this,icon = editor.document.createElement('span'),cls='';
		if(dialog.getValueOf('font-awesome','fixwidth') == "Yes") cls += ' fa-fw';
		if(dialog.getValueOf('font-awesome','bordered') == "Yes") cls += ' fa-border';
		if(dialog.getValueOf('font-awesome','spinning') == "Yes") cls += ' fa-spin';
		if(dialog.getValueOf('font-awesome','rotating') != "No") cls += ' '+dialog.getValueOf('font-awesome','rotating');
		icon.setAttribute('class', dialog.getValueOf('font-awesome','faicon')+cls);
		var style='';
		if(dialog.getValueOf('font-awesome','colorChooser') !='')
		style += 'color:' + dialog.getValueOf('font-awesome','colorChooser')+';';
		if(dialog.getValueOf('font-awesome','size') !='')
		style += 'font-size:' + dialog.getValueOf('font-awesome','size') + 'px';
		if(style) icon.setAttribute('style', style);
		icon.setAttribute('aria-hidden','true');
		editor.insertElement(icon);
	},
	onCancel:function () {
		clear();
	}
};
});