!function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define(["../../lib/codemirror","diff_match_patch"],e):e(CodeMirror)}(function(p){"use strict";var b=p.Pos,f="http://www.w3.org/2000/svg";function v(e,t){this.mv=e,this.type=t,this.classes="left"==t?{chunk:"CodeMirror-merge-l-chunk",start:"CodeMirror-merge-l-chunk-start",end:"CodeMirror-merge-l-chunk-end",insert:"CodeMirror-merge-l-inserted",del:"CodeMirror-merge-l-deleted",connect:"CodeMirror-merge-l-connect"}:{chunk:"CodeMirror-merge-r-chunk",start:"CodeMirror-merge-r-chunk-start",end:"CodeMirror-merge-r-chunk-end",insert:"CodeMirror-merge-r-inserted",del:"CodeMirror-merge-r-deleted",connect:"CodeMirror-merge-r-connect"}}function h(e){e.diffOutOfDate&&(e.diff=s(e.orig.getValue(),e.edit.getValue(),e.mv.options.ignoreWhitespace),e.chunks=c(e.diff),e.diffOutOfDate=!1,p.signal(e.edit,"updateDiff",e.diff))}var g=!(v.prototype={constructor:v,init:function(e,t,i){this.edit=this.mv.edit,(this.edit.state.diffViews||(this.edit.state.diffViews=[])).push(this),this.orig=p(e,N({value:t,readOnly:!this.mv.options.allowEditingOriginals},N(i))),"align"==this.mv.options.connect&&(this.edit.state.trackAlignable||(this.edit.state.trackAlignable=new H(this.edit)),this.orig.state.trackAlignable=new H(this.orig)),this.lockButton.title=this.edit.phrase("Toggle locked scrolling"),this.orig.state.diffViews=[this];e=i.chunkClassLocation||"background";"[object Array]"!=Object.prototype.toString.call(e)&&(e=[e]),this.classes.classLocation=e,this.diff=s(o(t),o(i.value),this.mv.options.ignoreWhitespace),this.chunks=c(this.diff),this.diffOutOfDate=this.dealigned=!1,this.needsScrollSync=null,this.showDifferences=!1!==i.showDifferences},registerEvents:function(e){var t,i;this.forceUpdate=function(i){var t,r={from:0,to:0,marked:[]},n={from:0,to:0,marked:[]},o=!1;function l(e){o=!(g=!0),"full"==e&&(i.svg&&x(i.svg),i.copyButtons&&x(i.copyButtons),m(i.edit,r.marked,i.classes),m(i.orig,n.marked,i.classes),r.from=r.to=n.from=n.to=0),h(i),i.showDifferences&&(k(i.edit,i.diff,r,DIFF_INSERT,i.classes),k(i.orig,i.diff,n,DIFF_DELETE,i.classes)),"align"==i.mv.options.connect&&S(i),C(i),null!=i.needsScrollSync&&d(i,i.needsScrollSync),g=!1}function a(e){g||(i.dealigned=!0,s(e))}function s(e){g||o||(clearTimeout(t),!0===e&&(o=!0),t=setTimeout(l,!0===e?20:250))}function e(e,t){i.diffOutOfDate||(i.diffOutOfDate=!0,r.from=r.to=n.from=n.to=0),a(t.text.length-1!=t.to.line-t.from.line)}function c(){i.diffOutOfDate=!0,i.dealigned=!0,l("full")}i.edit.on("change",e),i.orig.on("change",e),i.edit.on("swapDoc",c),i.orig.on("swapDoc",c),"align"==i.mv.options.connect&&(p.on(i.edit.state.trackAlignable,"realign",a),p.on(i.orig.state.trackAlignable,"realign",a));return i.edit.on("viewportChange",function(){s(!1)}),i.orig.on("viewportChange",function(){s(!1)}),l(),l}(this),r(this,!0,!1),i=e,(t=this).edit.on("scroll",function(){d(t,!0)&&C(t)}),t.orig.on("scroll",function(){d(t,!1)&&C(t),i&&d(i,!0)&&C(i)})},setShowDifferences:function(e){(e=!1!==e)!=this.showDifferences&&(this.showDifferences=e,this.forceUpdate("full"))}});function d(e,t){if(e.diffOutOfDate)e.lockScroll&&null==e.needsScrollSync&&(e.needsScrollSync=t);else{if(e.needsScrollSync=null,!e.lockScroll)return 1;var i=+new Date,r=t?(a=e.edit,e.orig):(a=e.orig,e.edit);if(!(a.state.scrollSetBy==e&&(a.state.scrollSetAt||0)+250>i)){var n,o,l,a,s,c,h=a.getScrollInfo();return"align"==e.mv.options.connect?l=h.top:(n=.5*h.clientHeight,o=h.top+n,c=a.lineAtHeight(o,"local"),c=function(e,t,i){for(var r,n,o,l,a=0;a<e.length;a++){var s=e[a],c=i?s.editFrom:s.origFrom,h=i?s.editTo:s.origTo;null==n&&(t<c?(n=s.editFrom,l=s.origFrom):t<h&&(n=s.editTo,l=s.origTo)),h<=t?(r=s.editTo,o=s.origTo):c<=t&&(r=s.editFrom,o=s.origFrom)}return{edit:{before:r,after:n},orig:{before:o,after:l}}}(e.chunks,c,t),a=u(a,t?c.edit:c.orig),c=u(r,t?c.orig:c.edit),a=(o-a.top)/(a.bot-a.top),(l=c.top-n+a*(c.bot-c.top))>h.top&&(s=h.top/n)<1?l=l*s+h.top*(1-s):(a=h.height-h.clientHeight-h.top)<n&&(a<(c=r.getScrollInfo()).height-c.clientHeight-l&&(s=a/n)<1&&(l=l*s+(c.height-c.clientHeight-a)*(1-s)))),r.scrollTo(h.left,l),r.state.scrollSetAt=i,r.state.scrollSetBy=e,1}}}function u(e,t){var i=t.after;return null==i&&(i=e.lastLine()+1),{top:e.heightAtLine(t.before||0,"local"),bot:e.heightAtLine(i,"local")}}function r(e,t,i){(e.lockScroll=t)&&0!=i&&d(e,DIFF_INSERT)&&C(e),(t?p.addClass:p.rmClass)(e.lockButton,"CodeMirror-merge-scrolllock-enabled")}function m(e,t,i){for(var r=0;r<t.length;++r){var n=t[r];n instanceof p.TextMarker?n.clear():n.parent&&function(e,t,i){for(var r=i.classLocation,n=0;n<r.length;n++)e.removeLineClass(t,r[n],i.chunk),e.removeLineClass(t,r[n],i.start),e.removeLineClass(t,r[n],i.end)}(e,n,i)}t.length=0}function k(e,t,i,r,n){var o=e.getViewport();e.operation(function(){i.from==i.to||20<o.from-i.to||20<i.from-o.to?(m(e,i.marked,n),l(e,t,r,i.marked,o.from,o.to,n),i.from=o.from,i.to=o.to):(o.from<i.from&&(l(e,t,r,i.marked,o.from,i.from,n),i.from=o.from),o.to>i.to&&(l(e,t,r,i.marked,i.to,o.to,n),i.to=o.to))})}function w(e,t,i,r,n,o){for(var l=i.classLocation,a=e.getLineHandle(t),s=0;s<l.length;s++)r&&e.addLineClass(a,l[s],i.chunk),n&&e.addLineClass(a,l[s],i.start),o&&e.addLineClass(a,l[s],i.end);return a}function l(o,e,t,l,a,s,c){var i=b(0,0),r=b(a,0),n=o.clipPos(b(s-1)),h=t==DIFF_DELETE?c.del:c.insert;function g(e,t){for(var i=Math.max(a,e),r=Math.min(s,t),n=i;n<r;++n)l.push(w(o,n,c,!0,n==e,n==t-1));e==t&&i==t&&r==t&&(i?l.push(w(o,i-1,c,!1,!1,!0)):l.push(w(o,i,c,!1,!0,!1)))}for(var f=0,d=!1,u=0;u<e.length;++u){var m,p,v=e[u],k=v[0],C=v[1];k==DIFF_EQUAL?(p=i.line+(E(e,u)?0:1),R(i,C),p<(m=i.line+(A(e,u)?1:0))&&(d&&(g(f,p),d=!1),f=m)):(d=!0,k==t&&(v=R(i,C,!0),p=i,k=0<((m=r).line-p.line||m.ch-p.ch)?m:p,C=v,p=((m=n).line-C.line||m.ch-C.ch)<0?m:C,m=p,(C=k).line==m.line&&C.ch==m.ch||l.push(o.markText(k,p,{className:h})),i=v))}d&&g(f,i.line+1)}function C(e){if(e.showDifferences){var t;e.svg&&(x(e.svg),t=e.gap.offsetWidth,I(e.svg,"width",t,"height",e.gap.offsetHeight)),e.copyButtons&&x(e.copyButtons);for(var i=e.edit.getViewport(),r=e.orig.getViewport(),n=e.mv.wrap.getBoundingClientRect().top,o=n-e.edit.getScrollerElement().getBoundingClientRect().top+e.edit.getScrollInfo().top,l=n-e.orig.getScrollerElement().getBoundingClientRect().top+e.orig.getScrollInfo().top,a=0;a<e.chunks.length;a++){var s=e.chunks[a];s.editFrom<=i.to&&s.editTo>=i.from&&s.origFrom<=r.to&&s.origTo>=r.from&&function(e,t,i,r,n){var o="left"==e.type,l=e.orig.heightAtLine(t.origFrom,"local",!0)-i;{var a,s,c;e.svg&&(c=l,a=e.edit.heightAtLine(t.editFrom,"local",!0)-r,o&&(s=c,c=a,a=s),h=e.orig.heightAtLine(t.origTo,"local",!0)-i,g=e.edit.heightAtLine(t.editTo,"local",!0)-r,o&&(s=h,h=g,g=s),c=" C "+n/2+" "+a+" "+n/2+" "+c+" "+(n+2)+" "+c,g=" C "+n/2+" "+h+" "+n/2+" "+g+" -1 "+g,I(e.svg.appendChild(document.createElementNS(f,"path")),"d","M -1 "+a+c+" L "+(n+2)+" "+h+g+" z","class",e.classes.connect))}{var h,g;e.copyButtons&&(h=e.copyButtons.appendChild(B("div","left"==e.type?"⇝":"⇜","CodeMirror-merge-copy")),g=e.mv.options.allowEditingOriginals,h.title=e.edit.phrase(g?"Push to left":"Revert chunk"),h.chunk=t,h.style.top=(t.origTo>t.origFrom?l:e.edit.heightAtLine(t.editFrom,"local")-r)+"px",h.setAttribute("role","button"),g&&(g=e.edit.heightAtLine(t.editFrom,"local")-r,(r=e.copyButtons.appendChild(B("div","right"==e.type?"⇝":"⇜","CodeMirror-merge-copy-reverse"))).title="Push to right",r.chunk={editFrom:t.origFrom,editTo:t.origTo,origFrom:t.editFrom,origTo:t.editTo},r.style.top=g+"px","right"==e.type?r.style.left="2px":r.style.right="2px",r.setAttribute("role","button")))}}(e,s,l,o,t)}}}function T(e,t){for(var i=0,r=0,n=0;n<t.length;n++){var o=t[n];if(o.editTo>e&&o.editFrom<=e)return null;if(o.editFrom>e)break;i=o.editTo,r=o.origTo}return r+(e-i)}function y(e,t,i){for(var r=e.state.trackAlignable,n=e.firstLine(),o=0,l=[],a=0;;a++){for(var s=t[a],c=s?i?s.origFrom:s.editFrom:1e9;o<r.alignable.length;o+=2){var h=r.alignable[o]+1;if(!(h<=n)){if(!(h<=c))break;l.push(h)}}if(!s)break;l.push(n=i?s.origTo:s.editTo)}return l}function F(e,t,i,r){var n=0,o=0,l=0,a=0;e:for(;;n++){var s=e[n],c=t[o];if(!s&&null==c)break;for(var h=s?s[0]:1e9,g=null==c?1e9:c;l<i.length;){var f=i[l];if(f.origFrom<=g&&f.origTo>g){o++,n--;continue e}if(f.editTo>h){if(f.editFrom<=h)continue e;break}a+=f.origTo-f.origFrom-(f.editTo-f.editFrom),l++}h==g-a?(s[r]=g,o++):h<g-a?s[r]=h+a:((s=[g-a,null,null])[r]=g,e.splice(n,0,s),o++)}}function S(e,t){if(e.dealigned||t){if(!e.orig.curOp)return e.orig.operation(function(){S(e,t)});e.dealigned=!1;var i=e.mv.left==e?e.mv.right:e.mv.left;i&&(h(i),i.dealigned=!1);for(var r=function(e,t){var i=y(e.edit,e.chunks,!1),r=[];if(t)for(var n=0,o=0;n<t.chunks.length;n++){for(var l=t.chunks[n].editTo;o<i.length&&i[o]<l;)o++;o!=i.length&&i[o]==l||i.splice(o++,0,l)}for(n=0;n<i.length;n++)r.push([i[n],null,null]);return F(r,y(e.orig,e.chunks,!0),e.chunks,1),t&&F(r,y(t.orig,t.chunks,!0),t.chunks,2),r}(e,i),n=e.mv.aligners,o=0;o<n.length;o++)n[o].clear();n.length=0;var l=[e.edit,e.orig],a=[],s=[];i&&l.push(i.orig);for(o=0;o<l.length;o++)a.push(l[o].getScrollInfo().top),s.push(-l[o].getScrollerElement().getBoundingClientRect().top);(s[0]!=s[1]||3==l.length&&s[1]!=s[2])&&M(l,s,[0,0,0],n);for(var c=0;c<r.length;c++)M(l,s,r[c],n);for(o=0;o<l.length;o++)l[o].scrollTo(null,a[o])}}function M(e,t,i,r){for(var n,o=-1e8,l=[],a=0;a<e.length;a++)null!=i[a]&&(n=e[a].heightAtLine(i[a],"local")-t[a],l[a]=n,o=Math.max(o,n));for(var s,a=0;a<e.length;a++)null==i[a]||1<(s=o-l[a])&&r.push(function(e,t,i){var r=!0;t>e.lastLine()&&(t--,r=!1);var n=document.createElement("div");return n.className="CodeMirror-merge-spacer",n.style.height=i+"px",n.style.minWidth="1px",e.addLineWidget(t,n,{height:i,above:r,mergeSpacer:!0,handleMouseEvents:!0})}(e[a],i[a],s))}function n(e,t,i,r){var n,o,l,a;e.diffOutOfDate||(n=r.origTo>i.lastLine()?b(r.origFrom-1):b(r.origFrom,0),o=b(r.origTo,0),l=r.editTo>t.lastLine()?b(r.editFrom-1):b(r.editFrom,0),a=b(r.editTo,0),(r=e.mv.options.revertChunk)?r(e.mv,i,n,o,t,l,a):t.replaceRange(i.getRange(n,o),l,a))}var a,L=p.MergeView=function(e,t){if(!(this instanceof L))return new L(e,t);var i,r=(this.options=t).origLeft,n=null==t.origRight?t.orig:t.origRight,o=null!=r,l=null!=n,a=1+(o?1:0)+(l?1:0),s=[],c=this.left=null,h=this.right=null,g=this;o&&(c=this.left=new v(this,"left"),i=B("div",null,"CodeMirror-merge-pane CodeMirror-merge-left"),s.push(i),s.push(D(c)));var f,o=B("div",null,"CodeMirror-merge-pane CodeMirror-merge-editor");s.push(o),l&&(h=this.right=new v(this,"right"),s.push(D(h)),f=B("div",null,"CodeMirror-merge-pane CodeMirror-merge-right"),s.push(f)),(l?f:o).className+=" CodeMirror-merge-pane-rightmost",s.push(B("div",null,null,"height: 0; clear: both;"));var d=this.wrap=e.appendChild(B("div",s,"CodeMirror-merge CodeMirror-merge-"+a+"pane"));this.edit=p(o,N(t)),c&&c.init(i,r,t),h&&h.init(f,n,t),t.collapseIdentical&&this.editor().operation(function(){!function(e,t){"number"!=typeof t&&(t=2);for(var i=[],r=e.editor(),n=r.firstLine(),o=n,l=r.lastLine();o<=l;o++)i.push(!0);e.left&&O(e.left,t,n,i);e.right&&O(e.right,t,n,i);for(var a=0;a<i.length;a++)if(i[a]){for(var s,c=a+n,h=1;a<i.length-1&&i[a+1];a++,h++);t<h&&(s=[{line:c,cm:r}],e.left&&s.push({line:T(c,e.left.chunks),cm:e.left.orig}),e.right&&s.push({line:T(c,e.right.chunks),cm:e.right.orig}),s=function(e,t){var i=[];function r(){for(var e=0;e<i.length;e++)i[e].clear()}for(var n=0;n<t.length;n++){var o=t[n],o=function(e,t,i){e.addLineClass(t,"wrap","CodeMirror-merge-collapsed-line");var r=document.createElement("span");r.className="CodeMirror-merge-collapsed-widget",r.title=e.phrase("Identical text collapsed. Click to expand.");var n=e.markText(b(t,0),b(i-1),{inclusiveLeft:!0,inclusiveRight:!0,replacedWith:r,clearOnEnter:!0});function o(){n.clear(),e.removeLineClass(t,"wrap","CodeMirror-merge-collapsed-line")}n.explicitlyCleared&&o();return p.on(r,"click",o),n.on("clear",o),p.on(r,"click",o),{mark:n,clear:o}}(o.cm,o.line,o.line+e);i.push(o),o.mark.on("clear",r)}return i[0].mark}(h,s),e.options.onCollapse&&e.options.onCollapse(e,c,h,s))}}(g,t.collapseIdentical)}),"align"==t.connect&&(this.aligners=[],S(this.left||this.right,!0)),c&&c.registerEvents(h),h&&h.registerEvents(c);function u(){c&&C(c),h&&C(h)}p.on(window,"resize",u);var m=setInterval(function(){for(var e=d.parentNode;e&&e!=document.body;e=e.parentNode);e||(clearInterval(m),p.off(window,"resize",u))},5e3)};function D(t){var e=t.lockButton=B("div",null,"CodeMirror-merge-scrolllock");e.setAttribute("role","button");var i=B("div",[e],"CodeMirror-merge-scrolllock-wrap");p.on(e,"click",function(){r(t,!t.lockScroll)});e=[i];return!1!==t.mv.options.revertButtons&&(t.copyButtons=B("div",null,"CodeMirror-merge-copybuttons-"+t.type),p.on(t.copyButtons,"click",function(e){e=e.target||e.srcElement;e.chunk&&("CodeMirror-merge-copy-reverse"!=e.className?n(t,t.edit,t.orig,e.chunk):n(t,t.orig,t.edit,e.chunk))}),e.unshift(t.copyButtons)),"align"!=t.mv.options.connect&&((i=document.createElementNS&&document.createElementNS(f,"svg"))&&!i.createSVGRect&&(i=null),(t.svg=i)&&e.push(i)),t.gap=B("div",e,"CodeMirror-merge-gap")}function o(e){return"string"==typeof e?e:e.getValue()}function s(e,t,i){for(var r=(a=a||new diff_match_patch).diff_main(e,t),n=0;n<r.length;++n){var o=r[n];(i?/[^ \t]/.test(o[1]):o[1])?n&&r[n-1][0]==o[0]&&(r.splice(n--,1),r[n][1]+=o[1]):r.splice(n--,1)}return r}function c(e){var t=[];if(!e.length)return t;for(var i=0,r=0,n=b(0,0),o=b(0,0),l=0;l<e.length;++l){var a,s,c,h,g=e[l],f=g[0];f==DIFF_EQUAL?(c=!E(e,l)||n.line<i||o.line<r?1:0,a=n.line+c,s=o.line+c,R(n,g[1],null,o),h=A(e,l)?1:0,c=n.line+h,h=o.line+h,a<c&&(l&&t.push({origFrom:r,origTo:s,editFrom:i,editTo:a}),i=c,r=h)):R(f==DIFF_INSERT?n:o,g[1])}return(i<=n.line||r<=o.line)&&t.push({origFrom:r,origTo:o.line+1,editFrom:i,editTo:n.line+1}),t}function A(e,t){if(t==e.length-1)return 1;var i=e[t+1][1];return!(1==i.length&&t<e.length-2||10!=i.charCodeAt(0))&&(t==e.length-2||(1<(i=e[t+2][1]).length||t==e.length-3)&&10==i.charCodeAt(0))}function E(e,t){if(0==t)return 1;var i=e[t-1][1];return 10==i.charCodeAt(i.length-1)&&(1==t||10==(i=e[t-2][1]).charCodeAt(i.length-1))}function O(e,t,i,r){for(var n=0;n<e.chunks.length;n++)for(var o=e.chunks[n],l=o.editFrom-t;l<o.editTo+t;l++){var a=l+i;0<=a&&a<r.length&&(r[a]=!1)}}function B(e,t,i,r){var n=document.createElement(e);if(i&&(n.className=i),r&&(n.style.cssText=r),"string"==typeof t)n.appendChild(document.createTextNode(t));else if(t)for(var o=0;o<t.length;++o)n.appendChild(t[o]);return n}function x(e){for(var t=e.childNodes.length;0<t;--t)e.removeChild(e.firstChild)}function I(e){for(var t=1;t<arguments.length;t+=2)e.setAttribute(arguments[t],arguments[t+1])}function N(e,t){for(var i in t=t||{},e)e.hasOwnProperty(i)&&(t[i]=e[i]);return t}function R(e,t,i,r){for(var n=i?b(e.line,e.ch):e,o=0;;){var l=t.indexOf("\n",o);if(-1==l)break;++n.line,r&&++r.line,o=l+1}return n.ch=(o?0:n.ch)+(t.length-o),r&&(r.ch=(o?0:r.ch)+(t.length-o)),n}L.prototype={constructor:L,editor:function(){return this.edit},rightOriginal:function(){return this.right&&this.right.orig},leftOriginal:function(){return this.left&&this.left.orig},setShowDifferences:function(e){this.right&&this.right.setShowDifferences(e),this.left&&this.left.setShowDifferences(e)},rightChunks:function(){if(this.right)return h(this.right),this.right.chunks},leftChunks:function(){if(this.left)return h(this.left),this.left.chunks}};var W=1,V=2,_=4;function H(e){this.cm=e,this.alignable=[],this.height=e.doc.height;var l=this;e.on("markerAdded",function(e,t){!t.collapsed||null!=(t=t.find(1))&&l.set(t.line,_)}),e.on("markerCleared",function(e,t,i,r){null!=r&&t.collapsed&&l.check(r,_,l.hasMarker)}),e.on("markerChanged",this.signal.bind(this)),e.on("lineWidgetAdded",function(e,t,i){t.mergeSpacer||(t.above?l.set(i-1,V):l.set(i,W))}),e.on("lineWidgetCleared",function(e,t,i){t.mergeSpacer||(t.above?l.check(i-1,V,l.hasWidgetBelow):l.check(i,W,l.hasWidget))}),e.on("lineWidgetChanged",this.signal.bind(this)),e.on("change",function(e,t){var i=t.from.line,r=t.to.line-t.from.line,n=t.text.length-1,o=i+n;(r||n)&&l.map(i,r,n),l.check(o,_,l.hasMarker),(r||n)&&l.check(t.from.line,_,l.hasMarker)}),e.on("viewportChange",function(){l.cm.doc.height!=l.height&&l.signal()})}function t(e,t){var i=null,r=e.state.diffViews,n=e.getCursor().line;if(r)for(var o=0;o<r.length;o++){var l=r[o],a=e==l.orig;h(l);a=(t<0?function(e,t,i){for(var r=e.length-1;0<=r;r--){var n=e[r],n=(i?n.origTo:n.editTo)-1;if(n<t)return n}}:function(e,t,i){for(var r=0;r<e.length;r++){var n=e[r],n=i?n.origFrom:n.editFrom;if(t<n)return n}})(l.chunks,n,a);null==a||null!=i&&!(t<0?i<a:a<i)||(i=a)}if(null==i)return p.Pass;e.setCursor(i,0)}H.prototype={signal:function(){p.signal(this,"realign"),this.height=this.cm.doc.height},set:function(e,t){for(var i=-1;i<this.alignable.length;i+=2){var r=this.alignable[i]-e;if(0==r)return(this.alignable[i+1]&t)==t?void 0:(this.alignable[i+1]|=t,void this.signal());if(0<r)break}this.signal(),this.alignable.splice(i,0,e,t)},find:function(e){for(var t=0;t<this.alignable.length;t+=2)if(this.alignable[t]==e)return t;return-1},check:function(e,t,i){var r=this.find(e);-1!=r&&this.alignable[r+1]&t&&(i.call(this,e)||(this.signal(),(t=this.alignable[r+1]&~t)?this.alignable[r+1]=t:this.alignable.splice(r,2)))},hasMarker:function(e){var t=this.cm.getLineHandle(e);if(t.markedSpans)for(var i=0;i<t.markedSpans.length;i++)if(t.markedSpans[i].marker.collapsed&&null!=t.markedSpans[i].to)return!0;return!1},hasWidget:function(e){var t=this.cm.getLineHandle(e);if(t.widgets)for(var i=0;i<t.widgets.length;i++)if(!t.widgets[i].above&&!t.widgets[i].mergeSpacer)return!0;return!1},hasWidgetBelow:function(e){if(e==this.cm.lastLine())return!1;var t=this.cm.getLineHandle(e+1);if(t.widgets)for(var i=0;i<t.widgets.length;i++)if(t.widgets[i].above&&!t.widgets[i].mergeSpacer)return!0;return!1},map:function(e,t,i){for(var r=i-t,n=e+t,o=-1,l=-1,a=0;a<this.alignable.length;a+=2){var s=this.alignable[a];s==e&&this.alignable[a+1]&V&&(o=a),s==n&&this.alignable[a+1]&V&&(l=a),s<=e||(s<n?this.alignable.splice(a--,2):this.alignable[a]+=r)}-1<o&&((t=this.alignable[o+1])==V?this.alignable.splice(o,2):this.alignable[o+1]=t&~V),-1<l&&i&&this.set(e+i,V)}},p.commands.goNextDiff=function(e){return t(e,1)},p.commands.goPrevDiff=function(e){return t(e,-1)}});