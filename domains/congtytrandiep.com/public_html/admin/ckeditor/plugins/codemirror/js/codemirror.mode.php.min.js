!function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define("mode/xml/xml",["../../lib/codemirror"],e):e(CodeMirror)}(function(e){"use strict";var t={autoSelfClosers:{area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0,menuitem:!0},implicitlyClosed:{dd:!0,li:!0,optgroup:!0,option:!0,p:!0,rp:!0,rt:!0,tbody:!0,td:!0,tfoot:!0,th:!0,tr:!0},contextGrabbers:{dd:{dd:!0,dt:!0},dt:{dd:!0,dt:!0},li:{li:!0},option:{option:!0,optgroup:!0},optgroup:{optgroup:!0},p:{address:!0,article:!0,aside:!0,blockquote:!0,dir:!0,div:!0,dl:!0,fieldset:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,menu:!0,nav:!0,ol:!0,p:!0,pre:!0,section:!0,table:!0,ul:!0},rp:{rp:!0,rt:!0},rt:{rp:!0,rt:!0},tbody:{tbody:!0,tfoot:!0},td:{td:!0,th:!0},tfoot:{tbody:!0},th:{td:!0,th:!0},thead:{tbody:!0,tfoot:!0},tr:{tr:!0}},doNotIndent:{pre:!0},allowUnquoted:!0,allowMissing:!0,caseFold:!0},r={autoSelfClosers:{},implicitlyClosed:{},contextGrabbers:{},doNotIndent:{},allowUnquoted:!1,allowMissing:!1,allowMissingTagName:!1,caseFold:!1};e.defineMode("xml",function(n,a){function i(e,t){function r(r){return t.tokenize=r,r(e,t)}var n=e.next();if("<"==n)return e.eat("!")?e.eat("[")?e.match("CDATA[")?r(s("atom","]]>")):null:e.match("--")?r(s("comment","--\x3e")):e.match("DOCTYPE",!0,!0)?(e.eatWhile(/[\w\._\-]/),r(c(1))):null:e.eat("?")?(e.eatWhile(/[\w\._\-]/),t.tokenize=s("meta","?>"),"meta"):(T=e.eat("/")?"closeTag":"openTag",t.tokenize=o,"tag bracket");if("&"==n){var a;return a=e.eat("#")?e.eat("x")?e.eatWhile(/[a-fA-F\d]/)&&e.eat(";"):e.eatWhile(/[\d]/)&&e.eat(";"):e.eatWhile(/[\w\.\-:]/)&&e.eat(";"),a?"atom":"error"}return e.eatWhile(/[^&<]/),null}function o(e,t){var r=e.next();if(">"==r||"/"==r&&e.eat(">"))return t.tokenize=i,T=">"==r?"endTag":"selfcloseTag","tag bracket";if("="==r)return T="equals",null;if("<"==r){t.tokenize=i,t.state=f,t.tagName=t.tagStart=null;var n=t.tokenize(e,t);return n?n+" tag error":"tag error"}return/[\'\"]/.test(r)?(t.tokenize=l(r),t.stringStartCol=e.column(),t.tokenize(e,t)):(e.match(/^[^\s\u00a0=<>\"\']*[^\s\u00a0=<>\"\'\/]/),"word")}function l(e){var t=function(t,r){for(;!t.eol();)if(t.next()==e){r.tokenize=o;break}return"string"};return t.isInAttribute=!0,t}function s(e,t){return function(r,n){for(;!r.eol();){if(r.match(t)){n.tokenize=i;break}r.next()}return e}}function c(e){return function(t,r){for(var n;null!=(n=t.next());){if("<"==n)return r.tokenize=c(e+1),r.tokenize(t,r);if(">"==n){if(1==e){r.tokenize=i;break}return r.tokenize=c(e-1),r.tokenize(t,r)}}return"meta"}}function d(e){return e&&e.toLowerCase()}function u(e,t,r){this.prev=e.context,this.tagName=t||"",this.indent=e.indented,this.startOfLine=r,(q.doNotIndent.hasOwnProperty(t)||e.context&&e.context.noIndent)&&(this.noIndent=!0)}function p(e){e.context&&(e.context=e.context.prev)}function m(e,t){for(var r;;){if(!e.context)return;if(r=e.context.tagName,!q.contextGrabbers.hasOwnProperty(d(r))||!q.contextGrabbers[d(r)].hasOwnProperty(d(t)))return;p(e)}}function f(e,t,r){return"openTag"==e?(r.tagStart=t.column(),g):"closeTag"==e?_:f}function g(e,t,r){return"word"==e?(r.tagName=t.current(),M="tag",b):q.allowMissingTagName&&"endTag"==e?(M="tag bracket",b(e,t,r)):(M="error",g)}function _(e,t,r){if("word"==e){var n=t.current();return r.context&&r.context.tagName!=n&&q.implicitlyClosed.hasOwnProperty(d(r.context.tagName))&&p(r),r.context&&r.context.tagName==n||!1===q.matchClosing?(M="tag",h):(M="tag error",y)}return q.allowMissingTagName&&"endTag"==e?(M="tag bracket",h(e,t,r)):(M="error",y)}function h(e,t,r){return"endTag"!=e?(M="error",h):(p(r),f)}function y(e,t,r){return M="error",h(e,t,r)}function b(e,t,r){if("word"==e)return M="attribute",k;if("endTag"==e||"selfcloseTag"==e){var n=r.tagName,a=r.tagStart;return r.tagName=r.tagStart=null,"selfcloseTag"==e||q.autoSelfClosers.hasOwnProperty(d(n))?m(r,n):(m(r,n),r.context=new u(r,n,a==r.indented)),f}return M="error",b}function k(e,t,r){return"equals"==e?v:(q.allowMissing||(M="error"),b(e,t,r))}function v(e,t,r){return"string"==e?w:"word"==e&&q.allowUnquoted?(M="string",b):(M="error",b(e,t,r))}function w(e,t,r){return"string"==e?w:b(e,t,r)}var x=n.indentUnit,q={},S=a.htmlMode?t:r;for(var z in S)q[z]=S[z];for(var z in a)q[z]=a[z];var T,M;return i.isInText=!0,{startState:function(e){var t={tokenize:i,state:f,indented:e||0,tagName:null,tagStart:null,context:null};return null!=e&&(t.baseIndent=e),t},token:function(e,t){if(!t.tagName&&e.sol()&&(t.indented=e.indentation()),e.eatSpace())return null;T=null;var r=t.tokenize(e,t);return(r||T)&&"comment"!=r&&(M=null,t.state=t.state(T||r,e,t),M&&(r="error"==M?r+" error":M)),r},indent:function(t,r,n){var a=t.context;if(t.tokenize.isInAttribute)return t.tagStart==t.indented?t.stringStartCol+1:t.indented+x;if(a&&a.noIndent)return e.Pass;if(t.tokenize!=o&&t.tokenize!=i)return n?n.match(/^(\s*)/)[0].length:0;if(t.tagName)return!1!==q.multilineTagIndentPastTag?t.tagStart+t.tagName.length+2:t.tagStart+x*(q.multilineTagIndentFactor||1);if(q.alignCDATA&&/<!\[CDATA\[/.test(r))return 0;var l=r&&/^<(\/)?([\w_:\.-]*)/.exec(r);if(l&&l[1])for(;a;){if(a.tagName==l[2]){a=a.prev;break}if(!q.implicitlyClosed.hasOwnProperty(d(a.tagName)))break;a=a.prev}else if(l)for(;a;){var s=q.contextGrabbers[d(a.tagName)];if(!s||!s.hasOwnProperty(d(l[2])))break;a=a.prev}for(;a&&a.prev&&!a.startOfLine;)a=a.prev;return a?a.indent+x:t.baseIndent||0},electricInput:/<\/[\s\w:]+>$/,blockCommentStart:"\x3c!--",blockCommentEnd:"--\x3e",configuration:q.htmlMode?"html":"xml",helperType:q.htmlMode?"html":"xml",skipAttribute:function(e){e.state==v&&(e.state=b)},xmlCurrentTag:function(e){return e.tagName?{name:e.tagName,close:"closeTag"==e.type}:null},xmlCurrentContext:function(e){for(var t=[],r=e.context;r;r=r.prev)t.push(r.tagName);return t.reverse()}}}),e.defineMIME("text/xml","xml"),e.defineMIME("application/xml","xml"),e.mimeModes.hasOwnProperty("text/html")||e.defineMIME("text/html",{name:"xml",htmlMode:!0})}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define("mode/javascript/javascript",["../../lib/codemirror"],e):e(CodeMirror)}(function(e){"use strict";e.defineMode("javascript",function(t,r){function n(e){for(var t,r=!1,n=!1;null!=(t=e.next());){if(!r){if("/"==t&&!n)return;"["==t?n=!0:n&&"]"==t&&(n=!1)}r=!r&&"\\"==t}}function a(e,t,r){return Ue=e,Re=r,t}function i(e,t){var r=e.next();if('"'==r||"'"==r)return t.tokenize=o(r),t.tokenize(e,t);if("."==r&&e.match(/^\d[\d_]*(?:[eE][+\-]?[\d_]+)?/))return a("number","number");if("."==r&&e.match(".."))return a("spread","meta");if(/[\[\]{}\(\),;\:\.]/.test(r))return a(r);if("="==r&&e.eat(">"))return a("=>","operator");if("0"==r&&e.match(/^(?:x[\dA-Fa-f_]+|o[0-7_]+|b[01_]+)n?/))return a("number","number");if(/\d/.test(r))return e.match(/^[\d_]*(?:n|(?:\.[\d_]*)?(?:[eE][+\-]?[\d_]+)?)?/),a("number","number");if("/"==r)return e.eat("*")?(t.tokenize=l,l(e,t)):e.eat("/")?(e.skipToEnd(),a("comment","comment")):Be(e,t,1)?(n(e),e.match(/^\b(([gimyus])(?![gimyus]*\2))+\b/),a("regexp","string-2")):(e.eat("="),a("operator","operator",e.current()));if("`"==r)return t.tokenize=s,s(e,t);if("#"==r&&"!"==e.peek())return e.skipToEnd(),a("meta","meta");if("#"==r&&e.eatWhile(Xe))return a("variable","property");if("<"==r&&e.match("!--")||"-"==r&&e.match("->")&&!/\S/.test(e.string.slice(0,e.start)))return e.skipToEnd(),a("comment","comment");if(Je.test(r))return">"==r&&t.lexical&&">"==t.lexical.type||(e.eat("=")?"!"!=r&&"="!=r||e.eat("="):/[<>*+\-|&?]/.test(r)&&(e.eat(r),">"==r&&e.eat(r))),"?"==r&&e.eat(".")?a("."):a("operator","operator",e.current());if(Xe.test(r)){e.eatWhile(Xe);var i=e.current();if("."!=t.lastType){if(Qe.propertyIsEnumerable(i)){var c=Qe[i];return a(c.type,c.style,i)}if("async"==i&&e.match(/^(\s|\/\*([^*]|\*(?!\/))*?\*\/)*[\[\(\w]/,!1))return a("async","keyword",i)}return a("variable","variable",i)}}function o(e){return function(t,r){var n,o=!1;if(Ge&&"@"==t.peek()&&t.match(et))return r.tokenize=i,a("jsonld-keyword","meta");for(;null!=(n=t.next())&&(n!=e||o);)o=!o&&"\\"==n;return o||(r.tokenize=i),a("string","string")}}function l(e,t){for(var r,n=!1;r=e.next();){if("/"==r&&n){t.tokenize=i;break}n="*"==r}return a("comment","comment")}function s(e,t){for(var r,n=!1;null!=(r=e.next());){if(!n&&("`"==r||"$"==r&&e.eat("{"))){t.tokenize=i;break}n=!n&&"\\"==r}return a("quasi","string-2",e.current())}function c(e,t){t.fatArrowAt&&(t.fatArrowAt=null);var r=e.string.indexOf("=>",e.start);if(!(r<0)){if(Ye){var n=/:\s*(?:\w+(?:<[^>]*>|\[\])?|\{[^}]*\})\s*$/.exec(e.string.slice(e.start,r));n&&(r=n.index)}for(var a=0,i=!1,o=r-1;o>=0;--o){var l=e.string.charAt(o),s=tt.indexOf(l);if(s>=0&&s<3){if(!a){++o;break}if(0==--a){"("==l&&(i=!0);break}}else if(s>=3&&s<6)++a;else if(Xe.test(l))i=!0;else if(/["'\/`]/.test(l))for(;;--o){if(0==o)return;var c=e.string.charAt(o-1);if(c==l&&"\\"!=e.string.charAt(o-2)){o--;break}}else if(i&&!a){++o;break}}i&&!a&&(t.fatArrowAt=o)}}function d(e,t,r,n,a,i){this.indented=e,this.column=t,this.type=r,this.prev=a,this.info=i,null!=n&&(this.align=n)}function u(e,t){if(!Ze)return!1;for(var r=e.localVars;r;r=r.next)if(r.name==t)return!0;for(var n=e.context;n;n=n.prev)for(var r=n.vars;r;r=r.next)if(r.name==t)return!0}function p(e,t,r,n,a){var i=e.cc;for(nt.state=e,nt.stream=a,nt.marked=null,nt.cc=i,nt.style=t,e.lexical.hasOwnProperty("align")||(e.lexical.align=!0);;){if((i.length?i.pop():He?C:T)(r,n)){for(;i.length&&i[i.length-1].lex;)i.pop()();return nt.marked?nt.marked:"variable"==r&&u(e,n)?"variable-2":t}}}function m(){for(var e=arguments.length-1;e>=0;e--)nt.cc.push(arguments[e])}function f(){return m.apply(null,arguments),!0}function g(e,t){for(var r=t;r;r=r.next)if(r.name==e)return!0;return!1}function _(e){var t=nt.state;if(nt.marked="def",Ze){if(t.context)if("var"==t.lexical.info&&t.context&&t.context.block){var n=h(e,t.context);if(null!=n)return void(t.context=n)}else if(!g(e,t.localVars))return void(t.localVars=new k(e,t.localVars));r.globalVars&&!g(e,t.globalVars)&&(t.globalVars=new k(e,t.globalVars))}}function h(e,t){if(t){if(t.block){var r=h(e,t.prev);return r?r==t.prev?t:new b(r,t.vars,!0):null}return g(e,t.vars)?t:new b(t.prev,new k(e,t.vars),!1)}return null}function y(e){return"public"==e||"private"==e||"protected"==e||"abstract"==e||"readonly"==e}function b(e,t,r){this.prev=e,this.vars=t,this.block=r}function k(e,t){this.name=e,this.next=t}function v(){nt.state.context=new b(nt.state.context,nt.state.localVars,!1),nt.state.localVars=at}function w(){nt.state.context=new b(nt.state.context,nt.state.localVars,!0),nt.state.localVars=null}function x(){nt.state.localVars=nt.state.context.vars,nt.state.context=nt.state.context.prev}function q(e,t){var r=function(){var r=nt.state,n=r.indented;if("stat"==r.lexical.type)n=r.lexical.indented;else for(var a=r.lexical;a&&")"==a.type&&a.align;a=a.prev)n=a.indented;r.lexical=new d(n,nt.stream.column(),e,null,r.lexical,t)};return r.lex=!0,r}function S(){var e=nt.state;e.lexical.prev&&(")"==e.lexical.type&&(e.indented=e.lexical.indented),e.lexical=e.lexical.prev)}function z(e){function t(r){return r==e?f():";"==e||"}"==r||")"==r||"]"==r?m():f(t)}return t}function T(e,t){return"var"==e?f(q("vardef",t),pe,z(";"),S):"keyword a"==e?f(q("form"),I,T,S):"keyword b"==e?f(q("form"),T,S):"keyword d"==e?nt.stream.match(/^\s*$/,!1)?f():f(q("stat"),P,z(";"),S):"debugger"==e?f(z(";")):"{"==e?f(q("}"),w,Y,S,x):";"==e?f():"if"==e?("else"==nt.state.lexical.info&&nt.state.cc[nt.state.cc.length-1]==S&&nt.state.cc.pop()(),f(q("form"),I,T,S,ye)):"function"==e?f(we):"for"==e?f(q("form"),w,be,T,x,S):"class"==e||Ye&&"interface"==t?(nt.marked="keyword",f(q("form","class"==e?e:t),Te,S)):"variable"==e?Ye&&"declare"==t?(nt.marked="keyword",f(T)):Ye&&("module"==t||"enum"==t||"type"==t)&&nt.stream.match(/^\s*\w/,!1)?(nt.marked="keyword","enum"==t?f($e):"type"==t?f(qe,z("operator"),te,z(";")):f(q("form"),me,z("{"),q("}"),Y,S,S)):Ye&&"namespace"==t?(nt.marked="keyword",f(q("form"),C,T,S)):Ye&&"abstract"==t?(nt.marked="keyword",f(T)):f(q("stat"),U):"switch"==e?f(q("form"),I,z("{"),q("}","switch"),w,Y,S,S,x):"case"==e?f(C,z(":")):"default"==e?f(z(":")):"catch"==e?f(q("form"),v,M,T,S,x):"export"==e?f(q("stat"),Ie,S):"import"==e?f(q("stat"),Pe,S):"async"==e?f(T):"@"==t?f(C,T):m(q("stat"),C,z(";"),S)}function M(e){if("("==e)return f(Se,z(")"))}function C(e,t){return N(e,t,!1)}function j(e,t){return N(e,t,!0)}function I(e){return"("!=e?m():f(q(")"),P,z(")"),S)}function N(e,t,r){if(nt.state.fatArrowAt==nt.stream.start){var n=r?$:D;if("("==e)return f(v,q(")"),H(Se,")"),S,z("=>"),n,x);if("variable"==e)return m(v,me,z("=>"),n,x)}var a=r?A:E;return rt.hasOwnProperty(e)?f(a):"function"==e?f(we,a):"class"==e||Ye&&"interface"==t?(nt.marked="keyword",f(q("form"),ze,S)):"keyword c"==e||"async"==e?f(r?j:C):"("==e?f(q(")"),P,z(")"),S,a):"operator"==e||"spread"==e?f(r?j:C):"["==e?f(q("]"),De,S,a):"{"==e?Z(V,"}",null,a):"quasi"==e?m(L,a):"new"==e?f(F(r)):f()}function P(e){return e.match(/[;\}\)\],]/)?m():m(C)}function E(e,t){return","==e?f(P):A(e,t,!1)}function A(e,t,r){var n=0==r?E:A,a=0==r?C:j;return"=>"==e?f(v,r?$:D,x):"operator"==e?/\+\+|--/.test(t)||Ye&&"!"==t?f(n):Ye&&"<"==t&&nt.stream.match(/^([^<>]|<[^<>]*>)*>\s*\(/,!1)?f(q(">"),H(te,">"),S,n):"?"==t?f(C,z(":"),a):f(a):"quasi"==e?m(L,n):";"!=e?"("==e?Z(j,")","call",n):"."==e?f(R,n):"["==e?f(q("]"),P,z("]"),S,n):Ye&&"as"==t?(nt.marked="keyword",f(te,n)):"regexp"==e?(nt.state.lastType=nt.marked="operator",nt.stream.backUp(nt.stream.pos-nt.stream.start-1),f(a)):void 0:void 0}function L(e,t){return"quasi"!=e?m():"${"!=t.slice(t.length-2)?f(L):f(P,O)}function O(e){if("}"==e)return nt.marked="string-2",nt.state.tokenize=s,f(L)}function D(e){return c(nt.stream,nt.state),m("{"==e?T:C)}function $(e){return c(nt.stream,nt.state),m("{"==e?T:j)}function F(e){return function(t){return"."==t?f(e?B:K):"variable"==t&&Ye?f(ce,e?A:E):m(e?j:C)}}function K(e,t){if("target"==t)return nt.marked="keyword",f(E)}function B(e,t){if("target"==t)return nt.marked="keyword",f(A)}function U(e){return":"==e?f(S,T):m(E,z(";"),S)}function R(e){if("variable"==e)return nt.marked="property",f()}function V(e,t){if("async"==e)return nt.marked="property",f(V);if("variable"==e||"keyword"==nt.style){if(nt.marked="property","get"==t||"set"==t)return f(W);var r;return Ye&&nt.state.fatArrowAt==nt.stream.start&&(r=nt.stream.match(/^\s*:\s*/,!1))&&(nt.state.fatArrowAt=nt.stream.pos+r[0].length),f(G)}return"number"==e||"string"==e?(nt.marked=Ge?"property":nt.style+" property",f(G)):"jsonld-keyword"==e?f(G):Ye&&y(t)?(nt.marked="keyword",f(V)):"["==e?f(C,X,z("]"),G):"spread"==e?f(j,G):"*"==t?(nt.marked="keyword",f(V)):":"==e?m(G):void 0}function W(e){return"variable"!=e?m(G):(nt.marked="property",f(we))}function G(e){return":"==e?f(j):"("==e?m(we):void 0}function H(e,t,r){function n(a,i){if(r?r.indexOf(a)>-1:","==a){var o=nt.state.lexical;return"call"==o.info&&(o.pos=(o.pos||0)+1),f(function(r,n){return r==t||n==t?m():m(e)},n)}return a==t||i==t?f():r&&r.indexOf(";")>-1?m(e):f(z(t))}return function(r,a){return r==t||a==t?f():m(e,n)}}function Z(e,t,r){for(var n=3;n<arguments.length;n++)nt.cc.push(arguments[n]);return f(q(t,r),H(e,t),S)}function Y(e){return"}"==e?f():m(T,Y)}function X(e,t){if(Ye){if(":"==e)return f(te);if("?"==t)return f(X)}}function Q(e,t){if(Ye&&(":"==e||"in"==t))return f(te)}function J(e){if(Ye&&":"==e)return nt.stream.match(/^\s*\w+\s+is\b/,!1)?f(C,ee,te):f(te)}function ee(e,t){if("is"==t)return nt.marked="keyword",f()}function te(e,t){return"keyof"==t||"typeof"==t||"infer"==t||"readonly"==t?(nt.marked="keyword",f("typeof"==t?j:te)):"variable"==e||"void"==t?(nt.marked="type",f(se)):"|"==t||"&"==t?f(te):"string"==e||"number"==e||"atom"==e?f(se):"["==e?f(q("]"),H(te,"]",","),S,se):"{"==e?f(q("}"),ne,S,se):"("==e?f(H(le,")"),re,se):"<"==e?f(H(te,">"),te):"quasi"==e?m(ie,se):void 0}function re(e){if("=>"==e)return f(te)}function ne(e){return e.match(/[\}\)\]]/)?f():","==e||";"==e?f(ne):m(ae,ne)}function ae(e,t){return"variable"==e||"keyword"==nt.style?(nt.marked="property",f(ae)):"?"==t||"number"==e||"string"==e?f(ae):":"==e?f(te):"["==e?f(z("variable"),Q,z("]"),ae):"("==e?m(xe,ae):e.match(/[;\}\)\],]/)?void 0:f()}function ie(e,t){return"quasi"!=e?m():"${"!=t.slice(t.length-2)?f(ie):f(te,oe)}function oe(e){if("}"==e)return nt.marked="string-2",nt.state.tokenize=s,f(ie)}function le(e,t){return"variable"==e&&nt.stream.match(/^\s*[?:]/,!1)||"?"==t?f(le):":"==e?f(te):"spread"==e?f(le):m(te)}function se(e,t){return"<"==t?f(q(">"),H(te,">"),S,se):"|"==t||"."==e||"&"==t?f(te):"["==e?f(te,z("]"),se):"extends"==t||"implements"==t?(nt.marked="keyword",f(te)):"?"==t?f(te,z(":"),te):void 0}function ce(e,t){if("<"==t)return f(q(">"),H(te,">"),S,se)}function de(){return m(te,ue)}function ue(e,t){if("="==t)return f(te)}function pe(e,t){return"enum"==t?(nt.marked="keyword",f($e)):m(me,X,_e,he)}function me(e,t){return Ye&&y(t)?(nt.marked="keyword",f(me)):"variable"==e?(_(t),f()):"spread"==e?f(me):"["==e?Z(ge,"]"):"{"==e?Z(fe,"}"):void 0}function fe(e,t){return"variable"!=e||nt.stream.match(/^\s*:/,!1)?("variable"==e&&(nt.marked="property"),"spread"==e?f(me):"}"==e?m():"["==e?f(C,z("]"),z(":"),fe):f(z(":"),me,_e)):(_(t),f(_e))}function ge(){return m(me,_e)}function _e(e,t){if("="==t)return f(j)}function he(e){if(","==e)return f(pe)}function ye(e,t){if("keyword b"==e&&"else"==t)return f(q("form","else"),T,S)}function be(e,t){return"await"==t?f(be):"("==e?f(q(")"),ke,S):void 0}function ke(e){return"var"==e?f(pe,ve):"variable"==e?f(ve):m(ve)}function ve(e,t){return")"==e?f():";"==e?f(ve):"in"==t||"of"==t?(nt.marked="keyword",f(C,ve)):m(C,ve)}function we(e,t){return"*"==t?(nt.marked="keyword",f(we)):"variable"==e?(_(t),f(we)):"("==e?f(v,q(")"),H(Se,")"),S,J,T,x):Ye&&"<"==t?f(q(">"),H(de,">"),S,we):void 0}function xe(e,t){return"*"==t?(nt.marked="keyword",f(xe)):"variable"==e?(_(t),f(xe)):"("==e?f(v,q(")"),H(Se,")"),S,J,x):Ye&&"<"==t?f(q(">"),H(de,">"),S,xe):void 0}function qe(e,t){return"keyword"==e||"variable"==e?(nt.marked="type",f(qe)):"<"==t?f(q(">"),H(de,">"),S):void 0}function Se(e,t){return"@"==t&&f(C,Se),"spread"==e?f(Se):Ye&&y(t)?(nt.marked="keyword",f(Se)):Ye&&"this"==e?f(X,_e):m(me,X,_e)}function ze(e,t){return"variable"==e?Te(e,t):Me(e,t)}function Te(e,t){if("variable"==e)return _(t),f(Me)}function Me(e,t){return"<"==t?f(q(">"),H(de,">"),S,Me):"extends"==t||"implements"==t||Ye&&","==e?("implements"==t&&(nt.marked="keyword"),f(Ye?te:C,Me)):"{"==e?f(q("}"),Ce,S):void 0}function Ce(e,t){return"async"==e||"variable"==e&&("static"==t||"get"==t||"set"==t||Ye&&y(t))&&nt.stream.match(/^\s+[\w$\xa1-\uffff]/,!1)?(nt.marked="keyword",f(Ce)):"variable"==e||"keyword"==nt.style?(nt.marked="property",f(je,Ce)):"number"==e||"string"==e?f(je,Ce):"["==e?f(C,X,z("]"),je,Ce):"*"==t?(nt.marked="keyword",f(Ce)):Ye&&"("==e?m(xe,Ce):";"==e||","==e?f(Ce):"}"==e?f():"@"==t?f(C,Ce):void 0}function je(e,t){if("!"==t)return f(je);if("?"==t)return f(je);if(":"==e)return f(te,_e);if("="==t)return f(j);var r=nt.state.lexical.prev;return m(r&&"interface"==r.info?xe:we)}function Ie(e,t){return"*"==t?(nt.marked="keyword",f(Oe,z(";"))):"default"==t?(nt.marked="keyword",f(C,z(";"))):"{"==e?f(H(Ne,"}"),Oe,z(";")):m(T)}function Ne(e,t){return"as"==t?(nt.marked="keyword",f(z("variable"))):"variable"==e?m(j,Ne):void 0}function Pe(e){return"string"==e?f():"("==e?m(C):"."==e?m(E):m(Ee,Ae,Oe)}function Ee(e,t){return"{"==e?Z(Ee,"}"):("variable"==e&&_(t),"*"==t&&(nt.marked="keyword"),f(Le))}function Ae(e){if(","==e)return f(Ee,Ae)}function Le(e,t){if("as"==t)return nt.marked="keyword",f(Ee)}function Oe(e,t){if("from"==t)return nt.marked="keyword",f(C)}function De(e){return"]"==e?f():m(H(j,"]"))}function $e(){return m(q("form"),me,z("{"),q("}"),H(Fe,"}"),S,S)}function Fe(){return m(me,_e)}function Ke(e,t){return"operator"==e.lastType||","==e.lastType||Je.test(t.charAt(0))||/[,.]/.test(t.charAt(0))}function Be(e,t,r){return t.tokenize==i&&/^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\[{}\(,;:]|=>)$/.test(t.lastType)||"quasi"==t.lastType&&/\{\s*$/.test(e.string.slice(0,e.pos-(r||0)))}var Ue,Re,Ve=t.indentUnit,We=r.statementIndent,Ge=r.jsonld,He=r.json||Ge,Ze=!1!==r.trackScope,Ye=r.typescript,Xe=r.wordCharacters||/[\w$\xa1-\uffff]/,Qe=function(){function e(e){return{type:e,style:"keyword"}}var t=e("keyword a"),r=e("keyword b"),n=e("keyword c"),a=e("keyword d"),i=e("operator"),o={type:"atom",style:"atom"};return{if:e("if"),while:t,with:t,else:r,do:r,try:r,finally:r,return:a,break:a,continue:a,new:e("new"),delete:n,void:n,throw:n,debugger:e("debugger"),var:e("var"),const:e("var"),let:e("var"),function:e("function"),catch:e("catch"),for:e("for"),switch:e("switch"),case:e("case"),default:e("default"),in:i,typeof:i,instanceof:i,true:o,false:o,null:o,undefined:o,NaN:o,Infinity:o,this:e("this"),class:e("class"),super:e("atom"),yield:n,export:e("export"),import:e("import"),extends:n,await:n}}(),Je=/[+\-*&%=<>!?|~^@]/,et=/^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)"/,tt="([{}])",rt={atom:!0,number:!0,variable:!0,string:!0,regexp:!0,this:!0,import:!0,"jsonld-keyword":!0},nt={state:null,column:null,marked:null,cc:null},at=new k("this",new k("arguments",null));return v.lex=w.lex=!0,x.lex=!0,S.lex=!0,{startState:function(e){var t={tokenize:i,lastType:"sof",cc:[],lexical:new d((e||0)-Ve,0,"block",!1),localVars:r.localVars,context:r.localVars&&new b(null,null,!1),indented:e||0};return r.globalVars&&"object"==typeof r.globalVars&&(t.globalVars=r.globalVars),t},token:function(e,t){if(e.sol()&&(t.lexical.hasOwnProperty("align")||(t.lexical.align=!1),t.indented=e.indentation(),c(e,t)),t.tokenize!=l&&e.eatSpace())return null;var r=t.tokenize(e,t);return"comment"==Ue?r:(t.lastType="operator"!=Ue||"++"!=Re&&"--"!=Re?Ue:"incdec",p(t,r,Ue,Re,e))},indent:function(t,n){if(t.tokenize==l||t.tokenize==s)return e.Pass;if(t.tokenize!=i)return 0;var a,o=n&&n.charAt(0),c=t.lexical;if(!/^\s*else\b/.test(n))for(var d=t.cc.length-1;d>=0;--d){var u=t.cc[d];if(u==S)c=c.prev;else if(u!=ye&&u!=x)break}for(;("stat"==c.type||"form"==c.type)&&("}"==o||(a=t.cc[t.cc.length-1])&&(a==E||a==A)&&!/^[,\.=+\-*:?[\(]/.test(n));)c=c.prev;We&&")"==c.type&&"stat"==c.prev.type&&(c=c.prev);var p=c.type,m=o==p;return"vardef"==p?c.indented+("operator"==t.lastType||","==t.lastType?c.info.length+1:0):"form"==p&&"{"==o?c.indented:"form"==p?c.indented+Ve:"stat"==p?c.indented+(Ke(t,n)?We||Ve:0):"switch"!=c.info||m||0==r.doubleIndentSwitch?c.align?c.column+(m?0:1):c.indented+(m?0:Ve):c.indented+(/^(?:case|default)\b/.test(n)?Ve:2*Ve)},electricInput:/^\s*(?:case .*?:|default:|\{|\})$/,blockCommentStart:He?null:"/*",blockCommentEnd:He?null:"*/",blockCommentContinue:He?null:" * ",lineComment:He?null:"//",fold:"brace",closeBrackets:"()[]{}''\"\"``",helperType:He?"json":"javascript",jsonldMode:Ge,jsonMode:He,expressionAllowed:Be,skipExpression:function(t){p(t,"atom","atom","true",new e.StringStream("",2,null))}}}),e.registerHelper("wordChars","javascript",/[\w$]/),e.defineMIME("text/javascript","javascript"),e.defineMIME("text/ecmascript","javascript"),e.defineMIME("application/javascript","javascript"),e.defineMIME("application/x-javascript","javascript"),e.defineMIME("application/ecmascript","javascript"),e.defineMIME("application/json",{name:"javascript",json:!0}),e.defineMIME("application/x-json",{name:"javascript",json:!0}),e.defineMIME("application/manifest+json",{name:"javascript",json:!0}),e.defineMIME("application/ld+json",{name:"javascript",jsonld:!0}),e.defineMIME("text/typescript",{name:"javascript",typescript:!0}),e.defineMIME("application/typescript",{name:"javascript",typescript:!0})}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define("mode/css/css",["../../lib/codemirror"],e):e(CodeMirror)}(function(e){"use strict";function t(e){for(var t={},r=0;r<e.length;++r)t[e[r].toLowerCase()]=!0;return t}function r(e,t){for(var r,n=!1;null!=(r=e.next());){if(n&&"/"==r){t.tokenize=null;break}n="*"==r}return["comment","comment"]}e.defineMode("css",function(t,r){function n(e,t){return f=t,e}function a(e,t){var r=e.next();if(h[r]){var a=h[r](e,t);if(!1!==a)return a}return"@"==r?(e.eatWhile(/[\w\\\-]/),n("def",e.current())):"="==r||("~"==r||"|"==r)&&e.eat("=")?n(null,"compare"):'"'==r||"'"==r?(t.tokenize=i(r),t.tokenize(e,t)):"#"==r?(e.eatWhile(/[\w\\\-]/),n("atom","hash")):"!"==r?(e.match(/^\s*\w*/),n("keyword","important")):/\d/.test(r)||"."==r&&e.eat(/\d/)?(e.eatWhile(/[\w.%]/),n("number","unit")):"-"!==r?/[,+>*\/]/.test(r)?n(null,"select-op"):"."==r&&e.match(/^-?[_a-z][_a-z0-9-]*/i)?n("qualifier","qualifier"):/[:;{}\[\]\(\)]/.test(r)?n(null,r):e.match(/^[\w-.]+(?=\()/)?(/^(url(-prefix)?|domain|regexp)$/i.test(e.current())&&(t.tokenize=o),n("variable callee","variable")):/[\w\\\-]/.test(r)?(e.eatWhile(/[\w\\\-]/),n("property","word")):n(null,null):/[\d.]/.test(e.peek())?(e.eatWhile(/[\w.%]/),n("number","unit")):e.match(/^-[\w\\\-]*/)?(e.eatWhile(/[\w\\\-]/),e.match(/^\s*:/,!1)?n("variable-2","variable-definition"):n("variable-2","variable")):e.match(/^\w+-/)?n("meta","meta"):void 0}function i(e){return function(t,r){for(var a,i=!1;null!=(a=t.next());){if(a==e&&!i){")"==e&&t.backUp(1);break}i=!i&&"\\"==a}return(a==e||!i&&")"!=e)&&(r.tokenize=null),n("string","string")}}function o(e,t){return e.next(),e.match(/^\s*[\"\')]/,!1)?t.tokenize=null:t.tokenize=i(")"),n(null,"(")}function l(e,t,r){this.type=e,this.indent=t,this.prev=r}function s(e,t,r,n){return e.context=new l(r,t.indentation()+(!1===n?0:_),e.context),r}function c(e){return e.context.prev&&(e.context=e.context.prev),e.context.type}function d(e,t,r){return N[r.context.type](e,t,r)}function u(e,t,r,n){for(var a=n||1;a>0;a--)r.context=r.context.prev;return d(e,t,r)}function p(e){var t=e.current().toLowerCase();g=T.hasOwnProperty(t)?"atom":z.hasOwnProperty(t)?"keyword":"variable"}var m=r.inline;r.propertyKeywords||(r=e.resolveMode("text/css"));var f,g,_=t.indentUnit,h=r.tokenHooks,y=r.documentTypes||{},b=r.mediaTypes||{},k=r.mediaFeatures||{},v=r.mediaValueKeywords||{},w=r.propertyKeywords||{},x=r.nonStandardPropertyKeywords||{},q=r.fontProperties||{},S=r.counterDescriptors||{},z=r.colorKeywords||{},T=r.valueKeywords||{},M=r.allowNested,C=r.lineComment,j=!0===r.supportsAtComponent,I=!1!==t.highlightNonStandardPropertyKeywords,N={};return N.top=function(e,t,r){if("{"==e)return s(r,t,"block");if("}"==e&&r.context.prev)return c(r);if(j&&/@component/i.test(e))return s(r,t,"atComponentBlock");if(/^@(-moz-)?document$/i.test(e))return s(r,t,"documentTypes");if(/^@(media|supports|(-moz-)?document|import)$/i.test(e))return s(r,t,"atBlock");if(/^@(font-face|counter-style)/i.test(e))return r.stateArg=e,"restricted_atBlock_before";if(/^@(-(moz|ms|o|webkit)-)?keyframes$/i.test(e))return"keyframes";if(e&&"@"==e.charAt(0))return s(r,t,"at");if("hash"==e)g="builtin";else if("word"==e)g="tag";else{if("variable-definition"==e)return"maybeprop";if("interpolation"==e)return s(r,t,"interpolation");if(":"==e)return"pseudo";if(M&&"("==e)return s(r,t,"parens")}return r.context.type},N.block=function(e,t,r){if("word"==e){var n=t.current().toLowerCase();return w.hasOwnProperty(n)?(g="property","maybeprop"):x.hasOwnProperty(n)?(g=I?"string-2":"property","maybeprop"):M?(g=t.match(/^\s*:(?:\s|$)/,!1)?"property":"tag","block"):(g+=" error","maybeprop")}return"meta"==e?"block":M||"hash"!=e&&"qualifier"!=e?N.top(e,t,r):(g="error","block")},N.maybeprop=function(e,t,r){return":"==e?s(r,t,"prop"):d(e,t,r)},N.prop=function(e,t,r){if(";"==e)return c(r);if("{"==e&&M)return s(r,t,"propBlock");if("}"==e||"{"==e)return u(e,t,r);if("("==e)return s(r,t,"parens");if("hash"!=e||/^#([0-9a-fA-f]{3,4}|[0-9a-fA-f]{6}|[0-9a-fA-f]{8})$/.test(t.current())){if("word"==e)p(t);else if("interpolation"==e)return s(r,t,"interpolation")}else g+=" error";return"prop"},N.propBlock=function(e,t,r){return"}"==e?c(r):"word"==e?(g="property","maybeprop"):r.context.type},N.parens=function(e,t,r){return"{"==e||"}"==e?u(e,t,r):")"==e?c(r):"("==e?s(r,t,"parens"):"interpolation"==e?s(r,t,"interpolation"):("word"==e&&p(t),"parens")},N.pseudo=function(e,t,r){return"meta"==e?"pseudo":"word"==e?(g="variable-3",r.context.type):d(e,t,r)},N.documentTypes=function(e,t,r){return"word"==e&&y.hasOwnProperty(t.current())?(g="tag",r.context.type):N.atBlock(e,t,r)},N.atBlock=function(e,t,r){if("("==e)return s(r,t,"atBlock_parens");if("}"==e||";"==e)return u(e,t,r);if("{"==e)return c(r)&&s(r,t,M?"block":"top");if("interpolation"==e)return s(r,t,"interpolation");if("word"==e){var n=t.current().toLowerCase();g="only"==n||"not"==n||"and"==n||"or"==n?"keyword":b.hasOwnProperty(n)?"attribute":k.hasOwnProperty(n)?"property":v.hasOwnProperty(n)?"keyword":w.hasOwnProperty(n)?"property":x.hasOwnProperty(n)?I?"string-2":"property":T.hasOwnProperty(n)?"atom":z.hasOwnProperty(n)?"keyword":"error"}return r.context.type},N.atComponentBlock=function(e,t,r){return"}"==e?u(e,t,r):"{"==e?c(r)&&s(r,t,M?"block":"top",!1):("word"==e&&(g="error"),r.context.type)},N.atBlock_parens=function(e,t,r){return")"==e?c(r):"{"==e||"}"==e?u(e,t,r,2):N.atBlock(e,t,r)},N.restricted_atBlock_before=function(e,t,r){return"{"==e?s(r,t,"restricted_atBlock"):"word"==e&&"@counter-style"==r.stateArg?(g="variable","restricted_atBlock_before"):d(e,t,r)},N.restricted_atBlock=function(e,t,r){return"}"==e?(r.stateArg=null,c(r)):"word"==e?(g="@font-face"==r.stateArg&&!q.hasOwnProperty(t.current().toLowerCase())||"@counter-style"==r.stateArg&&!S.hasOwnProperty(t.current().toLowerCase())?"error":"property","maybeprop"):"restricted_atBlock"},N.keyframes=function(e,t,r){return"word"==e?(g="variable","keyframes"):"{"==e?s(r,t,"top"):d(e,t,r)},N.at=function(e,t,r){return";"==e?c(r):"{"==e||"}"==e?u(e,t,r):("word"==e?g="tag":"hash"==e&&(g="builtin"),"at")},N.interpolation=function(e,t,r){return"}"==e?c(r):"{"==e||";"==e?u(e,t,r):("word"==e?g="variable":"variable"!=e&&"("!=e&&")"!=e&&(g="error"),"interpolation")},{startState:function(e){return{tokenize:null,state:m?"block":"top",stateArg:null,context:new l(m?"block":"top",e||0,null)}},token:function(e,t){if(!t.tokenize&&e.eatSpace())return null;var r=(t.tokenize||a)(e,t);return r&&"object"==typeof r&&(f=r[1],r=r[0]),g=r,"comment"!=f&&(t.state=N[t.state](f,e,t)),g},indent:function(e,t){var r=e.context,n=t&&t.charAt(0),a=r.indent;return"prop"!=r.type||"}"!=n&&")"!=n||(r=r.prev),r.prev&&("}"!=n||"block"!=r.type&&"top"!=r.type&&"interpolation"!=r.type&&"restricted_atBlock"!=r.type?(")"!=n||"parens"!=r.type&&"atBlock_parens"!=r.type)&&("{"!=n||"at"!=r.type&&"atBlock"!=r.type)||(a=Math.max(0,r.indent-_)):(r=r.prev,a=r.indent)),a},electricChars:"}",blockCommentStart:"/*",blockCommentEnd:"*/",blockCommentContinue:" * ",lineComment:C,fold:"brace"}})
;var n=["domain","regexp","url","url-prefix"],a=t(n),i=["all","aural","braille","handheld","print","projection","screen","tty","tv","embossed"],o=t(i),l=["width","min-width","max-width","height","min-height","max-height","device-width","min-device-width","max-device-width","device-height","min-device-height","max-device-height","aspect-ratio","min-aspect-ratio","max-aspect-ratio","device-aspect-ratio","min-device-aspect-ratio","max-device-aspect-ratio","color","min-color","max-color","color-index","min-color-index","max-color-index","monochrome","min-monochrome","max-monochrome","resolution","min-resolution","max-resolution","scan","grid","orientation","device-pixel-ratio","min-device-pixel-ratio","max-device-pixel-ratio","pointer","any-pointer","hover","any-hover","prefers-color-scheme","dynamic-range","video-dynamic-range"],s=t(l),c=["landscape","portrait","none","coarse","fine","on-demand","hover","interlace","progressive","dark","light","standard","high"],d=t(c),u=["align-content","align-items","align-self","alignment-adjust","alignment-baseline","all","anchor-point","animation","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-timing-function","appearance","azimuth","backdrop-filter","backface-visibility","background","background-attachment","background-blend-mode","background-clip","background-color","background-image","background-origin","background-position","background-position-x","background-position-y","background-repeat","background-size","baseline-shift","binding","bleed","block-size","bookmark-label","bookmark-level","bookmark-state","bookmark-target","border","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","bottom","box-decoration-break","box-shadow","box-sizing","break-after","break-before","break-inside","caption-side","caret-color","clear","clip","color","color-profile","column-count","column-fill","column-gap","column-rule","column-rule-color","column-rule-style","column-rule-width","column-span","column-width","columns","contain","content","counter-increment","counter-reset","crop","cue","cue-after","cue-before","cursor","direction","display","dominant-baseline","drop-initial-after-adjust","drop-initial-after-align","drop-initial-before-adjust","drop-initial-before-align","drop-initial-size","drop-initial-value","elevation","empty-cells","fit","fit-content","fit-position","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","float","float-offset","flow-from","flow-into","font","font-family","font-feature-settings","font-kerning","font-language-override","font-optical-sizing","font-size","font-size-adjust","font-stretch","font-style","font-synthesis","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-ligatures","font-variant-numeric","font-variant-position","font-variation-settings","font-weight","gap","grid","grid-area","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-column","grid-column-end","grid-column-gap","grid-column-start","grid-gap","grid-row","grid-row-end","grid-row-gap","grid-row-start","grid-template","grid-template-areas","grid-template-columns","grid-template-rows","hanging-punctuation","height","hyphens","icon","image-orientation","image-rendering","image-resolution","inline-box-align","inset","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","isolation","justify-content","justify-items","justify-self","left","letter-spacing","line-break","line-height","line-height-step","line-stacking","line-stacking-ruby","line-stacking-shift","line-stacking-strategy","list-style","list-style-image","list-style-position","list-style-type","margin","margin-bottom","margin-left","margin-right","margin-top","marks","marquee-direction","marquee-loop","marquee-play-count","marquee-speed","marquee-style","mask-clip","mask-composite","mask-image","mask-mode","mask-origin","mask-position","mask-repeat","mask-size","mask-type","max-block-size","max-height","max-inline-size","max-width","min-block-size","min-height","min-inline-size","min-width","mix-blend-mode","move-to","nav-down","nav-index","nav-left","nav-right","nav-up","object-fit","object-position","offset","offset-anchor","offset-distance","offset-path","offset-position","offset-rotate","opacity","order","orphans","outline","outline-color","outline-offset","outline-style","outline-width","overflow","overflow-style","overflow-wrap","overflow-x","overflow-y","padding","padding-bottom","padding-left","padding-right","padding-top","page","page-break-after","page-break-before","page-break-inside","page-policy","pause","pause-after","pause-before","perspective","perspective-origin","pitch","pitch-range","place-content","place-items","place-self","play-during","position","presentation-level","punctuation-trim","quotes","region-break-after","region-break-before","region-break-inside","region-fragment","rendering-intent","resize","rest","rest-after","rest-before","richness","right","rotate","rotation","rotation-point","row-gap","ruby-align","ruby-overhang","ruby-position","ruby-span","scale","scroll-behavior","scroll-margin","scroll-margin-block","scroll-margin-block-end","scroll-margin-block-start","scroll-margin-bottom","scroll-margin-inline","scroll-margin-inline-end","scroll-margin-inline-start","scroll-margin-left","scroll-margin-right","scroll-margin-top","scroll-padding","scroll-padding-block","scroll-padding-block-end","scroll-padding-block-start","scroll-padding-bottom","scroll-padding-inline","scroll-padding-inline-end","scroll-padding-inline-start","scroll-padding-left","scroll-padding-right","scroll-padding-top","scroll-snap-align","scroll-snap-type","shape-image-threshold","shape-inside","shape-margin","shape-outside","size","speak","speak-as","speak-header","speak-numeral","speak-punctuation","speech-rate","stress","string-set","tab-size","table-layout","target","target-name","target-new","target-position","text-align","text-align-last","text-combine-upright","text-decoration","text-decoration-color","text-decoration-line","text-decoration-skip","text-decoration-skip-ink","text-decoration-style","text-emphasis","text-emphasis-color","text-emphasis-position","text-emphasis-style","text-height","text-indent","text-justify","text-orientation","text-outline","text-overflow","text-rendering","text-shadow","text-size-adjust","text-space-collapse","text-transform","text-underline-position","text-wrap","top","touch-action","transform","transform-origin","transform-style","transition","transition-delay","transition-duration","transition-property","transition-timing-function","translate","unicode-bidi","user-select","vertical-align","visibility","voice-balance","voice-duration","voice-family","voice-pitch","voice-range","voice-rate","voice-stress","voice-volume","volume","white-space","widows","width","will-change","word-break","word-spacing","word-wrap","writing-mode","z-index","clip-path","clip-rule","mask","enable-background","filter","flood-color","flood-opacity","lighting-color","stop-color","stop-opacity","pointer-events","color-interpolation","color-interpolation-filters","color-rendering","fill","fill-opacity","fill-rule","image-rendering","marker","marker-end","marker-mid","marker-start","paint-order","shape-rendering","stroke","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","text-rendering","baseline-shift","dominant-baseline","glyph-orientation-horizontal","glyph-orientation-vertical","text-anchor","writing-mode"],p=t(u),m=["accent-color","aspect-ratio","border-block","border-block-color","border-block-end","border-block-end-color","border-block-end-style","border-block-end-width","border-block-start","border-block-start-color","border-block-start-style","border-block-start-width","border-block-style","border-block-width","border-inline","border-inline-color","border-inline-end","border-inline-end-color","border-inline-end-style","border-inline-end-width","border-inline-start","border-inline-start-color","border-inline-start-style","border-inline-start-width","border-inline-style","border-inline-width","content-visibility","margin-block","margin-block-end","margin-block-start","margin-inline","margin-inline-end","margin-inline-start","overflow-anchor","overscroll-behavior","padding-block","padding-block-end","padding-block-start","padding-inline","padding-inline-end","padding-inline-start","scroll-snap-stop","scrollbar-3d-light-color","scrollbar-arrow-color","scrollbar-base-color","scrollbar-dark-shadow-color","scrollbar-face-color","scrollbar-highlight-color","scrollbar-shadow-color","scrollbar-track-color","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","shape-inside","zoom"],f=t(m),g=["font-display","font-family","src","unicode-range","font-variant","font-feature-settings","font-stretch","font-weight","font-style"],_=t(g),h=["additive-symbols","fallback","negative","pad","prefix","range","speak-as","suffix","symbols","system"],y=t(h),b=["aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkgrey","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkslategrey","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dimgrey","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","grey","green","greenyellow","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightgrey","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightslategrey","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","slategrey","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen"],k=t(b),v=["above","absolute","activeborder","additive","activecaption","afar","after-white-space","ahead","alias","all","all-scroll","alphabetic","alternate","always","amharic","amharic-abegede","antialiased","appworkspace","arabic-indic","armenian","asterisks","attr","auto","auto-flow","avoid","avoid-column","avoid-page","avoid-region","axis-pan","background","backwards","baseline","below","bidi-override","binary","bengali","blink","block","block-axis","blur","bold","bolder","border","border-box","both","bottom","break","break-all","break-word","brightness","bullets","button","button-bevel","buttonface","buttonhighlight","buttonshadow","buttontext","calc","cambodian","capitalize","caps-lock-indicator","caption","captiontext","caret","cell","center","checkbox","circle","cjk-decimal","cjk-earthly-branch","cjk-heavenly-stem","cjk-ideographic","clear","clip","close-quote","col-resize","collapse","color","color-burn","color-dodge","column","column-reverse","compact","condensed","conic-gradient","contain","content","contents","content-box","context-menu","continuous","contrast","copy","counter","counters","cover","crop","cross","crosshair","cubic-bezier","currentcolor","cursive","cyclic","darken","dashed","decimal","decimal-leading-zero","default","default-button","dense","destination-atop","destination-in","destination-out","destination-over","devanagari","difference","disc","discard","disclosure-closed","disclosure-open","document","dot-dash","dot-dot-dash","dotted","double","down","drop-shadow","e-resize","ease","ease-in","ease-in-out","ease-out","element","ellipse","ellipsis","embed","end","ethiopic","ethiopic-abegede","ethiopic-abegede-am-et","ethiopic-abegede-gez","ethiopic-abegede-ti-er","ethiopic-abegede-ti-et","ethiopic-halehame-aa-er","ethiopic-halehame-aa-et","ethiopic-halehame-am-et","ethiopic-halehame-gez","ethiopic-halehame-om-et","ethiopic-halehame-sid-et","ethiopic-halehame-so-et","ethiopic-halehame-ti-er","ethiopic-halehame-ti-et","ethiopic-halehame-tig","ethiopic-numeric","ew-resize","exclusion","expanded","extends","extra-condensed","extra-expanded","fantasy","fast","fill","fill-box","fixed","flat","flex","flex-end","flex-start","footnotes","forwards","from","geometricPrecision","georgian","grayscale","graytext","grid","groove","gujarati","gurmukhi","hand","hangul","hangul-consonant","hard-light","hebrew","help","hidden","hide","higher","highlight","highlighttext","hiragana","hiragana-iroha","horizontal","hsl","hsla","hue","hue-rotate","icon","ignore","inactiveborder","inactivecaption","inactivecaptiontext","infinite","infobackground","infotext","inherit","initial","inline","inline-axis","inline-block","inline-flex","inline-grid","inline-table","inset","inside","intrinsic","invert","italic","japanese-formal","japanese-informal","justify","kannada","katakana","katakana-iroha","keep-all","khmer","korean-hangul-formal","korean-hanja-formal","korean-hanja-informal","landscape","lao","large","larger","left","level","lighter","lighten","line-through","linear","linear-gradient","lines","list-item","listbox","listitem","local","logical","loud","lower","lower-alpha","lower-armenian","lower-greek","lower-hexadecimal","lower-latin","lower-norwegian","lower-roman","lowercase","ltr","luminosity","malayalam","manipulation","match","matrix","matrix3d","media-controls-background","media-current-time-display","media-fullscreen-button","media-mute-button","media-play-button","media-return-to-realtime-button","media-rewind-button","media-seek-back-button","media-seek-forward-button","media-slider","media-sliderthumb","media-time-remaining-display","media-volume-slider","media-volume-slider-container","media-volume-sliderthumb","medium","menu","menulist","menulist-button","menulist-text","menulist-textfield","menutext","message-box","middle","min-intrinsic","mix","mongolian","monospace","move","multiple","multiple_mask_images","multiply","myanmar","n-resize","narrower","ne-resize","nesw-resize","no-close-quote","no-drop","no-open-quote","no-repeat","none","normal","not-allowed","nowrap","ns-resize","numbers","numeric","nw-resize","nwse-resize","oblique","octal","opacity","open-quote","optimizeLegibility","optimizeSpeed","oriya","oromo","outset","outside","outside-shape","overlay","overline","padding","padding-box","painted","page","paused","persian","perspective","pinch-zoom","plus-darker","plus-lighter","pointer","polygon","portrait","pre","pre-line","pre-wrap","preserve-3d","progress","push-button","radial-gradient","radio","read-only","read-write","read-write-plaintext-only","rectangle","region","relative","repeat","repeating-linear-gradient","repeating-radial-gradient","repeating-conic-gradient","repeat-x","repeat-y","reset","reverse","rgb","rgba","ridge","right","rotate","rotate3d","rotateX","rotateY","rotateZ","round","row","row-resize","row-reverse","rtl","run-in","running","s-resize","sans-serif","saturate","saturation","scale","scale3d","scaleX","scaleY","scaleZ","screen","scroll","scrollbar","scroll-position","se-resize","searchfield","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","self-start","self-end","semi-condensed","semi-expanded","separate","sepia","serif","show","sidama","simp-chinese-formal","simp-chinese-informal","single","skew","skewX","skewY","skip-white-space","slide","slider-horizontal","slider-vertical","sliderthumb-horizontal","sliderthumb-vertical","slow","small","small-caps","small-caption","smaller","soft-light","solid","somali","source-atop","source-in","source-out","source-over","space","space-around","space-between","space-evenly","spell-out","square","square-button","start","static","status-bar","stretch","stroke","stroke-box","sub","subpixel-antialiased","svg_masks","super","sw-resize","symbolic","symbols","system-ui","table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row","table-row-group","tamil","telugu","text","text-bottom","text-top","textarea","textfield","thai","thick","thin","threeddarkshadow","threedface","threedhighlight","threedlightshadow","threedshadow","tibetan","tigre","tigrinya-er","tigrinya-er-abegede","tigrinya-et","tigrinya-et-abegede","to","top","trad-chinese-formal","trad-chinese-informal","transform","translate","translate3d","translateX","translateY","translateZ","transparent","ultra-condensed","ultra-expanded","underline","unidirectional-pan","unset","up","upper-alpha","upper-armenian","upper-greek","upper-hexadecimal","upper-latin","upper-norwegian","upper-roman","uppercase","urdu","url","var","vertical","vertical-text","view-box","visible","visibleFill","visiblePainted","visibleStroke","visual","w-resize","wait","wave","wider","window","windowframe","windowtext","words","wrap","wrap-reverse","x-large","x-small","xor","xx-large","xx-small"],w=t(v),x=n.concat(i).concat(l).concat(c).concat(u).concat(m).concat(b).concat(v);e.registerHelper("hintWords","css",x),e.defineMIME("text/css",{documentTypes:a,mediaTypes:o,mediaFeatures:s,mediaValueKeywords:d,propertyKeywords:p,nonStandardPropertyKeywords:f,fontProperties:_,counterDescriptors:y,colorKeywords:k,valueKeywords:w,tokenHooks:{"/":function(e,t){return!!e.eat("*")&&(t.tokenize=r,r(e,t))}},name:"css"}),e.defineMIME("text/x-scss",{mediaTypes:o,mediaFeatures:s,mediaValueKeywords:d,propertyKeywords:p,nonStandardPropertyKeywords:f,colorKeywords:k,valueKeywords:w,fontProperties:_,allowNested:!0,lineComment:"//",tokenHooks:{"/":function(e,t){return e.eat("/")?(e.skipToEnd(),["comment","comment"]):e.eat("*")?(t.tokenize=r,r(e,t)):["operator","operator"]},":":function(e){return!!e.match(/^\s*\{/,!1)&&[null,null]},$:function(e){return e.match(/^[\w-]+/),e.match(/^\s*:/,!1)?["variable-2","variable-definition"]:["variable-2","variable"]},"#":function(e){return!!e.eat("{")&&[null,"interpolation"]}},name:"css",helperType:"scss"}),e.defineMIME("text/x-less",{mediaTypes:o,mediaFeatures:s,mediaValueKeywords:d,propertyKeywords:p,nonStandardPropertyKeywords:f,colorKeywords:k,valueKeywords:w,fontProperties:_,allowNested:!0,lineComment:"//",tokenHooks:{"/":function(e,t){return e.eat("/")?(e.skipToEnd(),["comment","comment"]):e.eat("*")?(t.tokenize=r,r(e,t)):["operator","operator"]},"@":function(e){return e.eat("{")?[null,"interpolation"]:!e.match(/^(charset|document|font-face|import|(-(moz|ms|o|webkit)-)?keyframes|media|namespace|page|supports)\b/i,!1)&&(e.eatWhile(/[\w\\\-]/),e.match(/^\s*:/,!1)?["variable-2","variable-definition"]:["variable-2","variable"])},"&":function(){return["atom","atom"]}},name:"css",helperType:"less"}),e.defineMIME("text/x-gss",{documentTypes:a,mediaTypes:o,mediaFeatures:s,propertyKeywords:p,nonStandardPropertyKeywords:f,fontProperties:_,counterDescriptors:y,colorKeywords:k,valueKeywords:w,supportsAtComponent:!0,tokenHooks:{"/":function(e,t){return!!e.eat("*")&&(t.tokenize=r,r(e,t))}},name:"css",helperType:"gss"})}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror"),require("../xml/xml"),require("../javascript/javascript"),require("../css/css")):"function"==typeof define&&define.amd?define("mode/htmlmixed/htmlmixed",["../../lib/codemirror","../xml/xml","../javascript/javascript","../css/css"],e):e(CodeMirror)}(function(e){"use strict";function t(e,t,r){var n=e.current(),a=n.search(t);return a>-1?e.backUp(n.length-a):n.match(/<\/?$/)&&(e.backUp(n.length),e.match(t,!1)||e.match(n)),r}function r(e){var t=s[e];return t||(s[e]=new RegExp("\\s+"+e+"\\s*=\\s*('|\")?([^'\"]+)('|\")?\\s*"))}function n(e,t){var n=e.match(r(t));return n?/^\s*(.*?)\s*$/.exec(n[2])[1]:""}function a(e,t){return new RegExp((t?"^":"")+"</\\s*"+e+"\\s*>","i")}function i(e,t){for(var r in e)for(var n=t[r]||(t[r]=[]),a=e[r],i=a.length-1;i>=0;i--)n.unshift(a[i])}function o(e,t){for(var r=0;r<e.length;r++){var a=e[r];if(!a[0]||a[1].test(n(t,a[0])))return a[2]}}var l={script:[["lang",/(javascript|babel)/i,"javascript"],["type",/^(?:text|application)\/(?:x-)?(?:java|ecma)script$|^module$|^$/i,"javascript"],["type",/./,"text/plain"],[null,null,"javascript"]],style:[["lang",/^css$/i,"css"],["type",/^(text\/)?(x-)?(stylesheet|css)$/i,"css"],["type",/./,"text/plain"],[null,null,"css"]]},s={};e.defineMode("htmlmixed",function(r,n){function s(n,i){var l,u=c.token(n,i.htmlState),p=/\btag\b/.test(u);if(p&&!/[<>\s\/]/.test(n.current())&&(l=i.htmlState.tagName&&i.htmlState.tagName.toLowerCase())&&d.hasOwnProperty(l))i.inTag=l+" ";else if(i.inTag&&p&&/>$/.test(n.current())){var m=/^([\S]+) (.*)/.exec(i.inTag);i.inTag=null;var f=">"==n.current()&&o(d[m[1]],m[2]),g=e.getMode(r,f),_=a(m[1],!0),h=a(m[1],!1);i.token=function(e,r){return e.match(_,!1)?(r.token=s,r.localState=r.localMode=null,null):t(e,h,r.localMode.token(e,r.localState))},i.localMode=g,i.localState=e.startState(g,c.indent(i.htmlState,"",""))}else i.inTag&&(i.inTag+=n.current(),n.eol()&&(i.inTag+=" "));return u}var c=e.getMode(r,{name:"xml",htmlMode:!0,multilineTagIndentFactor:n.multilineTagIndentFactor,multilineTagIndentPastTag:n.multilineTagIndentPastTag,allowMissingTagName:n.allowMissingTagName}),d={},u=n&&n.tags,p=n&&n.scriptTypes;if(i(l,d),u&&i(u,d),p)for(var m=p.length-1;m>=0;m--)d.script.unshift(["type",p[m].matches,p[m].mode]);return{startState:function(){return{token:s,inTag:null,localMode:null,localState:null,htmlState:e.startState(c)}},copyState:function(t){var r;return t.localState&&(r=e.copyState(t.localMode,t.localState)),{token:t.token,inTag:t.inTag,localMode:t.localMode,localState:r,htmlState:e.copyState(c,t.htmlState)}},token:function(e,t){return t.token(e,t)},indent:function(t,r,n){return!t.localMode||/^\s*<\//.test(r)?c.indent(t.htmlState,r,n):t.localMode.indent?t.localMode.indent(t.localState,r,n):e.Pass},innerMode:function(e){return{state:e.localState||e.htmlState,mode:e.localMode||c}}}},"xml","javascript","css"),e.defineMIME("text/html","htmlmixed")}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define("mode/clike/clike",["../../lib/codemirror"],e):e(CodeMirror)}(function(e){"use strict";function t(e,t,r,n,a,i){this.indented=e,this.column=t,this.type=r,this.info=n,this.align=a,this.prev=i}function r(e,r,n,a){var i=e.indented;return e.context&&"statement"==e.context.type&&"statement"!=n&&(i=e.context.indented),e.context=new t(i,r,n,a,null,e.context)}function n(e){var t=e.context.type;return")"!=t&&"]"!=t&&"}"!=t||(e.indented=e.context.indented),e.context=e.context.prev}function a(e,t,r){return"variable"==t.prevToken||"type"==t.prevToken||(!!/\S(?:[^- ]>|[*\]])\s*$|\*$/.test(e.string.slice(0,r))||(!(!t.typeAtEndOfLine||e.column()!=e.indentation())||void 0))}function i(e){for(;;){if(!e||"top"==e.type)return!0;if("}"==e.type&&"namespace"!=e.prev.info)return!1;e=e.prev}}function o(e){for(var t={},r=e.split(" "),n=0;n<r.length;++n)t[r[n]]=!0;return t}function l(e,t){return"function"==typeof e?e(t):e.propertyIsEnumerable(t)}function s(e){return l(T,e)||/.+_t$/.test(e)}function c(e){return s(e)||l(M,e)}function d(e,t){if(!t.startOfLine)return!1;for(var r,n=null;r=e.peek();){if("\\"==r&&e.match(/^.$/)){n=d;break}if("/"==r&&e.match(/^\/[\/\*]/,!1))break;e.next()}return t.tokenize=n,"meta"}function u(e,t){return"type"==t.prevToken&&"type"}function p(e){return!(!e||e.length<2)&&("_"==e[0]&&("_"==e[1]||e[1]!==e[1].toLowerCase()))}function m(e){return e.eatWhile(/[\w\.']/),"number"}function f(e,t){if(e.backUp(1),e.match(/^(?:R|u8R|uR|UR|LR)/)){var r=e.match(/^"([^\s\\()]{0,16})\(/);return!!r&&(t.cpp11RawStringDelim=r[1],t.tokenize=h,h(e,t))}return e.match(/^(?:u8|u|U|L)/)?!!e.match(/^["']/,!1)&&"string":(e.next(),!1)}function g(e){var t=/(\w+)::~?(\w+)$/.exec(e);return t&&t[1]==t[2]}function _(e,t){for(var r;null!=(r=e.next());)if('"'==r&&!e.eat('"')){t.tokenize=null;break}return"string"}function h(e,t){var r=t.cpp11RawStringDelim.replace(/[^\w\s]/g,"\\$&");return e.match(new RegExp(".*?\\)"+r+'"'))?t.tokenize=null:e.skipToEnd(),"string"}function y(t,r){function n(e){if(e)for(var t in e)e.hasOwnProperty(t)&&a.push(t)}"string"==typeof t&&(t=[t]);var a=[];n(r.keywords),n(r.types),n(r.builtin),n(r.atoms),a.length&&(r.helperType=t[0],e.registerHelper("hintWords",t[0],a));for(var i=0;i<t.length;++i)e.defineMIME(t[i],r)}function b(e,t){for(var r=!1;!e.eol();){if(!r&&e.match('"""')){t.tokenize=null;break}r="\\"==e.next()&&!r}return"string"}function k(e){return function(t,r){for(var n;n=t.next();){if("*"==n&&t.eat("/")){if(1==e){r.tokenize=null;break}return r.tokenize=k(e-1),r.tokenize(t,r)}if("/"==n&&t.eat("*"))return r.tokenize=k(e+1),r.tokenize(t,r)}return"comment"}}function v(e){return function(t,r){for(var n,a=!1,i=!1;!t.eol();){if(!e&&!a&&t.match('"')){i=!0;break}if(e&&t.match('"""')){i=!0;break}n=t.next(),!a&&"$"==n&&t.match("{")&&t.skipTo("}"),a=!a&&"\\"==n&&!e}return!i&&e||(r.tokenize=null),"string"}}function w(e){return function(t,r){for(var n,a=!1,i=!1;!t.eol();){if(!a&&t.match('"')&&("single"==e||t.match('""'))){i=!0;break}if(!a&&t.match("``")){j=w(e),i=!0;break}n=t.next(),a="single"==e&&!a&&"\\"==n}return i&&(r.tokenize=null),"string"}}e.defineMode("clike",function(o,s){function c(e,t){var r=e.next();if(q[r]){var n=q[r](e,t);if(!1!==n)return n}if('"'==r||"'"==r)return t.tokenize=d(r),t.tokenize(e,t);if(j.test(r)){if(e.backUp(1),e.match(I))return"number";e.next()}if(C.test(r))return m=r,null;if("/"==r){if(e.eat("*"))return t.tokenize=u,u(e,t);if(e.eat("/"))return e.skipToEnd(),"comment"}if(N.test(r)){for(;!e.match(/^\/[\/*]/,!1)&&e.eat(N););return"operator"}if(e.eatWhile(P),M)for(;e.match(M);)e.eatWhile(P);var a=e.current();return l(y,a)?(l(v,a)&&(m="newstatement"),l(w,a)&&(f=!0),"keyword"):l(b,a)?"type":l(k,a)||E&&E(a)?(l(v,a)&&(m="newstatement"),"builtin"):l(x,a)?"atom":"variable"}function d(e){return function(t,r){for(var n,a=!1,i=!1;null!=(n=t.next());){if(n==e&&!a){i=!0;break}a=!a&&"\\"==n}return(i||!a&&!S)&&(r.tokenize=null),"string"}}function u(e,t){for(var r,n=!1;r=e.next();){if("/"==r&&n){t.tokenize=null;break}n="*"==r}return"comment"}function p(e,t){s.typeFirstDefinitions&&e.eol()&&i(t.context)&&(t.typeAtEndOfLine=a(e,t,e.pos))}var m,f,g=o.indentUnit,_=s.statementIndentUnit||g,h=s.dontAlignCalls,y=s.keywords||{},b=s.types||{},k=s.builtin||{},v=s.blockKeywords||{},w=s.defKeywords||{},x=s.atoms||{},q=s.hooks||{},S=s.multiLineStrings,z=!1!==s.indentStatements,T=!1!==s.indentSwitch,M=s.namespaceSeparator,C=s.isPunctuationChar||/[\[\]{}\(\),;\:\.]/,j=s.numberStart||/[\d\.]/,I=s.number||/^(?:0x[a-f\d]+|0b[01]+|(?:\d+\.?\d*|\.\d+)(?:e[-+]?\d+)?)(u|ll?|l|f)?/i,N=s.isOperatorChar||/[+\-*&%=<>!?|\/]/,P=s.isIdentifierChar||/[\w\$_\xa1-\uffff]/,E=s.isReservedIdentifier||!1;return{startState:function(e){return{tokenize:null,context:new t((e||0)-g,0,"top",null,!1),indented:0,startOfLine:!0,prevToken:null}},token:function(e,t){var o=t.context;if(e.sol()&&(null==o.align&&(o.align=!1),t.indented=e.indentation(),t.startOfLine=!0),e.eatSpace())return p(e,t),null;m=f=null;var l=(t.tokenize||c)(e,t);if("comment"==l||"meta"==l)return l;if(null==o.align&&(o.align=!0),";"==m||":"==m||","==m&&e.match(/^\s*(?:\/\/.*)?$/,!1))for(;"statement"==t.context.type;)n(t);else if("{"==m)r(t,e.column(),"}");else if("["==m)r(t,e.column(),"]");else if("("==m)r(t,e.column(),")");else if("}"==m){for(;"statement"==o.type;)o=n(t);for("}"==o.type&&(o=n(t));"statement"==o.type;)o=n(t)}else m==o.type?n(t):z&&(("}"==o.type||"top"==o.type)&&";"!=m||"statement"==o.type&&"newstatement"==m)&&r(t,e.column(),"statement",e.current());if("variable"==l&&("def"==t.prevToken||s.typeFirstDefinitions&&a(e,t,e.start)&&i(t.context)&&e.match(/^\s*\(/,!1))&&(l="def"),q.token){var d=q.token(e,t,l);void 0!==d&&(l=d)}return"def"==l&&!1===s.styleDefs&&(l="variable"),t.startOfLine=!1,t.prevToken=f?"def":l||m,p(e,t),l},indent:function(t,r){if(t.tokenize!=c&&null!=t.tokenize||t.typeAtEndOfLine)return e.Pass;var n=t.context,a=r&&r.charAt(0),i=a==n.type;if("statement"==n.type&&"}"==a&&(n=n.prev),s.dontIndentStatements)for(;"statement"==n.type&&s.dontIndentStatements.test(n.info);)n=n.prev;if(q.indent){var o=q.indent(t,n,r,g);if("number"==typeof o)return o}var l=n.prev&&"switch"==n.prev.info;if(s.allmanIndentation&&/[{(]/.test(a)){for(;"top"!=n.type&&"}"!=n.type;)n=n.prev;return n.indented}return"statement"==n.type?n.indented+("{"==a?0:_):!n.align||h&&")"==n.type?")"!=n.type||i?n.indented+(i?0:g)+(i||!l||/^(?:case|default)\b/.test(r)?0:g):n.indented+_:n.column+(i?0:1)},electricInput:T?/^\s*(?:case .*?:|default:|\{\}?|\})$/:/^\s*[{}]$/,blockCommentStart:"/*",blockCommentEnd:"*/",blockCommentContinue:" * ",lineComment:"//",fold:"brace"}});var x="auto if break case register continue return default do sizeof static else struct switch extern typedef union for goto while enum const volatile inline restrict asm fortran",q="alignas alignof and and_eq audit axiom bitand bitor catch class compl concept constexpr const_cast decltype delete dynamic_cast explicit export final friend import module mutable namespace new noexcept not not_eq operator or or_eq override private protected public reinterpret_cast requires static_assert static_cast template this thread_local throw try typeid typename using virtual xor xor_eq",S="bycopy byref in inout oneway out self super atomic nonatomic retain copy readwrite readonly strong weak assign typeof nullable nonnull null_resettable _cmd @interface @implementation @end @protocol @encode @property @synthesize @dynamic @class @public @package @private @protected @required @optional @try @catch @finally @import @selector @encode @defs @synchronized @autoreleasepool @compatibility_alias @available",z="FOUNDATION_EXPORT FOUNDATION_EXTERN NS_INLINE NS_FORMAT_FUNCTION  NS_RETURNS_RETAINEDNS_ERROR_ENUM NS_RETURNS_NOT_RETAINED NS_RETURNS_INNER_POINTER NS_DESIGNATED_INITIALIZER NS_ENUM NS_OPTIONS NS_REQUIRES_NIL_TERMINATION NS_ASSUME_NONNULL_BEGIN NS_ASSUME_NONNULL_END NS_SWIFT_NAME NS_REFINED_FOR_SWIFT",T=o("int long char short double float unsigned signed void bool"),M=o("SEL instancetype id Class Protocol BOOL"),C="case do else for if switch while struct enum union";y(["text/x-csrc","text/x-c","text/x-chdr"],{name:"clike",keywords:o(x),types:s,blockKeywords:o(C),defKeywords:o("struct enum union"),typeFirstDefinitions:!0,atoms:o("NULL true false"),isReservedIdentifier:p,hooks:{"#":d,"*":u},modeProps:{fold:["brace","include"]}}),y(["text/x-c++src","text/x-c++hdr"],{name:"clike",keywords:o(x+" "+q),types:s,
blockKeywords:o(C+" class try catch"),defKeywords:o("struct enum union class namespace"),typeFirstDefinitions:!0,atoms:o("true false NULL nullptr"),dontIndentStatements:/^template$/,isIdentifierChar:/[\w\$_~\xa1-\uffff]/,isReservedIdentifier:p,hooks:{"#":d,"*":u,u:f,U:f,L:f,R:f,0:m,1:m,2:m,3:m,4:m,5:m,6:m,7:m,8:m,9:m,token:function(e,t,r){if("variable"==r&&"("==e.peek()&&(";"==t.prevToken||null==t.prevToken||"}"==t.prevToken)&&g(e.current()))return"def"}},namespaceSeparator:"::",modeProps:{fold:["brace","include"]}}),y("text/x-java",{name:"clike",keywords:o("abstract assert break case catch class const continue default do else enum extends final finally for goto if implements import instanceof interface native new package private protected public return static strictfp super switch synchronized this throw throws transient try volatile while @interface"),types:o("var byte short int long float double boolean char void Boolean Byte Character Double Float Integer Long Number Object Short String StringBuffer StringBuilder Void"),blockKeywords:o("catch class do else finally for if switch try while"),defKeywords:o("class interface enum @interface"),typeFirstDefinitions:!0,atoms:o("true false null"),number:/^(?:0x[a-f\d_]+|0b[01_]+|(?:[\d_]+\.?\d*|\.\d+)(?:e[-+]?[\d_]+)?)(u|ll?|l|f)?/i,hooks:{"@":function(e){return!e.match("interface",!1)&&(e.eatWhile(/[\w\$_]/),"meta")},'"':function(e,t){return!!e.match('""\n')&&(t.tokenize=b,t.tokenize(e,t))}},modeProps:{fold:["brace","import"]}}),y("text/x-csharp",{name:"clike",keywords:o("abstract as async await base break case catch checked class const continue default delegate do else enum event explicit extern finally fixed for foreach goto if implicit in interface internal is lock namespace new operator out override params private protected public readonly ref return sealed sizeof stackalloc static struct switch this throw try typeof unchecked unsafe using virtual void volatile while add alias ascending descending dynamic from get global group into join let orderby partial remove select set value var yield"),types:o("Action Boolean Byte Char DateTime DateTimeOffset Decimal Double Func Guid Int16 Int32 Int64 Object SByte Single String Task TimeSpan UInt16 UInt32 UInt64 bool byte char decimal double short int long object sbyte float string ushort uint ulong"),blockKeywords:o("catch class do else finally for foreach if struct switch try while"),defKeywords:o("class interface namespace struct var"),typeFirstDefinitions:!0,atoms:o("true false null"),hooks:{"@":function(e,t){return e.eat('"')?(t.tokenize=_,_(e,t)):(e.eatWhile(/[\w\$_]/),"meta")}}}),y("text/x-scala",{name:"clike",keywords:o("abstract case catch class def do else extends final finally for forSome if implicit import lazy match new null object override package private protected return sealed super this throw trait try type val var while with yield _ assert assume require print println printf readLine readBoolean readByte readShort readChar readInt readLong readFloat readDouble"),types:o("AnyVal App Application Array BufferedIterator BigDecimal BigInt Char Console Either Enumeration Equiv Error Exception Fractional Function IndexedSeq Int Integral Iterable Iterator List Map Numeric Nil NotNull Option Ordered Ordering PartialFunction PartialOrdering Product Proxy Range Responder Seq Serializable Set Specializable Stream StringBuilder StringContext Symbol Throwable Traversable TraversableOnce Tuple Unit Vector Boolean Byte Character CharSequence Class ClassLoader Cloneable Comparable Compiler Double Exception Float Integer Long Math Number Object Package Pair Process Runtime Runnable SecurityManager Short StackTraceElement StrictMath String StringBuffer System Thread ThreadGroup ThreadLocal Throwable Triple Void"),multiLineStrings:!0,blockKeywords:o("catch class enum do else finally for forSome if match switch try while"),defKeywords:o("class enum def object package trait type val var"),atoms:o("true false null"),indentStatements:!1,indentSwitch:!1,isOperatorChar:/[+\-*&%=<>!?|\/#:@]/,hooks:{"@":function(e){return e.eatWhile(/[\w\$_]/),"meta"},'"':function(e,t){return!!e.match('""')&&(t.tokenize=b,t.tokenize(e,t))},"'":function(e){return e.eatWhile(/[\w\$_\xa1-\uffff]/),"atom"},"=":function(e,r){var n=r.context;return!("}"!=n.type||!n.align||!e.eat(">"))&&(r.context=new t(n.indented,n.column,n.type,n.info,null,n.prev),"operator")},"/":function(e,t){return!!e.eat("*")&&(t.tokenize=k(1),t.tokenize(e,t))}},modeProps:{closeBrackets:{pairs:'()[]{}""',triples:'"'}}}),y("text/x-kotlin",{name:"clike",keywords:o("package as typealias class interface this super val operator var fun for is in This throw return annotation break continue object if else while do try when !in !is as? file import where by get set abstract enum open inner override private public internal protected catch finally out final vararg reified dynamic companion constructor init sealed field property receiver param sparam lateinit data inline noinline tailrec external annotation crossinline const operator infix suspend actual expect setparam value"),types:o("Boolean Byte Character CharSequence Class ClassLoader Cloneable Comparable Compiler Double Exception Float Integer Long Math Number Object Package Pair Process Runtime Runnable SecurityManager Short StackTraceElement StrictMath String StringBuffer System Thread ThreadGroup ThreadLocal Throwable Triple Void Annotation Any BooleanArray ByteArray Char CharArray DeprecationLevel DoubleArray Enum FloatArray Function Int IntArray Lazy LazyThreadSafetyMode LongArray Nothing ShortArray Unit"),intendSwitch:!1,indentStatements:!1,multiLineStrings:!0,number:/^(?:0x[a-f\d_]+|0b[01_]+|(?:[\d_]+(\.\d+)?|\.\d+)(?:e[-+]?[\d_]+)?)(u|ll?|l|f)?/i,blockKeywords:o("catch class do else finally for if where try while enum"),defKeywords:o("class val var object interface fun"),atoms:o("true false null this"),hooks:{"@":function(e){return e.eatWhile(/[\w\$_]/),"meta"},"*":function(e,t){return"."==t.prevToken?"variable":"operator"},'"':function(e,t){return t.tokenize=v(e.match('""')),t.tokenize(e,t)},"/":function(e,t){return!!e.eat("*")&&(t.tokenize=k(1),t.tokenize(e,t))},indent:function(e,t,r,n){var a=r&&r.charAt(0);return"}"!=e.prevToken&&")"!=e.prevToken||""!=r?"operator"==e.prevToken&&"}"!=r&&"}"!=e.context.type||"variable"==e.prevToken&&"."==a||("}"==e.prevToken||")"==e.prevToken)&&"."==a?2*n+t.indented:t.align&&"}"==t.type?t.indented+(e.context.type==(r||"").charAt(0)?0:n):void 0:e.indented}},modeProps:{closeBrackets:{triples:'"'}}}),y(["x-shader/x-vertex","x-shader/x-fragment"],{name:"clike",keywords:o("sampler1D sampler2D sampler3D samplerCube sampler1DShadow sampler2DShadow const attribute uniform varying break continue discard return for while do if else struct in out inout"),types:o("float int bool void vec2 vec3 vec4 ivec2 ivec3 ivec4 bvec2 bvec3 bvec4 mat2 mat3 mat4"),blockKeywords:o("for while do if else struct"),builtin:o("radians degrees sin cos tan asin acos atan pow exp log exp2 sqrt inversesqrt abs sign floor ceil fract mod min max clamp mix step smoothstep length distance dot cross normalize ftransform faceforward reflect refract matrixCompMult lessThan lessThanEqual greaterThan greaterThanEqual equal notEqual any all not texture1D texture1DProj texture1DLod texture1DProjLod texture2D texture2DProj texture2DLod texture2DProjLod texture3D texture3DProj texture3DLod texture3DProjLod textureCube textureCubeLod shadow1D shadow2D shadow1DProj shadow2DProj shadow1DLod shadow2DLod shadow1DProjLod shadow2DProjLod dFdx dFdy fwidth noise1 noise2 noise3 noise4"),atoms:o("true false gl_FragColor gl_SecondaryColor gl_Normal gl_Vertex gl_MultiTexCoord0 gl_MultiTexCoord1 gl_MultiTexCoord2 gl_MultiTexCoord3 gl_MultiTexCoord4 gl_MultiTexCoord5 gl_MultiTexCoord6 gl_MultiTexCoord7 gl_FogCoord gl_PointCoord gl_Position gl_PointSize gl_ClipVertex gl_FrontColor gl_BackColor gl_FrontSecondaryColor gl_BackSecondaryColor gl_TexCoord gl_FogFragCoord gl_FragCoord gl_FrontFacing gl_FragData gl_FragDepth gl_ModelViewMatrix gl_ProjectionMatrix gl_ModelViewProjectionMatrix gl_TextureMatrix gl_NormalMatrix gl_ModelViewMatrixInverse gl_ProjectionMatrixInverse gl_ModelViewProjectionMatrixInverse gl_TextureMatrixTranspose gl_ModelViewMatrixInverseTranspose gl_ProjectionMatrixInverseTranspose gl_ModelViewProjectionMatrixInverseTranspose gl_TextureMatrixInverseTranspose gl_NormalScale gl_DepthRange gl_ClipPlane gl_Point gl_FrontMaterial gl_BackMaterial gl_LightSource gl_LightModel gl_FrontLightModelProduct gl_BackLightModelProduct gl_TextureColor gl_EyePlaneS gl_EyePlaneT gl_EyePlaneR gl_EyePlaneQ gl_FogParameters gl_MaxLights gl_MaxClipPlanes gl_MaxTextureUnits gl_MaxTextureCoords gl_MaxVertexAttribs gl_MaxVertexUniformComponents gl_MaxVaryingFloats gl_MaxVertexTextureImageUnits gl_MaxTextureImageUnits gl_MaxFragmentUniformComponents gl_MaxCombineTextureImageUnits gl_MaxDrawBuffers"),indentSwitch:!1,hooks:{"#":d},modeProps:{fold:["brace","include"]}}),y("text/x-nesc",{name:"clike",keywords:o(x+" as atomic async call command component components configuration event generic implementation includes interface module new norace nx_struct nx_union post provides signal task uses abstract extends"),types:s,blockKeywords:o(C),atoms:o("null true false"),hooks:{"#":d},modeProps:{fold:["brace","include"]}}),y("text/x-objectivec",{name:"clike",keywords:o(x+" "+S),types:c,builtin:o(z),blockKeywords:o(C+" @synthesize @try @catch @finally @autoreleasepool @synchronized"),defKeywords:o("struct enum union @interface @implementation @protocol @class"),dontIndentStatements:/^@.*$/,typeFirstDefinitions:!0,atoms:o("YES NO NULL Nil nil true false nullptr"),isReservedIdentifier:p,hooks:{"#":d,"*":u},modeProps:{fold:["brace","include"]}}),y("text/x-objectivec++",{name:"clike",keywords:o(x+" "+S+" "+q),types:c,builtin:o(z),blockKeywords:o(C+" @synthesize @try @catch @finally @autoreleasepool @synchronized class try catch"),defKeywords:o("struct enum union @interface @implementation @protocol @class class namespace"),dontIndentStatements:/^@.*$|^template$/,typeFirstDefinitions:!0,atoms:o("YES NO NULL Nil nil true false nullptr"),isReservedIdentifier:p,hooks:{"#":d,"*":u,u:f,U:f,L:f,R:f,0:m,1:m,2:m,3:m,4:m,5:m,6:m,7:m,8:m,9:m,token:function(e,t,r){if("variable"==r&&"("==e.peek()&&(";"==t.prevToken||null==t.prevToken||"}"==t.prevToken)&&g(e.current()))return"def"}},namespaceSeparator:"::",modeProps:{fold:["brace","include"]}}),y("text/x-squirrel",{name:"clike",keywords:o("base break clone continue const default delete enum extends function in class foreach local resume return this throw typeof yield constructor instanceof static"),types:s,blockKeywords:o("case catch class else for foreach if switch try while"),defKeywords:o("function local class"),typeFirstDefinitions:!0,atoms:o("true false null"),hooks:{"#":d},modeProps:{fold:["brace","include"]}});var j=null;y("text/x-ceylon",{name:"clike",keywords:o("abstracts alias assembly assert assign break case catch class continue dynamic else exists extends finally for function given if import in interface is let module new nonempty object of out outer package return satisfies super switch then this throw try value void while"),types:function(e){var t=e.charAt(0);return t===t.toUpperCase()&&t!==t.toLowerCase()},blockKeywords:o("case catch class dynamic else finally for function if interface module new object switch try while"),defKeywords:o("class dynamic function interface module object package value"),builtin:o("abstract actual aliased annotation by default deprecated doc final formal late license native optional sealed see serializable shared suppressWarnings tagged throws variable"),isPunctuationChar:/[\[\]{}\(\),;\:\.`]/,isOperatorChar:/[+\-*&%=<>!?|^~:\/]/,numberStart:/[\d#$]/,number:/^(?:#[\da-fA-F_]+|\$[01_]+|[\d_]+[kMGTPmunpf]?|[\d_]+\.[\d_]+(?:[eE][-+]?\d+|[kMGTPmunpf]|)|)/i,multiLineStrings:!0,typeFirstDefinitions:!0,atoms:o("true false null larger smaller equal empty finished"),indentSwitch:!1,styleDefs:!1,hooks:{"@":function(e){return e.eatWhile(/[\w\$_]/),"meta"},'"':function(e,t){return t.tokenize=w(e.match('""')?"triple":"single"),t.tokenize(e,t)},"`":function(e,t){return!(!j||!e.match("`"))&&(t.tokenize=j,j=null,t.tokenize(e,t))},"'":function(e){return e.eatWhile(/[\w\$_\xa1-\uffff]/),"atom"},token:function(e,t,r){if(("variable"==r||"type"==r)&&"."==t.prevToken)return"variable-2"}},modeProps:{fold:["brace","import"],closeBrackets:{triples:'"'}}})}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror"),require("../htmlmixed/htmlmixed"),require("../clike/clike")):"function"==typeof define&&define.amd?define("mode/php/php.js",["../../lib/codemirror","../htmlmixed/htmlmixed","../clike/clike"],e):e(CodeMirror)}(function(e){"use strict";function t(e){for(var t={},r=e.split(" "),n=0;n<r.length;++n)t[r[n]]=!0;return t}function r(e,t,a){return 0==e.length?n(t):function(i,o){for(var l=e[0],s=0;s<l.length;s++)if(i.match(l[s][0]))return o.tokenize=r(e.slice(1),t),l[s][1];return o.tokenize=n(t,a),"string"}}function n(e,t){return function(r,n){return a(r,n,e,t)}}function a(e,t,n,a){if(!1!==a&&e.match("${",!1)||e.match("{$",!1))return t.tokenize=null,"string";if(!1!==a&&e.match(/^\$[a-zA-Z_][a-zA-Z0-9_]*/))return e.match("[",!1)&&(t.tokenize=r([[["[",null]],[[/\d[\w\.]*/,"number"],[/\$[a-zA-Z_][a-zA-Z0-9_]*/,"variable-2"],[/[\w\$]+/,"variable"]],[["]",null]]],n,a)),e.match(/^->\w/,!1)&&(t.tokenize=r([[["->",null]],[[/[\w]+/,"variable"]]],n,a)),"variable-2";for(var i=!1;!e.eol()&&(i||!1===a||!e.match("{$",!1)&&!e.match(/^(\$[a-zA-Z_][a-zA-Z0-9_]*|\$\{)/,!1));){if(!i&&e.match(n)){t.tokenize=null,t.tokStack.pop(),t.tokStack.pop();break}i="\\"==e.next()&&!i}return"string"}var i="abstract and array as break case catch class clone const continue declare default do else elseif enddeclare endfor endforeach endif endswitch endwhile enum extends final for foreach function global goto if implements interface instanceof namespace new or private protected public static switch throw trait try use var while xor die echo empty exit eval include include_once isset list require require_once return print unset __halt_compiler self static parent yield insteadof finally readonly match",o="true false null TRUE FALSE NULL __CLASS__ __DIR__ __FILE__ __LINE__ __METHOD__ __FUNCTION__ __NAMESPACE__ __TRAIT__",l="func_num_args func_get_arg func_get_args strlen strcmp strncmp strcasecmp strncasecmp each error_reporting define defined trigger_error user_error set_error_handler restore_error_handler get_declared_classes get_loaded_extensions extension_loaded get_extension_funcs debug_backtrace constant bin2hex hex2bin sleep usleep time mktime gmmktime strftime gmstrftime strtotime date gmdate getdate localtime checkdate flush wordwrap htmlspecialchars htmlentities html_entity_decode md5 md5_file crc32 getimagesize image_type_to_mime_type phpinfo phpversion phpcredits strnatcmp strnatcasecmp substr_count strspn strcspn strtok strtoupper strtolower strpos strrpos strrev hebrev hebrevc nl2br basename dirname pathinfo stripslashes stripcslashes strstr stristr strrchr str_shuffle str_word_count strcoll substr substr_replace quotemeta ucfirst ucwords strtr addslashes addcslashes rtrim str_replace str_repeat count_chars chunk_split trim ltrim strip_tags similar_text explode implode setlocale localeconv parse_str str_pad chop strchr sprintf printf vprintf vsprintf sscanf fscanf parse_url urlencode urldecode rawurlencode rawurldecode readlink linkinfo link unlink exec system escapeshellcmd escapeshellarg passthru shell_exec proc_open proc_close rand srand getrandmax mt_rand mt_srand mt_getrandmax base64_decode base64_encode abs ceil floor round is_finite is_nan is_infinite bindec hexdec octdec decbin decoct dechex base_convert number_format fmod ip2long long2ip getenv putenv getopt microtime gettimeofday getrusage uniqid quoted_printable_decode set_time_limit get_cfg_var magic_quotes_runtime set_magic_quotes_runtime get_magic_quotes_gpc get_magic_quotes_runtime import_request_variables error_log serialize unserialize memory_get_usage memory_get_peak_usage var_dump var_export debug_zval_dump print_r highlight_file show_source highlight_string ini_get ini_get_all ini_set ini_alter ini_restore get_include_path set_include_path restore_include_path setcookie header headers_sent connection_aborted connection_status ignore_user_abort parse_ini_file is_uploaded_file move_uploaded_file intval floatval doubleval strval gettype settype is_null is_resource is_bool is_long is_float is_int is_integer is_double is_real is_numeric is_string is_array is_object is_scalar ereg ereg_replace eregi eregi_replace split spliti join sql_regcase dl pclose popen readfile rewind rmdir umask fclose feof fgetc fgets fgetss fread fopen fpassthru ftruncate fstat fseek ftell fflush fwrite fputs mkdir rename copy tempnam tmpfile file file_get_contents file_put_contents stream_select stream_context_create stream_context_set_params stream_context_set_option stream_context_get_options stream_filter_prepend stream_filter_append fgetcsv flock get_meta_tags stream_set_write_buffer set_file_buffer set_socket_blocking stream_set_blocking socket_set_blocking stream_get_meta_data stream_register_wrapper stream_wrapper_register stream_set_timeout socket_set_timeout socket_get_status realpath fnmatch fsockopen pfsockopen pack unpack get_browser crypt opendir closedir chdir getcwd rewinddir readdir dir glob fileatime filectime filegroup fileinode filemtime fileowner fileperms filesize filetype file_exists is_writable is_writeable is_readable is_executable is_file is_dir is_link stat lstat chown touch clearstatcache mail ob_start ob_flush ob_clean ob_end_flush ob_end_clean ob_get_flush ob_get_clean ob_get_length ob_get_level ob_get_status ob_get_contents ob_implicit_flush ob_list_handlers ksort krsort natsort natcasesort asort arsort sort rsort usort uasort uksort shuffle array_walk count end prev next reset current key min max in_array array_search extract compact array_fill range array_multisort array_push array_pop array_shift array_unshift array_splice array_slice array_merge array_merge_recursive array_keys array_values array_count_values array_reverse array_reduce array_pad array_flip array_change_key_case array_rand array_unique array_intersect array_intersect_assoc array_diff array_diff_assoc array_sum array_filter array_map array_chunk array_key_exists array_intersect_key array_combine array_column pos sizeof key_exists assert assert_options version_compare ftok str_rot13 aggregate session_name session_module_name session_save_path session_id session_regenerate_id session_decode session_register session_unregister session_is_registered session_encode session_start session_destroy session_unset session_set_save_handler session_cache_limiter session_cache_expire session_set_cookie_params session_get_cookie_params session_write_close preg_match preg_match_all preg_replace preg_replace_callback preg_split preg_quote preg_grep overload ctype_alnum ctype_alpha ctype_cntrl ctype_digit ctype_lower ctype_graph ctype_print ctype_punct ctype_space ctype_upper ctype_xdigit virtual apache_request_headers apache_note apache_lookup_uri apache_child_terminate apache_setenv apache_response_headers apache_get_version getallheaders mysql_connect mysql_pconnect mysql_close mysql_select_db mysql_create_db mysql_drop_db mysql_query mysql_unbuffered_query mysql_db_query mysql_list_dbs mysql_list_tables mysql_list_fields mysql_list_processes mysql_error mysql_errno mysql_affected_rows mysql_insert_id mysql_result mysql_num_rows mysql_num_fields mysql_fetch_row mysql_fetch_array mysql_fetch_assoc mysql_fetch_object mysql_data_seek mysql_fetch_lengths mysql_fetch_field mysql_field_seek mysql_free_result mysql_field_name mysql_field_table mysql_field_len mysql_field_type mysql_field_flags mysql_escape_string mysql_real_escape_string mysql_stat mysql_thread_id mysql_client_encoding mysql_get_client_info mysql_get_host_info mysql_get_proto_info mysql_get_server_info mysql_info mysql mysql_fieldname mysql_fieldtable mysql_fieldlen mysql_fieldtype mysql_fieldflags mysql_selectdb mysql_createdb mysql_dropdb mysql_freeresult mysql_numfields mysql_numrows mysql_listdbs mysql_listtables mysql_listfields mysql_db_name mysql_dbname mysql_tablename mysql_table_name pg_connect pg_pconnect pg_close pg_connection_status pg_connection_busy pg_connection_reset pg_host pg_dbname pg_port pg_tty pg_options pg_ping pg_query pg_send_query pg_cancel_query pg_fetch_result pg_fetch_row pg_fetch_assoc pg_fetch_array pg_fetch_object pg_fetch_all pg_affected_rows pg_get_result pg_result_seek pg_result_status pg_free_result pg_last_oid pg_num_rows pg_num_fields pg_field_name pg_field_num pg_field_size pg_field_type pg_field_prtlen pg_field_is_null pg_get_notify pg_get_pid pg_result_error pg_last_error pg_last_notice pg_put_line pg_end_copy pg_copy_to pg_copy_from pg_trace pg_untrace pg_lo_create pg_lo_unlink pg_lo_open pg_lo_close pg_lo_read pg_lo_write pg_lo_read_all pg_lo_import pg_lo_export pg_lo_seek pg_lo_tell pg_escape_string pg_escape_bytea pg_unescape_bytea pg_client_encoding pg_set_client_encoding pg_meta_data pg_convert pg_insert pg_update pg_delete pg_select pg_exec pg_getlastoid pg_cmdtuples pg_errormessage pg_numrows pg_numfields pg_fieldname pg_fieldsize pg_fieldtype pg_fieldnum pg_fieldprtlen pg_fieldisnull pg_freeresult pg_result pg_loreadall pg_locreate pg_lounlink pg_loopen pg_loclose pg_loread pg_lowrite pg_loimport pg_loexport http_response_code get_declared_traits getimagesizefromstring socket_import_stream stream_set_chunk_size trait_exists header_register_callback class_uses session_status session_register_shutdown echo print global static exit array empty eval isset unset die include require include_once require_once json_decode json_encode json_last_error json_last_error_msg curl_close curl_copy_handle curl_errno curl_error curl_escape curl_exec curl_file_create curl_getinfo curl_init curl_multi_add_handle curl_multi_close curl_multi_exec curl_multi_getcontent curl_multi_info_read curl_multi_init curl_multi_remove_handle curl_multi_select curl_multi_setopt curl_multi_strerror curl_pause curl_reset curl_setopt_array curl_setopt curl_share_close curl_share_init curl_share_setopt curl_strerror curl_unescape curl_version mysqli_affected_rows mysqli_autocommit mysqli_change_user mysqli_character_set_name mysqli_close mysqli_commit mysqli_connect_errno mysqli_connect_error mysqli_connect mysqli_data_seek mysqli_debug mysqli_dump_debug_info mysqli_errno mysqli_error_list mysqli_error mysqli_fetch_all mysqli_fetch_array mysqli_fetch_assoc mysqli_fetch_field_direct mysqli_fetch_field mysqli_fetch_fields mysqli_fetch_lengths mysqli_fetch_object mysqli_fetch_row mysqli_field_count mysqli_field_seek mysqli_field_tell mysqli_free_result mysqli_get_charset mysqli_get_client_info mysqli_get_client_stats mysqli_get_client_version mysqli_get_connection_stats mysqli_get_host_info mysqli_get_proto_info mysqli_get_server_info mysqli_get_server_version mysqli_info mysqli_init mysqli_insert_id mysqli_kill mysqli_more_results mysqli_multi_query mysqli_next_result mysqli_num_fields mysqli_num_rows mysqli_options mysqli_ping mysqli_prepare mysqli_query mysqli_real_connect mysqli_real_escape_string mysqli_real_query mysqli_reap_async_query mysqli_refresh mysqli_rollback mysqli_select_db mysqli_set_charset mysqli_set_local_infile_default mysqli_set_local_infile_handler mysqli_sqlstate mysqli_ssl_set mysqli_stat mysqli_stmt_init mysqli_store_result mysqli_thread_id mysqli_thread_safe mysqli_use_result mysqli_warning_count";e.registerHelper("hintWords","php",[i,o,l].join(" ").split(" ")),e.registerHelper("wordChars","php",/[\w$]/);var s={name:"clike",helperType:"php",keywords:t(i),blockKeywords:t("catch do else elseif for foreach if switch try while finally"),defKeywords:t("class enum function interface namespace trait"),atoms:t(o),builtin:t(l),multiLineStrings:!0,hooks:{$:function(e){return e.eatWhile(/[\w\$_]/),"variable-2"},"<":function(e,t){var r;if(r=e.match(/^<<\s*/)){var a=e.eat(/['"]/);e.eatWhile(/[\w\.]/);var i=e.current().slice(r[0].length+(a?2:1));if(a&&e.eat(a),i)return(t.tokStack||(t.tokStack=[])).push(i,0),t.tokenize=n(i,"'"!=a),"string"}return!1},"#":function(e){for(;!e.eol()&&!e.match("?>",!1);)e.next();return"comment"},"/":function(e){if(e.eat("/")){for(;!e.eol()&&!e.match("?>",!1);)e.next();return"comment"}return!1},'"':function(e,t){return(t.tokStack||(t.tokStack=[])).push('"',0),t.tokenize=n('"'),"string"},"{":function(e,t){return t.tokStack&&t.tokStack.length&&t.tokStack[t.tokStack.length-1]++,!1},"}":function(e,t){return t.tokStack&&t.tokStack.length>0&&!--t.tokStack[t.tokStack.length-1]&&(t.tokenize=n(t.tokStack[t.tokStack.length-2])),!1}}};e.defineMode("php",function(t,r){function n(t,r){var n=r.curMode==i;if(t.sol()&&r.pending&&'"'!=r.pending&&"'"!=r.pending&&(r.pending=null),n)return n&&null==r.php.tokenize&&t.match("?>")?(r.curMode=a,r.curState=r.html,r.php.context.prev||(r.php=null),"meta"):i.token(t,r.curState);if(t.match(/^<\?\w*/))return r.curMode=i,r.php||(r.php=e.startState(i,a.indent(r.html,"",""))),r.curState=r.php,"meta";if('"'==r.pending||"'"==r.pending){for(;!t.eol()&&t.next()!=r.pending;);var o="string"}else if(r.pending&&t.pos<r.pending.end){t.pos=r.pending.end;var o=r.pending.style}else var o=a.token(t,r.curState);r.pending&&(r.pending=null);var l,s=t.current(),c=s.search(/<\?/);return-1!=c&&("string"==o&&(l=s.match(/[\'\"]$/))&&!/\?>/.test(s)?r.pending=l[0]:r.pending={end:t.pos,style:o},t.backUp(s.length-c)),o}var a=e.getMode(t,r&&r.htmlMode||"text/html"),i=e.getMode(t,s);return{startState:function(){var t=e.startState(a),n=r.startOpen?e.startState(i):null;return{html:t,php:n,curMode:r.startOpen?i:a,curState:r.startOpen?n:t,pending:null}},copyState:function(t){var r,n=t.html,o=e.copyState(a,n),l=t.php,s=l&&e.copyState(i,l);return r=t.curMode==a?o:s,{html:o,php:s,curMode:t.curMode,curState:r,pending:t.pending}},token:n,indent:function(e,t,r){return e.curMode!=i&&/^\s*<\//.test(t)||e.curMode==i&&/^\?>/.test(t)?a.indent(e.html,t,r):e.curMode.indent(e.curState,t,r)},blockCommentStart:"/*",blockCommentEnd:"*/",lineComment:"//",innerMode:function(e){return{state:e.curState,mode:e.curMode}}}},"htmlmixed","clike"),e.defineMIME("application/x-httpd-php","php"),e.defineMIME("application/x-httpd-php-open",{name:"php",startOpen:!0}),e.defineMIME("text/x-php",s)}),function(e){"function"==typeof e.define&&e.define("modePHP",["mode/php/php.js"],function(){})}(this);