!function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define("addon/mode/multiplex.js",["../../lib/codemirror"],e):e(CodeMirror)}(function(e){"use strict";e.multiplexingMode=function(t){function n(e,t,n,r){if("string"==typeof t){var i=e.indexOf(t,n);return r&&i>-1?i+t.length:i}var o=t.exec(n?e.slice(n):e);return o?o.index+n+(r?o[0].length:0):-1}var r=Array.prototype.slice.call(arguments,1);return{startState:function(){return{outer:e.startState(t),innerActive:null,inner:null,startingInner:!1}},copyState:function(n){return{outer:e.copyState(t,n.outer),innerActive:n.innerActive,inner:n.innerActive&&e.copyState(n.innerActive.mode,n.inner),startingInner:n.startingInner}},token:function(i,o){if(o.innerActive){var a=o.innerActive,l=i.string;if(!a.close&&i.sol())return o.innerActive=o.inner=null,this.token(i,o);var u=a.close&&!o.startingInner?n(l,a.close,i.pos,a.parseDelimiters):-1;if(u==i.pos&&!a.parseDelimiters)return i.match(a.close),o.innerActive=o.inner=null,a.delimStyle&&a.delimStyle+" "+a.delimStyle+"-close";u>-1&&(i.string=l.slice(0,u));var s=a.mode.token(i,o.inner);return u>-1?i.string=l:i.pos>i.start&&(o.startingInner=!1),u==i.pos&&a.parseDelimiters&&(o.innerActive=o.inner=null),a.innerStyle&&(s=s?s+" "+a.innerStyle:a.innerStyle),s}for(var c=1/0,l=i.string,d=0;d<r.length;++d){var f=r[d],u=n(l,f.open,i.pos);if(u==i.pos){f.parseDelimiters||i.match(f.open),o.startingInner=!!f.parseDelimiters,o.innerActive=f;var m=0;if(t.indent){var g=t.indent(o.outer,"","");g!==e.Pass&&(m=g)}return o.inner=e.startState(f.mode,m),f.delimStyle&&f.delimStyle+" "+f.delimStyle+"-open"}-1!=u&&u<c&&(c=u)}c!=1/0&&(i.string=l.slice(0,c));var p=t.token(i,o.outer);return c!=1/0&&(i.string=l),p},indent:function(n,r,i){var o=n.innerActive?n.innerActive.mode:t;return o.indent?o.indent(n.innerActive?n.inner:n.outer,r,i):e.Pass},blankLine:function(n){var i=n.innerActive?n.innerActive.mode:t;if(i.blankLine&&i.blankLine(n.innerActive?n.inner:n.outer),n.innerActive)"\n"===n.innerActive.close&&(n.innerActive=n.inner=null);else for(var o=0;o<r.length;++o){var a=r[o];"\n"===a.open&&(n.innerActive=a,n.inner=e.startState(a.mode,i.indent?i.indent(n.outer,"",""):0))}},electricChars:t.electricChars,innerMode:function(e){return e.inner?{state:e.inner,mode:e.innerActive.mode}:{state:e.outer,mode:t}}}}}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror"),require("../../addon/mode/multiplex")):"function"==typeof define&&define.amd?define("mode/twig/twig.js",["../../lib/codemirror","../../addon/mode/multiplex"],e):e(CodeMirror)}(function(e){"use strict";e.defineMode("twig:inner",function(){function e(e,a){var l=e.peek();if(a.incomment)return e.skipTo("#}")?(e.eatWhile(/\#|}/),a.incomment=!1):e.skipToEnd(),"comment";if(a.intag){if(a.operator){if(a.operator=!1,e.match(i))return"atom";if(e.match(o))return"number"}if(a.sign){if(a.sign=!1,e.match(i))return"atom";if(e.match(o))return"number"}if(a.instring)return l==a.instring&&(a.instring=!1),e.next(),"string";if("'"==l||'"'==l)return a.instring=l,e.next(),"string";if(e.match(a.intag+"}")||e.eat("-")&&e.match(a.intag+"}"))return a.intag=!1,"tag";if(e.match(n))return a.operator=!0,"operator";if(e.match(r))a.sign=!0;else if(e.eat(" ")||e.sol()){if(e.match(t))return"keyword";if(e.match(i))return"atom";if(e.match(o))return"number";e.sol()&&e.next()}else e.next();return"variable"}if(e.eat("{")){if(e.eat("#"))return a.incomment=!0,e.skipTo("#}")?(e.eatWhile(/\#|}/),a.incomment=!1):e.skipToEnd(),"comment";if(l=e.eat(/\{|%/))return a.intag=l,"{"==l&&(a.intag="}"),e.eat("-"),"tag"}e.next()}var t=["and","as","autoescape","endautoescape","block","do","endblock","else","elseif","extends","for","endfor","embed","endembed","filter","endfilter","flush","from","if","endif","in","is","include","import","not","or","set","spaceless","endspaceless","with","endwith","trans","endtrans","blocktrans","endblocktrans","macro","endmacro","use","verbatim","endverbatim"],n=/^[+\-*&%=<>!?|~^]/,r=/^[:\[\(\{]/,i=["true","false","null","empty","defined","divisibleby","divisible by","even","odd","iterable","sameas","same as"],o=/^(\d[+\-\*\/])?\d+(\.\d+)?/;return t=new RegExp("(("+t.join(")|(")+"))\\b"),i=new RegExp("(("+i.join(")|(")+"))\\b"),{startState:function(){return{}},token:function(t,n){return e(t,n)}}}),e.defineMode("twig",function(t,n){var r=e.getMode(t,"twig:inner");return n&&n.base?e.multiplexingMode(e.getMode(t,n.base),{open:/\{[{#%]/,close:/[}#%]\}/,mode:r,parseDelimiters:!0}):r}),e.defineMIME("text/x-twig","twig")}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define("mode/xml/xml.js",["../../lib/codemirror"],e):e(CodeMirror)}(function(e){"use strict";var t={autoSelfClosers:{area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0,menuitem:!0},implicitlyClosed:{dd:!0,li:!0,optgroup:!0,option:!0,p:!0,rp:!0,rt:!0,tbody:!0,td:!0,tfoot:!0,th:!0,tr:!0},contextGrabbers:{dd:{dd:!0,dt:!0},dt:{dd:!0,dt:!0},li:{li:!0},option:{option:!0,optgroup:!0},optgroup:{optgroup:!0},p:{address:!0,article:!0,aside:!0,blockquote:!0,dir:!0,div:!0,dl:!0,fieldset:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,menu:!0,nav:!0,ol:!0,p:!0,pre:!0,section:!0,table:!0,ul:!0},rp:{rp:!0,rt:!0},rt:{rp:!0,rt:!0},tbody:{tbody:!0,tfoot:!0},td:{td:!0,th:!0},tfoot:{tbody:!0},th:{td:!0,th:!0},thead:{tbody:!0,tfoot:!0},tr:{tr:!0}},doNotIndent:{pre:!0},allowUnquoted:!0,allowMissing:!0,caseFold:!0},n={autoSelfClosers:{},implicitlyClosed:{},contextGrabbers:{},doNotIndent:{},allowUnquoted:!1,allowMissing:!1,allowMissingTagName:!1,caseFold:!1};e.defineMode("xml",function(r,i){function o(e,t){function n(n){return t.tokenize=n,n(e,t)}var r=e.next();if("<"==r)return e.eat("!")?e.eat("[")?e.match("CDATA[")?n(u("atom","]]>")):null:e.match("--")?n(u("comment","--\x3e")):e.match("DOCTYPE",!0,!0)?(e.eatWhile(/[\w\._\-]/),n(s(1))):null:e.eat("?")?(e.eatWhile(/[\w\._\-]/),t.tokenize=u("meta","?>"),"meta"):(C=e.eat("/")?"closeTag":"openTag",t.tokenize=a,"tag bracket");if("&"==r){var i;return i=e.eat("#")?e.eat("x")?e.eatWhile(/[a-fA-F\d]/)&&e.eat(";"):e.eatWhile(/[\d]/)&&e.eat(";"):e.eatWhile(/[\w\.\-:]/)&&e.eat(";"),i?"atom":"error"}return e.eatWhile(/[^&<]/),null}function a(e,t){var n=e.next();if(">"==n||"/"==n&&e.eat(">"))return t.tokenize=o,C=">"==n?"endTag":"selfcloseTag","tag bracket";if("="==n)return C="equals",null;if("<"==n){t.tokenize=o,t.state=g,t.tagName=t.tagStart=null;var r=t.tokenize(e,t);return r?r+" tag error":"tag error"}return/[\'\"]/.test(n)?(t.tokenize=l(n),t.stringStartCol=e.column(),t.tokenize(e,t)):(e.match(/^[^\s\u00a0=<>\"\']*[^\s\u00a0=<>\"\'\/]/),"word")}function l(e){var t=function(t,n){for(;!t.eol();)if(t.next()==e){n.tokenize=a;break}return"string"};return t.isInAttribute=!0,t}function u(e,t){return function(n,r){for(;!n.eol();){if(n.match(t)){r.tokenize=o;break}n.next()}return e}}function s(e){return function(t,n){for(var r;null!=(r=t.next());){if("<"==r)return n.tokenize=s(e+1),n.tokenize(t,n);if(">"==r){if(1==e){n.tokenize=o;break}return n.tokenize=s(e-1),n.tokenize(t,n)}}return"meta"}}function c(e){return e&&e.toLowerCase()}function d(e,t,n){this.prev=e.context,this.tagName=t||"",this.indent=e.indented,this.startOfLine=n,(S.doNotIndent.hasOwnProperty(t)||e.context&&e.context.noIndent)&&(this.noIndent=!0)}function f(e){e.context&&(e.context=e.context.prev)}function m(e,t){for(var n;;){if(!e.context)return;if(n=e.context.tagName,!S.contextGrabbers.hasOwnProperty(c(n))||!S.contextGrabbers[c(n)].hasOwnProperty(c(t)))return;f(e)}}function g(e,t,n){return"openTag"==e?(n.tagStart=t.column(),p):"closeTag"==e?h:g}function p(e,t,n){return"word"==e?(n.tagName=t.current(),N="tag",x):S.allowMissingTagName&&"endTag"==e?(N="tag bracket",x(e,t,n)):(N="error",p)}function h(e,t,n){if("word"==e){var r=t.current();return n.context&&n.context.tagName!=r&&S.implicitlyClosed.hasOwnProperty(c(n.context.tagName))&&f(n),n.context&&n.context.tagName==r||!1===S.matchClosing?(N="tag",v):(N="tag error",b)}return S.allowMissingTagName&&"endTag"==e?(N="tag bracket",v(e,t,n)):(N="error",b)}function v(e,t,n){return"endTag"!=e?(N="error",v):(f(n),g)}function b(e,t,n){return N="error",v(e,t,n)}function x(e,t,n){if("word"==e)return N="attribute",k;if("endTag"==e||"selfcloseTag"==e){var r=n.tagName,i=n.tagStart;return n.tagName=n.tagStart=null,"selfcloseTag"==e||S.autoSelfClosers.hasOwnProperty(c(r))?m(n,r):(m(n,r),n.context=new d(n,r,i==n.indented)),g}return N="error",x}function k(e,t,n){return"equals"==e?y:(S.allowMissing||(N="error"),x(e,t,n))}function y(e,t,n){return"string"==e?w:"word"==e&&S.allowUnquoted?(N="string",x):(N="error",x(e,t,n))}function w(e,t,n){return"string"==e?w:x(e,t,n)}var A=r.indentUnit,S={},T=i.htmlMode?t:n;for(var M in T)S[M]=T[M];for(var M in i)S[M]=i[M];var C,N;return o.isInText=!0,{startState:function(e){var t={tokenize:o,state:g,indented:e||0,tagName:null,tagStart:null,context:null};return null!=e&&(t.baseIndent=e),t},token:function(e,t){if(!t.tagName&&e.sol()&&(t.indented=e.indentation()),e.eatSpace())return null;C=null;var n=t.tokenize(e,t);return(n||C)&&"comment"!=n&&(N=null,t.state=t.state(C||n,e,t),N&&(n="error"==N?n+" error":N)),n},indent:function(t,n,r){var i=t.context;if(t.tokenize.isInAttribute)return t.tagStart==t.indented?t.stringStartCol+1:t.indented+A;if(i&&i.noIndent)return e.Pass;if(t.tokenize!=a&&t.tokenize!=o)return r?r.match(/^(\s*)/)[0].length:0;if(t.tagName)return!1!==S.multilineTagIndentPastTag?t.tagStart+t.tagName.length+2:t.tagStart+A*(S.multilineTagIndentFactor||1);if(S.alignCDATA&&/<!\[CDATA\[/.test(n))return 0;var l=n&&/^<(\/)?([\w_:\.-]*)/.exec(n);if(l&&l[1])for(;i;){if(i.tagName==l[2]){i=i.prev;break}if(!S.implicitlyClosed.hasOwnProperty(c(i.tagName)))break;i=i.prev}else if(l)for(;i;){var u=S.contextGrabbers[c(i.tagName)];if(!u||!u.hasOwnProperty(c(l[2])))break;i=i.prev}for(;i&&i.prev&&!i.startOfLine;)i=i.prev;return i?i.indent+A:t.baseIndent||0},electricInput:/<\/[\s\w:]+>$/,blockCommentStart:"\x3c!--",blockCommentEnd:"--\x3e",configuration:S.htmlMode?"html":"xml",helperType:S.htmlMode?"html":"xml",skipAttribute:function(e){e.state==y&&(e.state=x)},xmlCurrentTag:function(e){return e.tagName?{name:e.tagName,close:"closeTag"==e.type}:null},xmlCurrentContext:function(e){for(var t=[],n=e.context;n;n=n.prev)t.push(n.tagName);return t.reverse()}}}),e.defineMIME("text/xml","xml"),e.defineMIME("application/xml","xml"),e.mimeModes.hasOwnProperty("text/html")||e.defineMIME("text/html",{name:"xml",htmlMode:!0})}),function(e){"function"==typeof e.define&&e.define("modeTwig",["mode/twig/twig.js"],function(){})}(this);