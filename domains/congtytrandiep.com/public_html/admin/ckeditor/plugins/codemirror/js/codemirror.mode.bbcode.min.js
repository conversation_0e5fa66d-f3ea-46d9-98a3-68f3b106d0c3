CodeMirror.defineMode("bbcode",function(e){var i,t={bbCodeTags:"b i u s img quote code list table  tr td size color url",bbCodeUnaryTags:"* :-) hr cut"};e.hasOwnProperty("bbCodeTags")&&(t.bbCodeTags=e.bbCodeTags),e.hasOwnProperty("bbCodeUnaryTags")&&(t.bbCodeUnaryTags=e.bbCodeUnaryTags);var o=function(e,t){return i=t,e},e=function(e){return e.replace(/([\:\-\)\(\*\+\?\[\]])/g,"\\$1")},s={validIdentifier:/[a-zA-Z0-9_]/,stringChar:/['"]/,tags:new RegExp("(?:"+e(t.bbCodeTags).split(" ").join("|")+")"),unaryTags:new RegExp("(?:"+e(t.bbCodeUnaryTags).split(" ").join("|")+")")},u={tokenizer:function(e,t){return e.eatSpace()?null:e.match("[",!0)?(t.tokenize=u.bbcode,o("tag","startTag")):(e.next(),null)},inAttribute:function(a){return function(e,t){for(var r,n=null;!e.eol();){if(r=e.peek(),e.next()==a&&"\\"!==n){t.tokenize=u.bbcode;break}n=r}return"string"}},bbcode:function(e,t){if(e.match("]",!0))return t.tokenize=u.tokenizer,o("tag",null);if(e.match("[",!0))return o("tag","startTag");var r=e.next();if(s.stringChar.test(r))return t.tokenize=u.inAttribute(r),o("string","string");if(/\d/.test(r))return e.eatWhile(/\d/),o("number","number");if("whitespace"==t.last)return e.eatWhile(s.validIdentifier),o("attribute","modifier");if("property"==t.last)return e.eatWhile(s.validIdentifier),o("property",null);if(/\s/.test(r))return i="whitespace",null;var n="";"/"!=r&&(n+=r);for(var a;a=e.eat(s.validIdentifier);)n+=a;return s.unaryTags.test(n)?o("atom","atom"):s.tags.test(n)?o("keyword","keyword"):/\s/.test(r)?null:o("tag","tag")}};return{startState:function(){return{tokenize:u.tokenizer,mode:"bbcode",last:null}},token:function(e,t){e=t.tokenize(e,t);return t.last=i,e},electricChars:""}}),CodeMirror.defineMIME("text/x-bbcode","bbcode");