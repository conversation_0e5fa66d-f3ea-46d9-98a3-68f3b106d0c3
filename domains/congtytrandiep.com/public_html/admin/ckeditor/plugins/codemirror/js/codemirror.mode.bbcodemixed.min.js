!function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define(["../../lib/codemirror"],e):e(CodeMirror)}(function(M){"use strict";var S={autoSelfClosers:{area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0,menuitem:!0},implicitlyClosed:{dd:!0,li:!0,optgroup:!0,option:!0,p:!0,rp:!0,rt:!0,tbody:!0,td:!0,tfoot:!0,th:!0,tr:!0},contextGrabbers:{dd:{dd:!0,dt:!0},dt:{dd:!0,dt:!0},li:{li:!0},option:{option:!0,optgroup:!0},optgroup:{optgroup:!0},p:{address:!0,article:!0,aside:!0,blockquote:!0,dir:!0,div:!0,dl:!0,fieldset:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,menu:!0,nav:!0,ol:!0,p:!0,pre:!0,section:!0,table:!0,ul:!0},rp:{rp:!0,rt:!0},rt:{rp:!0,rt:!0},tbody:{tbody:!0,tfoot:!0},td:{td:!0,th:!0},tfoot:{tbody:!0},th:{td:!0,th:!0},thead:{tbody:!0,tfoot:!0},tr:{tr:!0}},doNotIndent:{pre:!0},allowUnquoted:!0,allowMissing:!0,caseFold:!0},j={autoSelfClosers:{},implicitlyClosed:{},contextGrabbers:{},doNotIndent:{},allowUnquoted:!1,allowMissing:!1,allowMissingTagName:!1,caseFold:!1};M.defineMode("xml",function(e,t){var r,o,i,l=e.indentUnit,s={},n=t.htmlMode?S:j;for(r in n)s[r]=n[r];for(r in t)s[r]=t[r];function c(t,r){function e(e){return(r.tokenize=e)(t,r)}var n=t.next();if("<"==n)return t.eat("!")?t.eat("[")?t.match("CDATA[")?e(a("atom","]]>")):null:t.match("--")?e(a("comment","--\x3e")):t.match("DOCTYPE",!0,!0)?(t.eatWhile(/[\w\._\-]/),e(function n(a){return function(e,t){for(var r;null!=(r=e.next());){if("<"==r)return t.tokenize=n(a+1),t.tokenize(e,t);if(">"==r){if(1!=a)return t.tokenize=n(a-1),t.tokenize(e,t);t.tokenize=c;break}}return"meta"}}(1))):null:t.eat("?")?(t.eatWhile(/[\w\._\-]/),r.tokenize=a("meta","?>"),"meta"):(o=t.eat("/")?"closeTag":"openTag",r.tokenize=d,"tag bracket");if("&"!=n)return t.eatWhile(/[^&<]/),null;n=t.eat("#")?t.eat("x")?t.eatWhile(/[a-fA-F\d]/)&&t.eat(";"):t.eatWhile(/[\d]/)&&t.eat(";"):t.eatWhile(/[\w\.\-:]/)&&t.eat(";");return n?"atom":"error"}function d(e,t){var r=e.next();if(">"==r||"/"==r&&e.eat(">"))return t.tokenize=c,o=">"==r?"endTag":"selfcloseTag","tag bracket";if("="==r)return o="equals",null;if("<"!=r)return/[\'\"]/.test(r)?(t.tokenize=(n=r,a.isInAttribute=!0,a),t.stringStartCol=e.column(),t.tokenize(e,t)):(e.match(/^[^\s\u00a0=<>\"\']*[^\s\u00a0=<>\"\'\/]/),"word");t.tokenize=c,t.state=g,t.tagName=t.tagStart=null;var n,t=t.tokenize(e,t);return t?t+" tag error":"tag error";function a(e,t){for(;!e.eol();)if(e.next()==n){t.tokenize=d;break}return"string"}}function a(r,n){return function(e,t){for(;!e.eol();){if(e.match(n)){t.tokenize=c;break}e.next()}return r}}function u(e){return e&&e.toLowerCase()}function p(e,t,r){this.prev=e.context,this.tagName=t||"",this.indent=e.indented,this.startOfLine=r,(s.doNotIndent.hasOwnProperty(t)||e.context&&e.context.noIndent)&&(this.noIndent=!0)}function m(e){e.context&&(e.context=e.context.prev)}function f(e,t){for(var r;;){if(!e.context)return;if(r=e.context.tagName,!s.contextGrabbers.hasOwnProperty(u(r))||!s.contextGrabbers[u(r)].hasOwnProperty(u(t)))return;m(e)}}function g(e,t,r){return"openTag"==e?(r.tagStart=t.column(),h):"closeTag"==e?b:g}function h(e,t,r){return"word"==e?(r.tagName=t.current(),i="tag",w):s.allowMissingTagName&&"endTag"==e?(i="tag bracket",w(e,0,r)):(i="error",h)}function b(e,t,r){if("word"!=e)return s.allowMissingTagName&&"endTag"==e?(i="tag bracket",k(e,0,r)):(i="error",y);t=t.current();return r.context&&r.context.tagName!=t&&s.implicitlyClosed.hasOwnProperty(u(r.context.tagName))&&m(r),r.context&&r.context.tagName==t||!1===s.matchClosing?(i="tag",k):(i="tag error",y)}function k(e,t,r){return"endTag"!=e?(i="error",k):(m(r),g)}function y(e,t,r){return i="error",k(e,0,r)}function w(e,t,r){if("word"==e)return i="attribute",v;if("endTag"!=e&&"selfcloseTag"!=e)return i="error",w;var n=r.tagName,a=r.tagStart;return r.tagName=r.tagStart=null,"selfcloseTag"==e||s.autoSelfClosers.hasOwnProperty(u(n))?f(r,n):(f(r,n),r.context=new p(r,n,a==r.indented)),g}function v(e,t,r){return"equals"==e?x:(s.allowMissing||(i="error"),w(e,0,r))}function x(e,t,r){return"string"==e?z:"word"==e&&s.allowUnquoted?(i="string",w):(i="error",w(e,0,r))}function z(e,t,r){return"string"==e?z:w(e,0,r)}return c.isInText=!0,{startState:function(e){var t={tokenize:c,state:g,indented:e||0,tagName:null,tagStart:null,context:null};return null!=e&&(t.baseIndent=e),t},token:function(e,t){if(!t.tagName&&e.sol()&&(t.indented=e.indentation()),e.eatSpace())return null;o=null;var r=t.tokenize(e,t);return(r||o)&&"comment"!=r&&(i=null,t.state=t.state(o||r,e,t),i&&(r="error"==i?r+" error":i)),r},indent:function(e,t,r){var n=e.context;if(e.tokenize.isInAttribute)return e.tagStart==e.indented?e.stringStartCol+1:e.indented+l;if(n&&n.noIndent)return M.Pass;if(e.tokenize!=d&&e.tokenize!=c)return r?r.match(/^(\s*)/)[0].length:0;if(e.tagName)return!1!==s.multilineTagIndentPastTag?e.tagStart+e.tagName.length+2:e.tagStart+l*(s.multilineTagIndentFactor||1);if(s.alignCDATA&&/<!\[CDATA\[/.test(t))return 0;var a=t&&/^<(\/)?([\w_:\.-]*)/.exec(t);if(a&&a[1])for(;n;){if(n.tagName==a[2]){n=n.prev;break}if(!s.implicitlyClosed.hasOwnProperty(u(n.tagName)))break;n=n.prev}else if(a)for(;n;){var o=s.contextGrabbers[u(n.tagName)];if(!o||!o.hasOwnProperty(u(a[2])))break;n=n.prev}for(;n&&n.prev&&!n.startOfLine;)n=n.prev;return n?n.indent+l:e.baseIndent||0},electricInput:/<\/[\s\w:]+>$/,blockCommentStart:"\x3c!--",blockCommentEnd:"--\x3e",configuration:s.htmlMode?"html":"xml",helperType:s.htmlMode?"html":"xml",skipAttribute:function(e){e.state==x&&(e.state=w)},xmlCurrentTag:function(e){return e.tagName?{name:e.tagName,close:"closeTag"==e.type}:null},xmlCurrentContext:function(e){for(var t=[],r=e.context;r;r=r.prev)t.push(r.tagName);return t.reverse()}}}),M.defineMIME("text/xml","xml"),M.defineMIME("application/xml","xml"),M.mimeModes.hasOwnProperty("text/html")||M.defineMIME("text/html",{name:"xml",htmlMode:!0})}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define(["../../lib/codemirror"],e):e(CodeMirror)}(function(rt){"use strict";rt.defineMode("javascript",function(e,d){var t,r,n,a,u=e.indentUnit,p=d.statementIndent,o=d.jsonld,i=d.json||o,l=!1!==d.trackScope,c=d.typescript,m=d.wordCharacters||/[\w$\xa1-\uffff]/,s=(t=f("keyword a"),r=f("keyword b"),n=f("keyword c"),a=f("keyword d"),e=f("operator"),{if:f("if"),while:t,with:t,else:r,do:r,try:r,finally:r,return:a,break:a,continue:a,new:f("new"),delete:n,void:n,throw:n,debugger:f("debugger"),var:f("var"),const:f("var"),let:f("var"),function:f("function"),catch:f("catch"),for:f("for"),switch:f("switch"),case:f("case"),default:f("default"),in:e,typeof:e,instanceof:e,true:e={type:"atom",style:"atom"},false:e,null:e,undefined:e,NaN:e,Infinity:e,this:f("this"),class:f("class"),super:f("atom"),yield:n,export:f("export"),import:f("import"),extends:n,await:n});function f(e){return{type:e,style:"keyword"}}var g,h,b=/[+\-*&%=<>!?|~^@]/,k=/^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)"/;function y(e,t,r){return g=e,h=r,t}function w(e,t){var a,r=e.next();if('"'==r||"'"==r)return t.tokenize=(a=r,function(e,t){var r,n=!1;if(o&&"@"==e.peek()&&e.match(k))return t.tokenize=w,y("jsonld-keyword","meta");for(;null!=(r=e.next())&&(r!=a||n);)n=!n&&"\\"==r;return n||(t.tokenize=w),y("string","string")}),t.tokenize(e,t);if("."==r&&e.match(/^\d[\d_]*(?:[eE][+\-]?[\d_]+)?/))return y("number","number");if("."==r&&e.match(".."))return y("spread","meta");if(/[\[\]{}\(\),;\:\.]/.test(r))return y(r);if("="==r&&e.eat(">"))return y("=>","operator");if("0"==r&&e.match(/^(?:x[\dA-Fa-f_]+|o[0-7_]+|b[01_]+)n?/))return y("number","number");if(/\d/.test(r))return e.match(/^[\d_]*(?:n|(?:\.[\d_]*)?(?:[eE][+\-]?[\d_]+)?)?/),y("number","number");if("/"==r)return e.eat("*")?(t.tokenize=v)(e,t):e.eat("/")?(e.skipToEnd(),y("comment","comment")):tt(e,t,1)?(function(e){for(var t,r=!1,n=!1;null!=(t=e.next());){if(!r){if("/"==t&&!n)return;"["==t?n=!0:n&&"]"==t&&(n=!1)}r=!r&&"\\"==t}}(e),e.match(/^\b(([gimyus])(?![gimyus]*\2))+\b/),y("regexp","string-2")):(e.eat("="),y("operator","operator",e.current()));if("`"==r)return(t.tokenize=x)(e,t);if("#"==r&&"!"==e.peek())return e.skipToEnd(),y("meta","meta");if("#"==r&&e.eatWhile(m))return y("variable","property");if("<"==r&&e.match("!--")||"-"==r&&e.match("->")&&!/\S/.test(e.string.slice(0,e.start)))return e.skipToEnd(),y("comment","comment");if(b.test(r))return">"==r&&t.lexical&&">"==t.lexical.type||(e.eat("=")?"!"!=r&&"="!=r||e.eat("="):/[<>*+\-|&?]/.test(r)&&(e.eat(r),">"==r&&e.eat(r))),"?"==r&&e.eat(".")?y("."):y("operator","operator",e.current());if(m.test(r)){e.eatWhile(m);r=e.current();if("."!=t.lastType){if(s.propertyIsEnumerable(r)){t=s[r];return y(t.type,t.style,r)}if("async"==r&&e.match(/^(\s|\/\*([^*]|\*(?!\/))*?\*\/)*[\[\(\w]/,!1))return y("async","keyword",r)}return y("variable","variable",r)}}function v(e,t){for(var r,n=!1;r=e.next();){if("/"==r&&n){t.tokenize=w;break}n="*"==r}return y("comment","comment")}function x(e,t){for(var r,n=!1;null!=(r=e.next());){if(!n&&("`"==r||"$"==r&&e.eat("{"))){t.tokenize=w;break}n=!n&&"\\"==r}return y("quasi","string-2",e.current())}function z(e,t){t.fatArrowAt&&(t.fatArrowAt=null);var r,n=e.string.indexOf("=>",e.start);if(!(n<0)){!c||(r=/:\s*(?:\w+(?:<[^>]*>|\[\])?|\{[^}]*\})\s*$/.exec(e.string.slice(e.start,n)))&&(n=r.index);for(var a=0,o=!1,i=n-1;0<=i;--i){var l=e.string.charAt(i),s="([{}])".indexOf(l);if(0<=s&&s<3){if(!a){++i;break}if(0==--a){"("==l&&(o=!0);break}}else if(3<=s&&s<6)++a;else if(m.test(l))o=!0;else if(/["'\/`]/.test(l))for(;;--i){if(0==i)return;if(e.string.charAt(i-1)==l&&"\\"!=e.string.charAt(i-2)){i--;break}}else if(o&&!a){++i;break}}o&&!a&&(t.fatArrowAt=i)}}var M={atom:!0,number:!0,variable:!0,string:!0,regexp:!0,this:!0,import:!0,"jsonld-keyword":!0};function S(e,t,r,n,a,o){this.indented=e,this.column=t,this.type=r,this.prev=a,this.info=o,null!=n&&(this.align=n)}function j(e,t,r,n,a){var o=e.cc;for(T.state=e,T.stream=a,T.marked=null,T.cc=o,T.style=t,e.lexical.hasOwnProperty("align")||(e.lexical.align=!0);;)if((o.length?o.pop():i?U:W)(r,n)){for(;o.length&&o[o.length-1].lex;)o.pop()();return T.marked?T.marked:"variable"==r&&function(e,t){if(l){for(var r=e.localVars;r;r=r.next)if(r.name==t)return 1;for(var n=e.context;n;n=n.prev)for(r=n.vars;r;r=r.next)if(r.name==t)return 1}}(e,n)?"variable-2":t}}var T={state:null,column:null,marked:null,cc:null};function C(){for(var e=arguments.length-1;0<=e;e--)T.cc.push(arguments[e])}function P(){return C.apply(null,arguments),!0}function q(e,t){for(var r=t;r;r=r.next)if(r.name==e)return 1}function A(e){var t=T.state;if(T.marked="def",l){if(t.context)if("var"==t.lexical.info&&t.context&&t.context.block){var r=function e(t,r){{if(r){if(r.block){var n=e(t,r.prev);return n?n==r.prev?r:new E(n,r.vars,!0):null}return q(t,r.vars)?r:new E(r.prev,new N(t,r.vars),!1)}return null}}(e,t.context);if(null!=r)return void(t.context=r)}else if(!q(e,t.localVars))return void(t.localVars=new N(e,t.localVars));d.globalVars&&!q(e,t.globalVars)&&(t.globalVars=new N(e,t.globalVars))}}function I(e){return"public"==e||"private"==e||"protected"==e||"abstract"==e||"readonly"==e}function E(e,t,r){this.prev=e,this.vars=t,this.block=r}function N(e,t){this.name=e,this.next=t}var O=new N("this",new N("arguments",null));function L(){T.state.context=new E(T.state.context,T.state.localVars,!1),T.state.localVars=O}function $(){T.state.context=new E(T.state.context,T.state.localVars,!0),T.state.localVars=null}function _(){T.state.localVars=T.state.context.vars,T.state.context=T.state.context.prev}function K(n,a){function e(){var e=T.state,t=e.indented;if("stat"==e.lexical.type)t=e.lexical.indented;else for(var r=e.lexical;r&&")"==r.type&&r.align;r=r.prev)t=r.indented;e.lexical=new S(t,T.stream.column(),n,null,e.lexical,a)}return e.lex=!0,e}function B(){var e=T.state;e.lexical.prev&&(")"==e.lexical.type&&(e.indented=e.lexical.indented),e.lexical=e.lexical.prev)}function V(r){return function e(t){return t==r?P():";"==r||"}"==t||")"==t||"]"==t?C():P(e)}}function W(e,t){return"var"==e?P(K("vardef",t),je,V(";"),B):"keyword a"==e?P(K("form"),H,W,B):"keyword b"==e?P(K("form"),W,B):"keyword d"==e?T.stream.match(/^\s*$/,!1)?P():P(K("stat"),Y,V(";"),B):"debugger"==e?P(V(";")):"{"==e?P(K("}"),$,de,B,_):";"==e?P():"if"==e?("else"==T.state.lexical.info&&T.state.cc[T.state.cc.length-1]==B&&T.state.cc.pop()(),P(K("form"),H,W,B,Ie)):"function"==e?P(Le):"for"==e?P(K("form"),$,Ee,W,_,B):"class"==e||c&&"interface"==t?(T.marked="keyword",P(K("form","class"==e?e:t),Ve,B)):"variable"==e?c&&"declare"==t?(T.marked="keyword",P(W)):c&&("module"==t||"enum"==t||"type"==t)&&T.stream.match(/^\s*\w/,!1)?(T.marked="keyword","enum"==t?P(Qe):"type"==t?P(_e,V("operator"),ge,V(";")):P(K("form"),Te,V("{"),K("}"),de,B,B)):c&&"namespace"==t?(T.marked="keyword",P(K("form"),U,W,B)):c&&"abstract"==t?(T.marked="keyword",P(W)):P(K("stat"),ne):"switch"==e?P(K("form"),H,V("{"),K("}","switch"),$,de,B,B,_):"case"==e?P(U,V(":")):"default"==e?P(V(":")):"catch"==e?P(K("form"),L,F,W,B,_):"export"==e?P(K("stat"),De,B):"import"==e?P(K("stat"),Ge,B):"async"==e?P(W):"@"==t?P(U,W):C(K("stat"),U,V(";"),B)}function F(e){if("("==e)return P(Ke,V(")"))}function U(e,t){return G(e,t,!1)}function D(e,t){return G(e,t,!0)}function H(e){return"("!=e?C():P(K(")"),Y,V(")"),B)}function G(e,t,r){if(T.state.fatArrowAt==T.stream.start){var n=r?ee:Q;if("("==e)return P(L,K(")"),se(Ke,")"),B,V("=>"),n,_);if("variable"==e)return C(L,Te,V("=>"),n,_)}var a,n=r?X:R;return M.hasOwnProperty(e)?P(n):"function"==e?P(Le,n):"class"==e||c&&"interface"==t?(T.marked="keyword",P(K("form"),Be,B)):"keyword c"==e||"async"==e?P(r?D:U):"("==e?P(K(")"),Y,V(")"),B,n):"operator"==e||"spread"==e?P(r?D:U):"["==e?P(K("]"),Je,B,n):"{"==e?ce(oe,"}",null,n):"quasi"==e?C(Z,n):"new"==e?P((a=r,function(e){return"."==e?P(a?re:te):"variable"==e&&c?P(ze,a?X:R):C(a?D:U)})):P()}function Y(e){return e.match(/[;\}\)\],]/)?C():C(U)}function R(e,t){return","==e?P(Y):X(e,t,!1)}function X(e,t,r){var n=0==r?R:X,a=0==r?U:D;return"=>"==e?P(L,r?ee:Q,_):"operator"==e?/\+\+|--/.test(t)||c&&"!"==t?P(n):c&&"<"==t&&T.stream.match(/^([^<>]|<[^<>]*>)*>\s*\(/,!1)?P(K(">"),se(ge,">"),B,n):"?"==t?P(U,V(":"),a):P(a):"quasi"==e?C(Z,n):";"!=e?"("==e?ce(D,")","call",n):"."==e?P(ae,n):"["==e?P(K("]"),Y,V("]"),B,n):c&&"as"==t?(T.marked="keyword",P(ge,n)):"regexp"==e?(T.state.lastType=T.marked="operator",T.stream.backUp(T.stream.pos-T.stream.start-1),P(a)):void 0:void 0}function Z(e,t){return"quasi"!=e?C():"${"!=t.slice(t.length-2)?P(Z):P(Y,J)}function J(e){if("}"==e)return T.marked="string-2",T.state.tokenize=x,P(Z)}function Q(e){return z(T.stream,T.state),C("{"==e?W:U)}function ee(e){return z(T.stream,T.state),C("{"==e?W:D)}function te(e,t){if("target"==t)return T.marked="keyword",P(R)}function re(e,t){if("target"==t)return T.marked="keyword",P(X)}function ne(e){return":"==e?P(B,W):C(R,V(";"),B)}function ae(e){if("variable"==e)return T.marked="property",P()}function oe(e,t){return"async"==e?(T.marked="property",P(oe)):"variable"!=e&&"keyword"!=T.style?"number"==e||"string"==e?(T.marked=o?"property":T.style+" property",P(le)):"jsonld-keyword"==e?P(le):c&&I(t)?(T.marked="keyword",P(oe)):"["==e?P(U,ue,V("]"),le):"spread"==e?P(D,le):"*"==t?(T.marked="keyword",P(oe)):":"==e?C(le):void 0:(T.marked="property","get"==t||"set"==t?P(ie):(c&&T.state.fatArrowAt==T.stream.start&&(r=T.stream.match(/^\s*:\s*/,!1))&&(T.state.fatArrowAt=T.stream.pos+r[0].length),P(le)));var r}function ie(e){return"variable"!=e?C(le):(T.marked="property",P(Le))}function le(e){return":"==e?P(D):"("==e?C(Le):void 0}function se(n,a,o){function i(e,t){if(o?-1<o.indexOf(e):","==e){var r=T.state.lexical;return"call"==r.info&&(r.pos=(r.pos||0)+1),P(function(e,t){return e==a||t==a?C():C(n)},i)}return e==a||t==a?P():o&&-1<o.indexOf(";")?C(n):P(V(a))}return function(e,t){return e==a||t==a?P():C(n,i)}}function ce(e,t,r){for(var n=3;n<arguments.length;n++)T.cc.push(arguments[n]);return P(K(t,r),se(e,t),B)}function de(e){return"}"==e?P():C(W,de)}function ue(e,t){if(c)return":"==e?P(ge):"?"==t?P(ue):void 0}function pe(e,t){if(c&&(":"==e||"in"==t))return P(ge)}function me(e){if(c&&":"==e)return T.stream.match(/^\s*\w+\s+is\b/,!1)?P(U,fe,ge):P(ge)}function fe(e,t){if("is"==t)return T.marked="keyword",P()}function ge(e,t){return"keyof"==t||"typeof"==t||"infer"==t||"readonly"==t?(T.marked="keyword",P("typeof"==t?D:ge)):"variable"==e||"void"==t?(T.marked="type",P(xe)):"|"==t||"&"==t?P(ge):"string"==e||"number"==e||"atom"==e?P(xe):"["==e?P(K("]"),se(ge,"]",","),B,xe):"{"==e?P(K("}"),be,B,xe):"("==e?P(se(ve,")"),he,xe):"<"==e?P(se(ge,">"),ge):"quasi"==e?C(ye,xe):void 0}function he(e){if("=>"==e)return P(ge)}function be(e){return e.match(/[\}\)\]]/)?P():","==e||";"==e?P(be):C(ke,be)}function ke(e,t){return"variable"==e||"keyword"==T.style?(T.marked="property",P(ke)):"?"==t||"number"==e||"string"==e?P(ke):":"==e?P(ge):"["==e?P(V("variable"),pe,V("]"),ke):"("==e?C($e,ke):e.match(/[;\}\)\],]/)?void 0:P()}function ye(e,t){return"quasi"!=e?C():"${"!=t.slice(t.length-2)?P(ye):P(ge,we)}function we(e){if("}"==e)return T.marked="string-2",T.state.tokenize=x,P(ye)}function ve(e,t){return"variable"==e&&T.stream.match(/^\s*[?:]/,!1)||"?"==t?P(ve):":"==e?P(ge):"spread"==e?P(ve):C(ge)}function xe(e,t){return"<"==t?P(K(">"),se(ge,">"),B,xe):"|"==t||"."==e||"&"==t?P(ge):"["==e?P(ge,V("]"),xe):"extends"==t||"implements"==t?(T.marked="keyword",P(ge)):"?"==t?P(ge,V(":"),ge):void 0}function ze(e,t){if("<"==t)return P(K(">"),se(ge,">"),B,xe)}function Me(){return C(ge,Se)}function Se(e,t){if("="==t)return P(ge)}function je(e,t){return"enum"==t?(T.marked="keyword",P(Qe)):C(Te,ue,qe,Ae)}function Te(e,t){return c&&I(t)?(T.marked="keyword",P(Te)):"variable"==e?(A(t),P()):"spread"==e?P(Te):"["==e?ce(Pe,"]"):"{"==e?ce(Ce,"}"):void 0}function Ce(e,t){return"variable"!=e||T.stream.match(/^\s*:/,!1)?("variable"==e&&(T.marked="property"),"spread"==e?P(Te):"}"==e?C():"["==e?P(U,V("]"),V(":"),Ce):P(V(":"),Te,qe)):(A(t),P(qe))}function Pe(){return C(Te,qe)}function qe(e,t){if("="==t)return P(D)}function Ae(e){if(","==e)return P(je)}function Ie(e,t){if("keyword b"==e&&"else"==t)return P(K("form","else"),W,B)}function Ee(e,t){return"await"==t?P(Ee):"("==e?P(K(")"),Ne,B):void 0}function Ne(e){return"var"==e?P(je,Oe):("variable"==e?P:C)(Oe)}function Oe(e,t){return")"==e?P():";"==e?P(Oe):"in"==t||"of"==t?(T.marked="keyword",P(U,Oe)):C(U,Oe)}function Le(e,t){return"*"==t?(T.marked="keyword",P(Le)):"variable"==e?(A(t),P(Le)):"("==e?P(L,K(")"),se(Ke,")"),B,me,W,_):c&&"<"==t?P(K(">"),se(Me,">"),B,Le):void 0}function $e(e,t){return"*"==t?(T.marked="keyword",P($e)):"variable"==e?(A(t),P($e)):"("==e?P(L,K(")"),se(Ke,")"),B,me,_):c&&"<"==t?P(K(">"),se(Me,">"),B,$e):void 0}function _e(e,t){return"keyword"==e||"variable"==e?(T.marked="type",P(_e)):"<"==t?P(K(">"),se(Me,">"),B):void 0}function Ke(e,t){return"@"==t&&P(U,Ke),"spread"==e?P(Ke):c&&I(t)?(T.marked="keyword",P(Ke)):c&&"this"==e?P(ue,qe):C(Te,ue,qe)}function Be(e,t){return("variable"==e?Ve:We)(e,t)}function Ve(e,t){if("variable"==e)return A(t),P(We)}function We(e,t){return"<"==t?P(K(">"),se(Me,">"),B,We):"extends"==t||"implements"==t||c&&","==e?("implements"==t&&(T.marked="keyword"),P(c?ge:U,We)):"{"==e?P(K("}"),Fe,B):void 0}function Fe(e,t){return"async"==e||"variable"==e&&("static"==t||"get"==t||"set"==t||c&&I(t))&&T.stream.match(/^\s+[\w$\xa1-\uffff]/,!1)?(T.marked="keyword",P(Fe)):"variable"==e||"keyword"==T.style?(T.marked="property",P(Ue,Fe)):"number"==e||"string"==e?P(Ue,Fe):"["==e?P(U,ue,V("]"),Ue,Fe):"*"==t?(T.marked="keyword",P(Fe)):c&&"("==e?C($e,Fe):";"==e||","==e?P(Fe):"}"==e?P():"@"==t?P(U,Fe):void 0}function Ue(e,t){if("!"==t)return P(Ue);if("?"==t)return P(Ue);if(":"==e)return P(ge,qe);if("="==t)return P(D);t=T.state.lexical.prev;return C(t&&"interface"==t.info?$e:Le)}function De(e,t){return"*"==t?(T.marked="keyword",P(Ze,V(";"))):"default"==t?(T.marked="keyword",P(U,V(";"))):"{"==e?P(se(He,"}"),Ze,V(";")):C(W)}function He(e,t){return"as"==t?(T.marked="keyword",P(V("variable"))):"variable"==e?C(D,He):void 0}function Ge(e){return"string"==e?P():"("==e?C(U):"."==e?C(R):C(Ye,Re,Ze)}function Ye(e,t){return"{"==e?ce(Ye,"}"):("variable"==e&&A(t),"*"==t&&(T.marked="keyword"),P(Xe))}function Re(e){if(","==e)return P(Ye,Re)}function Xe(e,t){if("as"==t)return T.marked="keyword",P(Ye)}function Ze(e,t){if("from"==t)return T.marked="keyword",P(U)}function Je(e){return"]"==e?P():C(se(D,"]"))}function Qe(){return C(K("form"),Te,V("{"),K("}"),se(et,"}"),B,B)}function et(){return C(Te,qe)}function tt(e,t,r){return t.tokenize==w&&/^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\[{}\(,;:]|=>)$/.test(t.lastType)||"quasi"==t.lastType&&/\{\s*$/.test(e.string.slice(0,e.pos-(r||0)))}return L.lex=$.lex=!0,B.lex=_.lex=!0,{startState:function(e){e={tokenize:w,lastType:"sof",cc:[],lexical:new S((e||0)-u,0,"block",!1),localVars:d.localVars,context:d.localVars&&new E(null,null,!1),indented:e||0};return d.globalVars&&"object"==typeof d.globalVars&&(e.globalVars=d.globalVars),e},token:function(e,t){if(e.sol()&&(t.lexical.hasOwnProperty("align")||(t.lexical.align=!1),t.indented=e.indentation(),z(e,t)),t.tokenize!=v&&e.eatSpace())return null;var r=t.tokenize(e,t);return"comment"==g?r:(t.lastType="operator"!=g||"++"!=h&&"--"!=h?g:"incdec",j(t,r,g,h,e))},indent:function(e,t){if(e.tokenize==v||e.tokenize==x)return rt.Pass;if(e.tokenize!=w)return 0;var r,n=t&&t.charAt(0),a=e.lexical;if(!/^\s*else\b/.test(t))for(var o=e.cc.length-1;0<=o;--o){var i=e.cc[o];if(i==B)a=a.prev;else if(i!=Ie&&i!=_)break}for(;("stat"==a.type||"form"==a.type)&&("}"==n||(r=e.cc[e.cc.length-1])&&(r==R||r==X)&&!/^[,\.=+\-*:?[\(]/.test(t));)a=a.prev;var l,s=(a=p&&")"==a.type&&"stat"==a.prev.type?a.prev:a).type,c=n==s;return"vardef"==s?a.indented+("operator"==e.lastType||","==e.lastType?a.info.length+1:0):"form"==s&&"{"==n?a.indented:"form"==s?a.indented+u:"stat"==s?a.indented+(l=t,"operator"==(s=e).lastType||","==s.lastType||b.test(l.charAt(0))||/[,.]/.test(l.charAt(0))?p||u:0):"switch"!=a.info||c||0==d.doubleIndentSwitch?a.align?a.column+(c?0:1):a.indented+(c?0:u):a.indented+(/^(?:case|default)\b/.test(t)?u:2*u)},electricInput:/^\s*(?:case .*?:|default:|\{|\})$/,blockCommentStart:i?null:"/*",blockCommentEnd:i?null:"*/",blockCommentContinue:i?null:" * ",lineComment:i?null:"//",fold:"brace",closeBrackets:"()[]{}''\"\"``",helperType:i?"json":"javascript",jsonldMode:o,jsonMode:i,expressionAllowed:tt,skipExpression:function(e){j(e,"atom","atom","true",new rt.StringStream("",2,null))}}}),rt.registerHelper("wordChars","javascript",/[\w$]/),rt.defineMIME("text/javascript","javascript"),rt.defineMIME("text/ecmascript","javascript"),rt.defineMIME("application/javascript","javascript"),rt.defineMIME("application/x-javascript","javascript"),rt.defineMIME("application/ecmascript","javascript"),rt.defineMIME("application/json",{name:"javascript",json:!0}),rt.defineMIME("application/x-json",{name:"javascript",json:!0}),rt.defineMIME("application/manifest+json",{name:"javascript",json:!0}),rt.defineMIME("application/ld+json",{name:"javascript",jsonld:!0}),rt.defineMIME("text/typescript",{name:"javascript",typescript:!0}),rt.defineMIME("application/typescript",{name:"javascript",typescript:!0})}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define(["../../lib/codemirror"],e):e(CodeMirror)}(function(A){"use strict";function e(e){for(var t={},r=0;r<e.length;++r)t[e[r].toLowerCase()]=!0;return t}A.defineMode("css",function(e,t){var r=t.inline;t.propertyKeywords||(t=A.resolveMode("text/css"));var o,n,a=e.indentUnit,i=t.tokenHooks,l=t.documentTypes||{},s=t.mediaTypes||{},c=t.mediaFeatures||{},d=t.mediaValueKeywords||{},u=t.propertyKeywords||{},p=t.nonStandardPropertyKeywords||{},m=t.fontProperties||{},f=t.counterDescriptors||{},g=t.colorKeywords||{},h=t.valueKeywords||{},b=t.allowNested,k=t.lineComment,y=!0===t.supportsAtComponent,w=!1!==e.highlightNonStandardPropertyKeywords;function v(e,t){return o=t,e}function x(a){return function(e,t){for(var r,n=!1;null!=(r=e.next());){if(r==a&&!n){")"==a&&e.backUp(1);break}n=!n&&"\\"==r}return r!=a&&(n||")"==a)||(t.tokenize=null),o="string"}}function z(e,t){return e.next(),e.match(/^\s*[\"\')]/,!1)?t.tokenize=null:t.tokenize=x(")"),o="(",null}function M(e,t,r){this.type=e,this.indent=t,this.prev=r}function S(e,t,r,n){return e.context=new M(r,t.indentation()+(!1===n?0:a),e.context),r}function j(e){return e.context.prev&&(e.context=e.context.prev),e.context.type}function T(e,t,r){return q[r.context.type](e,t,r)}function C(e,t,r,n){for(var a=n||1;0<a;a--)r.context=r.context.prev;return T(e,t,r)}function P(e){e=e.current().toLowerCase();n=h.hasOwnProperty(e)?"atom":g.hasOwnProperty(e)?"keyword":"variable"}var q={top:function(e,t,r){if("{"==e)return S(r,t,"block");if("}"==e&&r.context.prev)return j(r);if(y&&/@component/i.test(e))return S(r,t,"atComponentBlock");if(/^@(-moz-)?document$/i.test(e))return S(r,t,"documentTypes");if(/^@(media|supports|(-moz-)?document|import)$/i.test(e))return S(r,t,"atBlock");if(/^@(font-face|counter-style)/i.test(e))return r.stateArg=e,"restricted_atBlock_before";if(/^@(-(moz|ms|o|webkit)-)?keyframes$/i.test(e))return"keyframes";if(e&&"@"==e.charAt(0))return S(r,t,"at");if("hash"==e)n="builtin";else if("word"==e)n="tag";else{if("variable-definition"==e)return"maybeprop";if("interpolation"==e)return S(r,t,"interpolation");if(":"==e)return"pseudo";if(b&&"("==e)return S(r,t,"parens")}return r.context.type},block:function(e,t,r){if("word"!=e)return"meta"==e?"block":b||"hash"!=e&&"qualifier"!=e?q.top(e,t,r):(n="error","block");r=t.current().toLowerCase();return u.hasOwnProperty(r)?(n="property","maybeprop"):p.hasOwnProperty(r)?(n=w?"string-2":"property","maybeprop"):b?(n=t.match(/^\s*:(?:\s|$)/,!1)?"property":"tag","block"):(n+=" error","maybeprop")},maybeprop:function(e,t,r){return":"==e?S(r,t,"prop"):T(e,t,r)},prop:function(e,t,r){if(";"==e)return j(r);if("{"==e&&b)return S(r,t,"propBlock");if("}"==e||"{"==e)return C(e,t,r);if("("==e)return S(r,t,"parens");if("hash"!=e||/^#([0-9a-fA-f]{3,4}|[0-9a-fA-f]{6}|[0-9a-fA-f]{8})$/.test(t.current())){if("word"==e)P(t);else if("interpolation"==e)return S(r,t,"interpolation")}else n+=" error";return"prop"},propBlock:function(e,t,r){return"}"==e?j(r):"word"==e?(n="property","maybeprop"):r.context.type},parens:function(e,t,r){return"{"==e||"}"==e?C(e,t,r):")"==e?j(r):"("==e?S(r,t,"parens"):"interpolation"==e?S(r,t,"interpolation"):("word"==e&&P(t),"parens")},pseudo:function(e,t,r){return"meta"==e?"pseudo":"word"==e?(n="variable-3",r.context.type):T(e,t,r)},documentTypes:function(e,t,r){return"word"==e&&l.hasOwnProperty(t.current())?(n="tag",r.context.type):q.atBlock(e,t,r)},atBlock:function(e,t,r){return"("==e?S(r,t,"atBlock_parens"):"}"==e||";"==e?C(e,t,r):"{"==e?j(r)&&S(r,t,b?"block":"top"):"interpolation"==e?S(r,t,"interpolation"):("word"==e&&(t=t.current().toLowerCase(),n="only"==t||"not"==t||"and"==t||"or"==t?"keyword":s.hasOwnProperty(t)?"attribute":c.hasOwnProperty(t)?"property":d.hasOwnProperty(t)?"keyword":u.hasOwnProperty(t)?"property":p.hasOwnProperty(t)?w?"string-2":"property":h.hasOwnProperty(t)?"atom":g.hasOwnProperty(t)?"keyword":"error"),r.context.type)},atComponentBlock:function(e,t,r){return"}"==e?C(e,t,r):"{"==e?j(r)&&S(r,t,b?"block":"top",!1):("word"==e&&(n="error"),r.context.type)},atBlock_parens:function(e,t,r){return")"==e?j(r):"{"==e||"}"==e?C(e,t,r,2):q.atBlock(e,t,r)},restricted_atBlock_before:function(e,t,r){return"{"==e?S(r,t,"restricted_atBlock"):"word"==e&&"@counter-style"==r.stateArg?(n="variable","restricted_atBlock_before"):T(e,t,r)},restricted_atBlock:function(e,t,r){return"}"==e?(r.stateArg=null,j(r)):"word"==e?(n="@font-face"==r.stateArg&&!m.hasOwnProperty(t.current().toLowerCase())||"@counter-style"==r.stateArg&&!f.hasOwnProperty(t.current().toLowerCase())?"error":"property","maybeprop"):"restricted_atBlock"},keyframes:function(e,t,r){return"word"==e?(n="variable","keyframes"):"{"==e?S(r,t,"top"):T(e,t,r)},at:function(e,t,r){return";"==e?j(r):"{"==e||"}"==e?C(e,t,r):("word"==e?n="tag":"hash"==e&&(n="builtin"),"at")},interpolation:function(e,t,r){return"}"==e?j(r):"{"==e||";"==e?C(e,t,r):("word"==e?n="variable":"variable"!=e&&"("!=e&&")"!=e&&(n="error"),"interpolation")}};return{startState:function(e){return{tokenize:null,state:r?"block":"top",stateArg:null,context:new M(r?"block":"top",e||0,null)}},token:function(e,t){if(!t.tokenize&&e.eatSpace())return null;var r=(t.tokenize||function(e,t){var r=e.next();if(i[r]){var n=i[r](e,t);if(!1!==n)return n}return"@"==r?(e.eatWhile(/[\w\\\-]/),v("def",e.current())):"="==r||("~"==r||"|"==r)&&e.eat("=")?(o="compare",null):'"'==r||"'"==r?(t.tokenize=x(r),t.tokenize(e,t)):"#"==r?(e.eatWhile(/[\w\\\-]/),o="hash","atom"):"!"==r?(e.match(/^\s*\w*/),o="important","keyword"):/\d/.test(r)||"."==r&&e.eat(/\d/)?(e.eatWhile(/[\w.%]/),o="unit","number"):"-"===r?/[\d.]/.test(e.peek())?(e.eatWhile(/[\w.%]/),o="unit","number"):e.match(/^-[\w\\\-]*/)?(e.eatWhile(/[\w\\\-]/),o=e.match(/^\s*:/,!1)?"variable-definition":"variable","variable-2"):e.match(/^\w+-/)?o="meta":void 0:/[,+>*\/]/.test(r)?(o="select-op",null):"."==r&&e.match(/^-?[_a-z][_a-z0-9-]*/i)?o="qualifier":/[:;{}\[\]\(\)]/.test(r)?v(null,r):e.match(/^[\w-.]+(?=\()/)?(/^(url(-prefix)?|domain|regexp)$/i.test(e.current())&&(t.tokenize=z),o="variable","variable callee"):/[\w\\\-]/.test(r)?(e.eatWhile(/[\w\\\-]/),o="word","property"):o=null})(e,t);return r&&"object"==typeof r&&(o=r[1],r=r[0]),n=r,"comment"!=o&&(t.state=q[t.state](o,e,t)),n},indent:function(e,t){var r=e.context,e=t&&t.charAt(0),t=r.indent;return(r="prop"==r.type&&("}"==e||")"==e)?r.prev:r).prev&&("}"!=e||"block"!=r.type&&"top"!=r.type&&"interpolation"!=r.type&&"restricted_atBlock"!=r.type?(")"!=e||"parens"!=r.type&&"atBlock_parens"!=r.type)&&("{"!=e||"at"!=r.type&&"atBlock"!=r.type)||(t=Math.max(0,r.indent-a)):t=(r=r.prev).indent),t},electricChars:"}",blockCommentStart:"/*",blockCommentEnd:"*/",blockCommentContinue:" * ",lineComment:k,fold:"brace"}});var t=["domain","regexp","url","url-prefix"],r=e(t),n=["all","aural","braille","handheld","print","projection","screen","tty","tv","embossed"],a=e(n),o=["width","min-width","max-width","height","min-height","max-height","device-width","min-device-width","max-device-width","device-height","min-device-height","max-device-height","aspect-ratio","min-aspect-ratio","max-aspect-ratio","device-aspect-ratio","min-device-aspect-ratio","max-device-aspect-ratio","color","min-color","max-color","color-index","min-color-index","max-color-index","monochrome","min-monochrome","max-monochrome","resolution","min-resolution","max-resolution","scan","grid","orientation","device-pixel-ratio","min-device-pixel-ratio","max-device-pixel-ratio","pointer","any-pointer","hover","any-hover","prefers-color-scheme","dynamic-range","video-dynamic-range"],i=e(o),l=["landscape","portrait","none","coarse","fine","on-demand","hover","interlace","progressive","dark","light","standard","high"],s=e(l),c=["align-content","align-items","align-self","alignment-adjust","alignment-baseline","all","anchor-point","animation","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-timing-function","appearance","azimuth","backdrop-filter","backface-visibility","background","background-attachment","background-blend-mode","background-clip","background-color","background-image","background-origin","background-position","background-position-x","background-position-y","background-repeat","background-size","baseline-shift","binding","bleed","block-size","bookmark-label","bookmark-level","bookmark-state","bookmark-target","border","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","bottom","box-decoration-break","box-shadow","box-sizing","break-after","break-before","break-inside","caption-side","caret-color","clear","clip","color","color-profile","column-count","column-fill","column-gap","column-rule","column-rule-color","column-rule-style","column-rule-width","column-span","column-width","columns","contain","content","counter-increment","counter-reset","crop","cue","cue-after","cue-before","cursor","direction","display","dominant-baseline","drop-initial-after-adjust","drop-initial-after-align","drop-initial-before-adjust","drop-initial-before-align","drop-initial-size","drop-initial-value","elevation","empty-cells","fit","fit-content","fit-position","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","float","float-offset","flow-from","flow-into","font","font-family","font-feature-settings","font-kerning","font-language-override","font-optical-sizing","font-size","font-size-adjust","font-stretch","font-style","font-synthesis","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-ligatures","font-variant-numeric","font-variant-position","font-variation-settings","font-weight","gap","grid","grid-area","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-column","grid-column-end","grid-column-gap","grid-column-start","grid-gap","grid-row","grid-row-end","grid-row-gap","grid-row-start","grid-template","grid-template-areas","grid-template-columns","grid-template-rows","hanging-punctuation","height","hyphens","icon","image-orientation","image-rendering","image-resolution","inline-box-align","inset","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","isolation","justify-content","justify-items","justify-self","left","letter-spacing","line-break","line-height","line-height-step","line-stacking","line-stacking-ruby","line-stacking-shift","line-stacking-strategy","list-style","list-style-image","list-style-position","list-style-type","margin","margin-bottom","margin-left","margin-right","margin-top","marks","marquee-direction","marquee-loop","marquee-play-count","marquee-speed","marquee-style","mask-clip","mask-composite","mask-image","mask-mode","mask-origin","mask-position","mask-repeat","mask-size","mask-type","max-block-size","max-height","max-inline-size","max-width","min-block-size","min-height","min-inline-size","min-width","mix-blend-mode","move-to","nav-down","nav-index","nav-left","nav-right","nav-up","object-fit","object-position","offset","offset-anchor","offset-distance","offset-path","offset-position","offset-rotate","opacity","order","orphans","outline","outline-color","outline-offset","outline-style","outline-width","overflow","overflow-style","overflow-wrap","overflow-x","overflow-y","padding","padding-bottom","padding-left","padding-right","padding-top","page","page-break-after","page-break-before","page-break-inside","page-policy","pause","pause-after","pause-before","perspective","perspective-origin","pitch","pitch-range","place-content","place-items","place-self","play-during","position","presentation-level","punctuation-trim","quotes","region-break-after","region-break-before","region-break-inside","region-fragment","rendering-intent","resize","rest","rest-after","rest-before","richness","right","rotate","rotation","rotation-point","row-gap","ruby-align","ruby-overhang","ruby-position","ruby-span","scale","scroll-behavior","scroll-margin","scroll-margin-block","scroll-margin-block-end","scroll-margin-block-start","scroll-margin-bottom","scroll-margin-inline","scroll-margin-inline-end","scroll-margin-inline-start","scroll-margin-left","scroll-margin-right","scroll-margin-top","scroll-padding","scroll-padding-block","scroll-padding-block-end","scroll-padding-block-start","scroll-padding-bottom","scroll-padding-inline","scroll-padding-inline-end","scroll-padding-inline-start","scroll-padding-left","scroll-padding-right","scroll-padding-top","scroll-snap-align","scroll-snap-type","shape-image-threshold","shape-inside","shape-margin","shape-outside","size","speak","speak-as","speak-header","speak-numeral","speak-punctuation","speech-rate","stress","string-set","tab-size","table-layout","target","target-name","target-new","target-position","text-align","text-align-last","text-combine-upright","text-decoration","text-decoration-color","text-decoration-line","text-decoration-skip","text-decoration-skip-ink","text-decoration-style","text-emphasis","text-emphasis-color","text-emphasis-position","text-emphasis-style","text-height","text-indent","text-justify","text-orientation","text-outline","text-overflow","text-rendering","text-shadow","text-size-adjust","text-space-collapse","text-transform","text-underline-position","text-wrap","top","touch-action","transform","transform-origin","transform-style","transition","transition-delay","transition-duration","transition-property","transition-timing-function","translate","unicode-bidi","user-select","vertical-align","visibility","voice-balance","voice-duration","voice-family","voice-pitch","voice-range","voice-rate","voice-stress","voice-volume","volume","white-space","widows","width","will-change","word-break","word-spacing","word-wrap","writing-mode","z-index","clip-path","clip-rule","mask","enable-background","filter","flood-color","flood-opacity","lighting-color","stop-color","stop-opacity","pointer-events","color-interpolation","color-interpolation-filters","color-rendering","fill","fill-opacity","fill-rule","image-rendering","marker","marker-end","marker-mid","marker-start","paint-order","shape-rendering","stroke","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","text-rendering","baseline-shift","dominant-baseline","glyph-orientation-horizontal","glyph-orientation-vertical","text-anchor","writing-mode"],d=e(c),u=["accent-color","aspect-ratio","border-block","border-block-color","border-block-end","border-block-end-color","border-block-end-style","border-block-end-width","border-block-start","border-block-start-color","border-block-start-style","border-block-start-width","border-block-style","border-block-width","border-inline","border-inline-color","border-inline-end","border-inline-end-color","border-inline-end-style","border-inline-end-width","border-inline-start","border-inline-start-color","border-inline-start-style","border-inline-start-width","border-inline-style","border-inline-width","content-visibility","margin-block","margin-block-end","margin-block-start","margin-inline","margin-inline-end","margin-inline-start","overflow-anchor","overscroll-behavior","padding-block","padding-block-end","padding-block-start","padding-inline","padding-inline-end","padding-inline-start","scroll-snap-stop","scrollbar-3d-light-color","scrollbar-arrow-color","scrollbar-base-color","scrollbar-dark-shadow-color","scrollbar-face-color","scrollbar-highlight-color","scrollbar-shadow-color","scrollbar-track-color","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","shape-inside","zoom"],p=e(u),m=e(["font-display","font-family","src","unicode-range","font-variant","font-feature-settings","font-stretch","font-weight","font-style"]),f=e(["additive-symbols","fallback","negative","pad","prefix","range","speak-as","suffix","symbols","system"]),g=["aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkgrey","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkslategrey","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dimgrey","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","grey","green","greenyellow","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightgrey","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightslategrey","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","slategrey","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen"],h=e(g),b=["above","absolute","activeborder","additive","activecaption","afar","after-white-space","ahead","alias","all","all-scroll","alphabetic","alternate","always","amharic","amharic-abegede","antialiased","appworkspace","arabic-indic","armenian","asterisks","attr","auto","auto-flow","avoid","avoid-column","avoid-page","avoid-region","axis-pan","background","backwards","baseline","below","bidi-override","binary","bengali","blink","block","block-axis","blur","bold","bolder","border","border-box","both","bottom","break","break-all","break-word","brightness","bullets","button","button-bevel","buttonface","buttonhighlight","buttonshadow","buttontext","calc","cambodian","capitalize","caps-lock-indicator","caption","captiontext","caret","cell","center","checkbox","circle","cjk-decimal","cjk-earthly-branch","cjk-heavenly-stem","cjk-ideographic","clear","clip","close-quote","col-resize","collapse","color","color-burn","color-dodge","column","column-reverse","compact","condensed","conic-gradient","contain","content","contents","content-box","context-menu","continuous","contrast","copy","counter","counters","cover","crop","cross","crosshair","cubic-bezier","currentcolor","cursive","cyclic","darken","dashed","decimal","decimal-leading-zero","default","default-button","dense","destination-atop","destination-in","destination-out","destination-over","devanagari","difference","disc","discard","disclosure-closed","disclosure-open","document","dot-dash","dot-dot-dash","dotted","double","down","drop-shadow","e-resize","ease","ease-in","ease-in-out","ease-out","element","ellipse","ellipsis","embed","end","ethiopic","ethiopic-abegede","ethiopic-abegede-am-et","ethiopic-abegede-gez","ethiopic-abegede-ti-er","ethiopic-abegede-ti-et","ethiopic-halehame-aa-er","ethiopic-halehame-aa-et","ethiopic-halehame-am-et","ethiopic-halehame-gez","ethiopic-halehame-om-et","ethiopic-halehame-sid-et","ethiopic-halehame-so-et","ethiopic-halehame-ti-er","ethiopic-halehame-ti-et","ethiopic-halehame-tig","ethiopic-numeric","ew-resize","exclusion","expanded","extends","extra-condensed","extra-expanded","fantasy","fast","fill","fill-box","fixed","flat","flex","flex-end","flex-start","footnotes","forwards","from","geometricPrecision","georgian","grayscale","graytext","grid","groove","gujarati","gurmukhi","hand","hangul","hangul-consonant","hard-light","hebrew","help","hidden","hide","higher","highlight","highlighttext","hiragana","hiragana-iroha","horizontal","hsl","hsla","hue","hue-rotate","icon","ignore","inactiveborder","inactivecaption","inactivecaptiontext","infinite","infobackground","infotext","inherit","initial","inline","inline-axis","inline-block","inline-flex","inline-grid","inline-table","inset","inside","intrinsic","invert","italic","japanese-formal","japanese-informal","justify","kannada","katakana","katakana-iroha","keep-all","khmer","korean-hangul-formal","korean-hanja-formal","korean-hanja-informal","landscape","lao","large","larger","left","level","lighter","lighten","line-through","linear","linear-gradient","lines","list-item","listbox","listitem","local","logical","loud","lower","lower-alpha","lower-armenian","lower-greek","lower-hexadecimal","lower-latin","lower-norwegian","lower-roman","lowercase","ltr","luminosity","malayalam","manipulation","match","matrix","matrix3d","media-controls-background","media-current-time-display","media-fullscreen-button","media-mute-button","media-play-button","media-return-to-realtime-button","media-rewind-button","media-seek-back-button","media-seek-forward-button","media-slider","media-sliderthumb","media-time-remaining-display","media-volume-slider","media-volume-slider-container","media-volume-sliderthumb","medium","menu","menulist","menulist-button","menulist-text","menulist-textfield","menutext","message-box","middle","min-intrinsic","mix","mongolian","monospace","move","multiple","multiple_mask_images","multiply","myanmar","n-resize","narrower","ne-resize","nesw-resize","no-close-quote","no-drop","no-open-quote","no-repeat","none","normal","not-allowed","nowrap","ns-resize","numbers","numeric","nw-resize","nwse-resize","oblique","octal","opacity","open-quote","optimizeLegibility","optimizeSpeed","oriya","oromo","outset","outside","outside-shape","overlay","overline","padding","padding-box","painted","page","paused","persian","perspective","pinch-zoom","plus-darker","plus-lighter","pointer","polygon","portrait","pre","pre-line","pre-wrap","preserve-3d","progress","push-button","radial-gradient","radio","read-only","read-write","read-write-plaintext-only","rectangle","region","relative","repeat","repeating-linear-gradient","repeating-radial-gradient","repeating-conic-gradient","repeat-x","repeat-y","reset","reverse","rgb","rgba","ridge","right","rotate","rotate3d","rotateX","rotateY","rotateZ","round","row","row-resize","row-reverse","rtl","run-in","running","s-resize","sans-serif","saturate","saturation","scale","scale3d","scaleX","scaleY","scaleZ","screen","scroll","scrollbar","scroll-position","se-resize","searchfield","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","self-start","self-end","semi-condensed","semi-expanded","separate","sepia","serif","show","sidama","simp-chinese-formal","simp-chinese-informal","single","skew","skewX","skewY","skip-white-space","slide","slider-horizontal","slider-vertical","sliderthumb-horizontal","sliderthumb-vertical","slow","small","small-caps","small-caption","smaller","soft-light","solid","somali","source-atop","source-in","source-out","source-over","space","space-around","space-between","space-evenly","spell-out","square","square-button","start","static","status-bar","stretch","stroke","stroke-box","sub","subpixel-antialiased","svg_masks","super","sw-resize","symbolic","symbols","system-ui","table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row","table-row-group","tamil","telugu","text","text-bottom","text-top","textarea","textfield","thai","thick","thin","threeddarkshadow","threedface","threedhighlight","threedlightshadow","threedshadow","tibetan","tigre","tigrinya-er","tigrinya-er-abegede","tigrinya-et","tigrinya-et-abegede","to","top","trad-chinese-formal","trad-chinese-informal","transform","translate","translate3d","translateX","translateY","translateZ","transparent","ultra-condensed","ultra-expanded","underline","unidirectional-pan","unset","up","upper-alpha","upper-armenian","upper-greek","upper-hexadecimal","upper-latin","upper-norwegian","upper-roman","uppercase","urdu","url","var","vertical","vertical-text","view-box","visible","visibleFill","visiblePainted","visibleStroke","visual","w-resize","wait","wave","wider","window","windowframe","windowtext","words","wrap","wrap-reverse","x-large","x-small","xor","xx-large","xx-small"],k=e(b),b=t.concat(n).concat(o).concat(l).concat(c).concat(u).concat(g).concat(b);function y(e,t){for(var r,n=!1;null!=(r=e.next());){if(n&&"/"==r){t.tokenize=null;break}n="*"==r}return["comment","comment"]}A.registerHelper("hintWords","css",b),A.defineMIME("text/css",{documentTypes:r,mediaTypes:a,mediaFeatures:i,mediaValueKeywords:s,propertyKeywords:d,nonStandardPropertyKeywords:p,fontProperties:m,counterDescriptors:f,colorKeywords:h,valueKeywords:k,tokenHooks:{"/":function(e,t){return!!e.eat("*")&&(t.tokenize=y)(e,t)}},name:"css"}),A.defineMIME("text/x-scss",{mediaTypes:a,mediaFeatures:i,mediaValueKeywords:s,propertyKeywords:d,nonStandardPropertyKeywords:p,colorKeywords:h,valueKeywords:k,fontProperties:m,allowNested:!0,lineComment:"//",tokenHooks:{"/":function(e,t){return e.eat("/")?(e.skipToEnd(),["comment","comment"]):e.eat("*")?(t.tokenize=y)(e,t):["operator","operator"]},":":function(e){return!!e.match(/^\s*\{/,!1)&&[null,null]},$:function(e){return e.match(/^[\w-]+/),e.match(/^\s*:/,!1)?["variable-2","variable-definition"]:["variable-2","variable"]},"#":function(e){return!!e.eat("{")&&[null,"interpolation"]}},name:"css",helperType:"scss"}),A.defineMIME("text/x-less",{mediaTypes:a,mediaFeatures:i,mediaValueKeywords:s,propertyKeywords:d,nonStandardPropertyKeywords:p,colorKeywords:h,valueKeywords:k,fontProperties:m,allowNested:!0,lineComment:"//",tokenHooks:{"/":function(e,t){return e.eat("/")?(e.skipToEnd(),["comment","comment"]):e.eat("*")?(t.tokenize=y)(e,t):["operator","operator"]},"@":function(e){return e.eat("{")?[null,"interpolation"]:!e.match(/^(charset|document|font-face|import|(-(moz|ms|o|webkit)-)?keyframes|media|namespace|page|supports)\b/i,!1)&&(e.eatWhile(/[\w\\\-]/),e.match(/^\s*:/,!1)?["variable-2","variable-definition"]:["variable-2","variable"])},"&":function(){return["atom","atom"]}},name:"css",helperType:"less"}),A.defineMIME("text/x-gss",{documentTypes:r,mediaTypes:a,mediaFeatures:i,propertyKeywords:d,nonStandardPropertyKeywords:p,fontProperties:m,counterDescriptors:f,colorKeywords:h,valueKeywords:k,supportsAtComponent:!0,tokenHooks:{"/":function(e,t){return!!e.eat("*")&&(t.tokenize=y)(e,t)}},name:"css",helperType:"gss"})}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror"),require("../xml/xml"),require("../javascript/javascript"),require("../css/css")):"function"==typeof define&&define.amd?define(["../../lib/codemirror","../xml/xml","../javascript/javascript","../css/css"],e):e(CodeMirror)}(function(u){"use strict";var a={script:[["lang",/(javascript|babel)/i,"javascript"],["type",/^(?:text|application)\/(?:x-)?(?:java|ecma)script$|^module$|^$/i,"javascript"],["type",/./,"text/plain"],[null,null,"javascript"]],style:[["lang",/^css$/i,"css"],["type",/^(text\/)?(x-)?(stylesheet|css)$/i,"css"],["type",/./,"text/plain"],[null,null,"css"]]};var r={};function p(e,t){t=e.match(r[t=t]||(r[t]=new RegExp("\\s+"+t+"\\s*=\\s*('|\")?([^'\"]+)('|\")?\\s*")));return t?/^\s*(.*?)\s*$/.exec(t[2])[1]:""}function m(e,t){return new RegExp((t?"^":"")+"</\\s*"+e+"\\s*>","i")}function o(e,t){for(var r in e)for(var n=t[r]||(t[r]=[]),a=e[r],o=a.length-1;0<=o;o--)n.unshift(a[o])}u.defineMode("htmlmixed",function(l,e){var s=u.getMode(l,{name:"xml",htmlMode:!0,multilineTagIndentFactor:e.multilineTagIndentFactor,multilineTagIndentPastTag:e.multilineTagIndentPastTag,allowMissingTagName:e.allowMissingTagName}),c={},t=e&&e.tags,r=e&&e.scriptTypes;if(o(a,c),t&&o(t,c),r)for(var n=r.length-1;0<=n;n--)c.script.unshift(["type",r[n].matches,r[n].mode]);function d(e,t){var r,o,i,n=s.token(e,t.htmlState),a=/\btag\b/.test(n);return a&&!/[<>\s\/]/.test(e.current())&&(r=t.htmlState.tagName&&t.htmlState.tagName.toLowerCase())&&c.hasOwnProperty(r)?t.inTag=r+" ":t.inTag&&a&&/>$/.test(e.current())?(r=/^([\S]+) (.*)/.exec(t.inTag),t.inTag=null,a=">"==e.current()&&function(e,t){for(var r=0;r<e.length;r++){var n=e[r];if(!n[0]||n[1].test(p(t,n[0])))return n[2]}}(c[r[1]],r[2]),a=u.getMode(l,a),o=m(r[1],!0),i=m(r[1],!1),t.token=function(e,t){return e.match(o,!1)?(t.token=d,t.localState=t.localMode=null):(r=e,n=i,a=t.localMode.token(e,t.localState),e=r.current(),-1<(t=e.search(n))?r.backUp(e.length-t):e.match(/<\/?$/)&&(r.backUp(e.length),r.match(n,!1)||r.match(e)),a);var r,n,a},t.localMode=a,t.localState=u.startState(a,s.indent(t.htmlState,"",""))):t.inTag&&(t.inTag+=e.current(),e.eol()&&(t.inTag+=" ")),n}return{startState:function(){return{token:d,inTag:null,localMode:null,localState:null,htmlState:u.startState(s)}},copyState:function(e){var t;return e.localState&&(t=u.copyState(e.localMode,e.localState)),{token:e.token,inTag:e.inTag,localMode:e.localMode,localState:t,htmlState:u.copyState(s,e.htmlState)}},token:function(e,t){return t.token(e,t)},indent:function(e,t,r){return!e.localMode||/^\s*<\//.test(t)?s.indent(e.htmlState,t,r):e.localMode.indent?e.localMode.indent(e.localState,t,r):u.Pass},innerMode:function(e){return{state:e.localState||e.htmlState,mode:e.localMode||s}}}},"xml","javascript","css"),u.defineMIME("text/html","htmlmixed")}),CodeMirror.defineMode("bbcodemixed",function(e){var r,a,n,o=CodeMirror.getMode(e,"htmlmixed"),i=CodeMirror.getMode(e,"bbcode"),t={bbCodeLiteral:"literal"};function l(e){return e.replace(/([\[\]\.\-\+\<\>\?\:\(\)\{\}])/g,"\\$1")}return e.hasOwnProperty("bbCodeLiteral")&&(t.bbCodeLiteral=e.bbCodeLiteral),r={hasLeftDelimeter:/.*\[/,htmlHasLeftDelimeter:/[^\<\>]*\[/,literalOpen:new RegExp(l("["+t.bbCodeLiteral+"]")),literalClose:new RegExp(l("[/"+t.bbCodeLiteral+"]"))},a={chain:function(e,t,r){return(t.tokenize=r)(e,t)},cleanChain:function(e,t,r){return t.tokenize=null,t.localState=null,t.localMode=null,"string"==typeof r?r||null:r(e,t)},maybeBackup:function(e,t,r){t=l(t);var n=e.current(),a=n.search(t);return-1<a?e.backUp(n.length-a):n.match(/<\/?$/)&&(e.backUp(n.length),e.match(t,!1)||e.match(n[0])),r}},n={html:function(e,t){return!t.inLiteral&&e.match(r.htmlHasLeftDelimeter,!1)&&null===t.htmlMixedState.htmlState.tagName||!t.inLiteral&&e.match("[",!1)?(t.tokenize=n.bbcode,t.localMode=i,t.localState=i.startState(o.indent(t.htmlMixedState,"")),a.maybeBackup(e,"[",i.token(e,t.localState))):o.token(e,t.htmlMixedState)},bbcode:function(e,t){return e.match("]",!1)?(e.eat("]"),t.tokenize=n.html,t.localMode=o,t.localState=t.htmlMixedState,"tag"):a.maybeBackup(e,"]",i.token(e,t.localState))},inBlock:function(r,n){return function(e,t){for(;!e.eol();){if(e.match(n)){a.cleanChain(e,t,"");break}e.next()}return r}}},{startState:function(){var e=o.startState();return{token:n.html,localMode:null,localState:null,htmlMixedState:e,tokenize:null,inLiteral:!1}},copyState:function(e){var t=null,r=e.tokenize||e.token;return e.localState&&(t=CodeMirror.copyState(r!=n.html?i:o,e.localState)),{token:e.token,tokenize:e.tokenize,localMode:e.localMode,localState:t,htmlMixedState:CodeMirror.copyState(o,e.htmlMixedState),inLiteral:e.inLiteral}},token:function(e,t){if(e.match("[",!1)){if(!t.inLiteral&&e.match(r.literalOpen,!0))return t.inLiteral=!0,"keyword";if(t.inLiteral&&e.match(r.literalClose,!0))return t.inLiteral=!1,"keyword"}return t.inLiteral&&t.localState!=t.htmlMixedState&&(t.tokenize=n.html,t.localMode=o,t.localState=t.htmlMixedState),(t.tokenize||t.token)(e,t)},indent:function(e,t){return e.localMode==i||e.inLiteral&&!e.localMode||r.hasLeftDelimeter.test(t)?CodeMirror.Pass:o.indent(e.htmlMixedState,t)},innerMode:function(e){return{state:e.localState||e.htmlMixedState,mode:e.localMode||o}}}},"xml","javascript","css"),CodeMirror.defineMIME("text/x-bbcode","bbcodemixed");