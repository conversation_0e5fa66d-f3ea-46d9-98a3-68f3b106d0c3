/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'liststyle', 'ru', {
	armenian: 'Армянская нумерация',
	bulletedTitle: 'Свойства маркированного списка',
	circle: 'Круг',
	decimal: 'Десятичные (1, 2, 3, и т.д.)',
	decimalLeadingZero: 'Десятичные с ведущим нулём (01, 02, 03, и т.д.)',
	disc: 'Окружность',
	georgian: 'Грузинская нумерация (ани, бани, гани, и т.д.)',
	lowerAlpha: 'Строчные латинские (a, b, c, d, e, и т.д.)',
	lowerGreek: 'Строчные греческие (альфа, бета, гамма, и т.д.)',
	lowerRoman: 'Строчные римские (i, ii, iii, iv, v, и т.д.)',
	none: 'Нет',
	notset: '<не указано>',
	numberedTitle: 'Свойства нумерованного списка',
	square: 'Квадрат',
	start: 'Начиная с',
	type: 'Тип',
	upperAlpha: 'Заглавные латинские (A, B, C, D, E, и т.д.)',
	upperRoman: 'Заглавные римские (I, II, III, IV, V, и т.д.)',
	validateStartNumber: 'Первый номер списка должен быть задан обычным целым числом.'
} );
