/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'liststyle', 'gl', {
	armenian: 'Numeración armenia',
	bulletedTitle: 'Propiedades da lista viñeteada',
	circle: 'Circulo',
	decimal: 'Decimal (1, 2, 3, etc.)',
	decimalLeadingZero: 'Decimal con cero á esquerda (01, 02, 03, etc.)',
	disc: 'Disc',
	georgian: 'Numeración xeorxiana (an, ban, gan, etc.)',
	lowerAlpha: 'Alfabeto en minúsculas (a, b, c, d, e, etc.)',
	lowerGreek: '<PERSON><PERSON> en minúsculas (alpha, beta, gamma, etc.)',
	lowerRoman: 'Números romanos en minúsculas (i, ii, iii, iv, v, etc.)',
	none: 'Ningún',
	notset: '<sen estabelecer>',
	numberedTitle: 'Propiedades da lista numerada',
	square: 'Cadrado',
	start: 'Inicio',
	type: 'Tipo',
	upperAlpha: 'Alfabeto en maiúsculas (A, B, C, D, E, etc.)',
	upperRoman: 'Números romanos en maiúsculas (I, II, III, IV, V, etc.)',
	validateStartNumber: 'O número de inicio da lista debe ser un número enteiro.'
} );
