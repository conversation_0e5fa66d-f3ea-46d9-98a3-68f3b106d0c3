/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'liststyle', 'he', {
	armenian: 'ספרות ארמניות',
	bulletedTitle: 'תכונות רשימת תבליטים',
	circle: 'עיגול ריק',
	decimal: 'ספרות (1, 2, 3 וכו\')',
	decimalLeadingZero: 'ספרות עם 0 בהתחלה (01, 02, 03 וכו\')',
	disc: 'עיגול מלא',
	georgian: 'ספרות גיאורגיות (an, ban, gan וכו\')',
	lowerAlpha: 'אותיות אנגליות קטנות (a, b, c, d, e וכו\')',
	lowerGreek: 'אותיות יווניות קטנות (alpha, beta, gamma וכו\')',
	lowerRoman: 'ספירה רומית באותיות קטנות (i, ii, iii, iv, v וכו\')',
	none: 'ללא',
	notset: '<לא נקבע>',
	numberedTitle: 'תכונות רשימה ממוספרת',
	square: 'ריבוע',
	start: 'תחילת מספור',
	type: 'סוג',
	upperAlpha: 'אותיות אנגליות גדולות (A, B, C, D, E וכו\')',
	upperRoman: 'ספירה רומיות באותיות גדולות (I, II, III, IV, V וכו\')',
	validateStartNumber: 'שדה תחילת המספור חייב להכיל מספר שלם.'
} );
