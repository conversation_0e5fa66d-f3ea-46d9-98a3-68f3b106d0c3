/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'liststyle', 'es-mx', {
	armenian: 'Numeración armenia',
	bulletedTitle: 'Propiedades de la lista con viñetas',
	circle: 'Círculo',
	decimal: 'Decimal (1, 2, 3, etc.)',
	decimalLeadingZero: 'Decimal con cero (01, 02, 03, etc.)',
	disc: 'Desc',
	georgian: 'Numeración gregoriana (an, ban, gan, etc.)',
	lowerAlpha: 'Alfabeto minúscula (a, b, c, d, e, etc.)',
	lowerGreek: 'Griego minúscula (alpha, beta, gamma, etc.)',
	lowerRoman: 'Romano minúscula (i, ii, iii, iv, v, etc.)',
	none: 'Ninguno',
	notset: '<not set>',
	numberedTitle: 'Propiedades de la lista numerada',
	square: 'Cuadrado',
	start: 'Inicio',
	type: 'Tipo',
	upperAlpha: 'Abeced<PERSON> mayúscula (A, B, C, D, E, etc.)',
	upperRoman: '<PERSON><PERSON> may<PERSON> (I, II, III, IV, V, etc.)',
	validateStartNumber: 'El número de inicio de la lista debe ser un número entero.'
} );
