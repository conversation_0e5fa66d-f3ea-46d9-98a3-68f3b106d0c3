/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'liststyle', 'lt', {
	armenian: 'Armėniški skaitmenys',
	bulletedTitle: 'Ženklelinio sąrašo nustatymai',
	circle: 'Apskritimas',
	decimal: '<PERSON><PERSON><PERSON><PERSON><PERSON> (1, 2, 3, t.t)',
	decimalLeadingZero: 'Dešimtainis su nuliu priekyje (01, 02, 03, t.t)',
	disc: 'Diskas',
	georgian: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> skaitmen<PERSON> (an, ban, gan, t.t)',
	lowerAlpha: '<PERSON>ž<PERSON>ios Alpha (a, b, c, d, e, t.t)',
	lowerGreek: '<PERSON><PERSON><PERSON><PERSON>rai<PERSON> (alpha, beta, gamma, t.t)',
	lowerRoman: '<PERSON><PERSON><PERSON><PERSON> (i, ii, iii, iv, v, t.t)',
	none: 'Niekas',
	notset: '<nenurodytas>',
	numberedTitle: 'Skaitmeninio sąrašo nustatymai',
	square: '<PERSON><PERSON><PERSON><PERSON>',
	start: 'Pradžia',
	type: '<PERSON><PERSON><PERSON><PERSON>',
	upperAlpha: 'Didžiosios Alpha (A, B, C, D, E, t.t)',
	upperRoman: 'Didžiosios Romėnų (I, II, III, IV, V, t.t)',
	validateStartNumber: 'Sąrašo pradžios skaitmuo turi būti sveikas skaičius.'
} );
