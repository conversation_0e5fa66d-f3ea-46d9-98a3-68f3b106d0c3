/*
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
CKEDITOR.plugins.setLang( 'liststyle', 'oc', {
	armenian: 'Numerotacion armènia',
	bulletedTitle: 'Proprietats de la lista de piuses',
	circle: 'Cercle',
	decimal: 'Decimal (1, 2, 3, etc.)',
	decimalLeadingZero: 'Decimal precedit per un 0 (01, 02, 03, etc.)',
	disc: 'Disc',
	georgian: 'Numeracion georgiana (an, ban, gan, etc.)',
	lowerAlpha: 'Letras minusculas (a, b, c, d, e, etc.)',
	lowerGreek: 'Grèc minuscula (alfa, bèta, gamma, etc.)',
	lowerRoman: 'Chifras romanas minusculas (i, ii, iii, iv, v, etc.)',
	none: 'Pas cap',
	notset: '<indefinit>',
	numberedTitle: 'Proprietats de la lista numerotada',
	square: 'Carrat',
	start: 'Començament',
	type: 'Tipe',
	upperAlpha: 'Letras majusculas (A, B, C, D, E, etc.)',
	upperRoman: 'Chifras romanas majusculas (I, II, III, IV, V, etc.)',
	validateStartNumber: 'Lo primièr element de la lista deu èsser un nombre entièr.'
} );
