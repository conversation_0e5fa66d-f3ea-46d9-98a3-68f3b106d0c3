<!DOCTYPE html>
<!--
Copyright (c) 2003-2017, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
-->
<html>
<head>
	<meta charset="utf-8">
	<title>Clipboard playground &ndash; CKEditor Sample</title>
	<script src="../../../ckeditor.js"></script>
	<link href="../../../samples/old/sample.css" rel="stylesheet">
	<style>
body {
	margin: 0;
}

#editables, #console
{
	width: 48%;
}
#editable {
	padding: 5px 10px;
}

#console {
	position: fixed;
	top: 10px;
	right: 30px;
	height: 500px;
	border: solid 3px #555;
	overflow: auto;
}
#console > p {
	border-bottom: solid 1px #555;
	margin: 0;
	padding: 0 5px;
	background: rgba(0, 0, 0, 0.25);
	transition: background-color 1s;
}
#console > p.old {
	background: rgba(0, 0, 0, 0);
}
#console time, #console .prompt {
	padding: 0 5px;
	display: inline-block;
}
#console time {
	background: #999;
	background: rgba(0, 0, 0, 0.5 );
	color: #FFF;
	margin-left: -5px;
}
#console .prompt {
	background: #DDD;
	background: rgba(0, 0, 0, 0.1 );
	min-width: 200px;
}
.someClass {
	color: blue;
}
.specChar {
	color: #777;
	background-color: #EEE;
	background-color: rgba(0, 0, 0, 0.1);
	font-size: 0.8em;
	border-radius: 2px;
	padding: 1px;
}
	</style>
</head>
<body>
	<h1 class="samples">
		CKEditor Sample &mdash; clipboard plugin playground
	</h1>
	<div id="editables">
		<p>
			<label for="editor1">
				Editor 1:</label>
			<textarea cols="80" id="editor1" name="editor1" rows="10">&lt;p&gt;This is some &lt;strong&gt;sample text&lt;/strong&gt;. You are using &lt;a href="http://ckeditor.com/"&gt;CKEditor&lt;/a&gt;.&lt;/p&gt;</textarea>
		</p>
		<p>
			<label for="editor2">
				Editor 2:</label>
			<textarea cols="80" id="editor2" name="editor2" rows="10">&lt;p&gt;This is more &lt;strong class="MsoNormal"&gt;sample text&lt;/strong&gt;.&lt;/p&gt;</textarea>
		</p>
		<p>
			<label for="editor3">
				Editor 3:</label>
			<textarea cols="80" id="editor3" name="editor3" rows="10">&lt;p&gt;This editor &lt;strong&gt;forces pasting in text mode&lt;/strong&gt; by listening for "beforePaste" event.&lt;/p&gt;</textarea>
		</p>
		<p>
			<label for="editor4">
				Editor 4:</label>
			<textarea cols="80" id="editor4" name="editor4" rows="10">&lt;p&gt;This editor &lt;strong&gt;forces pasting in text mode&lt;/strong&gt; by "forcePasteAsPlainText" config option.&lt;/p&gt;</textarea>
		</p>
		<p>
			<label for="editor5">
				Editor 5:</label>
			<textarea cols="80" id="editor5" name="editor5" rows="10">Editor with autoParagraphing set to off.</textarea>
		</p>
		<div id="editor6" contenteditable="true" style="font-family: Georgia; font-size: 14px">
			<h1>Editor 6</h1>
			<p>Content content content.</p>
			<p class="someClass">Styled by <code>.someClass</code>.</p>
		</div>
	</div>
	<div id="console">
	</div>
	<script>
( function()
{
	'use strict';

	var log = window.__log = function( title, msg ) {
		var msgEl = new CKEDITOR.dom.element( 'p' ),
			consoleEl = CKEDITOR.document.getById( 'console' ),
			time = new Date().toString().match( /\d\d:\d\d:\d\d/ )[ 0 ],
			format = function( tpl ) {
				return tpl.replace( /{time}/g, time ).replace( '{title}', title ).replace( '{msg}', msg || '' );
			};

		window.console && console.log && console.log( format( '[{time}] {title}: {msg}' ) );

		msg = ( msg || '' ).replace( /\r/g, '{\\r}' ).replace( /\n/g, '{\\n}' ).replace( /\t/g, '{\\t}' );
		msg = CKEDITOR.tools.htmlEncode( msg );
		msg = msg.replace( /\{(\\\w)\}/g, '<code class="specChar">$1</code>' );

		msgEl.setHtml( format( '<time datetime="{time}">{time}</time><span class="prompt">{title}</span> {msg}' ) );
		consoleEl.append( msgEl );
		consoleEl.$.scrollTop = consoleEl.$.scrollHeight;
		setTimeout( function() { msgEl.addClass( 'old' ); }, 250 );
	};

	var observe = function( editor, num ) {
		var p = 'EDITOR ' + num + ' > ';

		editor.on( 'paste', function( event ) {
			log( p + 'paste(prior:-1)', event.data.type + ' - "' + event.data.dataValue + '"' );
		}, null, null, -1 );
		editor.on( 'paste', function( event ) {
			log( p + 'paste(prior:10)', event.data.type + ' - "' + event.data.dataValue + '"' );
		} );
		editor.on( 'paste', function( event ) {
			log( p + 'paste(prior:999)', event.data.type + ' - "' + event.data.dataValue + '"' );
		}, null, null, 999 );
		editor.on( 'beforePaste', function( event ) {
			log( p + 'beforePaste', event.data.type );
		} );
		editor.on( 'beforePaste', function( event ) {
			log( p + 'beforePaste(prior:999)', event.data.type );
		}, null, null, 999 );
		editor.on( 'afterPaste', function( event ) {
			log( p + 'afterPaste' );
		} );
		editor.on( 'copy', function( event ) {
			log( p + 'copy' );
		} );
		editor.on( 'cut', function( event ) {
			log( p + 'cut' );
		} );
	};

	CKEDITOR.disableAutoInline = true;
	var config = {
			height: 120,
			toolbar: [ [ 'Source' ] ],
			allowedContent: true
		},
		editor1 = CKEDITOR.replace( 'editor1', config ),
		editor2 = CKEDITOR.replace( 'editor2', config ),
		editor3 = CKEDITOR.replace( 'editor3', config ),
		editor4 = CKEDITOR.replace( 'editor4', CKEDITOR.tools.extend( { forcePasteAsPlainText: true }, config ) ),
		editor5 = CKEDITOR.replace( 'editor5', CKEDITOR.tools.extend( { autoParagraph: false }, config ) ),
		editor6 = CKEDITOR.inline( document.getElementById( 'editor6' ), config );

	editor3.on( 'beforePaste', function( evt ) {
		evt.data.type = 'text';
	} );

	observe( editor1, 1 );
	observe( editor2, 2 );
	observe( editor3, 3 );
	observe( editor4, 4 );
	observe( editor5, 5 );
	observe( editor6, 6 );

})();
	</script>
</body>
</html>
